---
type: "manual"
description: "Example description"
---
## 技术方案生成规则

1. **最小修改原则**

- 必须优先复用现有函数/接口

- 新功能需标注是否属于现有框架扩展

2. **伪代码规范**

- 在技术方案中不要直接实现代码功能，而是每部分通过满足以下规范的伪代码：

- 伪代码仅描述函数签名（参数/返回值类型）

- 核心逻辑用中文注释表达

- 复杂算法用step by step伪代码描述

- 具体用文字描述伪代码内容的实现细节

3. **数据格式确认**

- 对无参考的数据结构必须暂停生成

- 针对无法确认格式的数据结构，要求用户提供样例数据格式

- 参考代码库中的其他代码，交叉验证关联接口的输入输出

- 所有使用到的数据结构请单独起一个模块整理出来，并给出你认为的格式样例。并请明确指出让我确认对应的格式是否正确

4. **技术方案控制**

- 引入新的依赖需要向我确认是否可以使用

- 在技术方案选型不确定，或有多种实现方案时，和我确认具体选择的方案

- 如果对原始方案有更好的技术实现参考，也可以向我二次确认进行调整

5. **测试策略**

- 首次生成跳过测试代码

- 复杂模块标注测试要点(TODO)

- 仅对核心路径生成测试桩

6. **流程确认**

- 在技术文档中必须给出完整的功能流程图

- 流程中不确认的技术方案实现流程和细节需要向我二次确认