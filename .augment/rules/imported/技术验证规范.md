---
type: "agent_requested"
description: "Example description"
---
## Cursor插件验证生成规则
1. **验证文档规范**
- 具体实现细节参考 **plugins/wasm-go/extensions** 下的代码。
- 验证指南放在目录**document/技术验证/**下，验证指南包括测试环境搭建，编译和构建镜像，测试策略

2. **镜像生成**
- 必须使用如下命令进行wasm-plugin镜像构建，镜像地址都使用 **registry.baidubce.com/csm-offline/wasm-go**, 镜像 tag 为**当前日期**
- 以 key-auth 插件为例，构建命令如下  
```shell  
DOCKER_BUILDKIT=1 docker build --build-arg PLUGIN_NAME=key-auth \  
                                --build-arg BUILDER=higress-registry.cn-hangzhou.cr.aliyuncs.com/plugins/wasm-go-builder:go1.20.14-tinygo0.29.0-oras1.0.0  \  
                                --build-arg GOPROXY=https://proxy.golang.org,direct \  
                                -t registry.baidubce.com/csm-offline/wasm-go/key-auth:20250617 \  
                                ../../higress/plugins/wasm-go/  
```

```shell  
docker push registry.baidubce.com/csm-offline/wasm-go/key-auth:20250617  
```
- 新功能需标注是否属于现有框架扩展

3.**测试环境规范**
- 测试验证环境为标准 K8s 集群，集群 kubeconf 在 **testdata/aigw-offline-bj**
- 使用 kubectl 进行验证之前，需要 **export TEST_KUBE=../../testdata/aigw-offline-bj, export EXTERNAL_IP=***************，之后都对测试集群进行操作
- 测试命名空间：**istio-system-aigw-wdv5qgx7**
- 测试访问路由：
```shell
curl -v http://$EXTERNAL_IP/test/v1/chat/completions \
-H "Content-Type: application/json" \
-d '{
  "model": "deepseek-r1:1.5b",
  "messages": [{"role": "user", "content": "hello!"}],
  "temperature": 0.7,
  "stream": false
}'
```

4. **流程确认**
- 在技术验证文档中必须给出完整的功能流程图
- 流程中不确认的技术验证流程和细节需要向我二次确认