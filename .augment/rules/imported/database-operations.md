---
type: "agent_requested"
---

# Database Operations Template

When implementing database operations, follow the standard template based on [pkg/model/jwtcert](mdc:pkg/model/jwtcert) directory structure:

## Directory Structure
```
pkg/model/<模型名称>/
├── interface.go       # 定义服务接口
├── service.go         # 实现服务接口
├── option.go          # 配置选项
├── service_test.go    # 测试文件
└── dao/
    └── dao.go         # 数据访问实现
```

## Key Files and Patterns

### Data Models
- Define models in [pkg/model/meta](mdc:pkg/model/meta) directory
- Use `TableName()` method for table naming
- Include `dbutil.BaseModel` for common fields
- Use proper field tags: `gorm`, `json`, `dbutil`, `valid`

### Service Interface
- Define in `interface.go` with `WithTx()` support
- Include CRUD operations: Create, GetByID, Delete
- Follow the pattern in [pkg/model/interface.go](mdc:pkg/model/interface.go)

### Service Implementation
- Implement in `service.go` with transaction support
- Use proper error handling and logging
- Include permission checks and data validation
- Follow soft delete pattern (deleted=0/1)

### Key Principles
- Use `WithTx()` for transaction support
- Implement proper error handling
- Add comprehensive logging
- Follow AccountId-based data isolation
- Use soft delete with `deleted` field
- Table naming: `t_模块_实体` format
