---
type: "agent_requested"
description: "Example description"
---
涉及数据库的操作，遵循以下模板进行开发。
# 数据库操作通用模板教程

基于`pkg/model/jwtcert`目录的实现，以下是一个可复用的数据库操作模板教程，适用于创建新的数据模型和操作。

## 1. 目录结构设计

为新模型创建标准目录结构：

```
pkg/model/<模型名称>/
├── interface.go       # 定义服务接口
├── service.go         # 实现服务接口
├── option.go          # 配置选项
├── service_test.go    # 测试文件
└── dao/
    └── dao.go         # 数据访问实现
```

## 2. 数据模型定义

在`pkg/model/meta/<模型名称>.go`中定义数据模型：

```go
package meta

import (
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// 定义表名
func (m *YourModel) TableName() string {
    return "t_your_table_name"
}

// 定义数据结构
type YourModel struct {
    dbutil.BaseModel               // 基础字段(ID、创建时间等)
    Field1 string `gorm:"column:field1" json:"field1" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
    Field2 string `gorm:"column:field2" json:"field2" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
    // 添加其他字段
    Deleted *int `gorm:"column:deleted" json:"deleted" dbutil:"searchable:wildcard,orderable,updatable"`
}

// 创建模型实例的工厂方法
func NewYourModel(field1, field2 string) *YourModel {
    return &YourModel{
        Field1: field1,
        Field2: field2,
        Deleted: csm.Int(0),
    }
}
```

## 3. 添加DAO接口

在`pkg/model/interface.go`中添加DAO接口：

```go
// 添加到已有的interface.go文件中
type YourModelDaoInterface interface {
    dao.BaseInterface
}
```

## 4. 创建接口文件

创建`pkg/model/<模型名称>/interface.go`：

```go
package yourmodel

import (
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type ServiceInterface interface {
    WithTx(tx *dbutil.DB) ServiceInterface
    Create(ctx context.CsmContext, model *meta.YourModel) error
    GetByID(ctx context.CsmContext, id string) (*meta.YourModel, error)
    Delete(ctx context.CsmContext, id string) error
    // 添加其他需要的方法
}
```

## 5. 实现DAO

创建`pkg/model/<模型名称>/dao/dao.go`：

```go
package dao

import (
    "reflect"

    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao"
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type YourModelDao struct {
    *dao.Dao
}

func NewYourModelDao(db *dbutil.DB) *YourModelDao {
    t := reflect.TypeOf(meta.YourModel{})
    return &YourModelDao{
        Dao: dao.NewDao(t, db),
    }
}
```

## 6. 创建选项文件

创建`pkg/model/<模型名称>/option.go`：

```go
package yourmodel

import (
    "github.com/jinzhu/gorm"
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Option struct {
    DB *dbutil.DB
}

func NewOption(d *gorm.DB) *Option {
    return &Option{
        DB: dbutil.NewDB(d),
    }
}
```

## 7. 实现服务

创建`pkg/model/<模型名称>/service.go`：

```go
package yourmodel

import (
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
    csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model"
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/yourmodel/dao"
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
    csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Service struct {
    opt *Option
    dao model.YourModelDaoInterface
}

// 创建服务实例
func NewYourModelService(option *Option) *Service {
    return &Service{
        opt: option,
        dao: dao.NewYourModelDao(option.DB),
    }
}

// 支持事务的服务
func (s *Service) WithTx(tx *dbutil.DB) ServiceInterface {
    opt := *s.opt
    opt.DB = tx
    return NewYourModelService(&opt)
}

// 创建记录
func (s *Service) Create(ctx csmContext.CsmContext, model *meta.YourModel) error {
    // 可以在保存前进行权限检查或其他逻辑
    return s.dao.Save(ctx, model)
}

// 根据ID获取记录
func (s *Service) GetByID(ctx csmContext.CsmContext, id string) (*meta.YourModel, error) {
    // 获取当前用户
    accountId, err := iam.GetAccountId(ctx)
    if err != nil {
        return nil, csmErr.NewUnauthorizedException("user is nil", err)
    }
    
    // 构造查询条件
    where := &meta.YourModel{
        Deleted:   csm.Int(0),
        AccountId: accountId,
        // 添加其他条件
    }
    
    result, err := s.dao.LoadWithWhere(ctx, where)
    if err != nil {
        return nil, err
    }
    
    return result.(*meta.YourModel), nil
}

// 删除记录
func (s *Service) Delete(ctx csmContext.CsmContext, id string) error {
    // 构造删除条件
    where := &meta.YourModel{
        Deleted: csm.Int(0),
        // 添加其他条件
    }
    
    return s.dao.BatchDelete(ctx, where)
}
```

## 8. 编写测试

创建`pkg/model/<模型名称>/service_test.go`：

```go
package yourmodel

import (
    "testing"
    
    "github.com/golang/mock/gomock"
    "github.com/jinzhu/gorm"
    "github.com/stretchr/testify/assert"
    
    daoMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao/mocks"
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
    "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

func TestService_Create(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    
    mockDB, _ := gorm.Open("sqlite3", ":memory:")
    mockDao := daoMock.NewMockBaseInterface(ctrl)
    
    service := &Service{
        opt: NewOption(mockDB),
        dao: mockDao,
    }
    
    t.Run("success", func(t *testing.T) {
        mockCtx := context.MockNewCsmContext()
        mockDao.EXPECT().Save(mockCtx, gomock.Any()).Return(nil)
        
        testModel := &meta.YourModel{}
        err := service.Create(mockCtx, testModel)
        assert.Nil(t, err)
    })
}
```

## 9. 使用示例

以下是使用模板创建的服务的示例：

```go
// 初始化服务
db := dbutil.NewDB(gormDB)
option := yourmodel.NewOption(gormDB)
service := yourmodel.NewYourModelService(option)

// 创建记录
model := meta.NewYourModel("field1", "field2")
err := service.Create(ctx, model)
if err != nil {
    // 处理错误
}

// 查询记录
record, err := service.GetByID(ctx, "your-id")
if err != nil {
    // 处理错误
}

// 删除记录
err = service.Delete(ctx, "your-id")
if err != nil {
    // 处理错误
}

// 在事务中使用
tx := db.Begin()
txService := service.WithTx(tx)

err = txService.Create(ctx, model)
if err != nil {
    tx.Rollback()
    return err
}

tx.Commit()
```

## 10. 核心工作流程

1. **定义数据模型**：在meta包中定义表结构
2. **定义接口**：创建服务接口定义
3. **实现DAO层**：创建数据访问对象
4. **实现服务层**：实现业务逻辑
5. **支持事务**：通过WithTx方法支持事务操作
6. **编写测试**：测试服务功能

## 注意事项

1. **软删除设计**：使用deleted字段(0表示正常，1表示删除)实现软删除
2. **权限检查**：在查询时加入AccountId条件确保数据隔离
3. **错误处理**：使用封装的错误类型返回给调用方
4. **事务支持**：通过WithTx方法实现事务支持
5. **表名规范**：遵循`t_模块_实体`的命名规范
6. **字段注解**：使用gorm、dbutil、valid等注解规范字段定义

按照这个模板，你可以快速创建标准化的数据模型和操作，确保代码风格统一并且功能完备。

