---
type: "agent_requested"
description: "Example description"
---
# Route Management Patterns

Route management follows specific patterns as implemented in [cmd/csm/app/core/aigateway.go](mdc:cmd/csm/app/core/aigateway.go) and [pkg/model/meta/airoute.go](mdc:pkg/model/meta/airoute.go).

## Core Components

### Route Data Models
- Main request/response models in [pkg/model/meta/airoute.go](mdc:pkg/model/meta/airoute.go)
- `AIRouteRequest` for input validation
- `AIRouteResponse` for API responses
- `MatchRule` with support for rewrite functionality

### Route Creation Flow
1. **Validation**: Multi-service, Token rate limit, and Rewrite config validation
2. **VirtualService Creation**: Single-service vs Multi-service modes
3. **DestinationRule Creation**: For load balancing algorithms
4. **Plugin Management**: Token rate limiting and AI quota plugins

### Key Functions in [cmd/csm/app/core/aigateway.go](mdc:cmd/csm/app/core/aigateway.go)
- `CreateRoute()` - Main route creation endpoint
- `createSingleServiceVirtualService()` - Single service mode
- `createRatioBasedVirtualService()` - Multi-service ratio distribution
- `createModelNameBasedVirtualService()` - Multi-service model-name distribution
- `UpdateRoute()` - Route updates with same validation patterns

## Rewrite Functionality
- Dynamic rewrite based on `MatchRules.Rewrite.Enabled`
- When enabled: Set `HTTPRewrite{Uri: path}`
- When disabled: No rewrite field, preserve original path
- Store state in VirtualService annotations: `rewrite-enabled`

## Multi-Service Support
- **Ratio Distribution**: Traffic split by percentage
- **Model Name Distribution**: Route by model header
- Proper validation for each strategy
- EnvoyFilter creation for model-based routing

## Logging and Debugging
- Comprehensive logging at validation points
- Route creation decision logging
- Rewrite configuration logging
- Error handling with proper error types

## Interface Documentation
- API specs in [document/接口文档/路由管理](mdc:document/接口文档/路由管理)
- Implementation summaries in [document/接口实现总结](mdc:document/接口实现总结)
