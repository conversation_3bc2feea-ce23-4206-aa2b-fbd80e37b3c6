---
type: "agent_requested"
---

# Project Structure Guide

This AI Gateway project follows a structured Go layout with specific patterns for different components.

## Core Directories

### Application Core
- [cmd/csm/app/core](mdc:cmd/csm/app/core) - Main application logic and API handlers
- [cmd/csm/app/router](mdc:cmd/csm/app/router) - HTTP route definitions
- [pkg/server](mdc:pkg/server) - Server setup, middleware, and context handling

### Data Layer
- [pkg/model/meta](mdc:pkg/model/meta) - Data structure definitions
- [pkg/model](mdc:pkg/model) - Business logic services organized by domain
- [pkg/dao](mdc:pkg/dao) - Data access layer interfaces and implementations

### Service Layer
- [pkg/service](mdc:pkg/service) - Business services organized by domain (aiservices, cluster, gateway, etc.)
- [pkg/util](mdc:pkg/util) - Utility functions and helpers

### Infrastructure
- [pkg/bce](mdc:pkg/bce) - Baidu Cloud Engine SDK integrations
- [pkg/rpc](mdc:pkg/rpc) - RPC client configurations
- [higress](mdc:higress) - Higress service mesh integration

## Key Configuration Files
- [go.mod](mdc:go.mod) - Go module dependencies
- [templates](mdc:templates) - YAML templates for various components
- [testdata](mdc:testdata) - Test configuration and kubeconfig files

## Documentation Structure
- [document/接口文档](mdc:document/接口文档) - API documentation by domain
- [document/技术方案](mdc:document/技术方案) - Technical design documents
- [document/技术验证](mdc:document/技术验证) - Technical validation guides
- [document/接口实现总结](mdc:document/接口实现总结) - Implementation summaries

## Domain Organization
Services are organized by business domains:
- **AI Services**: [pkg/service/aiservices](mdc:pkg/service/aiservices), [pkg/service/aiingress](mdc:pkg/service/aiingress)
- **Gateway Management**: [pkg/service/gateway](mdc:pkg/service/gateway), [pkg/service/instances](mdc:pkg/service/instances)
- **Infrastructure**: [pkg/service/cluster](mdc:pkg/service/cluster), [pkg/service/cce](mdc:pkg/service/cce)
- **Monitoring**: [pkg/service/monitor](mdc:pkg/service/monitor), [pkg/service/diagnosis](mdc:pkg/service/diagnosis)

## Entry Points
- [cmd/proxy/main.go](mdc:cmd/proxy/main.go) - Application entry point
- [pkg/server/server.go](mdc:pkg/server/server.go) - HTTP server setup
