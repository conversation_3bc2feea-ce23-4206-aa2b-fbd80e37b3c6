{"单服务模式Fallback示例": {"routeName": "ai-inference-route", "srcProduct": "aibox", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/v1/models/", "caseSensitive": true}, "methods": ["GET", "POST"]}, "multiService": false, "targetService": {"serviceSource": "CCE", "serviceName": "ai-primary-service", "namespace": "ai-namespace", "servicePort": 8080, "loadBalanceAlgorithm": "round-robin"}, "rewrite": {"enabled": false}, "authEnabled": false, "tokenRateLimit": {"enabled": false}, "timeoutPolicy": {"enabled": true, "timeout": 30}, "retryPolicy": {"enabled": true, "retryConditions": "gateway-error,connect-failure,refused-stream,5xx", "numRetries": 2}, "fallbackConfig": {"enabled": true, "serviceName": "ai-fallback-service", "namespace": "ai-namespace", "servicePort": 8080}}, "多服务模式Fallback示例": {"routeName": "ai-multi-service-route", "srcProduct": "aibox", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/v1/models/", "caseSensitive": true}, "methods": ["GET", "POST"]}, "multiService": true, "trafficDistributionStrategy": "ratio", "targetService": [{"serviceSource": "CCE", "serviceName": "ai-service-1", "namespace": "ai-namespace", "servicePort": 8080, "loadBalanceAlgorithm": "round-robin", "requestRatio": 70}, {"serviceSource": "CCE", "serviceName": "ai-service-2", "namespace": "ai-namespace", "servicePort": 8080, "loadBalanceAlgorithm": "round-robin", "requestRatio": 30}], "rewrite": {"enabled": false}, "authEnabled": false, "tokenRateLimit": {"enabled": false}, "timeoutPolicy": {"enabled": true, "timeout": 30}, "retryPolicy": {"enabled": true, "retryConditions": "gateway-error,connect-failure,refused-stream,5xx", "numRetries": 2}, "fallbackConfig": {"enabled": true, "serviceName": "ai-shared-fallback-service", "namespace": "ai-namespace", "servicePort": 8080}}, "模型名分发Fallback示例": {"routeName": "ai-model-route", "srcProduct": "aibox", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/v1/models/", "caseSensitive": true}, "methods": ["GET", "POST"]}, "multiService": true, "trafficDistributionStrategy": "model_name", "targetService": [{"serviceSource": "CCE", "serviceName": "gpt-4-service", "namespace": "ai-namespace", "servicePort": 8080, "loadBalanceAlgorithm": "round-robin", "modelName": "gpt-4"}, {"serviceSource": "CCE", "serviceName": "gpt-3-service", "namespace": "ai-namespace", "servicePort": 8080, "loadBalanceAlgorithm": "round-robin", "modelName": "gpt-3.5-turbo"}], "rewrite": {"enabled": false}, "authEnabled": false, "tokenRateLimit": {"enabled": false}, "fallbackConfig": {"enabled": true, "serviceName": "ai-shared-fallback-service", "namespace": "ai-namespace", "servicePort": 8080}}}