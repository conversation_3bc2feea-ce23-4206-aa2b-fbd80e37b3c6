# AI网关Fallback容灾策略API使用示例

本文档提供了AI网关Fallback容灾策略相关API的完整使用示例，包括创建路由、查询路由详情和更新路由的操作。

## 1. 创建带Fallback配置的路由

### 1.1 单服务模式

**请求示例：**
```bash
curl -X POST "https://csm.baidubce.com/api/aigw/v1/aigateway/gw-ist9vvin/cluster-123/route" \
  -H "Content-Type: application/json" \
  -H "X-Region: gz" \
  -H "Authorization: bce-auth-v1/..." \
  -d '{
    "routeName": "ai-inference-route",
    "srcProduct": "aibox",
    "matchRules": {
      "pathRule": {
        "matchType": "prefix",
        "value": "/v1/models/",
        "caseSensitive": true
      },
      "methods": ["GET", "POST"]
    },
    "multiService": false,
    "targetService": {
      "serviceSource": "CCE",
      "serviceName": "ai-primary-service",
      "namespace": "ai-namespace",
      "servicePort": 8080,
      "loadBalanceAlgorithm": "round-robin"
    },
    "rewrite": {
      "enabled": false
    },
    "authEnabled": false,
    "tokenRateLimit": {
      "enabled": false
    },
    "timeoutPolicy": {
      "enabled": true,
      "timeout": 30
    },
    "retryPolicy": {
      "enabled": true,
      "retryConditions": "gateway-error,connect-failure,refused-stream,5xx",
      "numRetries": 2
    },
    "fallbackConfig": {
      "enabled": true,
      "serviceName": "ai-fallback-service",
      "namespace": "ai-namespace",
      "servicePort": 8080
    }
  }'
```

**响应示例：**
```json
{
  "routeName": "ai-inference-route",
  "createTime": "2025-04-18 15:20:35",
  "multiService": false,
  "matchRules": {
    "pathRule": {
      "matchType": "prefix",
      "value": "/v1/models/",
      "caseSensitive": true
    },
    "methods": ["GET", "POST"]
  },
  "targetService": {
    "serviceSource": "CCE",
    "serviceName": "ai-primary-service",
    "namespace": "ai-namespace",
    "servicePort": 8080,
    "loadBalanceAlgorithm": "round-robin"
  },
  "rewrite": {
    "enabled": false
  },
  "authEnabled": false,
  "tokenRateLimit": {
    "enabled": false
  },
  "timeoutPolicy": {
    "enabled": true,
    "timeout": 30
  },
  "retryPolicy": {
    "enabled": true,
    "retryConditions": "gateway-error,connect-failure,refused-stream,5xx",
    "numRetries": 2
  },
  "fallbackConfig": {
    "enabled": true,
    "serviceName": "ai-fallback-service",
    "namespace": "ai-namespace",
    "servicePort": 8080
  }
}
```

### 1.2 多服务模式（比例分发）

**请求示例：**
```bash
curl -X POST "https://csm.baidubce.com/api/aigw/v1/aigateway/gw-ist9vvin/cluster-123/route" \
  -H "Content-Type: application/json" \
  -H "X-Region: gz" \
  -H "Authorization: bce-auth-v1/..." \
  -d '{
    "routeName": "ai-multi-service-route",
    "srcProduct": "aibox",
    "matchRules": {
      "pathRule": {
        "matchType": "prefix",
        "value": "/v1/models/",
        "caseSensitive": true
      },
      "methods": ["GET", "POST"]
    },
    "multiService": true,
    "trafficDistributionStrategy": "ratio",
    "targetService": [
      {
        "serviceSource": "CCE",
        "serviceName": "ai-service-1",
        "namespace": "ai-namespace",
        "servicePort": 8080,
        "loadBalanceAlgorithm": "round-robin",
        "requestRatio": 70
      },
      {
        "serviceSource": "CCE",
        "serviceName": "ai-service-2",
        "namespace": "ai-namespace",
        "servicePort": 8080,
        "loadBalanceAlgorithm": "round-robin",
        "requestRatio": 30
      }
    ],
    "rewrite": {
      "enabled": false
    },
    "authEnabled": false,
    "tokenRateLimit": {
      "enabled": false
    },
    "fallbackConfig": {
      "enabled": true,
      "serviceName": "ai-shared-fallback-service",
      "namespace": "ai-namespace",
      "servicePort": 8080
    }
  }'
```

## 2. 查询路由详情

**请求示例：**
```bash
curl -X GET "https://csm.baidubce.com/api/aigw/v1/aigateway/gw-ist9vvin/ai-inference-route/route/detail" \
  -H "X-Region: gz" \
  -H "Authorization: bce-auth-v1/..."
```

**响应示例：**
```json
{
  "routeName": "ai-inference-route",
  "createTime": "2025-04-18 15:20:35",
  "updateTime": "2025-04-19 10:15:20",
  "multiService": false,
  "matchRules": {
    "pathRule": {
      "matchType": "prefix",
      "value": "/v1/models/",
      "caseSensitive": true
    },
    "methods": ["GET", "POST"]
  },
  "targetService": {
    "serviceSource": "CCE",
    "serviceName": "ai-primary-service",
    "namespace": "ai-namespace",
    "servicePort": 8080,
    "loadBalanceAlgorithm": "round-robin"
  },
  "rewrite": {
    "enabled": false
  },
  "authEnabled": false,
  "allowedConsumers": [],
  "srcProduct": "aibox",
  "tokenRateLimit": {
    "enabled": false
  },
  "timeoutPolicy": {
    "enabled": true,
    "timeout": 30
  },
  "retryPolicy": {
    "enabled": true,
    "retryConditions": "gateway-error,connect-failure,refused-stream,5xx",
    "numRetries": 2
  },
  "fallbackConfig": {
    "enabled": true,
    "serviceName": "ai-fallback-service",
    "namespace": "ai-namespace",
    "servicePort": 8080
  }
}
```

## 3. 更新路由配置

### 3.1 启用Fallback配置

**请求示例：**
```bash
curl -X PUT "https://csm.baidubce.com/api/aigw/v1/aigateway/gw-ist9vvin/ai-inference-route/route/detail" \
  -H "Content-Type: application/json" \
  -H "X-Region: gz" \
  -H "Authorization: bce-auth-v1/..." \
  -d '{
    "srcProduct": "aibox",
    "matchRules": {
      "pathRule": {
        "matchType": "prefix",
        "value": "/v1/models/",
        "caseSensitive": true
      },
      "methods": ["GET", "POST"]
    },
    "multiService": false,
    "targetService": {
      "serviceSource": "CCE",
      "serviceName": "ai-primary-service-v2",
      "namespace": "ai-namespace",
      "servicePort": 8080,
      "loadBalanceAlgorithm": "round-robin"
    },
    "rewrite": {
      "enabled": false
    },
    "authEnabled": false,
    "tokenRateLimit": {
      "enabled": false
    },
    "fallbackConfig": {
      "enabled": true,
      "serviceName": "ai-fallback-service-v2",
      "namespace": "ai-namespace",
      "servicePort": 9090
    }
  }'
```

**响应示例：**
```json
{
  "routeName": "ai-inference-route",
  "updateTime": "2025-04-19 14:30:15",
  "tokenRateLimit": {
    "enabled": false
  },
  "fallbackConfig": {
    "enabled": true,
    "serviceName": "ai-fallback-service-v2",
    "namespace": "ai-namespace",
    "servicePort": 9090
  }
}
```

### 3.2 禁用Fallback配置

**请求示例：**
```bash
curl -X PUT "https://csm.baidubce.com/api/aigw/v1/aigateway/gw-ist9vvin/ai-inference-route/route/detail" \
  -H "Content-Type: application/json" \
  -H "X-Region: gz" \
  -H "Authorization: bce-auth-v1/..." \
  -d '{
    "srcProduct": "aibox",
    "matchRules": {
      "pathRule": {
        "matchType": "prefix",
        "value": "/v1/models/",
        "caseSensitive": true
      }
    },
    "multiService": false,
    "targetService": {
      "serviceSource": "CCE",
      "serviceName": "ai-primary-service",
      "namespace": "ai-namespace",
      "servicePort": 8080
    },
    "rewrite": {
      "enabled": false
    },
    "authEnabled": false,
    "tokenRateLimit": {
      "enabled": false
    },
    "fallbackConfig": {
      "enabled": false
    }
  }'
```

## 4. 错误处理示例

### 4.1 Fallback配置验证错误

**请求示例（缺少服务名）：**
```json
{
  "fallbackConfig": {
    "enabled": true,
    "serviceName": "",
    "namespace": "ai-namespace",
    "servicePort": 8080
  }
}
```

**错误响应：**
```json
{
  "success": false,
  "status": 400,
  "error": {
    "code": "InvalidParameterValue",
    "message": "fallback service name is required when fallback is enabled"
  }
}
```

### 4.2 端口范围错误

**请求示例（无效端口）：**
```json
{
  "fallbackConfig": {
    "enabled": true,
    "serviceName": "ai-fallback-service",
    "namespace": "ai-namespace",
    "servicePort": 70000
  }
}
```

**错误响应：**
```json
{
  "success": false,
  "status": 400,
  "error": {
    "code": "InvalidParameterValue",
    "message": "fallback service port must be between 1 and 65535"
  }
}
```

## 5. 最佳实践

### 5.1 容灾服务部署建议
- 容灾服务应部署在不同的可用区
- 容灾服务应具备与主服务相同的API接口
- 建议容灾服务提供基本的降级功能

### 5.2 配置建议
- 在生产环境中启用Fallback配置
- 定期测试容灾切换功能
- 监控容灾切换事件的发生频率
- 设置适当的超时和重试策略

### 5.3 监控告警
- 监控主服务的健康状态
- 设置容灾切换的告警通知
- 监控容灾服务的性能指标
- 配置主服务恢复的自动通知
