package testdata

import (
	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/spf13/viper"

	v1 "k8s.io/api/admissionregistration/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	LocalAccessValue = "ALTAKgEYq7AE0lP3wECucIC3Ba"

	LocalSecretValue = "48261c922bc94bbabf645398addc5355"

	localAccessKey = "local.access_key"

	localSecretKey = "local.secret_key"

	EipEndpoint = "http://eip.%s.baidubce.com"

	CceEndpoint = "http://cce.%s.baidubce.com"

	LocalDev = "local.dev"

	CceAccessKey = "cloud.hosting.access_key"

	CceSecretKey = "cloud.hosting.secret_key"

	CceEndpointKey = "cloud.cce.endpoint"
)

var (
	kubeConfig = `apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: xxx
    server: https://*************:6443
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: 21609f0c2f29409c8567840642f721d2
  name: 21609f0c2f29409c8567840642f721d2@kubernetes
current-context: 21609f0c2f29409c8567840642f721d2@kubernetes
kind: Config
preferences: {}
users:
- name: 21609f0c2f29409c8567840642f721d2
  user:
    client-certificate-data: xxx
    client-key-data: xxx`

	Version1146 = "1.14.6"
)

func BuildMutatingWebhookConfiguration(name string) *v1.MutatingWebhookConfiguration {
	return &v1.MutatingWebhookConfiguration{
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
		},
		Webhooks: []v1.MutatingWebhook{
			{
				Name:         name,
				ClientConfig: v1.WebhookClientConfig{},
			},
		},
	}
}

func MockViper() {
	viper.Set(localAccessKey, LocalAccessValue)
	viper.Set(localSecretKey, LocalSecretValue)
}

func MockHostingAKSK() {
	viper.Set(CceAccessKey, LocalAccessValue)
	viper.Set(CceSecretKey, LocalSecretValue)
	viper.Set(CceEndpointKey, CceEndpoint)
}

func MockLocalAKSK() {
	MockViper()
}

func MockLocalEnv(dev bool) {
	viper.Set(LocalDev, dev)
}

func MockBuildGetKubeConfigResponse() *cce_v2.GetKubeConfigResponse {
	return &cce_v2.GetKubeConfigResponse{
		KubeConfigType: "",
		KubeConfig:     kubeConfig,
		RequestID:      "xxx",
	}
}
