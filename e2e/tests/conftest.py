"""
pytest配置文件

定义全局的pytest配置和夹具
"""

import pytest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入所有fixtures
from e2e.fixtures.test_fixtures import *


def pytest_configure(config):
    """pytest配置钩子"""
    # 添加自定义标记
    config.addinivalue_line(
        "markers", "e2e: 端到端测试标记"
    )
    config.addinivalue_line(
        "markers", "instance: 实例管理测试标记"
    )
    config.addinivalue_line(
        "markers", "service: 服务管理测试标记"
    )
    config.addinivalue_line(
        "markers", "route: 路由管理测试标记"
    )
    config.addinivalue_line(
        "markers", "slow: 耗时较长的测试标记"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试项集合"""
    # 为特定测试添加标记
    for item in items:
        # 为E2E测试添加slow标记
        if "test_complete_ai_gateway_lifecycle" in item.nodeid:
            item.add_marker(pytest.mark.slow)
            item.add_marker(pytest.mark.e2e)
        
        # 为错误处理测试添加标记
        if "TestErrorHandling" in item.nodeid:
            item.add_marker(pytest.mark.fast)


def pytest_runtest_setup(item):
    """测试运行前的设置"""
    # 可以在这里添加测试前的通用设置
    pass


def pytest_runtest_teardown(item, nextitem):
    """测试运行后的清理"""
    # 可以在这里添加测试后的通用清理
    pass 