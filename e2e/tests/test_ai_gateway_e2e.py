"""
AI网关E2E测试用例

测试AI网关实例的完整生命周期管理流程
重构后的测试用例，每个测试方法独立，减少耦合
"""

import pytest
import time
from typing import Dict, Any
from loguru import logger

from ..services.instance_service import InstanceService
from ..services.service_service import ServiceManagementService
from ..services.route_service import RouteService
from ..core.api_client import APIError


@pytest.mark.e2e
@pytest.mark.slow
class TestAIGatewayE2E:
    """AI网关E2E完整生命周期测试类"""

    def test_complete_ai_gateway_lifecycle(
        self,
        shared_test_instance: Dict[str, Any],
        service_management_service: ServiceManagementService,
        route_service: RouteService,
        mock_route_config: Dict[str, Any]
    ):
        """
        测试完整的AI网关生命周期

        这是一个集成测试，验证整个流程的连贯性：
        1. 验证网关实例已创建
        2. 验证实例已就绪
        3. 添加服务到网关
        4. 配置路由规则
        """
        logger.info("=== 开始AI网关完整生命周期测试 ===")

        create_response = shared_test_instance["create_response"]
        ready_detail = shared_test_instance["ready_detail"]
        instance_id = shared_test_instance["instance_id"]

        # 步骤1: 验证网关实例已创建
        logger.info("步骤1: 验证网关实例已创建")
        assert create_response.instanceId is not None, "实例ID不能为空"
        assert create_response.requestId is not None, "请求ID不能为空"
        assert create_response.taskId is not None, "任务ID不能为空"
        logger.success(f"✓ 实例创建验证通过，ID: {instance_id}")

        # 步骤2: 验证实例已就绪
        logger.info("步骤2: 验证实例已就绪")
        assert ready_detail.instanceId == instance_id, "实例ID不匹配"
        assert ready_detail.status == "running", f"实例状态应为running，实际为: {ready_detail.status}"
        logger.success(f"✓ 实例就绪验证通过，状态: {ready_detail.status}")

        # 步骤3: 添加服务到网关
        logger.info("步骤3: 添加服务到网关")
        add_service_response = service_management_service.add_services_to_instance(instance_id)

        # 验证服务添加结果
        assert add_service_response.addedCount > 0, "至少应该添加一个服务"
        logger.success(f"✓ 服务添加成功，共添加{add_service_response.addedCount}个服务")

        # 步骤4: 配置路由规则
        logger.info("步骤4: 配置路由规则")
        cluster_id = service_management_service.settings.instance.cluster_id
        route_name = f"test-route-{int(time.time())}"

        route_response = route_service.create_route(
            instance_id=instance_id,
            cluster_id=cluster_id,
            route_name=route_name,
            route_config=mock_route_config
        )

        # 验证路由创建结果
        assert route_response.success is True, "路由创建应该成功"
        logger.success(f"✓ 路由配置成功，路由名称: {route_name}")

        logger.success("=== AI网关完整生命周期测试完成 ===")


@pytest.mark.instance
class TestInstanceManagement:
    """实例管理测试类 - 使用共享实例测试实例管理功能"""

    def test_create_instance_success(self, shared_test_instance: Dict[str, Any]):
        """测试创建实例成功 - 验证共享实例的创建响应"""
        create_response = shared_test_instance["create_response"]
        instance_id = shared_test_instance["instance_id"]

        # 验证创建响应的完整性
        assert create_response.instanceId is not None, "实例ID不能为空"
        assert len(create_response.instanceId) > 0, "实例ID不能为空字符串"
        assert create_response.instanceId.startswith("aigw-"), f"实例ID格式不正确: {create_response.instanceId}"
        assert create_response.requestId is not None, "请求ID不能为空"
        assert create_response.taskId is not None, "任务ID不能为空"

        logger.info(f"✓ 实例创建测试通过，实例ID: {instance_id}")

    def test_wait_for_instance_ready_success(self, shared_test_instance: Dict[str, Any]):
        """测试等待实例就绪成功 - 验证共享实例的就绪状态"""
        ready_detail = shared_test_instance["ready_detail"]
        instance_id = shared_test_instance["instance_id"]

        # 验证就绪状态
        assert ready_detail.instanceId == instance_id, "实例ID不匹配"
        assert ready_detail.status == "running", f"实例应为running状态，实际为: {ready_detail.status}"

        logger.info(f"✓ 实例就绪等待测试通过，状态: {ready_detail.status}")

    def test_instance_status_validation(self, shared_test_instance: Dict[str, Any]):
        """测试实例状态验证 - 验证共享实例的状态信息"""
        ready_detail = shared_test_instance["ready_detail"]

        # 验证实例状态信息
        assert ready_detail.status == "running", f"实例状态应为running: {ready_detail.status}"
        assert ready_detail.gatewayType == "small", f"网关类型应为small: {ready_detail.gatewayType}"
        assert ready_detail.replicas == 2, f"副本数应为2: {ready_detail.replicas}"
        assert ready_detail.region == "bj", f"地域应为bj: {ready_detail.region}"

        logger.info(f"✓ 实例状态验证测试通过")


@pytest.mark.error
class TestErrorHandling:
    """错误处理测试类 - 独立测试各种错误场景"""
    def test_create_route_without_services(
        self,
        route_service: RouteService,
        mock_route_config: Dict[str, Any]
    ):
        """测试在不存在的实例上创建路由 - 独立测试路由创建错误处理"""
        # 使用不存在的实例ID测试路由创建错误处理
        nonexistent_instance_id = "aigw-nonexistent-12345"
        cluster_id = "cce-nonexistent"
        route_name = f"test-route-no-instance-{int(time.time())}"

        # 尝试在不存在的实例上创建路由（应该失败）
        with pytest.raises(APIError) as exc_info:
            route_service.create_route(
                instance_id=nonexistent_instance_id,
                cluster_id=cluster_id,
                route_name=route_name,
                route_config=mock_route_config
            )

        # 验证错误信息
        assert exc_info.value.status_code == 200  # API返回200但success为false

        logger.info("✓ 不存在实例路由创建错误处理测试通过")


@pytest.mark.validation
class TestDataValidation:
    """数据验证测试类 - 独立测试数据格式和内容验证"""

    def test_instance_id_format_validation(self, shared_test_instance: Dict[str, Any]):
        """测试实例ID格式验证 - 使用共享实例验证ID格式"""
        create_response = shared_test_instance["create_response"]
        instance_id = shared_test_instance["instance_id"]

        # 验证实例ID格式
        assert create_response.instanceId.startswith("aigw-"), f"实例ID格式不正确: {create_response.instanceId}"
        assert len(create_response.instanceId) > 5, f"实例ID长度不足: {create_response.instanceId}"
        assert "-" in create_response.instanceId, f"实例ID格式不正确，缺少分隔符: {create_response.instanceId}"

        logger.info(f"✓ 实例ID格式验证测试通过: {instance_id}")



    def test_service_list_validation(self, service_management_service: ServiceManagementService):
        """测试服务列表验证 - 独立测试服务配置验证"""
        # 验证配置中的服务列表
        service_list = service_management_service.settings.service.service_list

        assert isinstance(service_list, list), "服务列表应该是数组类型"
        assert len(service_list) > 0, "服务列表不能为空"

        for service_name in service_list:
            assert isinstance(service_name, str), f"服务名称应该是字符串: {service_name}"
            assert len(service_name) > 0, f"服务名称不能为空: {service_name}"

        logger.info(f"✓ 服务列表验证测试通过，共 {len(service_list)} 个服务")



# 测试标记说明
"""
测试标记使用说明：

@pytest.mark.e2e - 端到端测试，测试完整流程
@pytest.mark.instance - 实例管理相关测试
@pytest.mark.service - 服务管理相关测试
@pytest.mark.route - 路由管理相关测试
@pytest.mark.error - 错误处理相关测试
@pytest.mark.validation - 数据验证相关测试
@pytest.mark.performance - 性能测试
@pytest.mark.integration - 集成测试
@pytest.mark.slow - 耗时较长的测试

运行示例：
- 运行所有测试: pytest e2e/tests/test_ai_gateway_e2e.py
- 运行实例管理测试: pytest e2e/tests/test_ai_gateway_e2e.py -m instance
- 运行快速测试: pytest e2e/tests/test_ai_gateway_e2e.py -m "not slow"
- 运行错误处理测试: pytest e2e/tests/test_ai_gateway_e2e.py -m error
"""