"""
API客户端模块

提供统一的HTTP客户端接口，支持：
- 会话管理和认证
- 自动重试机制  
- 请求/响应日志
- 错误处理
"""

import time
from typing import Dict, Any, Optional, Union
from urllib.parse import urljoin
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from ..config.settings import Settings


class APIError(Exception):
    """API请求异常"""
    
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(self.message)


class AIGatewayAPIClient:
    """AI网关API客户端"""
    
    def __init__(self, settings: Settings):
        """
        初始化API客户端
        
        Args:
            settings: 配置对象
        """
        self.settings = settings
        self.base_url = settings.api.base_url
        self.session = self._create_session()
        
        logger.info(f"API客户端初始化完成, 基础URL: {self.base_url}")
    
    def _create_session(self) -> requests.Session:
        """创建requests会话对象"""
        session = requests.Session()

        # 禁用SSL证书验证（仅用于测试环境）
        session.verify = False

        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # 设置重试策略
        retry_strategy = Retry(
            total=self.settings.api.max_retries,
            backoff_factor=self.settings.api.retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "PUT", "DELETE", "OPTIONS", "TRACE", "POST"]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # 设置默认headers
        session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": "AIGateway-E2E-Test/1.0.0",
            "X-Region": self.settings.auth.region,
            "Cookie": self.settings.auth.cookie,
            "csrftoken": self.settings.auth.csrf_token
        })

        return session
    
    def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> requests.Response:
        """
        执行HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求体数据
            params: URL参数
            **kwargs: 其他requests参数
            
        Returns:
            响应对象
            
        Raises:
            APIError: 请求失败时抛出
        """
        # 确保 base_url 以 / 结尾，endpoint 不以 / 开头
        base_url = self.base_url.rstrip('/') + '/'
        endpoint = endpoint.lstrip('/')
        url = urljoin(base_url, endpoint)
        
        # 记录请求日志
        logger.info(f"发送{method}请求: {url}")
        if params:
            logger.debug(f"请求参数: {params}")
        if data:
            logger.debug(f"请求体: {data}")
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                json=data if data else None,
                params=params,
                timeout=self.settings.api.timeout,
                **kwargs
            )
            
            # 记录响应日志
            logger.info(f"响应状态码: {response.status_code}")
            if response.content:
                logger.debug(f"响应内容: {response.text}")
            
            # 检查响应状态
            if not response.ok:
                error_msg = f"API请求失败: {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f", 错误信息: {error_data}"
                except:
                    error_msg += f", 响应内容: {response.text}"

                raise APIError(
                    message=error_msg,
                    status_code=response.status_code,
                    response_data=error_data if 'error_data' in locals() else None
                )

            # 检查业务逻辑错误（API返回200但success为false）
            if response.content:
                try:
                    response_json = response.json()
                    if isinstance(response_json, dict) and response_json.get('success') is False:
                        error_msg = f"业务逻辑错误: {response_json.get('message', '未知错误')}"
                        raise APIError(
                            message=error_msg,
                            status_code=response.status_code,
                            response_data=response_json
                        )
                except ValueError:
                    # JSON解析失败，忽略业务逻辑检查
                    pass

            return response
            
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求异常: {str(e)}"
            logger.error(error_msg)
            raise APIError(error_msg) from e
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """GET请求"""
        response = self._make_request("GET", endpoint, params=params, **kwargs)
        return response.json() if response.content else {}
    
    def post(self, endpoint: str, data: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """POST请求"""
        response = self._make_request("POST", endpoint, data=data, **kwargs)
        return response.json() if response.content else {}
    
    def put(self, endpoint: str, data: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """PUT请求"""
        response = self._make_request("PUT", endpoint, data=data, **kwargs)
        return response.json() if response.content else {}
    
    def delete(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """DELETE请求"""
        response = self._make_request("DELETE", endpoint, **kwargs)
        return response.json() if response.content else {}
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((requests.exceptions.ConnectionError, requests.exceptions.Timeout))
    )
    def request_with_retry(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        带重试机制的请求方法
        
        适用于网络不稳定的场景
        """
        logger.info(f"执行带重试的{method}请求: {endpoint}")
        response = self._make_request(method, endpoint, data=data, **kwargs)
        return response.json() if response.content else {}
    
    def close(self):
        """关闭会话连接"""
        if self.session:
            self.session.close()
            logger.info("API客户端会话已关闭")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close() 