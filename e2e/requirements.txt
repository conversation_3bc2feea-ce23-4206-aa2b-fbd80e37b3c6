# AI网关E2E测试框架依赖

# 核心框架
pytest>=7.0.0
pytest-html>=3.0.0
pytest-xdist>=3.0.0

# HTTP客户端和网络
requests>=2.28.0
urllib3>=1.26.0

# 数据验证和序列化
pydantic>=1.10.0
pydantic-settings>=2.0.0

# 配置管理
PyYAML>=6.0

# 日志系统
loguru>=0.6.0

# 重试和异步
tenacity>=8.0.0

# 类型检查
mypy>=1.0.0

# 代码格式化
black>=22.0.0
isort>=5.10.0

# 代码质量检查
flake8>=5.0.0
pylint>=2.15.0

# 测试覆盖率
coverage>=6.0.0
pytest-cov>=4.0.0

# 开发和调试
ipython>=8.0.0
pdbpp>=0.10.0 