"""
AI网关E2E测试框架安装配置
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# 读取requirements.txt
requirements = []
with open('requirements.txt', 'r', encoding='utf-8') as f:
    requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="ai-gateway-e2e-tests",
    version="1.0.0",
    author="AI Assistant",
    author_email="<EMAIL>",
    description="百度云AI网关服务的端到端自动化测试框架",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/example/ai-gateway-e2e-tests",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Topic :: Software Development :: Testing",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "black>=22.0.0",
            "isort>=5.10.0", 
            "flake8>=5.0.0",
            "mypy>=1.0.0",
            "pylint>=2.15.0",
        ],
        "docs": [
            "sphinx>=5.0.0",
            "sphinx-rtd-theme>=1.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "ai-gateway-e2e=e2e.run_tests:main",
        ],
    },
    include_package_data=True,
    package_data={
        "e2e": [
            "config/*.yaml",
            "fixtures/*.py",
        ],
    },
    zip_safe=False,
) 