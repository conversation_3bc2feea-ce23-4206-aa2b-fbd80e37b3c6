# AI网关E2E测试配置文件
# 注意：敏感信息如cookie、csrf_token应通过环境变量设置

api:
  base_url: "https://console.bce.baidu.com/api/aigw/v1"
  timeout: 30
  max_retries: 3
  retry_delay: 1.0

auth:
  # 请通过环境变量设置: AUTH_COOKIE, AUTH_CSRF_TOKEN
  cookie: >-
    UUAP_TRACE_TOKEN=03383b1e59b05bdecea4bc89711c8cd3; BAIDUID=69B23AED4EEBB9B5D15087DC8D983DCC:FG=1; BAIDUID_BFESS=69B23AED4EEBB9B5D15087DC8D983DCC:FG=1; Hm_lvt_0b9d9bfca881b506ea692cf1d0ec05de=1736907977; MAWEBCUID=web_NwRRlpoBKeFNUMnteGlnnKfIwnEkKFnYoLDquwOHZXUZCYugDL; BIDUPSID=69B23AED4EEBB9B5D15087DC8D983DCC; PSTM=1739890786; ZFY=KHznI:AvsLAK58yedIzCTxrQS5HcA4:AC3ODrijN:B4:Aew:C; H_PS_PSSID=60279_61027_62125_62167_62230_62241_62283_62136_62327_62341_62346_62330_62370; H_WISE_SIDS=110085_1992049_626068_628198_632156_633611_634604_635511_639037_632292_639613_632299_640072_627286_637859_640380_639929_641069_641080_641171_641221_641319_641401_641424_641466_641459_641668_641592_641756_641842_641905_641917_642010_642050_642074_641765_642208_642322_641154_641324_642413_641423_642545_642219_642564_642657_642452_642533_639680_642950_642987_642992_642976_643040_643022_641211_639697_643252_643294_643281_643173_643276_643099_643028_643445_638376_643542_643587_643595_643673_641261_643768_643812; H_WISE_SIDS_BFESS=110085_1992049_626068_628198_632156_633611_634604_635511_639037_632292_639613_632299_640072_627286_637859_640380_639929_641069_641080_641171_641221_641319_641401_641424_641466_641459_641668_641592_641756_641842_641905_641917_642010_642050_642074_641765_642208_642322_641154_641324_642413_641423_642545_642219_642564_642657_642452_642533_639680_642950_642987_642992_642976_643040_643022_641211_639697_643252_643294_643281_643173_643276_643099_643028_643445_638376_643542_643587_643595_643673_641261_643768_643812; jsdk-uuid=0fdf0fbe-2807-4b10-b1c8-f44198141e8b; BDUSS=0FIa3FNdjF1eXBEei16QnFja3d-ajlBdGtvd050WDFDakwyQ09JaEZ-dGRYNHRvRVFBQUFBJCQAAAAABwAAAAEAAAADySyRZmFhc19xYQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAF3SY2hd0mNoR; BDUSS_BFESS=0FIa3FNdjF1eXBEei16QnFja3d-ajlBdGtvd050WDFDakwyQ09JaEZ-dGRYNHRvRVFBQUFBJCQAAAAABwAAAAEAAAADySyRZmFhc19xYQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAF3SY2hd0mNoR; Hm_lvt_20ba3a41aeedac500c94bdef787f57e6=**********; UUAP_P_TOKEN=PT-1159076490108772353-aba8d900e55d936074d07875b45e2fcc14d3f408ee86afd57f6d90ab3a56c4da-uuapenc; SECURE_UUAP_P_TOKEN=PT-1159076490108772353-Fv3aBULMVXwSywEGwls6-uuap; __bid_n=1941ac76f58436862d29b3; Hm_lvt_1d218938b987e1cd56daa5e45102d928=**********,**********,**********,**********; Hm_lvt_28a17f66627d87f1d046eae152a1c93d=**********,**********,**********,**********; HMACCOUNT=B30C16FFAF002B2E; BCE_MONITOR_TRACK_SESSION_ID=**********094f7bb; __bce-console-referrer__=; ppfuid=FOCoIC3q5fKa8fgJnwzbE0LGziLN3VHbX8wfShDP6RCsfXQp/69CStRUAcn/QmhIlFDxPrAc/s5tJmCocrihdwitHd04Lvs3Nfz26Zt2holplnIKVacidp8Sue4dMTyfg65BJnOFhn1HthtSiwtygiD7piS4vjG/W9dLb1VAdqN/BwMntIdwfP0PEb9nt0GfO0V6uxgO+hV7+7wZFfXG0JyEcVtM7yNeV3zSUAMphgRqEJq9O14SZXr8u8o/EmJVZ77btup0W8HDa4x5wBW4KUIYL7ubmYEYlZiivYWEOqpfF7ozSx2WUjlOduTlsW9EzZNnk8BWIbmbBo8AkexVQD4TDTFi/kJX2nM5fjwUAQ7erld8wjGrZonj8gE2rhKeVRKCfkBUbih20ajKSj+uMd9VhKN7twq3rkZNAwacJ7Cu9NkQAZjJM3cNSn8drSS2pFfrEE/pu3OvGFkczjhaPf2ut/j6klxnuL/xmL7E/AhEODBFHq78mKpa297XMQbBoarF670H8yeNLJG1aZqWsQgLB9ivWe/rxb0kx2p27KfObP+WO+UXF1DaP4BaaaNfVoHqw1VEiE8I6O7XT5X14P0EnpN/9TChjbTdRYXYppzkRbRrXfKQ/BQqI+osDgX4qyrZmA334z85dJRxrU6CciTX5OK50gZIlk7z/O3RhP/MsKbBUbY0WVfy4iHIydRFymJoUgxVPL71+ry0nSLtZ7+NbQo7J7rsggavp5N0iqqs4TUezsi47KXLUBSBU2pTqqVAx6sMp14kt6Ruwr1vweC+f1eTdcvVKr1sEIASztdAROk8TCDBshOnlgS4C2u/xC22F5iUn5EVyvyP4B0Xr/r/vqemw0XHTeVbk8+w4Yw3T38BaoP9oLRuHgSCadgo4wUmOdGJpOUsHSOiXB0F/jB2TS4N8MgGooC7Zof5r1CD8Q0BPU/ZOSPRg4zSSsFwlOdtEFmS5PAz4Zcv50SVBUBvZuZE+3lnR4Z/lNwzmfB17EdkeEVJDBcxbuLnpPCWbZzG6JAlgPu6RXlLKZ9U8QaYXvleBbteLbUi5NoCAChP5oZfoCeoKKuvUEAPXXTPVjO0TTi0sVqFSdG+GFyi03wlrm3wCRN8QsWhT10pXJL0RhcLTagDnxauF9flnVwi9Ab2aMyW4+YH1VZWIUhb3tLvNHPTHkHFSp+jk/nvSIqm2QKY6eaGrGin5tWA6V16uqFuBSz8I0Kfe0QZwk5OQWx/Ad12sO21S5ezXVRSSgXzQ2GA5eh8aiV0nDOGmtfhiYNjbs2NxP0acAgApNd0ew==; BCE_MONITOR_TRACK_SESSION_ID=**********094f7bb; bce-org-delegate=false; bce_mfa_type=PHONE; bce-login-accountid=eca97e148cb74e9683d7b7240829d1ff; bce-org-master-id=eca97e148cb74e9683d7b7240829d1ff; bce-login-domain-account=ICM%3Ae4e8e2da190049b4b9bd6e56f1483c76-0%3A210870; bce-org-assign-type=MASTER; bce-org-id=ea7b344c88524929b31cb3b23eab6db1; bce-auth-type=UUAP_COLLABORATOR; bce-sessionid=0050c15172cb7a1429aa4524170a9fdf099; bce-long-term-sessionid=0053f7d465475184681a3ad4ad00ee745d7; bce-sessionid=0050c15172cb7a1429aa4524170a9fdf099; bce-ctl-client-cookies="bce-device-cuid,bce-device-token,BAIDUID"; bce-ctl-client-parameters=ticket&brt; bce-ctl-client-headers=""; bce-user-info="2025-08-07T19:47:34Z|8307f7c1cf5a94974b8056529558ec25"; bce-login-display-name=zhoujun24; bce-userbind-source=PASSPORT%3BUUAP; bce-login-type=UUAP_COLLABORATOR; bce-session=3e32192e10b741069893dc3c8a66ff37ca0ccae52f154f98858a467781506a24|d231f388e0c5f3da30cf472c86a482ab; bce-ctl-sessionmfa-cookie=bce-session; bce-login-userid=3e32192e10b741069893dc3c8a66ff37; bce-login-expire-time="2025-08-07T12:17:34Z|cca359c6ed15431bc1b6aa0c5bfce60b"; Hm_lpvt_28a17f66627d87f1d046eae152a1c93d=**********; JSESSIONID=1ECB684125472AB0524C0E8910B89F59; RT="z=1&dm=baidu.com&si=14bcef29-fb21-413c-8368-146bf6c216a8&ss=me1ayrtl&sl=q&tt=rff&bcn=https%3A%2F%2Ffclog.baidu.com%2Flog%2Fweirwood%3Ftype%3Dperf&ld=17a6t"; ab_sr=1.0.1_MThiNGY5NGVlNzI0YTI0ZWEzYTNlNmVmZmNjYjI4NjRiY2YxYTI1ODNjYTI5NzM0NDI1YmI2Mzg2Y2RlMzRjYzQxOTAyMWI5YjI1MjgxNTJjMDM2ODk5M2YzNzBkNzEyMGU1N2ZiZjI3MDdiODM4NDNiNDM3NDAwNmFkMGI3ZjE1OGJmNWEwODcyNjBhZTI5NTQ1MzgwN2ZkYTc4NDkyYw==
  csrf_token: "2025-08-07T19:47:34Z|8307f7c1cf5a94974b8056529558ec25"
  region: "bj"

instance:
   name_prefix: "aigw-e2e-case"
   cluster_id: "cce-gzn3d1zq"
   vpc_id: "vpc-dsqmnm91kf50"
   vpc_cidr: "10.0.0.0/8"
   subnet_id: "sbn-hjzth3bkn86a"
   gateway_type: "small"
   description: "bj 测试"
   is_internal: "true"
   delete_protection: false
   replicas: 2
   src_product: ""

service:
  service_source: "CCE"
  namespace: "ollama"
  service_list:
    - "ollama-service-v1"
    - "ollama-service-v2"

test:
  polling_interval: 5
  polling_timeout: 300
  cleanup_enabled: true
  parallel_tests: false

log:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file_path: "logs/e2e_test.log"
  max_size: "10 MB"
  rotation: "1 day"
  retention: "7 days" 