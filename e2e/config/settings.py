"""
配置管理模块

支持从环境变量、YAML文件和默认值加载配置信息。
使用pydantic进行配置验证和类型检查。
"""

import os
from typing import Optional, List, Dict, Any
from pathlib import Path
from pydantic import BaseModel, Field, field_validator
from pydantic_settings import BaseSettings
import yaml


class APIConfig(BaseSettings):
    """API相关配置"""
    base_url: str = Field(default="https://console.bce.baidu.com/api/aigw/v1", description="API基础URL")
    timeout: int = Field(default=30, description="请求超时时间(秒)")
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟(秒)")
    
    model_config = {"env_prefix": "API_"}


class AuthConfig(BaseSettings):
    """认证配置"""
    cookie: str = Field(default="", description="认证Cookie")
    csrf_token: str = Field(default="", description="CSRF Token")
    region: str = Field(default="bj", description="区域标识")

    model_config = {"env_prefix": "AUTH_"}


class InstanceConfig(BaseSettings):
    """实例相关配置"""
    name_prefix: str = Field(default="aigw-e2e-case", description="实例名称前缀")
    cluster_id: str = Field(default="cce-gzn3d1zq", description="集群ID")
    vpc_id: str = Field(default="vpc-dsqmnm91kf50", description="VPC ID")
    vpc_cidr: str = Field(default="10.0.0.0/8", description="VPC CIDR")
    subnet_id: str = Field(default="sbn-hjzth3bkn86a", description="子网ID")
    gateway_type: str = Field(default="small", description="网关类型")
    description: str = Field(default="bj 测试", description="实例描述")
    is_internal: str = Field(default="true", description="是否内部网关")
    delete_protection: bool = Field(default=False, description="删除保护")
    replicas: int = Field(default=2, description="副本数量")
    src_product: str = Field(default="", description="来源产品")
    
    model_config = {"env_prefix": "INSTANCE_"}


class ServiceConfig(BaseSettings):
    """服务相关配置"""
    service_source: str = Field(default="CCE", description="服务来源")
    namespace: str = Field(default="ollama", description="命名空间")
    service_list: List[str] = Field(
        default=["ollama-service-v1", "ollama-service-v2"], 
        description="服务列表"
    )
    
    model_config = {"env_prefix": "SERVICE_"}


class TestConfig(BaseSettings):
    """测试相关配置"""
    polling_interval: int = Field(default=5, description="状态轮询间隔(秒)")
    polling_timeout: int = Field(default=300, description="状态轮询超时(秒)")
    cleanup_enabled: bool = Field(default=True, description="是否启用清理")
    parallel_tests: bool = Field(default=False, description="是否并行执行测试")
    
    model_config = {"env_prefix": "TEST_"}


class LogConfig(BaseSettings):
    """日志配置"""
    level: str = Field(default="INFO", description="日志级别")
    format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        description="日志格式"
    )
    file_path: Optional[str] = Field(default=None, description="日志文件路径")
    max_size: str = Field(default="10 MB", description="日志文件最大大小")
    rotation: str = Field(default="1 day", description="日志文件轮转周期")
    retention: str = Field(default="7 days", description="日志文件保留时间")
    
    model_config = {"env_prefix": "LOG_"}


class Settings(BaseSettings):
    """主配置类"""
    api: APIConfig = Field(default_factory=APIConfig)
    auth: AuthConfig = Field(default_factory=AuthConfig)
    instance: InstanceConfig = Field(default_factory=InstanceConfig)
    service: ServiceConfig = Field(default_factory=ServiceConfig)
    test: TestConfig = Field(default_factory=TestConfig)
    log: LogConfig = Field(default_factory=LogConfig)
    
    @classmethod
    def load_from_yaml(cls, config_path: str) -> "Settings":
        """从YAML文件加载配置"""
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        return cls(**config_data)
    
    @classmethod
    def load_config(cls, config_path: Optional[str] = None) -> "Settings":
        """
        加载配置信息
        
        优先级：环境变量 > YAML文件 > 默认值
        """
        if config_path and Path(config_path).exists():
            # 从YAML文件加载基础配置
            settings = cls.load_from_yaml(config_path)
        else:
            # 使用默认配置
            settings = cls()
        
        # 环境变量会自动覆盖对应的配置项
        return settings


# 全局配置实例
def get_settings(config_path: Optional[str] = None) -> Settings:
    """获取配置实例"""
    default_config_path = os.getenv("E2E_CONFIG_PATH", "config/config.yaml")
    return Settings.load_config(config_path or default_config_path)