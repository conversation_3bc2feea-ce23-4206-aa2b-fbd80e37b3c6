# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
venv_*/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
logs/
reports/
*.tmp
*.temp

# Test artifacts
.pytest_cache/
test-results/
allure-results/
allure-report/

# Coverage reports
htmlcov/
.coverage
coverage.xml
*.cover

# Backup files
*.bak
*.backup
*.orig

# Local configuration overrides
config/local.yaml
config/local.yml
.env.local
.env.*.local

# Documentation build
docs/_build/
docs/build/

# Temporary files
tmp/
temp/
.tmp/

# Lock files (keep requirements.txt but ignore lock files)
poetry.lock
Pipfile.lock

# Virtual environments
venv*/
.venv*/

# Local development
.local/
local/

# Editor specific
.vscode/settings.json
.idea/workspace.xml
.idea/tasks.xml
.idea/dictionaries/
.idea/shelf/

# Jupyter
.ipynb_checkpoints/
*.ipynb

# pytest
.pytest_cache/
pytest.ini.local

# mypy
.mypy_cache/

# Profiling
*.prof
*.pstats

# Database
*.db
*.sqlite
*.sqlite3

# Certificates and keys
*.pem
*.key
*.crt
*.p12
*.pfx

# Secrets
secrets/
.secrets/
*.secret

# Build artifacts
build/
dist/
*.egg-info/

# Documentation
docs/_build/
site/

# Node.js (if any frontend components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Terraform (if used for infrastructure)
*.tfstate
*.tfstate.*
.terraform/

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
*.kubeconfig

# Cloud provider specific
.aws/
.gcp/
.azure/

# Monitoring and observability
*.trace
*.span

# Performance testing
*.jmx
*.jtl

# Security scanning
.snyk
.semgrep.yml

# Local overrides
local_*
*_local.*
