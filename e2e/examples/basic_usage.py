#!/usr/bin/env python3
"""
AI网关E2E测试框架基础使用示例

演示如何使用测试框架的核心功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import get_settings
from core.api_client import AIGatewayAPIClient
from services.instance_service import InstanceService
from services.service_service import ServiceManagementService
from services.route_service import RouteService
from utils.logger import setup_logger

# 设置日志
logger = setup_logger(__name__)


def example_instance_management():
    """实例管理示例"""
    logger.info("=== 实例管理示例 ===")
    
    # 获取配置
    settings = get_settings()
    
    # 创建API客户端
    api_client = AIGatewayAPIClient(settings)
    
    # 创建实例服务
    instance_service = InstanceService(api_client, settings)
    
    try:
        # 创建实例
        logger.info("创建AI网关实例...")
        create_response = instance_service.create_instance()
        instance_id = create_response.instanceId
        logger.info(f"实例创建成功: {instance_id}")
        
        # 等待实例就绪
        logger.info("等待实例就绪...")
        ready_detail = instance_service.wait_for_instance_ready(instance_id)
        logger.info(f"实例就绪: {ready_detail.status}")
        
        # 删除实例（可选）
        # logger.info("删除实例...")
        # instance_service.delete_instance(instance_id)
        # logger.info("实例删除成功")
        
    except Exception as e:
        logger.error(f"实例管理示例失败: {e}")
    finally:
        api_client.close()


def example_service_management():
    """服务管理示例"""
    logger.info("=== 服务管理示例 ===")
    
    # 获取配置
    settings = get_settings()
    
    # 创建API客户端
    api_client = AIGatewayAPIClient(settings)
    
    # 创建服务
    instance_service = InstanceService(api_client, settings)
    service_management_service = ServiceManagementService(api_client, settings)
    
    try:
        # 首先需要一个运行中的实例
        logger.info("创建实例...")
        create_response = instance_service.create_instance()
        instance_id = create_response.instanceId
        
        # 等待实例就绪
        logger.info("等待实例就绪...")
        instance_service.wait_for_instance_ready(instance_id)
        
        # 添加服务
        logger.info("添加服务到实例...")
        service_response = service_management_service.add_services_to_instance(instance_id)
        logger.info(f"服务添加成功，共添加 {service_response.addedCount} 个服务")
        
        # 使用自定义服务列表
        logger.info("添加自定义服务...")
        custom_services = ["ollama-service-v1"]
        custom_response = service_management_service.add_services_to_instance(
            instance_id=instance_id,
            service_list=custom_services
        )
        logger.info(f"自定义服务添加成功，共添加 {custom_response.addedCount} 个服务")
        
    except Exception as e:
        logger.error(f"服务管理示例失败: {e}")
    finally:
        api_client.close()


def example_route_management():
    """路由管理示例"""
    logger.info("=== 路由管理示例 ===")
    
    # 获取配置
    settings = get_settings()
    
    # 创建API客户端
    api_client = AIGatewayAPIClient(settings)
    
    # 创建服务
    instance_service = InstanceService(api_client, settings)
    service_management_service = ServiceManagementService(api_client, settings)
    route_service = RouteService(api_client, settings)
    
    try:
        # 创建实例并添加服务
        logger.info("准备实例和服务...")
        create_response = instance_service.create_instance()
        instance_id = create_response.instanceId
        
        instance_service.wait_for_instance_ready(instance_id)
        service_management_service.add_services_to_instance(instance_id)
        
        # 创建路由
        logger.info("创建路由...")
        cluster_id = settings.instance.cluster_id
        route_name = "example-route"
        
        # 路由配置
        route_config = {
            "routeName": route_name,
            "matchRules": {
                "pathRule": {
                    "matchType": "prefix",
                    "value": "/api/",
                    "caseSensitive": True
                },
                "methods": ["GET", "POST"]
            },
            "rewrite": {
                "enabled": True,
                "path": "/"
            },
            "multiService": True,
            "trafficDistributionStrategy": "ratio",
            "targetService": [
                {
                    "serviceSource": "CCE",
                    "serviceName": "ollama-service-v1",
                    "namespace": "ollama",
                    "servicePort": 80,
                    "loadBalanceAlgorithm": "round-robin",
                    "requestRatio": 1
                }
            ],
            "authEnabled": False,
            "tokenRateLimit": {
                "enabled": False
            }
        }
        
        route_response = route_service.create_route(
            instance_id=instance_id,
            cluster_id=cluster_id,
            route_name=route_name,
            route_config=route_config
        )
        
        if route_response.success:
            logger.info("路由创建成功")
        else:
            logger.error("路由创建失败")
        
    except Exception as e:
        logger.error(f"路由管理示例失败: {e}")
    finally:
        api_client.close()


def example_complete_workflow():
    """完整工作流示例"""
    logger.info("=== 完整工作流示例 ===")
    
    # 获取配置
    settings = get_settings()
    
    # 创建API客户端
    api_client = AIGatewayAPIClient(settings)
    
    # 创建所有服务
    instance_service = InstanceService(api_client, settings)
    service_management_service = ServiceManagementService(api_client, settings)
    route_service = RouteService(api_client, settings)
    
    instance_id = None
    
    try:
        # 步骤1: 创建实例
        logger.info("步骤1: 创建AI网关实例")
        create_response = instance_service.create_instance()
        instance_id = create_response.instanceId
        logger.info(f"实例创建成功: {instance_id}")
        
        # 步骤2: 等待实例就绪
        logger.info("步骤2: 等待实例就绪")
        ready_detail = instance_service.wait_for_instance_ready(instance_id)
        logger.info(f"实例就绪: {ready_detail.status}")
        
        # 步骤3: 添加服务
        logger.info("步骤3: 添加服务到实例")
        service_response = service_management_service.add_services_to_instance(instance_id)
        logger.info(f"服务添加成功，共 {service_response.addedCount} 个服务")
        
        # 步骤4: 创建路由
        logger.info("步骤4: 创建路由规则")
        cluster_id = settings.instance.cluster_id
        route_name = "complete-workflow-route"
        
        route_config = {
            "routeName": route_name,
            "matchRules": {
                "pathRule": {
                    "matchType": "prefix",
                    "value": "/workflow/",
                    "caseSensitive": True
                },
                "methods": ["GET", "POST", "PUT"]
            },
            "rewrite": {
                "enabled": True,
                "path": "/"
            },
            "multiService": True,
            "trafficDistributionStrategy": "ratio",
            "targetService": [
                {
                    "serviceSource": "CCE",
                    "serviceName": "ollama-service-v1",
                    "namespace": "ollama",
                    "servicePort": 80,
                    "loadBalanceAlgorithm": "round-robin",
                    "requestRatio": 1
                }
            ],
            "authEnabled": False,
            "tokenRateLimit": {
                "enabled": False
            }
        }
        
        route_response = route_service.create_route(
            instance_id=instance_id,
            cluster_id=cluster_id,
            route_name=route_name,
            route_config=route_config
        )
        
        if route_response.success:
            logger.info("路由创建成功")
        else:
            logger.error("路由创建失败")
        
        logger.info("完整工作流执行成功！")
        
    except Exception as e:
        logger.error(f"完整工作流失败: {e}")
    finally:
        # 清理资源（可选）
        # if instance_id:
        #     try:
        #         instance_service.delete_instance(instance_id)
        #         logger.info("实例清理完成")
        #     except Exception as e:
        #         logger.error(f"实例清理失败: {e}")
        
        api_client.close()


def main():
    """主函数"""
    logger.info("AI网关E2E测试框架基础使用示例")
    logger.info("=" * 50)
    
    try:
        # 运行各种示例
        example_instance_management()
        print()
        
        example_service_management()
        print()
        
        example_route_management()
        print()
        
        example_complete_workflow()
        
    except KeyboardInterrupt:
        logger.info("用户中断执行")
    except Exception as e:
        logger.error(f"示例执行失败: {e}")
    
    logger.info("示例执行完成")


if __name__ == "__main__":
    main()
