"""
服务管理服务

负责向AI网关实例添加和管理服务
"""

from typing import List, Dict, Any, Optional
from loguru import logger

from ..core.api_client import AIGatewayAPIClient, APIError
from ..models.requests import AddServiceRequest
from ..models.responses import AddServiceResponse
from ..utils.retry import retry_on_exception
from ..config.settings import Settings


class ServiceManagementService:
    """服务管理服务"""
    
    def __init__(self, api_client: AIGatewayAPIClient, settings: Settings):
        """
        初始化服务管理服务
        
        Args:
            api_client: API客户端
            settings: 配置对象
        """
        self.api_client = api_client
        self.settings = settings
        logger.info("服务管理服务初始化完成")
    
    @retry_on_exception(max_attempts=3, delay=1.0, description="添加服务")
    def add_services_to_instance(
        self, 
        instance_id: str, 
        cluster_id: Optional[str] = None,
        service_source: Optional[str] = None,
        namespace: Optional[str] = None,
        service_list: Optional[List[str]] = None
    ) -> AddServiceResponse:
        """
        向实例添加服务
        
        Args:
            instance_id: 实例ID
            cluster_id: 集群ID，默认使用配置中的值
            service_source: 服务来源，默认使用配置中的值
            namespace: 命名空间，默认使用配置中的值
            service_list: 服务列表，默认使用配置中的值
            
        Returns:
            添加服务响应对象
        """
        # 使用默认配置填充参数
        cluster_id = cluster_id or self.settings.instance.cluster_id
        service_source = service_source or self.settings.service.service_source
        namespace = namespace or self.settings.service.namespace
        service_list = service_list or self.settings.service.service_list
        
        # 构建请求数据
        request_data = AddServiceRequest(
            clusterId=cluster_id,
            serviceSource=service_source,
            namespace=namespace,
            serviceList=service_list
        )
        
        logger.info(f"向实例{instance_id}添加服务: {service_list}")
        logger.debug(f"添加服务请求数据: {request_data.model_dump()}")
        
        try:
            # 发送添加服务请求
            endpoint = f"/aigateway/cluster/{instance_id}/serviceList"
            response_data = self.api_client.post(endpoint, data=request_data.model_dump())
            
            # 解析响应 - API返回的数据结构包含result字段
            if 'result' in response_data:
                result_data = response_data['result']
                response = AddServiceResponse(**result_data)
            else:
                # 兼容直接返回数据的情况
                response = AddServiceResponse(**response_data)
            
            logger.success(f"服务添加成功，已添加{response.addedCount}个服务")
            
            # 验证添加数量
            expected_count = len(service_list)
            if response.addedCount != expected_count:
                logger.warning(f"预期添加{expected_count}个服务，实际添加{response.addedCount}个")
            
            return response
            
        except APIError as e:
            logger.error(f"向实例{instance_id}添加服务失败: {e.message}")
            raise
        except Exception as e:
            logger.error(f"添加服务时发生未知错误: {str(e)}")
            raise APIError(f"添加服务时发生未知错误: {str(e)}") from e
    
    def get_instance_services(self, instance_id: str) -> List[Dict[str, Any]]:
        """
        查询实例中的服务列表
        
        Args:
            instance_id: 实例ID
            
        Returns:
            服务列表
        """
        logger.info(f"查询实例{instance_id}中的服务列表")
        
        try:
            endpoint = f"/aigateway/{instance_id}/services"
            response_data = self.api_client.get(endpoint)
            
            services = response_data.get('services', [])
            logger.info(f"实例{instance_id}中有{len(services)}个服务")
            
            return services
            
        except APIError as e:
            logger.error(f"查询实例{instance_id}服务列表失败: {e.message}")
            raise
    
    def remove_service_from_instance(
        self, 
        instance_id: str, 
        service_name: str,
        cluster_id: Optional[str] = None
    ) -> bool:
        """
        从实例中移除服务
        
        Args:
            instance_id: 实例ID
            service_name: 服务名称
            cluster_id: 集群ID，默认使用配置中的值
            
        Returns:
            是否移除成功
        """
        cluster_id = cluster_id or self.settings.instance.cluster_id
        
        logger.info(f"从实例{instance_id}移除服务: {service_name}")
        
        try:
            endpoint = f"/aigateway/cluster/{instance_id}/service/{service_name}"
            params = {"clusterId": cluster_id}
            
            self.api_client.delete(endpoint, params=params)
            logger.success(f"服务{service_name}移除成功")
            return True
            
        except APIError as e:
            if e.status_code == 404:
                logger.warning(f"服务{service_name}不存在，视为移除成功")
                return True
            else:
                logger.error(f"移除服务{service_name}失败: {e.message}")
                raise
    
    def get_service_detail(self, instance_id: str, service_name: str) -> Dict[str, Any]:
        """
        查询服务详情
        
        Args:
            instance_id: 实例ID
            service_name: 服务名称
            
        Returns:
            服务详情
        """
        logger.info(f"查询服务{service_name}详情")
        
        try:
            endpoint = f"/aigateway/{instance_id}/service/{service_name}"
            response_data = self.api_client.get(endpoint)
            
            logger.debug(f"服务{service_name}详情: {response_data}")
            return response_data
            
        except APIError as e:
            logger.error(f"查询服务{service_name}详情失败: {e.message}")
            raise 