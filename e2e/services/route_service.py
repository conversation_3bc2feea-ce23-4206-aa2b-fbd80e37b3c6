"""
路由管理服务

负责AI网关实例的路由配置管理
"""

from typing import Dict, Any, Optional, List
from loguru import logger

from ..core.api_client import AIGatewayAPIClient, APIError
from ..models.requests import CreateRouteRequest, RouteMatchRule, RouteRewriteRule, ServiceWeight
from ..models.responses import CreateRouteResponse
from ..utils.retry import retry_on_exception
from ..config.settings import Settings


class RouteService:
    """路由管理服务"""
    
    def __init__(self, api_client: AIGatewayAPIClient, settings: Settings):
        """
        初始化路由服务
        
        Args:
            api_client: API客户端
            settings: 配置对象
        """
        self.api_client = api_client
        self.settings = settings
        logger.info("路由管理服务初始化完成")
    
    @retry_on_exception(max_attempts=3, delay=1.0, description="创建路由")
    def create_route(
        self, 
        instance_id: str,
        cluster_id: str,
        route_name: str,
        route_config: Optional[Dict[str, Any]] = None
    ) -> CreateRouteResponse:
        """
        创建路由规则
        
        注意：这是一个简化版本的实现，实际的路由配置可能更复杂
        您需要提供完整的路由请求体示例来完善此方法
        
        Args:
            instance_id: 实例ID
            cluster_id: 集群ID
            route_name: 路由名称
            route_config: 路由配置字典（如果不提供，使用默认配置）
            
        Returns:
            创建路由响应对象
        """
        logger.info(f"为实例{instance_id}创建路由: {route_name}")
        
        if route_config:
            # 使用提供的配置
            request_data = route_config
        else:
            # 使用默认配置构建基础路由
            request_data = self._build_default_route_config(route_name)
        
        logger.debug(f"创建路由请求数据: {request_data}")
        
        try:
            # 发送创建路由请求
            endpoint = f"/aigateway/{instance_id}/{cluster_id}/route"
            response_data = self.api_client.post(endpoint, data=request_data)
            
            # 解析响应
            response = CreateRouteResponse(**response_data)
            logger.success(f"路由{route_name}创建成功")
            
            return response
            
        except APIError as e:
            logger.error(f"创建路由{route_name}失败: {e.message}")
            raise
        except Exception as e:
            logger.error(f"创建路由时发生未知错误: {str(e)}")
            raise APIError(f"创建路由时发生未知错误: {str(e)}") from e
    
    def _build_default_route_config(self, route_name: str) -> Dict[str, Any]:
        """
        构建默认路由配置
        
        这是一个简化的实现，实际配置可能需要根据具体API要求调整
        
        Args:
            route_name: 路由名称
            
        Returns:
            路由配置字典
        """
        return {
            "name": route_name,
            "description": f"Auto-generated route for E2E test: {route_name}",
            "matchRules": [
                {
                    "type": "path",
                    "value": f"/{route_name}"
                }
            ],
            "rewriteRules": [
                {
                    "type": "path",
                    "value": "/api/v1"
                }
            ],
            "serviceWeights": [
                {
                    "serviceName": "ollama-service-v1",
                    "weight": 50
                },
                {
                    "serviceName": "ollama-service-v2", 
                    "weight": 50
                }
            ]
        }
    
    def create_route_with_pydantic(
        self,
        instance_id: str,
        cluster_id: str,
        route_name: str,
        match_rules: List[RouteMatchRule],
        service_weights: List[ServiceWeight],
        rewrite_rules: Optional[List[RouteRewriteRule]] = None,
        description: Optional[str] = None
    ) -> CreateRouteResponse:
        """
        使用Pydantic模型创建路由（类型安全版本）
        
        Args:
            instance_id: 实例ID
            cluster_id: 集群ID
            route_name: 路由名称
            match_rules: 匹配规则列表
            service_weights: 服务权重配置
            rewrite_rules: 重写规则列表（可选）
            description: 路由描述（可选）
            
        Returns:
            创建路由响应对象
        """
        # 构建请求对象
        route_request = CreateRouteRequest(
            name=route_name,
            description=description,
            matchRules=match_rules,
            rewriteRules=rewrite_rules,
            serviceWeights=service_weights
        )
        
        return self.create_route(
            instance_id=instance_id,
            cluster_id=cluster_id,
            route_name=route_name,
            route_config=route_request.model_dump()
        )
    
    def get_route_list(self, instance_id: str, cluster_id: str) -> List[Dict[str, Any]]:
        """
        查询路由列表
        
        Args:
            instance_id: 实例ID
            cluster_id: 集群ID
            
        Returns:
            路由列表
        """
        logger.info(f"查询实例{instance_id}的路由列表")
        
        try:
            endpoint = f"/aigateway/{instance_id}/{cluster_id}/routes"
            response_data = self.api_client.get(endpoint)
            
            routes = response_data.get('routes', [])
            logger.info(f"实例{instance_id}中有{len(routes)}条路由")
            
            return routes
            
        except APIError as e:
            logger.error(f"查询路由列表失败: {e.message}")
            raise
    
    def get_route_detail(self, instance_id: str, cluster_id: str, route_id: str) -> Dict[str, Any]:
        """
        查询路由详情
        
        Args:
            instance_id: 实例ID
            cluster_id: 集群ID
            route_id: 路由ID
            
        Returns:
            路由详情
        """
        logger.info(f"查询路由{route_id}详情")
        
        try:
            endpoint = f"/aigateway/{instance_id}/{cluster_id}/route/{route_id}"
            response_data = self.api_client.get(endpoint)
            
            logger.debug(f"路由{route_id}详情: {response_data}")
            return response_data
            
        except APIError as e:
            logger.error(f"查询路由{route_id}详情失败: {e.message}")
            raise
    
    def delete_route(self, instance_id: str, cluster_id: str, route_id: str) -> bool:
        """
        删除路由
        
        Args:
            instance_id: 实例ID
            cluster_id: 集群ID
            route_id: 路由ID
            
        Returns:
            是否删除成功
        """
        logger.info(f"删除路由: {route_id}")
        
        try:
            endpoint = f"/aigateway/{instance_id}/{cluster_id}/route/{route_id}"
            self.api_client.delete(endpoint)
            logger.success(f"路由{route_id}删除成功")
            return True
            
        except APIError as e:
            if e.status_code == 404:
                logger.warning(f"路由{route_id}不存在，视为删除成功")
                return True
            else:
                logger.error(f"删除路由{route_id}失败: {e.message}")
                raise 