"""
实例管理服务

负责AI网关实例的创建、查询、删除等操作
"""

import uuid
from typing import Dict, Any, Optional
from loguru import logger

from ..core.api_client import AIGatewayAPIClient, APIError
from ..models.requests import CreateInstanceRequest, ClusterInfo
from ..models.responses import CreateInstanceResponse, InstanceDetailResponse
from ..utils.retry import InstanceStatusPoller, retry_on_exception
from ..config.settings import Settings


class InstanceService:
    """实例管理服务"""
    
    def __init__(self, api_client: AIGatewayAPIClient, settings: Settings):
        """
        初始化实例服务
        
        Args:
            api_client: API客户端
            settings: 配置对象
        """
        self.api_client = api_client
        self.settings = settings
        self.poller = InstanceStatusPoller(
            get_instance_func=self._get_instance_detail,
            timeout=settings.test.polling_timeout,
            interval=settings.test.polling_interval
        )
        logger.info("实例管理服务初始化完成")
    
    def create_instance(self, custom_name: Optional[str] = None) -> CreateInstanceResponse:
        """
        创建AI网关实例
        
        Args:
            custom_name: 自定义实例名称，如果不提供则自动生成
            
        Returns:
            创建响应对象
            
        Raises:
            APIError: 创建失败时抛出
        """
        # 生成实例名称
        if not custom_name:
            timestamp = str(int(uuid.uuid4().time))[-8:]  # 使用UUID时间戳的后8位
            instance_name = f"{self.settings.instance.name_prefix}-{timestamp}"
        else:
            instance_name = custom_name
        
        # 构建请求数据
        request_data = CreateInstanceRequest(
            name=instance_name,
            clusters=[ClusterInfo(clusterId=self.settings.instance.cluster_id)],
            vpcId=self.settings.instance.vpc_id,
            vpcCidr=self.settings.instance.vpc_cidr,
            subnetId=self.settings.instance.subnet_id,
            gatewayType=self.settings.instance.gateway_type,
            description=self.settings.instance.description,
            isInternal=self.settings.instance.is_internal,
            deleteProtection=self.settings.instance.delete_protection,
            replicas=self.settings.instance.replicas,
            srcProduct=self.settings.instance.src_product
        )
        
        logger.info(f"开始创建实例: {instance_name}")
        logger.debug(f"创建实例请求数据: {request_data.model_dump()}")
        
        try:
            # 发送创建请求
            response_data = self.api_client.post("/aigateway", data=request_data.model_dump())

            # 解析响应 - API返回的数据结构包含result字段
            if 'result' in response_data:
                result_data = response_data['result']
                response = CreateInstanceResponse(**result_data)
            else:
                # 兼容直接返回数据的情况
                response = CreateInstanceResponse(**response_data)

            logger.success(f"实例创建请求成功，实例ID: {response.instanceId}")

            return response
            
        except APIError as e:
            logger.error(f"创建实例失败: {e.message}")
            raise
        except Exception as e:
            logger.error(f"创建实例时发生未知错误: {str(e)}")
            raise APIError(f"创建实例时发生未知错误: {str(e)}") from e
    
    def _get_instance_detail(self, instance_id: str) -> Dict[str, Any]:
        """
        获取实例详情（内部方法，用于轮询）
        
        Args:
            instance_id: 实例ID
            
        Returns:
            实例详情字典
        """
        try:
            response_data = self.api_client.get(f"/aigateway/{instance_id}")
            return response_data
        except APIError as e:
            logger.warning(f"获取实例{instance_id}详情失败: {e.message}")
            raise
    
    def get_instance_detail(self, instance_id: str) -> InstanceDetailResponse:
        """
        获取实例详情
        
        Args:
            instance_id: 实例ID
            
        Returns:
            实例详情对象
        """
        logger.info(f"查询实例详情: {instance_id}")
        
        try:
            response_data = self._get_instance_detail(instance_id)

            # 解析响应 - API返回的数据结构包含result字段
            if 'result' in response_data:
                result_data = response_data['result']
                instance_detail = InstanceDetailResponse(**result_data)
            else:
                # 兼容直接返回数据的情况
                instance_detail = InstanceDetailResponse(**response_data)

            logger.info(f"实例{instance_id}状态: {instance_detail.status}")

            if instance_detail.internalIP:
                logger.info(f"实例{instance_id}内部IP: {instance_detail.internalIP}")

            return instance_detail
            
        except APIError as e:
            logger.error(f"查询实例{instance_id}详情失败: {e.message}")
            raise
    
    def wait_for_instance_ready(self, instance_id: str) -> InstanceDetailResponse:
        """
        等待实例就绪
        
        Args:
            instance_id: 实例ID
            
        Returns:
            就绪后的实例详情
        """
        logger.info(f"等待实例{instance_id}就绪...")
        
        try:
            # 使用轮询器等待实例就绪
            instance_data = self.poller.wait_for_ready(instance_id)

            # 解析响应数据 - API返回的数据结构包含result字段
            if 'result' in instance_data:
                result_data = instance_data['result']
                instance_detail = InstanceDetailResponse(**result_data)
            else:
                # 兼容直接返回数据的情况
                instance_detail = InstanceDetailResponse(**instance_data)

            logger.success(f"实例{instance_id}已就绪，状态: {instance_detail.status}")
            return instance_detail
            
        except TimeoutError as e:
            logger.error(f"等待实例{instance_id}就绪超时: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"等待实例{instance_id}就绪时发生错误: {str(e)}")
            raise
    
    @retry_on_exception(max_attempts=3, delay=2.0, description="删除实例")
    def delete_instance(self, instance_id: str) -> bool:
        """
        删除实例
        
        Args:
            instance_id: 实例ID
            
        Returns:
            是否删除成功
        """
        logger.info(f"开始删除实例: {instance_id}")
        
        try:
            # 发送删除请求
            self.api_client.delete(f"/aigateway/{instance_id}")
            logger.success(f"实例{instance_id}删除成功")
            return True
            
        except APIError as e:
            if e.status_code == 404:
                logger.warning(f"实例{instance_id}不存在，视为删除成功")
                return True
            else:
                logger.error(f"删除实例{instance_id}失败: {e.message}")
                raise
    
    def create_and_wait_ready(self, custom_name: Optional[str] = None) -> tuple[CreateInstanceResponse, InstanceDetailResponse]:
        """
        创建实例并等待就绪
        
        Args:
            custom_name: 自定义实例名称
            
        Returns:
            (创建响应, 实例详情) 元组
        """
        # 创建实例
        create_response = self.create_instance(custom_name)
        
        # 等待就绪
        instance_detail = self.wait_for_instance_ready(create_response.instanceId)
        
        return create_response, instance_detail 