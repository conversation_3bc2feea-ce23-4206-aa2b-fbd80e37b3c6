#!/usr/bin/env python3
"""
清理脚本

用于清理测试过程中创建的资源和临时文件
"""

import os
import sys
import argparse
import shutil
from pathlib import Path
from typing import List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import get_settings
from core.api_client import AIGatewayAPIClient
from services.instance_service import InstanceService
from utils.logger import setup_logger

logger = setup_logger(__name__)


def cleanup_test_instances(api_client: AIGatewayAPIClient, instance_service: InstanceService, dry_run: bool = False):
    """清理测试实例"""
    logger.info("开始清理测试实例...")
    
    try:
        # 这里需要实现获取所有实例的逻辑
        # 由于当前API客户端没有列出所有实例的方法，这里只是示例
        if dry_run:
            logger.info("DRY RUN: 将清理测试实例")
        else:
            logger.info("清理测试实例完成")
    except Exception as e:
        logger.error(f"清理测试实例失败: {e}")


def cleanup_log_files(dry_run: bool = False):
    """清理日志文件"""
    logger.info("开始清理日志文件...")
    
    log_dir = Path("logs")
    if log_dir.exists():
        log_files = list(log_dir.glob("*.log"))
        if log_files:
            for log_file in log_files:
                try:
                    if dry_run:
                        logger.info(f"DRY RUN: 将删除日志文件: {log_file}")
                    else:
                        log_file.unlink()
                        logger.info(f"删除日志文件: {log_file}")
                except Exception as e:
                    logger.error(f"删除日志文件失败 {log_file}: {e}")
        else:
            logger.info("没有找到日志文件")
    else:
        logger.info("日志目录不存在")


def cleanup_report_files(dry_run: bool = False):
    """清理报告文件"""
    logger.info("开始清理报告文件...")
    
    report_dir = Path("reports")
    if report_dir.exists():
        report_files = [f for f in report_dir.glob("*") if f.is_file()]
        if report_files:
            for report_file in report_files:
                try:
                    if dry_run:
                        logger.info(f"DRY RUN: 将删除报告文件: {report_file}")
                    else:
                        report_file.unlink()
                        logger.info(f"删除报告文件: {report_file}")
                except Exception as e:
                    logger.error(f"删除报告文件失败 {report_file}: {e}")
        else:
            logger.info("没有找到报告文件")
    else:
        logger.info("报告目录不存在")


def cleanup_cache_files(dry_run: bool = False):
    """清理缓存文件"""
    logger.info("开始清理缓存文件...")
    
    # 清理pytest缓存
    cache_dir = Path(".pytest_cache")
    if cache_dir.exists():
        try:
            if dry_run:
                logger.info(f"DRY RUN: 将删除pytest缓存目录: {cache_dir}")
            else:
                shutil.rmtree(cache_dir)
                logger.info("删除pytest缓存目录")
        except Exception as e:
            logger.error(f"删除pytest缓存失败: {e}")
    
    # 清理Python缓存
    pycache_dirs = list(Path(".").rglob("__pycache__"))
    if pycache_dirs:
        for pycache_dir in pycache_dirs:
            try:
                if dry_run:
                    logger.info(f"DRY RUN: 将删除Python缓存: {pycache_dir}")
                else:
                    shutil.rmtree(pycache_dir)
                    logger.info(f"删除Python缓存: {pycache_dir}")
            except Exception as e:
                logger.error(f"删除Python缓存失败 {pycache_dir}: {e}")
    else:
        logger.info("没有找到Python缓存目录")


def cleanup_temp_files(dry_run: bool = False):
    """清理临时文件"""
    logger.info("开始清理临时文件...")
    
    temp_patterns = ["*.tmp", "*.temp", ".DS_Store", "Thumbs.db"]
    temp_files = []
    
    for pattern in temp_patterns:
        temp_files.extend(Path(".").rglob(pattern))
    
    if temp_files:
        for temp_file in temp_files:
            try:
                if dry_run:
                    logger.info(f"DRY RUN: 将删除临时文件: {temp_file}")
                else:
                    temp_file.unlink()
                    logger.info(f"删除临时文件: {temp_file}")
            except Exception as e:
                logger.error(f"删除临时文件失败 {temp_file}: {e}")
    else:
        logger.info("没有找到临时文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="AI网关E2E测试清理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s --all                    # 清理所有内容
  %(prog)s --logs --reports         # 只清理日志和报告
  %(prog)s --cache                  # 只清理缓存
  %(prog)s --dry-run --all          # 预览将要清理的内容
        """
    )
    
    parser.add_argument(
        "--instances", "-i",
        action="store_true",
        help="清理测试实例"
    )
    
    parser.add_argument(
        "--logs", "-l",
        action="store_true",
        help="清理日志文件"
    )
    
    parser.add_argument(
        "--reports", "-r",
        action="store_true",
        help="清理报告文件"
    )
    
    parser.add_argument(
        "--cache", "-c",
        action="store_true",
        help="清理缓存文件"
    )
    
    parser.add_argument(
        "--temp", "-t",
        action="store_true",
        help="清理临时文件"
    )
    
    parser.add_argument(
        "--all", "-a",
        action="store_true",
        help="清理所有内容"
    )
    
    parser.add_argument(
        "--dry-run", "-n",
        action="store_true",
        help="预览模式，不实际删除文件"
    )
    
    args = parser.parse_args()
    
    # 如果没有指定任何选项，默认清理所有
    if not any([args.instances, args.logs, args.reports, args.cache, args.temp]):
        args.all = True
    
    if args.dry_run:
        logger.info("运行在预览模式，不会实际删除文件")
    
    logger.info("开始清理操作...")
    
    try:
        if args.instances or args.all:
            settings = get_settings()
            api_client = AIGatewayAPIClient(settings)
            instance_service = InstanceService(api_client, settings)
            cleanup_test_instances(api_client, instance_service, args.dry_run)
            api_client.close()
        
        if args.logs or args.all:
            cleanup_log_files(args.dry_run)
        
        if args.reports or args.all:
            cleanup_report_files(args.dry_run)
        
        if args.cache or args.all:
            cleanup_cache_files(args.dry_run)
        
        if args.temp or args.all:
            cleanup_temp_files(args.dry_run)
        
        logger.info("清理操作完成")
        
    except Exception as e:
        logger.error(f"清理操作失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
