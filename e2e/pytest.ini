[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = tests

# Python文件匹配模式
python_files = test_*.py *_test.py

# Python类匹配模式
python_classes = Test*

# Python函数匹配模式
python_functions = test_*

# 添加标记
markers =
    e2e: 端到端测试
    slow: 耗时较长的测试
    fast: 快速测试
    instance: 实例管理测试
    service: 服务管理测试
    route: 路由管理测试
    integration: 集成测试
    unit: 单元测试
    error: 错误处理测试
    validation: 数据验证测试
    performance: 性能测试

# 默认参数
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 最小版本要求
minversion = 7.0

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S 