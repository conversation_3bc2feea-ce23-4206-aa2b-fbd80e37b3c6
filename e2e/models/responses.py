"""
响应数据模型

定义所有API响应的数据结构
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class CreateInstanceResponse(BaseModel):
    """创建实例响应"""
    instanceId: str = Field(..., description="实例ID")
    requestId: str = Field(..., description="请求ID")
    taskId: str = Field(..., description="任务ID")


class InstanceDetailResponse(BaseModel):
    """实例详情响应"""
    instanceId: str = Field(..., description="实例ID")
    name: str = Field(..., description="实例名称")
    ingressStatus: str = Field(..., description="实例状态")
    internalIP: Optional[str] = Field(None, description="内部IP地址")

    # 添加其他可能有用的字段
    description: Optional[str] = Field(None, description="实例描述")
    gatewayType: Optional[str] = Field(None, description="网关类型")
    replicas: Optional[int] = Field(None, description="副本数量")
    createTime: Optional[str] = Field(None, description="创建时间")
    region: Optional[str] = Field(None, description="地域")
    vpcId: Optional[str] = Field(None, description="VPC ID")
    subnetId: Optional[str] = Field(None, description="子网ID")

    @property
    def status(self) -> str:
        """兼容性属性，返回实例状态"""
        return self.ingressStatus


class AddServiceResponse(BaseModel):
    """添加服务响应"""
    addedCount: int = Field(..., description="添加的服务数量")


class CreateRouteResponse(BaseModel):
    """创建路由响应"""
    routeId: Optional[str] = Field(None, description="路由ID")
    requestId: Optional[str] = Field(None, description="请求ID")
    success: bool = Field(default=True, description="是否成功")


class ErrorResponse(BaseModel):
    """错误响应"""
    code: Optional[str] = Field(None, description="错误代码")
    message: str = Field(..., description="错误消息")
    requestId: Optional[str] = Field(None, description="请求ID")


class APIResponse(BaseModel):
    """通用API响应包装"""
    success: bool = Field(default=True, description="请求是否成功")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    error: Optional[ErrorResponse] = Field(None, description="错误信息")
    timestamp: Optional[str] = Field(None, description="响应时间戳") 