"""
请求数据模型

定义所有API请求的数据结构和验证规则
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field, field_validator


class ClusterInfo(BaseModel):
    """集群信息"""
    clusterId: str = Field(..., description="集群ID")


class CreateInstanceRequest(BaseModel):
    """创建实例请求"""
    name: str = Field(..., description="实例名称")
    clusters: List[ClusterInfo] = Field(..., description="关联的集群列表")
    vpcId: str = Field(..., description="VPC ID")
    vpcCidr: str = Field(..., description="VPC CIDR")
    subnetId: str = Field(..., description="子网ID")
    gatewayType: str = Field(..., description="网关类型")
    description: str = Field(..., description="实例描述")
    isInternal: str = Field(..., description="是否内部网关")
    deleteProtection: bool = Field(..., description="删除保护")
    replicas: int = Field(..., ge=1, description="副本数量")
    srcProduct: str = Field(default="", description="来源产品")
    
    @field_validator('gatewayType')
    @classmethod
    def validate_gateway_type(cls, v):
        """验证网关类型"""
        allowed_types = ['small', 'medium', 'large']
        if v not in allowed_types:
            raise ValueError(f'网关类型必须是: {allowed_types}')
        return v
    
    @field_validator('isInternal')
    @classmethod
    def validate_is_internal(cls, v):
        """验证内部网关标识"""
        if v not in ['true', 'false']:
            raise ValueError('isInternal必须是"true"或"false"')
        return v


class AddServiceRequest(BaseModel):
    """添加服务请求"""
    clusterId: str = Field(..., description="集群ID")
    serviceSource: str = Field(..., description="服务来源")
    namespace: str = Field(..., description="命名空间")
    serviceList: List[str] = Field(..., min_length=1, description="服务列表")
    
    @field_validator('serviceSource')
    @classmethod
    def validate_service_source(cls, v):
        """验证服务来源"""
        allowed_sources = ['CCE', 'K8S', 'DOCKER']
        if v not in allowed_sources:
            raise ValueError(f'服务来源必须是: {allowed_sources}')
        return v


class RouteMatchRule(BaseModel):
    """路由匹配规则"""
    type: str = Field(..., description="匹配类型")
    value: str = Field(..., description="匹配值")


class RouteRewriteRule(BaseModel):
    """路由重写规则"""
    type: str = Field(..., description="重写类型")
    value: str = Field(..., description="重写值")


class ServiceWeight(BaseModel):
    """服务权重"""
    serviceName: str = Field(..., description="服务名称")
    weight: int = Field(..., ge=0, le=100, description="权重")


class CreateRouteRequest(BaseModel):
    """创建路由请求"""
    name: str = Field(..., description="路由名称")
    description: Optional[str] = Field(None, description="路由描述")
    matchRules: List[RouteMatchRule] = Field(..., description="匹配规则")
    rewriteRules: Optional[List[RouteRewriteRule]] = Field(None, description="重写规则")
    serviceWeights: List[ServiceWeight] = Field(..., description="服务权重配置")
    
    @field_validator('serviceWeights')
    @classmethod
    def validate_service_weights(cls, v):
        """验证服务权重总和"""
        total_weight = sum(service.weight for service in v)
        if total_weight != 100:
            raise ValueError('服务权重总和必须等于100')
        return v 