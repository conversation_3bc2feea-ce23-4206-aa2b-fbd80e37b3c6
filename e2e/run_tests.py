#!/usr/bin/env python3
"""
AI网关E2E测试运行脚本

提供便捷的测试执行接口和参数配置
"""

import os
import sys
import argparse
from pathlib import Path
import subprocess
from typing import List, Optional

# 设置项目根目录为当前目录（e2e文件夹）
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def run_command(cmd: List[str], cwd: Optional[Path] = None) -> int:
    """执行命令并返回退出码"""
    print(f"执行命令: {' '.join(cmd)}")
    if cwd:
        print(f"工作目录: {cwd}")
    
    result = subprocess.run(cmd, cwd=cwd)
    return result.returncode


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI网关E2E测试运行器")
    
    # 测试选择参数
    parser.add_argument(
        "--test", "-t",
        choices=["all", "e2e", "instance", "service", "route", "fast", "slow"],
        default="all",
        help="选择要运行的测试类型"
    )
    
    parser.add_argument(
        "--pattern", "-k",
        help="测试用例名称匹配模式"
    )
    
    parser.add_argument(
        "--file", "-f",
        help="指定测试文件"
    )
    
    # 输出参数
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "--quiet", "-q",
        action="store_true", 
        help="安静模式"
    )
    
    parser.add_argument(
        "--html-report",
        help="生成HTML报告的路径"
    )
    
    parser.add_argument(
        "--junit-xml",
        help="生成JUnit XML报告的路径"
    )
    
    # 执行参数
    parser.add_argument(
        "--parallel", "-n",
        type=int,
        help="并行执行的进程数"
    )
    
    parser.add_argument(
        "--failfast", "-x",
        action="store_true",
        help="遇到第一个失败时停止"
    )
    
    parser.add_argument(
        "--pdb",
        action="store_true",
        help="遇到失败时进入调试器"
    )
    
    # 配置参数
    parser.add_argument(
        "--config",
        help="指定配置文件路径"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别"
    )
    
    args = parser.parse_args()
    
    # 构建pytest命令
    cmd = ["python3", "-m", "pytest"]
    
    # 添加测试目录或文件
    if args.file:
        cmd.append(args.file)
    else:
        cmd.append("tests/")
    
    # 添加测试标记
    if args.test != "all":
        cmd.extend(["-m", args.test])
    
    # 添加模式匹配
    if args.pattern:
        cmd.extend(["-k", args.pattern])
    
    # 添加输出控制
    if args.verbose:
        cmd.append("-v")
    elif args.quiet:
        cmd.append("-q")
    
    # 添加报告生成
    if args.html_report:
        cmd.extend(["--html", args.html_report, "--self-contained-html"])
    
    if args.junit_xml:
        cmd.extend(["--junitxml", args.junit_xml])
    
    # 添加并行执行
    if args.parallel:
        cmd.extend(["-n", str(args.parallel)])
    
    # 添加失败处理
    if args.failfast:
        cmd.append("-x")
    
    if args.pdb:
        cmd.append("--pdb")
    
    # 设置环境变量
    env = os.environ.copy()
    
    # 设置配置文件路径
    if args.config:
        env["E2E_CONFIG_PATH"] = args.config
    
    # 设置日志级别
    env["LOG_LEVEL"] = args.log_level
    
    # 确保创建输出目录
    if args.html_report:
        Path(args.html_report).parent.mkdir(parents=True, exist_ok=True)
    
    if args.junit_xml:
        Path(args.junit_xml).parent.mkdir(parents=True, exist_ok=True)
    
    # 执行测试
    print("=" * 60)
    print("AI网关E2E测试开始")
    print(f"工作目录: {project_root}")
    print(f"执行命令: {' '.join(cmd)}")
    print("=" * 60)

    result = subprocess.run(cmd, env=env, cwd=project_root)
    
    print("=" * 60)
    if result.returncode == 0:
        print("✅ 测试执行完成，所有测试通过")
    else:
        print("❌ 测试执行失败，存在失败的测试用例")
    print("=" * 60)
    
    return result.returncode


if __name__ == "__main__":
    sys.exit(main()) 