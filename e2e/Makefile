# AI网关E2E测试框架 Makefile
# 提供常用的开发和测试命令

.PHONY: help install test test-fast test-slow test-instance test-service test-route test-error test-validation test-performance test-integration test-all clean clean-cache clean-logs clean-reports clean-all lint format check setup dev-install docs

# 默认目标
help:
	@echo "AI网关E2E测试框架 - 可用命令:"
	@echo ""
	@echo "安装和设置:"
	@echo "  install      - 安装项目依赖"
	@echo "  dev-install  - 安装开发依赖"
	@echo "  setup        - 初始化项目环境"
	@echo ""
	@echo "测试命令:"
	@echo "  test         - 运行所有测试"
	@echo "  test-fast    - 运行快速测试"
	@echo "  test-slow    - 运行耗时测试"
	@echo "  test-smoke   - 运行烟雾测试"
	@echo ""
	@echo "按模块测试:"
	@echo "  test-instance     - 运行实例管理测试"
	@echo "  test-service      - 运行服务管理测试"
	@echo "  test-route        - 运行路由管理测试"
	@echo "  test-error        - 运行错误处理测试"
	@echo "  test-validation   - 运行数据验证测试"
	@echo "  test-performance  - 运行性能测试"
	@echo "  test-integration  - 运行集成测试"
	@echo ""
	@echo "报告生成:"
	@echo "  test-html    - 生成HTML测试报告"
	@echo "  test-coverage - 生成覆盖率报告"
	@echo ""
	@echo "清理命令:"
	@echo "  clean        - 清理缓存文件"
	@echo "  clean-cache  - 清理pytest和Python缓存"
	@echo "  clean-logs   - 清理日志文件"
	@echo "  clean-reports - 清理报告文件"
	@echo "  clean-all    - 清理所有生成的文件"
	@echo ""
	@echo "代码质量:"
	@echo "  lint         - 运行代码检查"
	@echo "  format       - 格式化代码"
	@echo "  check        - 运行所有检查"
	@echo ""
	@echo "其他:"
	@echo "  docs         - 生成文档"
	@echo "  example      - 运行基础使用示例"

# 安装依赖
install:
	pip3 install -r requirements.txt

# 安装开发依赖
dev-install: install
	pip3 install -e .[dev]

# 初始化项目环境
setup:
	@echo "初始化项目环境..."
	mkdir -p logs reports
	@if [ ! -f .env ]; then cp env.example .env; echo "已创建 .env 文件，请根据需要修改配置"; fi
	@echo "环境初始化完成"

# 基础测试命令
test:
	python3 -m pytest tests/ -v

test-fast:
	python3 -m pytest tests/ -m "fast" -v

test-slow:
	python3 -m pytest tests/ -m "slow" -v

test-smoke:
	python3 -m pytest tests/ -m "instance or (fast and not slow)" -v

# 按模块测试
test-instance:
	python3 -m pytest tests/ -m "instance" -v

test-service:
	python3 -m pytest tests/ -m "service" -v

test-route:
	python3 -m pytest tests/ -m "route" -v

test-error:
	python3 -m pytest tests/ -m "error" -v

test-validation:
	python3 -m pytest tests/ -m "validation" -v

test-performance:
	python3 -m pytest tests/ -m "performance" -v

test-integration:
	python3 -m pytest tests/ -m "integration" -v

test-all:
	python3 -m pytest tests/ -v --tb=short

# 报告生成
test-html:
	python3 -m pytest tests/ -v --html=reports/test_report.html --self-contained-html

test-coverage:
	python3 -m pytest tests/ --cov=. --cov-report=html:reports/coverage_html --cov-report=term-missing

# 使用运行脚本
run-tests:
	python3 run_tests.py

run-tests-fast:
	python3 run_tests.py --fast

run-tests-html:
	python3 run_tests.py --html

run-tests-smoke:
	python3 run_tests.py --smoke

# 清理命令
clean: clean-cache

clean-cache:
	python3 scripts/cleanup.py --cache

clean-logs:
	python3 scripts/cleanup.py --logs

clean-reports:
	python3 scripts/cleanup.py --reports

clean-temp:
	python3 scripts/cleanup.py --temp

clean-all:
	python3 scripts/cleanup.py --all

clean-dry-run:
	python3 scripts/cleanup.py --all --dry-run

# 代码质量检查
lint:
	@echo "运行代码检查..."
	@if command -v flake8 >/dev/null 2>&1; then \
		flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics; \
		flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics; \
	else \
		echo "flake8 未安装，跳过代码检查"; \
	fi

format:
	@echo "格式化代码..."
	@if command -v black >/dev/null 2>&1; then \
		black . --line-length=127; \
	else \
		echo "black 未安装，跳过代码格式化"; \
	fi
	@if command -v isort >/dev/null 2>&1; then \
		isort . --profile black; \
	else \
		echo "isort 未安装，跳过导入排序"; \
	fi

check: lint
	@echo "运行类型检查..."
	@if command -v mypy >/dev/null 2>&1; then \
		mypy . --ignore-missing-imports; \
	else \
		echo "mypy 未安装，跳过类型检查"; \
	fi

# 文档生成
docs:
	@echo "生成文档..."
	@echo "README.md 已存在，包含完整的项目文档"

# 运行示例
example:
	python3 examples/basic_usage.py

# 调试命令
debug-test:
	python3 -m pytest tests/ -v --pdb

debug-instance:
	python3 -m pytest tests/ -m "instance" -v --pdb -s

# 并发测试
test-parallel:
	@if command -v pytest-xdist >/dev/null 2>&1; then \
		python3 -m pytest tests/ -n auto -v; \
	else \
		echo "pytest-xdist 未安装，使用串行测试"; \
		python3 -m pytest tests/ -v; \
	fi

# 持续集成相关
ci-test:
	python3 -m pytest tests/ -v --tb=short --junit-xml=reports/junit.xml

ci-coverage:
	python3 -m pytest tests/ --cov=. --cov-report=xml:reports/coverage.xml --cov-report=term

# 开发辅助
watch-test:
	@echo "监控文件变化并自动运行测试..."
	@if command -v pytest-watch >/dev/null 2>&1; then \
		ptw tests/ -- -v; \
	else \
		echo "pytest-watch 未安装，请手动运行测试"; \
	fi

# 环境信息
info:
	@echo "环境信息:"
	@echo "Python版本: $$(python3 --version)"
	@echo "Pip版本: $$(pip3 --version)"
	@echo "当前目录: $$(pwd)"
	@echo "虚拟环境: $${VIRTUAL_ENV:-未激活}"
	@echo ""
	@echo "已安装的主要包:"
	@pip3 list | grep -E "(pytest|requests|pydantic|loguru)" || echo "未找到相关包"

# 快速开始
quickstart: setup install
	@echo ""
	@echo "快速开始完成！"
	@echo "1. 编辑 .env 文件配置API端点"
	@echo "2. 编辑 config/config.yaml 配置测试参数"
	@echo "3. 运行 'make test-smoke' 进行烟雾测试"
	@echo "4. 运行 'make example' 查看使用示例"
