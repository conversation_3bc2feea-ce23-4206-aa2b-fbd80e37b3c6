"""
日志配置工具

统一的日志配置和管理
"""

import sys
from pathlib import Path
from loguru import logger
from ..config.settings import Settings


def setup_logger(settings: Settings) -> None:
    """
    配置loguru日志系统
    
    Args:
        settings: 配置对象
    """
    # 移除默认handler
    logger.remove()
    
    # 控制台输出
    logger.add(
        sys.stderr,
        level=settings.log.level,
        format=settings.log.format,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 文件输出（如果配置了文件路径）
    if settings.log.file_path:
        log_file = Path(settings.log.file_path)
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_file,
            level=settings.log.level,
            format=settings.log.format,
            rotation=settings.log.rotation,
            retention=settings.log.retention,
            compression="zip",
            backtrace=True,
            diagnose=True
        )
    
    logger.info(f"日志系统初始化完成，级别: {settings.log.level}")


def get_logger(name: str = None):
    """
    获取logger实例
    
    Args:
        name: logger名称
        
    Returns:
        logger实例
    """
    if name:
        return logger.bind(name=name)
    return logger 