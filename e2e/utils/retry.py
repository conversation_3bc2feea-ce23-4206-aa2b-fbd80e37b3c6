"""
重试和状态轮询工具

提供灵活的重试机制和状态轮询功能
"""

import time
from typing import Callable, Any, Optional, List
from functools import wraps
from loguru import logger
from tenacity import retry, stop_after_delay, wait_fixed, retry_if_result


def poll_until_condition(
    check_function: Callable[[], Any],
    condition_function: Callable[[Any], bool],
    timeout: int = 300,
    interval: int = 5,
    description: str = "状态检查"
) -> Any:
    """
    轮询直到满足条件
    
    Args:
        check_function: 检查函数，返回当前状态
        condition_function: 条件判断函数，接收check_function返回值，返回bool
        timeout: 超时时间（秒）
        interval: 轮询间隔（秒）
        description: 操作描述
        
    Returns:
        满足条件时check_function的返回值
        
    Raises:
        TimeoutError: 超时时抛出
    """
    start_time = time.time()
    logger.info(f"开始{description}，超时时间: {timeout}秒，轮询间隔: {interval}秒")
    
    while time.time() - start_time < timeout:
        try:
            result = check_function()
            logger.debug(f"{description}当前结果: {result}")
            
            if condition_function(result):
                elapsed = time.time() - start_time
                logger.success(f"{description}成功，耗时: {elapsed:.2f}秒")
                return result
                
        except Exception as e:
            logger.warning(f"{description}检查时发生异常: {e}")
        
        time.sleep(interval)
    
    elapsed = time.time() - start_time
    error_msg = f"{description}超时，耗时: {elapsed:.2f}秒"
    logger.error(error_msg)
    raise TimeoutError(error_msg)


def retry_on_exception(
    max_attempts: int = 3,
    delay: float = 1.0,
    exceptions: tuple = (Exception,),
    description: str = "操作"
):
    """
    异常重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 重试延迟（秒）
        exceptions: 需要重试的异常类型
        description: 操作描述
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(1, max_attempts + 1):
                try:
                    logger.debug(f"{description} - 第{attempt}次尝试")
                    result = func(*args, **kwargs)
                    if attempt > 1:
                        logger.info(f"{description}在第{attempt}次尝试后成功")
                    return result
                    
                except exceptions as e:
                    last_exception = e
                    if attempt < max_attempts:
                        logger.warning(f"{description}第{attempt}次尝试失败: {e}，{delay}秒后重试")
                        time.sleep(delay)
                    else:
                        logger.error(f"{description}在{max_attempts}次尝试后仍然失败")
            
            raise last_exception
        return wrapper
    return decorator


class InstanceStatusPoller:
    """实例状态轮询器"""
    
    # 实例状态常量（使用小写，匹配API实际返回值）
    STATUS_CREATING = "creating"
    STATUS_RUNNING = "running"
    STATUS_ERROR = "error"
    STATUS_UPDATING = "updating"
    STATUS_DELETING = "deleting"

    READY_STATUSES = [STATUS_RUNNING]
    ERROR_STATUSES = [STATUS_ERROR]
    TRANSITIONAL_STATUSES = [STATUS_CREATING, STATUS_UPDATING, STATUS_DELETING]
    
    def __init__(self, get_instance_func: Callable[[str], dict], timeout: int = 300, interval: int = 5):
        """
        初始化状态轮询器
        
        Args:
            get_instance_func: 获取实例详情的函数
            timeout: 轮询超时时间
            interval: 轮询间隔
        """
        self.get_instance_func = get_instance_func
        self.timeout = timeout
        self.interval = interval
    
    def wait_for_ready(self, instance_id: str) -> dict:
        """
        等待实例就绪
        
        Args:
            instance_id: 实例ID
            
        Returns:
            实例详情信息
        """
        def check_status():
            instance_info = self.get_instance_func(instance_id)
            return instance_info
        
        def is_ready(instance_info):
            # API返回的数据结构中，状态字段在result.ingressStatus中
            if 'result' in instance_info:
                result_data = instance_info['result']
                status = result_data.get('ingressStatus', '')
            else:
                # 兼容直接返回数据的情况
                status = instance_info.get('ingressStatus', instance_info.get('status', ''))

            if status in self.ERROR_STATUSES:
                raise RuntimeError(f"实例{instance_id}状态异常: {status}")

            return status in self.READY_STATUSES
        
        return poll_until_condition(
            check_function=check_status,
            condition_function=is_ready,
            timeout=self.timeout,
            interval=self.interval,
            description=f"实例{instance_id}就绪检查"
        )
    
    def wait_for_status(self, instance_id: str, target_statuses: List[str]) -> dict:
        """
        等待实例到达指定状态
        
        Args:
            instance_id: 实例ID
            target_statuses: 目标状态列表
            
        Returns:
            实例详情信息
        """
        def check_status():
            return self.get_instance_func(instance_id)
        
        def is_target_status(instance_info):
            status = instance_info.get('status', '')
            return status in target_statuses
        
        return poll_until_condition(
            check_function=check_status,
            condition_function=is_target_status,
            timeout=self.timeout,
            interval=self.interval,
            description=f"实例{instance_id}状态等待"
        ) 