# AI网关E2E测试框架

## 项目概述

AI网关E2E测试框架是一个全面的端到端测试解决方案，用于验证AI网关服务的完整功能和性能。该框架提供了实例管理、服务管理、路由管理等核心功能的自动化测试，确保AI网关服务的稳定性和可靠性。

### 主要特性

- 🚀 **完整的生命周期测试**：覆盖实例创建、配置、服务添加、路由管理等完整流程
- 🔧 **模块化设计**：清晰的服务分层，易于维护和扩展
- 📊 **多种测试类型**：支持功能测试、性能测试、错误处理测试等
- 🎯 **智能重试机制**：内置重试逻辑，提高测试稳定性
- 📈 **详细的测试报告**：生成HTML格式的测试报告
- 🏷️ **灵活的测试标记**：支持按功能模块、测试类型运行测试

## 目录结构

```
e2e/
├── README.md                 # 项目说明文档
├── requirements.txt          # Python依赖包
├── setup.py                 # 项目安装配置
├── pytest.ini              # pytest配置文件
├── env.example              # 环境变量示例
├── run_tests.py             # 测试运行脚本
├── config/                  # 配置文件目录
│   ├── __init__.py
│   ├── config.yaml          # 主配置文件
│   └── settings.py          # 配置管理模块
├── core/                    # 核心模块
│   ├── __init__.py
│   ├── api_client.py        # API客户端
│   └── exceptions.py        # 自定义异常
├── models/                  # 数据模型
│   ├── __init__.py
│   ├── requests.py          # 请求模型
│   └── responses.py         # 响应模型
├── services/                # 服务层
│   ├── __init__.py
│   ├── instance_service.py  # 实例管理服务
│   ├── service_service.py   # 服务管理服务
│   └── route_service.py     # 路由管理服务
├── utils/                   # 工具模块
│   ├── __init__.py
│   ├── logger.py            # 日志工具
│   └── retry.py             # 重试工具
├── fixtures/                # 测试夹具
│   ├── __init__.py
│   └── test_fixtures.py     # 测试夹具定义
├── tests/                   # 测试用例
│   ├── __init__.py
│   ├── conftest.py          # pytest配置
│   └── test_ai_gateway_e2e.py # 主要测试用例
├── scripts/                 # 脚本目录
│   ├── __init__.py
│   └── cleanup.py           # 清理脚本
└── examples/                # 示例代码
    ├── __init__.py
    └── basic_usage.py       # 基础使用示例
```

## 环境配置

### 系统要求

- Python 3.9+
- 网络连接到AI网关服务
- 足够的权限创建和管理AI网关实例

### 依赖安装

1. **克隆项目**（如果适用）
   ```bash
   git clone <repository-url>
   cd e2e
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # 或
   venv\Scripts\activate     # Windows
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **配置环境变量**
   ```bash
   cp env.example .env
   # 编辑 .env 文件，设置必要的环境变量
   ```

### 配置文件

主要配置文件位于 `config/config.yaml`，包含以下配置项：

```yaml
# API配置
api:
  base_url: "https://your-api-endpoint/api/aigw/v1"
  timeout: 30
  max_retries: 3

# 实例配置
instance:
  name_prefix: "aigw-e2e-case"
  region: "your-region"
  cluster_id: "your-cluster-id"
  vpc_id: "your-vpc-id"
  # ... 其他配置项
```

## 测试运行

### 基本命令

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_ai_gateway_e2e.py

# 运行特定测试类
pytest tests/test_ai_gateway_e2e.py::TestInstanceManagement

# 运行特定测试方法
pytest tests/test_ai_gateway_e2e.py::TestInstanceManagement::test_create_instance_success
```

### 使用测试标记

```bash
# 运行实例管理相关测试
pytest -m instance

# 运行快速测试（排除耗时测试）
pytest -m "not slow"

# 运行错误处理测试
pytest -m error

# 运行性能测试
pytest -m performance

# 组合标记
pytest -m "instance and not slow"
```

### 生成测试报告

```bash
# 生成HTML报告
pytest --html=reports/test_report.html --self-contained-html

# 生成覆盖率报告
pytest --cov=e2e --cov-report=html
```

### 使用运行脚本

```bash
# 使用内置运行脚本
python run_tests.py

# 运行特定模块
python run_tests.py --module instance

# 运行性能测试
python run_tests.py --performance
```

## 测试分类和标记

### 功能模块标记

| 标记 | 描述 | 示例 |
|------|------|------|
| `instance` | 实例管理测试 | `pytest -m instance` |
| `service` | 服务管理测试 | `pytest -m service` |
| `route` | 路由管理测试 | `pytest -m route` |
| `integration` | 集成测试 | `pytest -m integration` |

### 测试类型标记

| 标记 | 描述 | 示例 |
|------|------|------|
| `e2e` | 端到端测试 | `pytest -m e2e` |
| `unit` | 单元测试 | `pytest -m unit` |
| `performance` | 性能测试 | `pytest -m performance` |
| `error` | 错误处理测试 | `pytest -m error` |
| `validation` | 数据验证测试 | `pytest -m validation` |

### 执行时间标记

| 标记 | 描述 | 示例 |
|------|------|------|
| `fast` | 快速测试（<10秒） | `pytest -m fast` |
| `slow` | 耗时测试（>30秒） | `pytest -m slow` |

## 故障排除

### 常见问题

1. **连接超时**
   ```
   ConnectionError: Failed to establish connection
   ```
   - 检查网络连接
   - 验证API端点配置
   - 确认防火墙设置

2. **认证失败**
   ```
   APIError: Authentication failed
   ```
   - 检查API密钥配置
   - 验证权限设置
   - 确认账户状态

3. **实例创建失败**
   ```
   APIError: Instance creation failed
   ```
   - 检查配置参数
   - 验证资源配额
   - 确认网络配置

### 调试技巧

1. **启用详细日志**
   ```bash
   pytest -v --log-cli-level=DEBUG
   ```

2. **单步调试**
   ```bash
   pytest --pdb
   ```

3. **保留测试数据**
   ```bash
   pytest --keep-duplicates
   ```

## 开发和扩展

### 添加新测试

1. **创建测试类**
   ```python
   @pytest.mark.your_module
   class TestYourModule:
       def test_your_feature(self, shared_test_instance):
           # 测试逻辑
           pass
   ```

2. **添加测试标记**
   在 `pytest.ini` 中添加新标记：
   ```ini
   markers =
       your_module: 您的模块测试
   ```

3. **创建服务类**
   ```python
   class YourService:
       def __init__(self, api_client, settings):
           self.api_client = api_client
           self.settings = settings
   ```

### 扩展配置

1. **添加配置项**
   在 `config/config.yaml` 中添加配置
   
2. **更新设置模型**
   在 `config/settings.py` 中添加对应的Pydantic模型

### 贡献指南

1. Fork项目
2. 创建功能分支
3. 编写测试用例
4. 确保所有测试通过
5. 提交Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

如有问题或建议，请联系：
- 邮箱: <EMAIL>
- 项目地址: https://github.com/your-org/ai-gateway-e2e

---

**注意**: 请确保在生产环境中谨慎使用此测试框架，避免对生产数据造成影响。
