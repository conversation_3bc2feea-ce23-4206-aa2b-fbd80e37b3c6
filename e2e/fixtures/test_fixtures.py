"""
测试夹具

提供pytest测试所需的共享资源和数据
"""

import pytest
from typing import Generator, Dict, Any, Optional
from loguru import logger

from ..config.settings import Settings, get_settings
from ..core.api_client import AIGatewayAPIClient
from ..services.instance_service import InstanceService
from ..services.service_service import ServiceManagementService
from ..services.route_service import RouteService
from ..utils.logger import setup_logger


@pytest.fixture(scope="session")
def settings() -> Settings:
    """配置fixture"""
    return get_settings()


@pytest.fixture(scope="session", autouse=True)
def setup_logging(settings: Settings):
    """自动设置日志配置"""
    setup_logger(settings)
    logger.info("测试会话开始")
    yield
    logger.info("测试会话结束")


@pytest.fixture(scope="session")
def api_client(settings: Settings) -> Generator[AIGatewayAPIClient, None, None]:
    """API客户端fixture"""
    client = AIGatewayAPIClient(settings)
    logger.info("API客户端已创建")
    yield client
    client.close()
    logger.info("API客户端已关闭")


@pytest.fixture(scope="session")
def instance_service(api_client: AIGatewayAPIClient, settings: Settings) -> InstanceService:
    """实例服务fixture"""
    return InstanceService(api_client, settings)


@pytest.fixture(scope="session")
def service_management_service(api_client: AIGatewayAPIClient, settings: Settings) -> ServiceManagementService:
    """服务管理服务fixture"""
    return ServiceManagementService(api_client, settings)


@pytest.fixture(scope="session")
def route_service(api_client: AIGatewayAPIClient, settings: Settings) -> RouteService:
    """路由服务fixture"""
    return RouteService(api_client, settings)


@pytest.fixture(scope="function")
def test_instance_data() -> Dict[str, Any]:
    """测试实例数据fixture"""
    return {
        "instance_id": None,
        "instance_name": None,
        "internal_ip": None,
        "created": False
    }


@pytest.fixture(scope="function")
def cleanup_instance(instance_service: InstanceService, test_instance_data: Dict[str, Any]):
    """
    实例清理fixture
    
    测试结束后自动清理创建的实例
    """
    yield
    
    # 清理逻辑
    instance_id = test_instance_data.get("instance_id")
    if instance_id and test_instance_data.get("created"):
        try:
            logger.info(f"开始清理测试实例: {instance_id}")
            instance_service.delete_instance(instance_id)
            logger.success(f"测试实例{instance_id}清理完成")
        except Exception as e:
            logger.error(f"清理测试实例{instance_id}失败: {str(e)}")


@pytest.fixture(scope="function")
def mock_route_config() -> Dict[str, Any]:
    """模拟路由配置fixture - 使用正确的API格式"""
    return {
        "routeName": "ai-route",
        "matchRules": {
            "pathRule": {
                "matchType": "prefix",
                "value": "/test/",
                "caseSensitive": True
            },
            "methods": ["GET", "POST", "PUT"]
        },
        "rewrite": {
            "enabled": True,
            "path": "/"
        },
        "multiService": True,
        "trafficDistributionStrategy": "ratio",
        "targetService": [
            {
                "serviceSource": "CCE",
                "serviceName": "ollama-service-v1",
                "namespace": "ollama",
                "servicePort": 80,
                "loadBalanceAlgorithm": "round-robin",
                "requestRatio": 1
            }
        ],
        "authEnabled": False,
        "tokenRateLimit": {
            "enabled": False
        }
    }


class DataManager:
    """测试数据管理器"""
    
    def __init__(self):
        self.created_instances = []
        self.created_routes = []
        self.added_services = []
    
    def add_instance(self, instance_id: str):
        """记录创建的实例"""
        self.created_instances.append(instance_id)
        logger.debug(f"记录创建的实例: {instance_id}")
    
    def add_route(self, instance_id: str, cluster_id: str, route_id: str):
        """记录创建的路由"""
        route_info = {
            "instance_id": instance_id,
            "cluster_id": cluster_id,
            "route_id": route_id
        }
        self.created_routes.append(route_info)
        logger.debug(f"记录创建的路由: {route_info}")
    
    def add_service(self, instance_id: str, service_names: list):
        """记录添加的服务"""
        service_info = {
            "instance_id": instance_id,
            "service_names": service_names
        }
        self.added_services.append(service_info)
        logger.debug(f"记录添加的服务: {service_info}")
    
    def cleanup_all(self, instance_service, route_service, service_management_service):
        """清理所有创建的资源"""
        logger.info("开始清理所有测试资源")
        
        # 清理路由
        for route_info in self.created_routes:
            try:
                route_service.delete_route(
                    route_info["instance_id"],
                    route_info["cluster_id"],
                    route_info["route_id"]
                )
            except Exception as e:
                logger.warning(f"清理路由失败: {e}")
        
        # 清理实例（会自动清理关联的服务）
        for instance_id in self.created_instances:
            try:
                instance_service.delete_instance(instance_id)
            except Exception as e:
                logger.warning(f"清理实例{instance_id}失败: {e}")
        
        logger.info("测试资源清理完成")


@pytest.fixture(scope="session")
def shared_test_instance(instance_service: InstanceService) -> Generator[Dict[str, Any], None, None]:
    """
    共享测试实例fixture

    在测试会话开始时创建一个实例，所有测试共享使用，会话结束时不删除
    """
    logger.info("创建共享测试实例...")

    # 创建实例
    create_response = instance_service.create_instance()
    instance_id = create_response.instanceId

    # 等待实例就绪
    ready_detail = instance_service.wait_for_instance_ready(instance_id)

    instance_info = {
        "instance_id": instance_id,
        "instance_name": ready_detail.name,
        "status": ready_detail.status,
        "create_response": create_response,
        "ready_detail": ready_detail
    }

    logger.success(f"共享测试实例创建完成: {instance_id}")

    yield instance_info

    # 会话结束时不删除实例，避免重复创建
    logger.info(f"测试会话结束，保留共享实例: {instance_id}")


@pytest.fixture(scope="session")
def test_data_manager() -> Generator[DataManager, None, None]:
    """测试数据管理器fixture"""
    manager = DataManager()
    yield manager
    # 这里不执行清理，因为具体的清理逻辑在各个测试中处理