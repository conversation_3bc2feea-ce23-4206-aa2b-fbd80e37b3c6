package test

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	istionetworkingv1alpha3 "istio.io/api/networking/v1alpha3"
)

// isFallbackService 检查destination是否为fallback容灾服务（测试用辅助函数）
func isFallbackService(dest *istionetworkingv1alpha3.Destination) bool {
	if dest == nil || dest.Subset == "" {
		return false
	}
	// fallback服务的subset包含"-fallback"后缀
	return strings.Contains(dest.Subset, "-fallback")
}

// TestFallbackServiceFilter 测试fallback服务过滤功能
func TestFallbackServiceFilter(t *testing.T) {
	t.Run("isFallbackService", func(t *testing.T) {
		testCases := []struct {
			name     string
			dest     *istionetworkingv1alpha3.Destination
			expected bool
		}{
			{
				name:     "nil destination",
				dest:     nil,
				expected: false,
			},
			{
				name: "destination without subset",
				dest: &istionetworkingv1alpha3.Destination{
					Host: "service.namespace.svc.cluster.local",
				},
				expected: false,
			},
			{
				name: "primary service destination",
				dest: &istionetworkingv1alpha3.Destination{
					Host:   "ai-service.ai-namespace.svc.cluster.local",
					Subset: "route-name-ai-service",
				},
				expected: false,
			},
			{
				name: "fallback service destination",
				dest: &istionetworkingv1alpha3.Destination{
					Host:   "ai-fallback-service.ai-namespace.svc.cluster.local",
					Subset: "route-name-ai-fallback-service-fallback",
				},
				expected: true,
			},
			{
				name: "another fallback service destination",
				dest: &istionetworkingv1alpha3.Destination{
					Host:   "backup-service.default.svc.cluster.local",
					Subset: "test-route-backup-service-fallback",
				},
				expected: true,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				result := isFallbackService(tc.dest)
				assert.Equal(t, tc.expected, result, "isFallbackService should return %v for %s", tc.expected, tc.name)
			})
		}
	})

	t.Run("FilterFallbackServices", func(t *testing.T) {
		// 模拟HTTP路由，包含主服务和fallback服务
		httpRoute := &istionetworkingv1alpha3.HTTPRoute{
			Route: []*istionetworkingv1alpha3.HTTPRouteDestination{
				{
					// 主服务1
					Destination: &istionetworkingv1alpha3.Destination{
						Host:   "ai-service-1.ai-namespace.svc.cluster.local",
						Subset: "route-name-ai-service-1",
						Port: &istionetworkingv1alpha3.PortSelector{
							Number: 8080,
						},
					},
					Weight: 70,
				},
				{
					// 主服务2
					Destination: &istionetworkingv1alpha3.Destination{
						Host:   "ai-service-2.ai-namespace.svc.cluster.local",
						Subset: "route-name-ai-service-2",
						Port: &istionetworkingv1alpha3.PortSelector{
							Number: 8080,
						},
					},
					Weight: 30,
				},
				{
					// Fallback服务（应该被过滤掉）
					Destination: &istionetworkingv1alpha3.Destination{
						Host:   "ai-fallback-service.ai-namespace.svc.cluster.local",
						Subset: "route-name-ai-fallback-service-fallback",
						Port: &istionetworkingv1alpha3.PortSelector{
							Number: 8080,
						},
					},
					Weight: 0,
				},
			},
		}

		// 模拟提取服务信息的过程
		var filteredServices []map[string]interface{}
		for _, route := range httpRoute.Route {
			if route.Destination == nil {
				continue
			}

			// 跳过fallback容灾服务
			if isFallbackService(route.Destination) {
				continue
			}

			// 创建服务信息
			service := map[string]interface{}{
				"serviceSource": "CCE",
				"serviceName":   "",
				"namespace":     "",
				"servicePort":   0,
				"requestRatio":  int(route.Weight),
			}

			// 解析destination信息
			if route.Destination.Host != "" {
				parts := strings.Split(route.Destination.Host, ".")
				if len(parts) >= 2 {
					service["serviceName"] = parts[0]
					service["namespace"] = parts[1]
				}
			}
			if route.Destination.Port != nil {
				service["servicePort"] = int(route.Destination.Port.Number)
			}

			filteredServices = append(filteredServices, service)
		}

		// 验证结果
		assert.Len(t, filteredServices, 2, "应该只返回2个主服务，fallback服务应该被过滤掉")

		// 验证第一个服务
		service1 := filteredServices[0]
		assert.Equal(t, "ai-service-1", service1["serviceName"])
		assert.Equal(t, "ai-namespace", service1["namespace"])
		assert.Equal(t, 8080, service1["servicePort"])
		assert.Equal(t, 70, service1["requestRatio"])

		// 验证第二个服务
		service2 := filteredServices[1]
		assert.Equal(t, "ai-service-2", service2["serviceName"])
		assert.Equal(t, "ai-namespace", service2["namespace"])
		assert.Equal(t, 8080, service2["servicePort"])
		assert.Equal(t, 30, service2["requestRatio"])

		// 确保没有fallback服务
		for _, service := range filteredServices {
			serviceName := service["serviceName"].(string)
			assert.NotContains(t, serviceName, "fallback", "返回的服务列表不应包含fallback服务")
		}
	})

	t.Run("SingleServiceWithFallback", func(t *testing.T) {
		// 模拟单服务模式，包含主服务和fallback服务
		httpRoute := &istionetworkingv1alpha3.HTTPRoute{
			Route: []*istionetworkingv1alpha3.HTTPRouteDestination{
				{
					// 主服务
					Destination: &istionetworkingv1alpha3.Destination{
						Host:   "ai-primary-service.ai-namespace.svc.cluster.local",
						Subset: "route-name-ai-primary-service",
						Port: &istionetworkingv1alpha3.PortSelector{
							Number: 8080,
						},
					},
					Weight: 100,
				},
				{
					// Fallback服务（应该被过滤掉）
					Destination: &istionetworkingv1alpha3.Destination{
						Host:   "ai-fallback-service.ai-namespace.svc.cluster.local",
						Subset: "route-name-ai-fallback-service-fallback",
						Port: &istionetworkingv1alpha3.PortSelector{
							Number: 8080,
						},
					},
					Weight: 0,
				},
			},
		}

		// 查找主服务（非fallback服务）
		var primaryService map[string]interface{}
		for _, route := range httpRoute.Route {
			if route.Destination != nil && !isFallbackService(route.Destination) {
				primaryService = map[string]interface{}{
					"serviceSource": "CCE",
					"serviceName":   "",
					"namespace":     "",
					"servicePort":   0,
				}

				// 解析destination信息
				if route.Destination.Host != "" {
					parts := strings.Split(route.Destination.Host, ".")
					if len(parts) >= 2 {
						primaryService["serviceName"] = parts[0]
						primaryService["namespace"] = parts[1]
					}
				}
				if route.Destination.Port != nil {
					primaryService["servicePort"] = int(route.Destination.Port.Number)
				}
				break // 找到主服务后退出循环
			}
		}

		// 验证结果
		assert.NotNil(t, primaryService, "应该找到主服务")
		assert.Equal(t, "ai-primary-service", primaryService["serviceName"])
		assert.Equal(t, "ai-namespace", primaryService["namespace"])
		assert.Equal(t, 8080, primaryService["servicePort"])
		assert.NotContains(t, primaryService["serviceName"].(string), "fallback", "主服务名称不应包含fallback")
	})
}
