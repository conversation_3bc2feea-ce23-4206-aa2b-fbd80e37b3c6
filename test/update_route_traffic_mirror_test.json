{"description": "更新路由和查询路由详情的流量镜像功能测试用例", "test_cases": [{"name": "更新单服务路由 - 启用流量镜像", "method": "PUT", "url": "/api/aigw/v1/aigateway/gw-test123/single-mirror-route/route/detail", "headers": {"X-Region": "gz", "Content-Type": "application/json"}, "request": {"srcProduct": "aibox", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/api/v1/test", "caseSensitive": true}, "methods": ["POST"]}, "multiService": false, "targetService": {"serviceSource": "CCE", "serviceName": "test-service", "namespace": "default", "servicePort": 8080, "subset": "v1", "labels": {"version": "v1", "app": "test-service"}}, "trafficMirror": {"enabled": true, "mirror": {"subset": "v2", "labels": {"version": "v2", "app": "test-service"}}, "percentage": 50.0}, "authEnabled": false}, "expected_response": {"success": true, "status": 200, "result": {"routeName": "single-mirror-route", "multiService": false, "targetService": {"serviceSource": "CCE", "serviceName": "test-service", "namespace": "default", "servicePort": 8080, "subset": "v1", "labels": {"version": "v1", "app": "test-service"}}, "trafficMirror": {"enabled": true, "mirror": {"subset": "v2", "labels": {"version": "v2", "app": "test-service"}}, "percentage": 50.0}}}}, {"name": "更新多服务路由 - 启用流量镜像", "method": "PUT", "url": "/api/aigw/v1/aigateway/gw-test123/multi-mirror-route/route/detail", "headers": {"X-Region": "gz", "Content-Type": "application/json"}, "request": {"srcProduct": "aibox", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/api/v1/multi", "caseSensitive": true}, "methods": ["POST"]}, "multiService": true, "trafficDistributionStrategy": "ratio", "targetService": [{"serviceSource": "CCE", "serviceName": "test-service-1", "namespace": "default", "servicePort": 8080, "requestRatio": 70, "subset": "stable", "labels": {"version": "stable", "app": "test-service-1"}}, {"serviceSource": "CCE", "serviceName": "test-service-2", "namespace": "default", "servicePort": 8080, "requestRatio": 30}], "trafficMirror": {"enabled": true, "mirror": {"subset": "canary", "labels": {"version": "canary", "app": "test-service-1"}}, "percentage": 100.0}, "authEnabled": false}, "expected_response": {"success": true, "status": 200, "result": {"routeName": "multi-mirror-route", "multiService": true, "trafficDistributionStrategy": "ratio", "trafficMirror": {"enabled": true, "mirror": {"subset": "canary", "labels": {"version": "canary", "app": "test-service-1"}}, "percentage": 100.0}}}}, {"name": "查询路由详情 - 包含流量镜像信息", "method": "GET", "url": "/api/aigw/v1/aigateway/gw-test123/single-mirror-route/route/detail", "headers": {"X-Region": "gz"}, "expected_response": {"success": true, "status": 200, "result": {"routeName": "single-mirror-route", "multiService": false, "targetService": {"serviceSource": "CCE", "serviceName": "test-service", "namespace": "default", "servicePort": 8080, "subset": "v1", "labels": {"version": "v1", "app": "test-service"}}, "trafficMirror": {"enabled": true, "mirror": {"subset": "v2", "labels": {"version": "v2", "app": "test-service"}}, "percentage": 50.0}, "authEnabled": false}}}, {"name": "更新路由 - 禁用流量镜像", "method": "PUT", "url": "/api/aigw/v1/aigateway/gw-test123/single-mirror-route/route/detail", "headers": {"X-Region": "gz", "Content-Type": "application/json"}, "request": {"srcProduct": "aibox", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/api/v1/test", "caseSensitive": true}, "methods": ["POST"]}, "multiService": false, "targetService": {"serviceSource": "CCE", "serviceName": "test-service", "namespace": "default", "servicePort": 8080}, "trafficMirror": {"enabled": false}, "authEnabled": false}, "expected_response": {"success": true, "status": 200, "result": {"routeName": "single-mirror-route", "multiService": false, "trafficMirror": {"enabled": false}}}}, {"name": "更新路由 - 流量镜像配置验证错误", "method": "PUT", "url": "/api/aigw/v1/aigateway/gw-test123/invalid-mirror-route/route/detail", "headers": {"X-Region": "gz", "Content-Type": "application/json"}, "request": {"matchRules": {"pathRule": {"matchType": "prefix", "value": "/api/v1/invalid"}, "methods": ["POST"]}, "multiService": false, "targetService": {"serviceSource": "CCE", "serviceName": "test-service", "namespace": "default", "servicePort": 8080}, "trafficMirror": {"enabled": true, "mirror": {"labels": {"version": "v2", "app": "test-service"}}, "percentage": 50.0}}, "expected_response": {"success": false, "status": 400, "message": "mirror subset cannot be empty when traffic mirror is enabled"}}]}