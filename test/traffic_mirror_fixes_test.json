{"description": "验证流量镜像功能修复的测试用例", "fixes": ["修复1: 更新路由时正确处理 mirrorPercentage 的更新", "修复2: 查询路由详情接口返回完整的 labels 内容"], "test_cases": [{"name": "验证更新路由时 mirrorPercentage 正确更新", "description": "测试更新路由时镜像百分比能够正确设置和返回", "method": "PUT", "url": "/api/aigw/v1/aigateway/gw-test123/percentage-test-route/route/detail", "headers": {"X-Region": "gz", "Content-Type": "application/json"}, "request": {"srcProduct": "aibox", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/api/v1/percentage-test", "caseSensitive": true}, "methods": ["POST"]}, "multiService": false, "targetService": {"serviceSource": "CCE", "serviceName": "percentage-test-service", "namespace": "default", "servicePort": 8080, "subset": "v1", "labels": {"version": "v1", "app": "percentage-test-service"}}, "trafficMirror": {"enabled": true, "mirror": {"subset": "v2", "labels": {"version": "v2", "app": "percentage-test-service"}}, "percentage": 75.5}, "authEnabled": false}, "expected_response": {"success": true, "status": 200, "result": {"routeName": "percentage-test-route", "multiService": false, "trafficMirror": {"enabled": true, "mirror": {"subset": "v2", "labels": {"version": "v2", "app": "percentage-test-service"}}, "percentage": 75.5}}}, "validation_points": ["响应中包含完整的 trafficMirror 配置", "percentage 字段值为 75.5，与请求一致", "mirror.labels 包含完整的标签信息"]}, {"name": "验证查询路由详情返回完整的 labels 内容", "description": "测试查询路由详情时能够返回镜像目标的完整标签信息", "method": "GET", "url": "/api/aigw/v1/aigateway/gw-test123/percentage-test-route/route/detail", "headers": {"X-Region": "gz"}, "expected_response": {"success": true, "status": 200, "result": {"routeName": "percentage-test-route", "multiService": false, "targetService": {"serviceSource": "CCE", "serviceName": "percentage-test-service", "namespace": "default", "servicePort": 8080, "subset": "v1", "labels": {"version": "v1", "app": "percentage-test-service"}}, "trafficMirror": {"enabled": true, "mirror": {"subset": "v2", "labels": {"version": "v2", "app": "percentage-test-service"}}, "percentage": 75.5}, "authEnabled": false}}, "validation_points": ["targetService 包含 subset 和 labels 字段", "trafficMirror.mirror.labels 包含完整的标签信息", "percentage 字段正确显示为 75.5", "所有版本标签信息完整返回"]}, {"name": "验证多服务模式的流量镜像百分比更新", "description": "测试多服务模式下镜像百分比的正确处理", "method": "PUT", "url": "/api/aigw/v1/aigateway/gw-test123/multi-percentage-route/route/detail", "headers": {"X-Region": "gz", "Content-Type": "application/json"}, "request": {"srcProduct": "aibox", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/api/v1/multi-percentage", "caseSensitive": true}, "methods": ["POST"]}, "multiService": true, "trafficDistributionStrategy": "ratio", "targetService": [{"serviceSource": "CCE", "serviceName": "multi-service-1", "namespace": "default", "servicePort": 8080, "requestRatio": 60, "subset": "stable", "labels": {"version": "stable", "app": "multi-service-1"}}, {"serviceSource": "CCE", "serviceName": "multi-service-2", "namespace": "default", "servicePort": 8080, "requestRatio": 40}], "trafficMirror": {"enabled": true, "mirror": {"subset": "experimental", "labels": {"version": "experimental", "app": "multi-service-1"}}, "percentage": 25.0}, "authEnabled": false}, "expected_response": {"success": true, "status": 200, "result": {"routeName": "multi-percentage-route", "multiService": true, "trafficDistributionStrategy": "ratio", "trafficMirror": {"enabled": true, "mirror": {"subset": "experimental", "labels": {"version": "experimental", "app": "multi-service-1"}}, "percentage": 25.0}}}, "validation_points": ["多服务模式下镜像百分比正确设置", "镜像目标的 labels 信息完整", "响应包含完整的流量分发策略信息"]}, {"name": "验证禁用流量镜像时的响应", "description": "测试禁用流量镜像时响应的正确性", "method": "PUT", "url": "/api/aigw/v1/aigateway/gw-test123/disabled-mirror-route/route/detail", "headers": {"X-Region": "gz", "Content-Type": "application/json"}, "request": {"srcProduct": "aibox", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/api/v1/disabled-mirror", "caseSensitive": true}, "methods": ["POST"]}, "multiService": false, "targetService": {"serviceSource": "CCE", "serviceName": "disabled-mirror-service", "namespace": "default", "servicePort": 8080}, "trafficMirror": {"enabled": false}, "authEnabled": false}, "expected_response": {"success": true, "status": 200, "result": {"routeName": "disabled-mirror-route", "multiService": false, "trafficMirror": {"enabled": false}}}, "validation_points": ["禁用状态下只返回 enabled: false", "不包含 mirror 和 percentage 字段", "响应结构简洁正确"]}]}