{"description": "AI网关流量镜像功能测试用例", "test_cases": [{"name": "单服务模式 - 启用流量镜像", "request": {"routeName": "test-single-mirror", "srcProduct": "aibox", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/api/v1/test", "caseSensitive": true}, "methods": ["POST"]}, "multiService": false, "targetService": {"serviceSource": "CCE", "serviceName": "test-service", "namespace": "default", "servicePort": 8080, "subset": "v1", "labels": {"version": "v1", "app": "test-service"}}, "trafficMirror": {"enabled": true, "mirror": {"subset": "v2", "labels": {"version": "v2", "app": "test-service"}}, "percentage": 50.0}, "authEnabled": false}, "expected_response": {"success": true, "status": 200, "result": {"routeName": "test-single-mirror", "multiService": false, "trafficMirror": {"enabled": true, "mirror": {"subset": "v2", "labels": {"version": "v2", "app": "test-service"}}, "percentage": 50.0}}}}, {"name": "多服务模式 - 启用流量镜像", "request": {"routeName": "test-multi-mirror", "srcProduct": "aibox", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/api/v1/multi", "caseSensitive": true}, "methods": ["POST"]}, "multiService": true, "trafficDistributionStrategy": "ratio", "targetService": [{"serviceSource": "CCE", "serviceName": "test-service-1", "namespace": "default", "servicePort": 8080, "requestRatio": 70}, {"serviceSource": "CCE", "serviceName": "test-service-2", "namespace": "default", "servicePort": 8080, "requestRatio": 30}], "trafficMirror": {"enabled": true, "mirror": {"subset": "canary", "labels": {"version": "canary", "app": "test-service-1"}}, "percentage": 100.0}, "authEnabled": false}, "expected_response": {"success": true, "status": 200, "result": {"routeName": "test-multi-mirror", "multiService": true, "trafficDistributionStrategy": "ratio", "trafficMirror": {"enabled": true, "mirror": {"subset": "canary", "labels": {"version": "canary", "app": "test-service-1"}}, "percentage": 100.0}}}}, {"name": "流量镜像配置验证 - 缺少subset", "request": {"routeName": "test-invalid-mirror", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/api/v1/invalid"}, "methods": ["POST"]}, "multiService": false, "targetService": {"serviceSource": "CCE", "serviceName": "test-service", "namespace": "default", "servicePort": 8080}, "trafficMirror": {"enabled": true, "mirror": {"labels": {"version": "v2", "app": "test-service"}}, "percentage": 50.0}}, "expected_response": {"success": false, "status": 400, "message": "mirror subset cannot be empty when traffic mirror is enabled"}}, {"name": "流量镜像配置验证 - 百分比超出范围", "request": {"routeName": "test-invalid-percentage", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/api/v1/invalid"}, "methods": ["POST"]}, "multiService": false, "targetService": {"serviceSource": "CCE", "serviceName": "test-service", "namespace": "default", "servicePort": 8080}, "trafficMirror": {"enabled": true, "mirror": {"subset": "v2", "labels": {"version": "v2", "app": "test-service"}}, "percentage": 150.0}}, "expected_response": {"success": false, "status": 400, "message": "mirror percentage must be between 0 and 100, got 150.000000"}}, {"name": "未启用流量镜像", "request": {"routeName": "test-no-mirror", "matchRules": {"pathRule": {"matchType": "prefix", "value": "/api/v1/no-mirror"}, "methods": ["POST"]}, "multiService": false, "targetService": {"serviceSource": "CCE", "serviceName": "test-service", "namespace": "default", "servicePort": 8080}, "trafficMirror": {"enabled": false}}, "expected_response": {"success": true, "status": 200, "result": {"routeName": "test-no-mirror", "multiService": false, "trafficMirror": {"enabled": false}}}}]}