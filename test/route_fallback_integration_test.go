package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
)

// TestRouteWithFallbackIntegration 集成测试：测试带有Fallback配置的路由创建、查询和更新
func TestRouteWithFallbackIntegration(t *testing.T) {
	// 这是一个示例集成测试，展示如何测试Fallback功能
	// 在实际环境中，需要mock相关的依赖服务

	t.Run("ValidateFallbackConfigInRequest", func(t *testing.T) {
		// 测试请求中的Fallback配置验证
		testCases := []struct {
			name        string
			request     *meta.AIRouteRequest
			expectError bool
		}{
			{
				name: "Valid single service with fallback",
				request: &meta.AIRouteRequest{
					RouteName:  "test-route",
					SrcProduct: "aibox",
					MatchRules: meta.MatchRule{
						PathRule: struct {
							MatchType     string `json:"matchType" valid:"required"`
							Value         string `json:"value" valid:"required"`
							CaseSensitive bool   `json:"caseSensitive"`
						}{
							MatchType:     "prefix",
							Value:         "/v1/models/",
							CaseSensitive: true,
						},
						Methods: []string{"GET", "POST"},
					},
					MultiService: false,
					TargetService: map[string]interface{}{
						"serviceSource": "CCE",
						"serviceName":   "ai-primary-service",
						"namespace":     "ai-namespace",
						"servicePort":   8080,
					},
					Rewrite: meta.Rewrite{
						Enabled: false,
					},
					AuthEnabled: false,
					TokenRateLimit: meta.TokenRateLimit{
						Enabled: false,
					},
					FallbackConfig: &meta.FallbackConfig{
						Enabled:     true,
						ServiceName: "ai-fallback-service",
						Namespace:   "ai-namespace",
						ServicePort: 8080,
					},
				},
				expectError: false,
			},
			{
				name: "Invalid fallback config - missing service name",
				request: &meta.AIRouteRequest{
					RouteName: "test-route",
					FallbackConfig: &meta.FallbackConfig{
						Enabled:     true,
						ServiceName: "", // 缺少服务名
						Namespace:   "ai-namespace",
						ServicePort: 8080,
					},
				},
				expectError: true,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				err := tc.request.ValidateFallbackConfig()
				if tc.expectError {
					assert.Error(t, err)
				} else {
					assert.NoError(t, err)
				}
			})
		}
	})

	t.Run("TestFallbackConfigSerialization", func(t *testing.T) {
		// 测试Fallback配置的JSON序列化和反序列化
		originalRequest := &meta.AIRouteRequest{
			RouteName: "test-route",
			FallbackConfig: &meta.FallbackConfig{
				Enabled:     true,
				ServiceName: "ai-fallback-service",
				Namespace:   "ai-namespace",
				ServicePort: 8080,
			},
		}

		// 序列化
		jsonData, err := json.Marshal(originalRequest)
		assert.NoError(t, err)

		// 反序列化
		var deserializedRequest meta.AIRouteRequest
		err = json.Unmarshal(jsonData, &deserializedRequest)
		assert.NoError(t, err)

		// 验证Fallback配置是否正确保留
		assert.NotNil(t, deserializedRequest.FallbackConfig)
		assert.True(t, deserializedRequest.FallbackConfig.Enabled)
		assert.Equal(t, "ai-fallback-service", deserializedRequest.FallbackConfig.ServiceName)
		assert.Equal(t, "ai-namespace", deserializedRequest.FallbackConfig.Namespace)
		assert.Equal(t, 8080, deserializedRequest.FallbackConfig.ServicePort)
	})

	t.Run("TestFallbackConfigInResponse", func(t *testing.T) {
		// 测试响应中的Fallback配置
		response := &meta.AIRouteResponse{
			RouteName: "test-route",
			FallbackConfig: &meta.FallbackConfig{
				Enabled:     true,
				ServiceName: "ai-fallback-service",
				Namespace:   "ai-namespace",
				ServicePort: 8080,
			},
		}

		// 验证响应结构
		assert.NotNil(t, response.FallbackConfig)
		assert.True(t, response.FallbackConfig.Enabled)
		assert.Equal(t, "ai-fallback-service", response.FallbackConfig.ServiceName)
	})
}

// TestHTTPRequestHandling 测试HTTP请求处理（模拟）
func TestHTTPRequestHandling(t *testing.T) {
	t.Run("CreateRouteWithFallback", func(t *testing.T) {
		// 创建带有Fallback配置的路由请求
		requestBody := map[string]interface{}{
			"routeName":  "test-route",
			"srcProduct": "aibox",
			"matchRules": map[string]interface{}{
				"pathRule": map[string]interface{}{
					"matchType":     "prefix",
					"value":         "/v1/models/",
					"caseSensitive": true,
				},
				"methods": []string{"GET", "POST"},
			},
			"multiService": false,
			"targetService": map[string]interface{}{
				"serviceSource": "CCE",
				"serviceName":   "ai-primary-service",
				"namespace":     "ai-namespace",
				"servicePort":   8080,
			},
			"rewrite": map[string]interface{}{
				"enabled": false,
			},
			"authEnabled": false,
			"tokenRateLimit": map[string]interface{}{
				"enabled": false,
			},
			"fallbackConfig": map[string]interface{}{
				"enabled":     true,
				"serviceName": "ai-fallback-service",
				"namespace":   "ai-namespace",
				"servicePort": 8080,
			},
		}

		jsonBody, _ := json.Marshal(requestBody)

		// 创建HTTP请求
		req := httptest.NewRequest(http.MethodPost, "/api/aigw/v1/aigateway/test-instance/test-cluster/route", bytes.NewReader(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Region", "gz")

		rec := httptest.NewRecorder()
		c := echo.New().NewContext(req, rec)
		c.SetParamNames("instanceId", "clusterId")
		c.SetParamValues("test-instance", "test-cluster")

		// 验证请求体可以正确解析
		var routeRequest meta.AIRouteRequest
		err := c.Bind(&routeRequest)
		assert.NoError(t, err)

		// 验证Fallback配置
		assert.NotNil(t, routeRequest.FallbackConfig)
		assert.True(t, routeRequest.FallbackConfig.Enabled)
		assert.Equal(t, "ai-fallback-service", routeRequest.FallbackConfig.ServiceName)

		// 验证配置验证通过
		err = routeRequest.ValidateFallbackConfig()
		assert.NoError(t, err)
	})

	t.Run("UpdateRouteWithFallback", func(t *testing.T) {
		// 更新路由的Fallback配置
		updateBody := map[string]interface{}{
			"srcProduct": "aibox",
			"matchRules": map[string]interface{}{
				"pathRule": map[string]interface{}{
					"matchType":     "prefix",
					"value":         "/v1/models/",
					"caseSensitive": true,
				},
			},
			"multiService": false,
			"targetService": map[string]interface{}{
				"serviceSource": "CCE",
				"serviceName":   "ai-primary-service-v2",
				"namespace":     "ai-namespace",
				"servicePort":   8080,
			},
			"fallbackConfig": map[string]interface{}{
				"enabled":     true,
				"serviceName": "ai-fallback-service-v2",
				"namespace":   "ai-namespace",
				"servicePort": 9090,
			},
		}

		jsonBody, _ := json.Marshal(updateBody)

		// 创建HTTP请求
		req := httptest.NewRequest("PUT", "/api/aigw/v1/aigateway/test-instance/test-route/route/detail", bytes.NewReader(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Region", "gz")

		rec := httptest.NewRecorder()
		c := echo.New().NewContext(req, rec)
		c.SetParamNames("instanceId", "routeName")
		c.SetParamValues("test-instance", "test-route")

		// 验证请求体可以正确解析
		var updateRequest meta.AIRouteRequest
		err := c.Bind(&updateRequest)
		assert.NoError(t, err)

		// 验证更新的Fallback配置
		assert.NotNil(t, updateRequest.FallbackConfig)
		assert.True(t, updateRequest.FallbackConfig.Enabled)
		assert.Equal(t, "ai-fallback-service-v2", updateRequest.FallbackConfig.ServiceName)
		assert.Equal(t, 9090, updateRequest.FallbackConfig.ServicePort)

		// 验证配置验证通过
		err = updateRequest.ValidateFallbackConfig()
		assert.NoError(t, err)
	})
}
