# 消费者配额管理技术方案

## 1. 方案概述

### 1.1 背景
在AI网关服务中，消费者作为API的调用方，需要对其API调用次数进行限制，以防止资源滥用、保障服务质量。目前系统缺乏对消费者调用次数的配额管理功能，无法实现灵活的调用量控制。

### 1.2 目标
设计并实现一套消费者配额管理功能，支持：
- 创建消费者时设置配额限制
- 编辑消费者时修改配额限制
- 查询消费者时展示配额信息
- 删除消费者时清理配额数据
- 支持无限配额和有限配额两种模式

### 1.3 范围
本方案涉及以下接口的修改和实现：
- 创建消费者接口
- 编辑消费者接口
- 查询消费者详情接口
- 查询消费者列表接口
- 删除消费者接口

## 2. 系统设计

### 2.1 整体架构
![架构图](../images/quota_management_architecture.png)

配额管理功能的整体架构包括：
1. **接口层**：处理HTTP请求，参数校验和响应
2. **服务层**：实现业务逻辑，包括配额的创建、更新、查询和删除
3. **数据存储层**：
   - Kubernetes中的WasmPlugin资源存储消费者信息，包括无限配额标识
   - Redis存储具体的配额值

### 2.2 数据模型

#### 2.2.1 请求/响应模型扩展
1. **CreateConsumerRequest**：
```go
type CreateConsumerRequest struct {
    ConsumerName   string   `json:"consumerName"`
    Description    string   `json:"description"`
    AuthType       string   `json:"authType"`
    RouteNames     []string `json:"routeNames"`
    UnlimitedQuota bool     `json:"unlimitedQuota"` // 是否无限配额
    QuotaValue     int64    `json:"quotaValue"`     // 配额值
}
```

2. **UpdateConsumerRequest**：
```go
type UpdateConsumerRequest struct {
    Description    string   `json:"description"`
    RouteNames     []string `json:"routeNames"`
    UnlimitedQuota bool     `json:"unlimitedQuota"` // 是否无限配额
    QuotaValue     int64    `json:"quotaValue"`     // 配额值
}
```

3. **ConsumerDetailInfo**：
```go
type ConsumerDetailInfo struct {
    ConsumerID     string              `json:"consumerId"`
    ConsumerName   string              `json:"consumerName"`
    Description    string              `json:"description"`
    AuthType       string              `json:"authType"`
    AuthInfo       ConsumerAuthInfo    `json:"authInfo"`
    Routes         []ConsumerRouteInfo `json:"routes"`
    UnlimitedQuota bool                `json:"unlimitedQuota"` // 是否无限配额
    QuotaValue     int64               `json:"quotaValue"`     // 配额值
}
```

4. **Consumer**（消费者列表项）：
```go
type Consumer struct {
    ConsumerId     string `json:"consumerId"`
    ConsumerName   string `json:"consumerName"`
    Description    string `json:"description"`
    CreateTime     string `json:"createTime"`
    UnlimitedQuota bool   `json:"unlimitedQuota"` // 是否无限配额
    QuotaValue     int64  `json:"quotaValue"`     // 配额值
}
```

#### 2.2.2 存储模型
1. **WasmPlugin中的消费者对象**：
```json
{
    "credential": "...",
    "name": "consumer-name",
    "id": "consumer-id",
    "description": "...",
    "authType": "...",
    "createTime": "...",
    "unlimitedQuota": true|false
}
```

2. **Redis存储格式**：
   - Key: `chat_quota_<instanceId>:<consumerName>`
   - Value: 整数表示的配额值，或者 `math.MaxInt64`（无限配额）

### 2.3 核心流程

#### 2.3.1 创建消费者流程
1. 接收并验证请求参数，包括配额相关参数
2. 验证配额值：当`UnlimitedQuota`为`false`时，`QuotaValue`必须大于0
3. 在WasmPlugin资源中创建消费者信息，并保存`unlimitedQuota`字段
4. 在Redis中保存配额值：
   - 如果`UnlimitedQuota`为`true`，存储`math.MaxInt64`
   - 如果`UnlimitedQuota`为`false`，存储`QuotaValue`

#### 2.3.2 编辑消费者流程
1. 接收并验证请求参数
2. 更新WasmPlugin资源中消费者的`unlimitedQuota`字段
3. 更新Redis中的配额值

#### 2.3.3 查询消费者详情/列表流程
1. 从WasmPlugin资源中获取消费者信息，包括`unlimitedQuota`字段
2. 从Redis中获取具体配额值
3. 组装响应数据并返回

#### 2.3.4 删除消费者流程
1. 从WasmPlugin资源中删除消费者信息
2. 从Redis中删除对应的配额数据

## 3. 技术实现

### 3.1 Redis服务集成

为了实现配额值的存储和管理，本方案将Redis服务集成到APIServerCore中：

```go
type APIServerCore struct {
    // 其他字段...
    redisService redis.ServiceInterface
}
```

Redis服务初始化方法：

```go
func (core *APIServerCore) InitRedisService(ctx csmContext.CsmContext, region, hostedClusterId string) (redis.ServiceInterface, error) {
    // 如果已经初始化过,直接返回
    if core.redisService != nil && core.redisService.IsConnected() {
        return core.redisService, nil
    }

    // 从配置文件中读取Redis配置
    redisNamespace := viper.GetString("redis.namespace")
    redisPodName := viper.GetString("redis.podName")

    // 创建Redis服务配置
    redisOption := redis.NewOption(
        region,          // 区域
        hostedClusterId, // 集群ID
        redisNamespace,  // 命名空间
        redisPodName,    // Pod名称
        core.cceService, // CCE服务
    )

    // 创建Redis服务实例
    redisService := redis.NewRedisService(redisOption)

    // 连接Redis
    if err := redisService.Connect(ctx); err != nil {
        ctx.CsmLogger().Errorf("failed to connect to redis: %v", err)
        return nil, errors.Wrap(err, "failed to connect to redis")
    }

    // 保存服务实例
    core.redisService = redisService
    return redisService, nil
}
```

配置文件中添加Redis相关配置：

```yaml
redis:
  namespace: "aigw-redis"
  podName: "redis-5f69478ddb-wfjtc"
```

### 3.2 接口实现

#### 3.2.1 创建消费者接口

```go
func (core *APIServerCore) CreateConsumer(ctx csmContext.CsmContext) error {
    // 验证配额值
    if !createConsumerReq.UnlimitedQuota && createConsumerReq.QuotaValue < 0 {
        return csmErr.NewInvalidParameterValueException("quotaValue必须大于等于0")
    }
    
    // 初始化Redis服务
    redisService, err := core.InitRedisService(ctx, region, hostedClusterId)
    if err != nil {
        ctx.CsmLogger().Errorf("failed to initialize redis service: %v", err)
        return errors.Wrap(err, "failed to initialize redis service")
    }
    defer redisService.Disconnect()

    // 设置redis中的配额值
    redisKey := fmt.Sprintf("chat_quota_%s:%s", instanceId, createConsumerReq.ConsumerName)
    quotaValue := createConsumerReq.QuotaValue
    if createConsumerReq.UnlimitedQuota {
        quotaValue = math.MaxInt64
    }

    // 设置配额值到redis
    err = redisService.Set(ctx, redisKey, fmt.Sprintf("%d", quotaValue))
    if err != nil {
        ctx.CsmLogger().Errorf("failed to set quota value in redis: %v", err)
        return errors.Wrap(err, "failed to set quota value in redis")
    }
    
    // 添加新的consumer，包含unlimitedQuota字段
    newConsumer := map[string]interface{}{
        "credential":     credential,
        "name":           createConsumerReq.ConsumerName,
        "id":             consumerId,
        "description":    createConsumerReq.Description,
        "authType":       createConsumerReq.AuthType,
        "createTime":     createTime,
        "unlimitedQuota": createConsumerReq.UnlimitedQuota,
    }
    
    // 其他逻辑...
}
```

#### 3.2.2 编辑消费者接口

```go
func (core *APIServerCore) UpdateConsumer(ctx csmContext.CsmContext) error {
    // 验证配额值
    if !updateReq.UnlimitedQuota && updateReq.QuotaValue < 0 {
        return csmErr.NewInvalidParameterValueException("quotaValue必须大于等于0")
    }
    
    // 更新消费者信息，包括unlimitedQuota字段
    consumer["unlimitedQuota"] = updateReq.UnlimitedQuota
    
    // 初始化Redis服务
    redisService, err := core.InitRedisService(ctx, region, hostedClusterId)
    if err != nil {
        ctx.CsmLogger().Errorf("failed to initialize redis service: %v", err)
        return errors.Wrap(err, "failed to initialize redis service")
    }
    defer redisService.Disconnect()

    // 设置redis中的配额值
    redisKey := fmt.Sprintf("chat_quota_%s:%s", instanceId, consumerName)
    quotaValue := updateReq.QuotaValue
    if updateReq.UnlimitedQuota {
        quotaValue = math.MaxInt64
    }

    // 设置配额值到redis
    err = redisService.Set(ctx, redisKey, fmt.Sprintf("%d", quotaValue))
    if err != nil {
        ctx.CsmLogger().Errorf("failed to set quota value in redis: %v", err)
        return errors.Wrap(err, "failed to set quota value in redis")
    }
    
    // 其他逻辑...
}
```

#### 3.2.3 查询消费者接口

```go
func getConsumerDetailFromList(core *APIServerCore, ctx csmContext.CsmContext, items []unstructured.Unstructured, region, consumerId string, client kube.Client) (*meta.ConsumerDetailInfo, bool, error) {
    // 获取unlimitedQuota信息
    unlimitedQuota := false
    unlimitedQuotaValue, exists, _ := unstructured.NestedBool(consumer, "unlimitedQuota")
    if exists {
        unlimitedQuota = unlimitedQuotaValue
    }

    // 初始化Redis服务
    redisService, err := core.InitRedisService(ctx, region, hostedClusterId)
    if err != nil {
        ctx.CsmLogger().Errorf("failed to initialize redis service: %v", err)
        return nil, false, errors.Wrap(err, "failed to initialize redis service")
    }
    defer redisService.Disconnect()

    // 从redis获取配额值
    quotaValue := int64(-1)
    if !unlimitedQuota {
        redisKey := fmt.Sprintf("chat_quota_%s:%s", instanceId, consumerName)
        quotaStr, err := redisService.Get(ctx, redisKey)
        if err != nil {
            ctx.CsmLogger().Errorf("failed to get quota value from redis: %v", err)
            return nil, false, errors.Wrap(err, "failed to get quota value from redis")
        }
        if quotaStr != "" {
            quota, err := strconv.ParseInt(quotaStr, 10, 64)
            if err != nil {
                ctx.CsmLogger().Errorf("failed to parse quota value: %v", err)
                return nil, false, errors.Wrap(err, "failed to parse quota value")
            }
            quotaValue = quota
        }
    }
    
    // 构造返回结果，包含配额信息
    return &meta.ConsumerDetailInfo{
        ConsumerID:     id,
        ConsumerName:   consumerName,
        Description:    description,
        AuthType:       authType,
        AuthInfo:       meta.ConsumerAuthInfo{Token: credential},
        Routes:         routes,
        UnlimitedQuota: unlimitedQuota,
        QuotaValue:     quotaValue,
    }, true, nil
}
```

#### 3.2.4 删除消费者接口

```go
func (core *APIServerCore) DeleteConsumer(ctx csmContext.CsmContext) error {
    // 初始化Redis服务
    redisService, err := core.InitRedisService(ctx, region, hostedClusterId)
    if err != nil {
        ctx.CsmLogger().Errorf("failed to initialize redis service: %v", err)
        return errors.Wrap(err, "failed to initialize redis service")
    }
    defer redisService.Disconnect()

    // 删除redis中的配额值
    redisKey := fmt.Sprintf("chat_quota_%s:%s", instanceId, consumerName)
    if err := redisService.Delete(ctx, redisKey); err != nil {
        ctx.CsmLogger().Errorf("failed to delete quota value from redis: %v", err)
        // 不返回错误，继续处理
    } else {
        ctx.CsmLogger().Infof("successfully deleted quota value for consumer %s from redis", consumerName)
    }
    
    // 其他删除逻辑...
}
```

## 4. 测试方案

### 4.1 单元测试
为以下函数编写单元测试：
- `CreateConsumer`
- `UpdateConsumer`
- `GetConsumerDetail`
- `ListConsumers`
- `DeleteConsumer`

测试内容包括：
- 参数校验：配额值验证、参数有效性验证
- Redis交互：配额的存储和读取
- 异常处理：Redis连接失败、数据格式错误等

### 4.2 集成测试
1. 创建消费者并设置配额
2. 编辑消费者修改配额
3. 查询消费者验证配额信息
4. 删除消费者清理配额数据

### 4.3 API测试
使用Postman或curl测试各个API接口，验证请求和响应的正确性。

## 5. 部署与运维

### 5.1 部署流程
1. 确保部署环境中有可用的Redis服务
2. 更新配置文件中的Redis配置（namespace和podName）
3. 部署新版本的服务

### 5.2 监控与告警
1. 监控Redis连接状态
2. 监控配额相关接口的调用情况
3. 设置Redis存储异常的告警

### 5.3 扩展性考虑
1. 未来可以基于配额实现更细粒度的限流
2. 考虑添加配额使用统计和可视化功能
3. 支持按时间段的配额重置机制

## 6. 风险与应对

### 6.1 潜在风险
1. Redis服务不可用导致配额功能失效
2. 配额数据与消费者信息不一致
3. 高并发场景下的性能问题

### 6.2 应对措施
1. 实现Redis连接重试和故障转移机制
2. 定期同步检查配额数据与消费者信息的一致性
3. 优化Redis访问模式，考虑使用批量操作和缓存

## 7. 后续优化

1. 支持配额使用情况的实时统计
2. 配额预警和自动扩容机制
3. 多级配额管理（全局配额、消费者组配额、单个消费者配额）
4. 接口级别的配额管理
5. 可配置的配额重置周期（按天、按周、按月）

## 8. 结论

消费者配额管理功能的实现将为AI网关服务提供更精细的流量控制能力，保障服务的稳定性和可靠性。通过在WasmPlugin和Redis中分别存储配额标志和具体配额值，实现了数据的持久化和高效访问。同时，将Redis服务集成到APIServerCore中，提高了代码的复用性和可维护性。 