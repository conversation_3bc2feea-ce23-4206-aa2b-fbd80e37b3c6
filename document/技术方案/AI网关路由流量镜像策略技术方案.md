# AI网关路由流量镜像策略技术方案

## 1. 方案概述

### 1.1 功能需求
为AI网关的路由管理功能增加流量镜像策略支持，允许在创建VirtualService路由时配置流量镜像策略，将部分或全部流量镜像到指定的目标服务，用于生产环境的测试、监控和调试。

### 1.2 技术目标
- 基于现有路由管理代码框架，最小化修改原则
- 支持单服务模式和多服务模式下的流量镜像
- 兼容现有的多服务分发策略（比例分发、模型名分发）
- 遵循现有的验证、日志记录和错误处理模式
- 支持Istio VirtualService的流量镜像配置语法

### 1.3 技术架构
```
AIRouteRequest (扩展) 
    ↓
验证流量镜像配置
    ↓
创建VirtualService (单服务/多服务)
    ↓
配置HTTPMirrorPolicy
    ↓
生成Istio资源
```

## 2. 功能流程图

```mermaid
graph TD
    A[接收路由创建请求] --> B{是否配置流量镜像?}
    B -->|否| C[按现有逻辑创建路由]
    B -->|是| D[验证镜像配置]
    D --> E{验证通过?}
    E -->|否| F[返回验证错误]
    E -->|是| G{单服务模式?}
    G -->|是| H[创建单服务VirtualService + 镜像]
    G -->|否| I{多服务模式类型?}
    I -->|比例分发| J[创建比例分发VirtualService + 镜像]
    I -->|模型名分发| K[创建模型名分发VirtualService + 镜像]
    H --> L[生成Istio资源]
    J --> L
    K --> L
    L --> M[记录镜像配置日志]
    M --> N[返回成功响应]
    C --> N
```

## 3. 数据结构设计

### 3.1 流量镜像配置结构

```go
// TrafficMirrorPolicy 流量镜像策略配置
type TrafficMirrorPolicy struct {
    Enabled     bool                  `json:"enabled"`                // 是否启用流量镜像
    Mirror      MirrorDestination     `json:"mirror,omitempty"`       // 镜像目标配置
    Percentage  float64               `json:"percentage,omitempty"`   // 镜像流量百分比 (0-100)
}

// MirrorDestination 镜像目标配置（基于同一服务的不同版本）
type MirrorDestination struct {
    Subset      string                `json:"subset" valid:"required"`     // 目标subset名称
    Labels      map[string]string     `json:"labels" valid:"required"`     // subset标签选择器
}
```

### 3.2 TargetService扩展

```go
// TargetService 扩展后的目标服务配置结构
type TargetService struct {
    // ... 现有字段保持不变 ...
    ServiceSource        string            `json:"serviceSource" valid:"required"` // CCE
    ServiceName          string            `json:"serviceName" valid:"required"`   // service name
    Namespace            string            `json:"namespace" valid:"required"`     // namespace
    ServicePort          int               `json:"servicePort" valid:"required"`   // service port
    LoadBalanceAlgorithm string            `json:"loadBalanceAlgorithm"`           // round-robin, least-conn, random, consistent-hash
    RequestRatio         int               `json:"requestRatio,omitempty"`         // for ratio distribution strategy
    ModelName            string            `json:"modelName,omitempty"`            // for model_name distribution strategy
    Weight               int               `json:"weight,omitempty"`               // for weighted distribution strategy
    HashType             string            `json:"hashType,omitempty"`             // 哈希一致性类型
    HashKey              string            `json:"hashKey,omitempty"`              // 哈希一致性参数

    // 新增字段：支持主服务版本自定义
    Subset               string            `json:"subset,omitempty"`               // 主服务版本subset名称
    Labels               map[string]string `json:"labels,omitempty"`               // 主服务版本标签选择器
}
```

### 3.3 AIRouteRequest扩展

```go
// AIRouteRequest 扩展后的路由请求结构
type AIRouteRequest struct {
    // ... 现有字段保持不变 ...

    // 新增流量镜像配置字段
    TrafficMirror TrafficMirrorPolicy `json:"trafficMirror,omitempty"` // 流量镜像策略
}
```

### 3.4 AIRouteResponse扩展

```go
// AIRouteResponse 扩展后的路由响应结构  
type AIRouteResponse struct {
    // ... 现有字段保持不变 ...
    
    // 新增流量镜像响应字段
    TrafficMirror *TrafficMirrorPolicy `json:"trafficMirror,omitempty"` // 流量镜像策略响应
}
```

## 4. 核心实现逻辑

### 4.1 流量镜像配置验证

```go
// validateTrafficMirrorConfig 验证流量镜像配置
func (core *APIServerCore) validateTrafficMirrorConfig(routeRequest *meta.AIRouteRequest) error {
    if !routeRequest.TrafficMirror.Enabled {
        return nil // 未启用镜像，跳过验证
    }

    // 验证镜像目标配置
    mirror := routeRequest.TrafficMirror.Mirror
    if mirror.Subset == "" {
        return csmErr.NewInvalidParameterValueException("mirror subset cannot be empty when traffic mirror is enabled")
    }

    // 验证标签选择器不为空
    if len(mirror.Labels) == 0 {
        return csmErr.NewInvalidParameterValueException("mirror labels cannot be empty")
    }

    // 验证镜像百分比范围
    if routeRequest.TrafficMirror.Percentage < 0 || routeRequest.TrafficMirror.Percentage > 100 {
        return csmErr.NewInvalidParameterValueException(fmt.Sprintf("mirror percentage must be between 0 and 100, got %f", routeRequest.TrafficMirror.Percentage))
    }

    return nil
}
```

### 4.2 DestinationRule创建逻辑扩展

```go
// createDestinationRule 扩展DestinationRule创建逻辑以支持主服务和镜像subset
func (core *APIServerCore) createDestinationRule(
    ctx csmContext.CsmContext,
    routeName string,
    service meta.TargetService,
    gatewayNamespace string,
    istioClient istioclientset.Interface,
    routeRequest *meta.AIRouteRequest) error {

    // 创建主服务subset
    var mainSubsetName string
    var mainSubsetLabels map[string]string

    if service.Subset != "" && len(service.Labels) > 0 {
        // 用户自定义了主服务版本
        mainSubsetName = service.Subset
        mainSubsetLabels = service.Labels
        ctx.CsmLogger().Infof("使用用户自定义主服务版本: subset=%s, labels=%v",
            mainSubsetName, mainSubsetLabels)
    } else {
        // 使用默认主服务版本
        mainSubsetName = routeName + "-" + service.ServiceName
        mainSubsetLabels = map[string]string{
            "app": service.ServiceName,
        }
        ctx.CsmLogger().Infof("使用默认主服务版本: subset=%s, labels=%v",
            mainSubsetName, mainSubsetLabels)
    }

    subsets := []*istionetworkingv1alpha3.Subset{
        {
            Name:   mainSubsetName,
            Labels: mainSubsetLabels,
        },
    }

    // 如果启用了流量镜像，添加镜像subset
    if routeRequest.TrafficMirror.Enabled {
        mirrorSubset := &istionetworkingv1alpha3.Subset{
            Name:   routeRequest.TrafficMirror.Mirror.Subset,
            Labels: routeRequest.TrafficMirror.Mirror.Labels,
        }
        subsets = append(subsets, mirrorSubset)
        ctx.CsmLogger().Infof("添加镜像subset: %s, 标签: %v",
            mirrorSubset.Name, mirrorSubset.Labels)
    }

    destinationRule := &v1alpha3.DestinationRule{
        TypeMeta: metav1.TypeMeta{
            Kind:       "DestinationRule",
            APIVersion: "networking.istio.io/v1beta1",
        },
        ObjectMeta: metav1.ObjectMeta{
            Name:      routeName + "-" + service.ServiceName + "-dr",
            Namespace: gatewayNamespace,
        },
        Spec: istionetworkingv1alpha3.DestinationRule{
            Host:    service.ServiceName + "." + service.Namespace + ".svc.cluster.local",
            Subsets: subsets,
        },
    }

    // 设置负载均衡算法
    if service.LoadBalanceAlgorithm != "" {
        // ... 现有负载均衡逻辑 ...
    }

    // 创建DestinationRule资源
    _, err := istioClient.NetworkingV1alpha3().DestinationRules(gatewayNamespace).Create(
        context.TODO(), destinationRule, metav1.CreateOptions{})
    if err != nil && !kubeErrors.IsAlreadyExists(err) {
        return errors.Wrap(err, "Failed to create DestinationRule")
    }

    return nil
}
```

### 4.3 单服务模式镜像配置

```go
// createSingleServiceVirtualService 扩展单服务VirtualService创建逻辑
func (core *APIServerCore) createSingleServiceVirtualService(
    ctx csmContext.CsmContext,
    routeRequest *meta.AIRouteRequest,
    gatewayNamespace string) (*v1alpha3.VirtualService, error) {

    service, err := routeRequest.GetSingleTargetService()
    if err != nil {
        return nil, err
    }

    // ... 现有匹配规则创建逻辑保持不变 ...

    // 确定主服务subset名称
    var mainSubsetName string
    if service.Subset != "" {
        mainSubsetName = service.Subset
    } else {
        mainSubsetName = routeRequest.RouteName + "-" + service.ServiceName
    }

    // 构建HTTP路由配置
    httpRoute := &istionetworkingv1alpha3.HTTPRoute{
        Name:  routeRequest.RouteName,
        Match: httpMatches,
        Route: []*istionetworkingv1alpha3.HTTPRouteDestination{
            {
                Destination: &istionetworkingv1alpha3.Destination{
                    Host: service.ServiceName + "." + service.Namespace + ".svc.cluster.local",
                    Port: &istionetworkingv1alpha3.PortSelector{
                        Number: uint32(service.ServicePort),
                    },
                    Subset: mainSubsetName, // 使用确定的主版本subset
                },
                Weight: 100,
            },
        },
    }

    // 添加流量镜像配置
    if routeRequest.TrafficMirror.Enabled {
        ctx.CsmLogger().Infof("单服务模式：配置流量镜像 主版本=%s -> 镜像版本=%s",
            mainSubsetName, routeRequest.TrafficMirror.Mirror.Subset)

        httpRoute.Mirror = &istionetworkingv1alpha3.Destination{
            Host: service.ServiceName + "." + service.Namespace + ".svc.cluster.local",
            Port: &istionetworkingv1alpha3.PortSelector{
                Number: uint32(service.ServicePort),
            },
            Subset: routeRequest.TrafficMirror.Mirror.Subset, // 镜像版本subset
        }

        // 设置镜像百分比
        if routeRequest.TrafficMirror.Percentage > 0 && routeRequest.TrafficMirror.Percentage < 100 {
            httpRoute.MirrorPercentage = &istionetworkingv1alpha3.Percent{
                Value: routeRequest.TrafficMirror.Percentage,
            }
        }

        ctx.CsmLogger().Infof("添加镜像配置: subset=%s, 百分比=%.1f%%",
            routeRequest.TrafficMirror.Mirror.Subset, routeRequest.TrafficMirror.Percentage)
    }

    // ... 其余逻辑保持不变 ...
}
```

### 4.4 多服务模式镜像配置

```go
// createRatioBasedVirtualService 扩展比例分发VirtualService创建逻辑
func (core *APIServerCore) createRatioBasedVirtualService(
    ctx csmContext.CsmContext,
    routeRequest *meta.AIRouteRequest,
    services []meta.TargetService,
    gatewayNamespace string) (*v1alpha3.VirtualService, error) {

    // ... 现有路由目标创建逻辑保持不变 ...

    // 构建HTTP路由配置
    httpRoute := &istionetworkingv1alpha3.HTTPRoute{
        Name:  routeRequest.RouteName,
        Match: httpMatches,
        Route: routeDestinations, // 现有的比例分发目标
    }

    // 添加流量镜像配置（镜像到第一个服务的不同版本）
    if routeRequest.TrafficMirror.Enabled {
        ctx.CsmLogger().Infof("多服务比例分发模式：配置流量镜像")

        // 使用第一个服务作为镜像的基础服务
        firstService := services[0]
        httpRoute.Mirror = &istionetworkingv1alpha3.Destination{
            Host: firstService.ServiceName + "." + firstService.Namespace + ".svc.cluster.local",
            Port: &istionetworkingv1alpha3.PortSelector{
                Number: uint32(firstService.ServicePort),
            },
            Subset: routeRequest.TrafficMirror.Mirror.Subset, // 镜像版本subset
        }

        // 设置镜像百分比
        if routeRequest.TrafficMirror.Percentage > 0 && routeRequest.TrafficMirror.Percentage < 100 {
            httpRoute.MirrorPercentage = &istionetworkingv1alpha3.Percent{
                Value: routeRequest.TrafficMirror.Percentage,
            }
        }

        ctx.CsmLogger().Infof("多服务模式添加镜像配置: service=%s, subset=%s, 百分比=%.1f%%",
            firstService.ServiceName, routeRequest.TrafficMirror.Mirror.Subset, routeRequest.TrafficMirror.Percentage)
    }

    // ... 其余逻辑保持不变 ...
}
```

## 5. 数据格式样例

### 5.1 请求数据格式样例

```json
{
  "routeName": "ai-model-route",
  "srcProduct": "AI-Gateway",
  "matchRules": {
    "pathRule": {
      "matchType": "prefix",
      "value": "/api/v1/chat",
      "caseSensitive": false
    },
    "methods": ["POST", "GET"]
  },
  "multiService": false,
  "targetService": {
    "serviceSource": "CCE",
    "serviceName": "ai-model-service",
    "namespace": "ai-prod",
    "servicePort": 8080,
    "loadBalanceAlgorithm": "round-robin"
  },
  "trafficMirror": {
    "enabled": true,
    "mirrors": [
      {
        "serviceSource": "CCE",
        "serviceName": "ai-model-test",
        "namespace": "ai-test", 
        "servicePort": 8080,
        "percentage": 50.0
      }
    ]
  },
  "authEnabled": true,
  "rewrite": {
    "enabled": false
  }
}
```

### 5.2 多服务模式请求样例

```json
{
  "routeName": "ai-multi-route",
  "multiService": true,
  "trafficDistributionStrategy": "ratio",
  "targetService": [
    {
      "serviceSource": "CCE",
      "serviceName": "ai-model-v1",
      "namespace": "ai-prod",
      "servicePort": 8080,
      "requestRatio": 70
    },
    {
      "serviceSource": "CCE", 
      "serviceName": "ai-model-v2",
      "namespace": "ai-prod",
      "servicePort": 8080,
      "requestRatio": 30
    }
  ],
  "trafficMirror": {
    "enabled": true,
    "mirrors": [
      {
        "serviceSource": "CCE",
        "serviceName": "ai-model-shadow",
        "namespace": "ai-test",
        "servicePort": 8080,
        "percentage": 100.0
      }
    ]
  }
}
```

**请确认以上数据结构格式是否正确，特别是TrafficMirrorPolicy和MirrorDestination的字段定义。**

## 6. 测试要点 (TODO)

### 6.1 单元测试要点
- 流量镜像配置验证逻辑测试
- VirtualService生成逻辑测试
- 数据结构序列化/反序列化测试

### 6.2 集成测试要点  
- 单服务模式流量镜像功能测试
- 多服务模式流量镜像功能测试
- 与现有分发策略兼容性测试

### 6.3 性能测试要点
- 镜像流量对主流量的影响测试
- 大流量场景下的镜像性能测试

## 7. 技术风险和注意事项

### 7.1 技术限制
- 当前Istio 1.13.2版本每个HTTP路由只支持一个镜像目标
- 镜像流量是"fire and forget"模式，不影响主请求响应
- 镜像请求的Host头会自动添加"-shadow"后缀

### 7.2 兼容性考虑
- 需要确保与现有多服务分发策略的兼容性
- 镜像配置不应影响现有路由的正常功能
- 向后兼容，未配置镜像的路由保持原有行为

### 7.3 运维考虑
- 需要监控镜像流量的资源消耗
- 建议在生产环境中谨慎使用高百分比的流量镜像
- 镜像目标服务需要有足够的处理能力

### 4.4 模型名分发模式镜像配置

```go
// createModelNameBasedVirtualService 扩展模型名分发VirtualService创建逻辑
func (core *APIServerCore) createModelNameBasedVirtualService(
    ctx csmContext.CsmContext,
    routeRequest *meta.AIRouteRequest,
    services []meta.TargetService,
    gatewayNamespace string) (*v1alpha3.VirtualService, error) {

    // ... 现有逻辑保持不变 ...

    // 为每个HTTP路由添加流量镜像配置
    if routeRequest.TrafficMirror.Enabled && len(routeRequest.TrafficMirror.Mirrors) > 0 {
        ctx.CsmLogger().Infof("模型名分发模式：配置流量镜像")

        mirror := routeRequest.TrafficMirror.Mirrors[0]
        mirrorPolicy := &istionetworkingv1alpha3.HTTPMirrorPolicy{
            Destination: &istionetworkingv1alpha3.Destination{
                Host: mirror.ServiceName + "." + mirror.Namespace + ".svc.cluster.local",
                Port: &istionetworkingv1alpha3.PortSelector{
                    Number: uint32(mirror.ServicePort),
                },
            },
        }

        if mirror.Percentage > 0 && mirror.Percentage < 100 {
            mirrorPolicy.Percentage = &istionetworkingv1alpha3.Percent{
                Value: mirror.Percentage,
            }
        }

        // 为所有HTTP路由添加镜像配置
        for _, httpRoute := range httpRoutes {
            httpRoute.Mirror = mirrorPolicy
        }

        ctx.CsmLogger().Infof("模型名分发模式添加镜像目标: %s.%s:%d",
            mirror.ServiceName, mirror.Namespace, mirror.ServicePort)
    }

    // ... 其余逻辑保持不变 ...
}
```

### 4.5 路由更新逻辑扩展

```go
// UpdateRoute 扩展路由更新逻辑以支持流量镜像
func (core *APIServerCore) UpdateRoute(ctx csmContext.CsmContext) error {
    // ... 现有参数获取和绑定逻辑保持不变 ...

    // 验证流量镜像配置
    if err := core.validateTrafficMirrorConfig(updateRequest); err != nil {
        ctx.CsmLogger().Errorf("流量镜像配置验证失败: %v", err)
        return err
    }

    // ... 其余更新逻辑保持不变，使用相同的VirtualService创建函数 ...
}
```

### 4.6 响应数据构建

```go
// buildAIRouteResponse 构建包含流量镜像信息的响应
func buildAIRouteResponse(routeRequest *meta.AIRouteRequest, serviceName string) *meta.AIRouteResponse {
    // ... 现有响应构建逻辑保持不变 ...

    response := &meta.AIRouteResponse{
        // ... 现有字段 ...
    }

    // 添加流量镜像配置到响应
    if routeRequest.TrafficMirror.Enabled {
        response.TrafficMirror = &routeRequest.TrafficMirror
    }

    return response
}
```

## 5. 详细实现步骤

### 5.1 pkg/model/meta/airoute.go 修改

```go
// 在文件末尾添加新的结构体定义
type TrafficMirrorPolicy struct {
    Enabled     bool                  `json:"enabled"`                // 是否启用流量镜像
    Mirror      MirrorDestination     `json:"mirror,omitempty"`       // 镜像目标配置
    Percentage  float64               `json:"percentage,omitempty"`   // 镜像流量百分比 (0-100)
}

type MirrorDestination struct {
    Subset      string                `json:"subset" valid:"required"`     // 目标subset名称
    Labels      map[string]string     `json:"labels" valid:"required"`     // subset标签选择器
}

// 修改TargetService结构体，在第71行后添加新字段
type TargetService struct {
    // ... 现有字段保持不变 ...
    HashType             string            `json:"hashType,omitempty"`             // 哈希一致性类型
    HashKey              string            `json:"hashKey,omitempty"`              // 哈希一致性参数
    Subset               string            `json:"subset,omitempty"`               // 新增：主服务版本subset名称
    Labels               map[string]string `json:"labels,omitempty"`               // 新增：主服务版本标签选择器
}

// 修改AIRouteRequest结构体，在第95行后添加
type AIRouteRequest struct {
    // ... 现有字段保持不变 ...
    RetryPolicy                 *RetryPolicy         `json:"retryPolicy,omitempty"`
    TrafficMirror               TrafficMirrorPolicy  `json:"trafficMirror,omitempty"`  // 新增字段
}

// 修改AIRouteResponse结构体，在第226行后添加
type AIRouteResponse struct {
    // ... 现有字段保持不变 ...
    RetryPolicy                 *RetryPolicy         `json:"retryPolicy,omitempty"`
    TrafficMirror               *TrafficMirrorPolicy `json:"trafficMirror,omitempty"` // 新增字段
}
```

### 5.2 cmd/csm/app/core/aigateway.go 修改

```go
// 在validateMultiServiceConfig函数后添加新的验证函数
func (core *APIServerCore) validateTrafficMirrorConfig(routeRequest *meta.AIRouteRequest) error {
    // 实现如前面伪代码所示的验证逻辑
}

// 在CreateRoute函数的验证部分添加镜像配置验证
func (core *APIServerCore) CreateRoute(ctx csmContext.CsmContext) error {
    // ... 现有验证逻辑 ...

    // 验证流量镜像配置
    if err := core.validateTrafficMirrorConfig(routeRequest); err != nil {
        ctx.CsmLogger().Errorf("流量镜像配置验证失败: %v", err)
        return err
    }

    // ... 其余逻辑保持不变 ...
}

// 修改createSingleServiceVirtualService函数，在第2128行重试策略配置后添加镜像配置
// 修改createRatioBasedVirtualService函数，在第2267行重试策略配置后添加镜像配置
// 修改createModelNameBasedVirtualService函数，添加镜像配置逻辑
```

### 5.3 VirtualService注解扩展

```go
// 在VirtualService的Annotations中添加镜像相关注解
Annotations: map[string]string{
    // ... 现有注解 ...
    "traffic-mirror-enabled": strconv.FormatBool(routeRequest.TrafficMirror.Enabled),
    "mirror-targets-count":   strconv.Itoa(len(routeRequest.TrafficMirror.Mirrors)),
}
```

## 6. 数据格式样例

### 6.1 单服务模式请求数据格式样例

#### 6.1.1 使用默认主服务版本
```json
{
  "routeName": "ai-model-route",
  "srcProduct": "AI-Gateway",
  "matchRules": {
    "pathRule": {
      "matchType": "prefix",
      "value": "/api/v1/chat",
      "caseSensitive": false
    },
    "methods": ["POST", "GET"]
  },
  "multiService": false,
  "targetService": {
    "serviceSource": "CCE",
    "serviceName": "ai-model-service",
    "namespace": "ai-prod",
    "servicePort": 8080,
    "loadBalanceAlgorithm": "round-robin"
  },
  "trafficMirror": {
    "enabled": true,
    "mirror": {
      "subset": "v2",
      "labels": {
        "version": "v2",
        "app": "ai-model-service"
      }
    },
    "percentage": 50.0
  },
  "authEnabled": true,
  "rewrite": {
    "enabled": false
  }
}
```

#### 6.1.2 使用自定义主服务版本
```json
{
  "routeName": "ai-model-route-custom",
  "srcProduct": "AI-Gateway",
  "matchRules": {
    "pathRule": {
      "matchType": "prefix",
      "value": "/api/v1/chat",
      "caseSensitive": false
    },
    "methods": ["POST", "GET"]
  },
  "multiService": false,
  "targetService": {
    "serviceSource": "CCE",
    "serviceName": "ai-model-service",
    "namespace": "ai-prod",
    "servicePort": 8080,
    "loadBalanceAlgorithm": "round-robin",
    "subset": "v1",
    "labels": {
      "version": "v1",
      "app": "ai-model-service",
      "env": "production"
    }
  },
  "trafficMirror": {
    "enabled": true,
    "mirror": {
      "subset": "v2",
      "labels": {
        "version": "v2",
        "app": "ai-model-service",
        "env": "staging"
      }
    },
    "percentage": 30.0
  },
  "authEnabled": true,
  "rewrite": {
    "enabled": false
  }
}
```

### 6.2 多服务模式请求样例

```json
{
  "routeName": "ai-multi-route",
  "multiService": true,
  "trafficDistributionStrategy": "ratio",
  "targetService": [
    {
      "serviceSource": "CCE",
      "serviceName": "ai-model-service",
      "namespace": "ai-prod",
      "servicePort": 8080,
      "requestRatio": 70
    },
    {
      "serviceSource": "CCE",
      "serviceName": "ai-model-service-v2",
      "namespace": "ai-prod",
      "servicePort": 8080,
      "requestRatio": 30
    }
  ],
  "trafficMirror": {
    "enabled": true,
    "mirror": {
      "subset": "canary",
      "labels": {
        "version": "canary",
        "app": "ai-model-service"
      }
    },
    "percentage": 100.0
  }
}
```

### 6.3 响应数据格式样例

```json
{
  "success": true,
  "status": 200,
  "result": {
    "routeName": "ai-model-route",
    "matchPath": "/api/v1/chat",
    "serviceName": "ai-model-service",
    "createTime": "2025-01-08 10:30:00",
    "multiService": false,
    "authEnabled": true,
    "trafficMirror": {
      "enabled": true,
      "mirror": {
        "subset": "v2",
        "labels": {
          "version": "v2",
          "app": "ai-model-service"
        }
      },
      "percentage": 50.0
    }
  }
}
```

### 6.4 生成的Istio资源样例

#### 6.4.1 使用默认主服务版本的DestinationRule
```yaml
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: ai-model-route-ai-model-service-dr
  namespace: istio-system
spec:
  host: ai-model-service.ai-prod.svc.cluster.local
  subsets:
  - name: ai-model-route-ai-model-service  # 默认主版本subset
    labels:
      app: ai-model-service
  - name: v2  # 镜像版本subset
    labels:
      version: v2
      app: ai-model-service
```

#### 6.4.2 使用自定义主服务版本的DestinationRule
```yaml
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: ai-model-route-custom-ai-model-service-dr
  namespace: istio-system
spec:
  host: ai-model-service.ai-prod.svc.cluster.local
  subsets:
  - name: v1  # 自定义主版本subset
    labels:
      version: v1
      app: ai-model-service
      env: production
  - name: v2  # 镜像版本subset
    labels:
      version: v2
      app: ai-model-service
      env: staging
```

#### 6.4.3 对应的VirtualService样例
```yaml
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: ai-model-route-custom
  namespace: istio-system
  annotations:
    traffic-mirror-enabled: "true"
    mirror-subset: "v2"
    main-subset: "v1"
spec:
  hosts:
  - "*"
  gateways:
  - istio-system/gateway-internal
  http:
  - name: ai-model-route-custom
    match:
    - uri:
        prefix: /api/v1/chat
      gateways:
      - istio-system/gateway-internal
    route:
    - destination:
        host: ai-model-service.ai-prod.svc.cluster.local
        port:
          number: 8080
        subset: v1  # 使用自定义主版本subset
      weight: 100
    mirror:
      host: ai-model-service.ai-prod.svc.cluster.local
      port:
        number: 8080
      subset: v2  # 镜像到v2版本
    mirrorPercentage:
      value: 30.0
```

**请确认以上数据结构格式是否正确，特别是：**
1. **TrafficMirrorPolicy结构**：包含enabled、mirror、percentage字段
2. **MirrorDestination结构**：包含subset和labels字段，用于定义同一服务的不同版本
3. **TargetService扩展**：新增subset和labels字段，支持主服务版本自定义
4. **DestinationRule和VirtualService的配置方式**是否符合您的预期

## 7. 测试要点 (TODO)

### 7.1 单元测试要点
- 流量镜像配置验证逻辑测试
  - 测试启用镜像但镜像列表为空的情况
  - 测试镜像目标字段缺失的情况
  - 测试镜像百分比超出范围的情况
- VirtualService生成逻辑测试
  - 测试单服务模式镜像配置生成
  - 测试多服务模式镜像配置生成
  - 测试未启用镜像时的行为
- 数据结构序列化/反序列化测试

### 7.2 集成测试要点
- 单服务模式流量镜像功能测试
  - 验证镜像流量正确发送到目标服务
  - 验证主流量不受镜像影响
- 多服务模式流量镜像功能测试
  - 验证比例分发 + 镜像的组合功能
  - 验证模型名分发 + 镜像的组合功能
- 与现有分发策略兼容性测试
  - 验证现有路由功能不受影响
  - 验证路由更新功能正常

### 7.3 性能测试要点
- 镜像流量对主流量的影响测试
- 大流量场景下的镜像性能测试
- 不同镜像百分比下的性能表现

## 8. 技术风险和注意事项

### 8.1 技术限制
- 当前Istio 1.13.2版本每个HTTP路由只支持一个镜像目标
- 镜像流量是"fire and forget"模式，不影响主请求响应
- 镜像请求的Host头会自动添加"-shadow"后缀

### 8.2 兼容性考虑
- 需要确保与现有多服务分发策略的兼容性
- 镜像配置不应影响现有路由的正常功能
- 向后兼容，未配置镜像的路由保持原有行为

### 8.3 运维考虑
- 需要监控镜像流量的资源消耗
- 建议在生产环境中谨慎使用高百分比的流量镜像
- 镜像目标服务需要有足够的处理能力

## 9. 实施计划

1. **阶段一**：数据结构扩展和验证逻辑实现
2. **阶段二**：单服务模式流量镜像功能实现
3. **阶段三**：多服务模式流量镜像功能实现
4. **阶段四**：测试和文档完善
5. **阶段五**：生产环境部署和监控

## 10. 依赖确认

**需要确认是否可以使用以下新依赖（如有）：**
- 当前方案基于现有的Istio依赖，无需引入新的外部依赖
- 使用现有的`istio.io/api/networking/v1alpha3`包中的HTTPMirrorPolicy结构

**技术方案选型确认：**
1. **是否同意采用基于同一服务不同subset的流量镜像方式？**
2. **是否同意在DestinationRule中创建主服务和镜像subset，在VirtualService中配置mirror字段？**
3. **是否同意主服务版本和镜像目标都通过subset和labels来定义？**
4. **是否同意支持用户自定义主服务版本的subset和labels？**
5. **对于多服务模式，是否同意镜像到第一个服务的指定版本？**
