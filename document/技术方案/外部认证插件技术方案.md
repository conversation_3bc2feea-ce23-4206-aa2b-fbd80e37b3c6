# 外部认证插件技术方案

## 1. 概述

为AI网关实现外部认证（ext-auth）插件的完整管理功能，支持网关维度和路由维度两种生效粒度，提供插件的安装、卸载、配置和启用/停用等功能。

## 2. 功能需求

### 2.1 核心功能
- **插件安装**：创建外部认证 WasmPlugin 资源
- **插件卸载**：删除对应的 WasmPlugin 资源  
- **插件配置**：支持动态更新插件配置参数
- **插件启用/停用**：通过 defaultConfigDisable 字段控制

### 2.2 生效粒度
- **网关维度**：不需要传入matchType和matchList，对整个网关生效
- **路由维度**：需要传入routeList，根据routeName查询VirtualService资源解析MatchList

## 3. 数据结构设计

### 3.1 请求数据结构

```go
// CreateExtAuthRequest 创建外部认证请求
type CreateExtAuthRequest struct {
    Enabled     bool                `json:"enabled"`                      // 是否开启
    Name        string              `json:"name" valid:"required"`        // 插件名称（用户自定义）
    Description string              `json:"description"`                  // 备注
    Scope       string              `json:"scope"`                        // 生效粒度：gateway(网关维度)、route(路由维度)
    MatchType   string              `json:"matchType"`                    // 匹配类型：blacklist、whitelist（仅路由维度）
    RouteList   []string            `json:"routeList"`                    // 路由名称列表（仅路由维度）
    HTTPService ExtAuthHTTPService  `json:"httpService" valid:"required"` // HTTP服务配置
    StatusOnError int               `json:"statusOnError"`                // 认证失败时的HTTP状态码，默认403
}

// ExtAuthHTTPService 外部认证HTTP服务配置
type ExtAuthHTTPService struct {
    EndpointMode string          `json:"endpointMode"` // 端点模式：envoy
    Endpoint     ExtAuthEndpoint `json:"endpoint"`     // 端点配置
    Timeout      int             `json:"timeout"`      // 超时时间，默认2000ms
}

// ExtAuthEndpoint 外部认证端点配置
type ExtAuthEndpoint struct {
    ServiceName string `json:"serviceName"` // 服务名称
    ServicePort int    `json:"servicePort"` // 服务端口
    PathPrefix  string `json:"pathPrefix"`  // 路径前缀
}

// ExtAuthMatchRule 外部认证匹配规则（从VirtualService解析）
type ExtAuthMatchRule struct {
    Path string `json:"path"` // 匹配路径
    Type string `json:"type"` // 匹配类型：prefix、exact
}
```

### 3.2 参数验证逻辑

```go
func (r *CreateExtAuthRequest) Validate() error {
    // 验证插件名称
    if r.Name == "" {
        return errors.New("name不能为空")
    }
    if len(r.Name) < 2 || len(r.Name) > 64 {
        return errors.New("name长度必须在2-64个字符之间")
    }
    
    // 验证生效粒度
    if r.Scope == "" {
        r.Scope = "gateway" // 默认为网关维度
    }
    if r.Scope != "gateway" && r.Scope != "route" {
        return errors.New("scope只能是gateway或route")
    }
    
    // 路由维度需要验证额外参数
    if r.Scope == "route" {
        if r.MatchType == "" {
            r.MatchType = "blacklist" // 默认黑名单
        }
        if r.MatchType != "blacklist" && r.MatchType != "whitelist" {
            return errors.New("matchType只能是blacklist或whitelist")
        }
        if len(r.RouteList) == 0 {
            return errors.New("路由维度时routeList不能为空")
        }
    }
    
    // 设置默认状态码
    if r.StatusOnError == 0 {
        r.StatusOnError = 403
    }
    
    // 验证HTTP服务配置
    return r.HTTPService.Validate()
}
```

## 4. VirtualService解析逻辑

### 4.1 路由匹配规则提取

```go
// extractMatchListFromRoutes 从路由列表中提取匹配规则
func (core *APIServerCore) extractMatchListFromRoutes(
    ctx csmContext.CsmContext, 
    routeList []string, 
    namespace string, 
    client kube.Client,
) ([]meta.ExtAuthMatchRule, error) {
    var matchList []meta.ExtAuthMatchRule
    
    istioClient := client.Istio()
    
    for _, routeName := range routeList {
        // 获取VirtualService资源
        vs, err := istioClient.NetworkingV1alpha3().VirtualServices(namespace).Get(
            context.TODO(), routeName, metav1.GetOptions{})
        if err != nil {
            if k8serrors.IsNotFound(err) {
                ctx.CsmLogger().Warnf("路由 %s 不存在，跳过", routeName)
                continue
            }
            return nil, errors.Wrapf(err, "获取路由 %s 失败", routeName)
        }
        
        // 解析HTTP路由规则
        rules := core.extractMatchRulesFromVirtualService(vs)
        matchList = append(matchList, rules...)
    }
    
    return matchList, nil
}

// extractMatchRulesFromVirtualService 从VirtualService中提取匹配规则
func (core *APIServerCore) extractMatchRulesFromVirtualService(vs *v1alpha3.VirtualService) []meta.ExtAuthMatchRule {
    var matchList []meta.ExtAuthMatchRule
    
    if vs.Spec.Http == nil {
        return matchList
    }
    
    for _, httpRoute := range vs.Spec.Http {
        if httpRoute.Match == nil {
            continue
        }
        
        for _, match := range httpRoute.Match {
            if match.Uri == nil {
                continue
            }
            
            var rule meta.ExtAuthMatchRule
            
            // 提取路径匹配规则
            if match.Uri.GetExact() != "" {
                rule.Path = match.Uri.GetExact()
                rule.Type = "exact"
            } else if match.Uri.GetPrefix() != "" {
                rule.Path = match.Uri.GetPrefix()
                rule.Type = "prefix"
            } else {
                continue // 跳过不支持的匹配类型
            }
            
            matchList = append(matchList, rule)
        }
    }
    
    return matchList
}
```

## 5. WasmPlugin模板设计

### 5.1 模板文件结构

**文件路径**: `templates/higress/wasm/ext-auth.tmpl`

```yaml
apiVersion: extensions.higress.io/v1alpha1
kind: WasmPlugin
metadata:
  name: {{ .PluginName }}
  namespace: {{ .Namespace }}
  labels:
    aigw.plugin.type: "ext-auth"
  annotations:
    aigw.rule.name: "{{ .RuleName }}"
    aigw.rule.description: "{{ .RuleDescription }}"
    aigw.rule.scope: "{{ .Scope }}"
    {{- if eq .Scope "route" }}
    aigw.rule.match.type: "{{ .MatchType }}"
    {{- end }}
spec:
  defaultConfig:
    {{- if eq .Scope "route" }}
    match_type: {{ .MatchType }}
    match_list:
    {{- range .MatchList }}
      - match_rule_path: {{ .Path }}
        match_rule_type: {{ .Type }}
    {{- end }}
    {{- end }}
{{ .HTTPService | indent 4 }}
    status_on_error: {{ .StatusOnError }}
  defaultConfigDisable: {{ if .Enabled }}false{{ else }}true{{ end }}
  url: {{ .PluginURL }}
```

### 5.2 模板数据结构

```go
// ExtAuthTemplateData 外部认证模板数据
type ExtAuthTemplateData struct {
    PluginName      string                `json:"pluginName"`      // 插件名称（用户自定义）
    Namespace       string                `json:"namespace"`       // 命名空间
    RuleName        string                `json:"ruleName"`        // 规则名称
    RuleDescription string                `json:"ruleDescription"` // 规则描述
    Scope           string                `json:"scope"`           // 生效粒度
    MatchType       string                `json:"matchType"`       // 匹配类型（仅路由维度）
    MatchList       []meta.ExtAuthMatchRule `json:"matchList"`     // 匹配规则列表（仅路由维度）
    HTTPService     string                `json:"httpService"`     // HTTP服务配置（YAML字符串）
    StatusOnError   int                   `json:"statusOnError"`   // 认证失败状态码
    PluginURL       string                `json:"pluginURL"`       // 插件镜像地址
    Enabled         bool                  `json:"enabled"`         // 是否启用
}
```

## 6. API接口设计

### 6.1 创建外部认证插件

```go
// CreateExtAuth 创建外部认证插件
func (core *APIServerCore) CreateExtAuth(ctx csmContext.CsmContext) error {
    region := ctx.Get(reg.ContextRegion).(string)
    instanceId := ctx.Param("instanceId")
    
    ctx.CsmLogger().Infof("开始创建外部认证插件，实例ID: %s", instanceId)
    
    // 参数绑定和验证
    createExtAuthReq := &meta.CreateExtAuthRequest{}
    if err := ctx.Bind(createExtAuthReq); err != nil {
        ctx.CsmLogger().Errorf("绑定请求参数失败: %v", err)
        return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
    }
    
    if err := createExtAuthReq.Validate(); err != nil {
        ctx.CsmLogger().Errorf("参数验证失败: %v", err)
        return csmErr.NewInvalidParameterValueException(err.Error())
    }
    
    // 创建k8s客户端
    hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
    if len(hostedClusterId) == 0 {
        return csmErr.NewInvalidParameterValueException("no hosting cluster found")
    }
    
    hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
    if err != nil {
        ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
        return err
    }
    
    namespace := fmt.Sprintf("istio-system-%s", instanceId)
    ctx.CsmLogger().Infof("在命名空间 %s 中创建外部认证插件", namespace)
    
    // 使用用户传入的名称作为插件名称
    pluginName := createExtAuthReq.Name
    
    // 解析匹配规则（仅路由维度需要）
    var matchList []meta.ExtAuthMatchRule
    if createExtAuthReq.Scope == "route" {
        matchList, err = core.extractMatchListFromRoutes(
            ctx, createExtAuthReq.RouteList, namespace, hostingClient)
        if err != nil {
            ctx.CsmLogger().Errorf("解析路由匹配规则失败: %v", err)
            return errors.Wrap(err, "解析路由匹配规则失败")
        }
        ctx.CsmLogger().Infof("解析到 %d 个匹配规则", len(matchList))
    }
    
    // 生成HTTP服务配置YAML
    httpServiceYAML := createExtAuthReq.HTTPService.GenerateHTTPServiceYAML()
    
    // 准备模板数据
    templateData := meta.ExtAuthTemplateData{
        PluginName:      pluginName,
        Namespace:       namespace,
        RuleName:        createExtAuthReq.Name,
        RuleDescription: createExtAuthReq.Description,
        Scope:           createExtAuthReq.Scope,
        MatchType:       createExtAuthReq.MatchType,
        MatchList:       matchList,
        HTTPService:     httpServiceYAML,
        StatusOnError:   createExtAuthReq.StatusOnError,
        PluginURL:       constants.GetExtAuthPluginURL(),
        Enabled:         createExtAuthReq.Enabled,
    }
    
    ctx.CsmLogger().Infof("模板数据准备完成，生效粒度: %s", templateData.Scope)
    
    // 模板渲染和资源创建
    return core.renderAndCreateExtAuthPlugin(ctx, templateData, hostingClient, createExtAuthReq)
}
```

### 6.2 其他CRUD操作伪代码

```go
// ListExtAuths 查看外部认证插件列表
func (core *APIServerCore) ListExtAuths(ctx csmContext.CsmContext) error {
    // 1. 获取实例ID和区域信息
    // 2. 创建k8s客户端
    // 3. 使用标签选择器查询WasmPlugin资源
    // 4. 解析配置信息并返回列表
}

// GetExtAuthDetail 查看外部认证插件详情  
func (core *APIServerCore) GetExtAuthDetail(ctx csmContext.CsmContext) error {
    // 1. 获取实例ID和插件ID
    // 2. 创建k8s客户端
    // 3. 根据ID查询具体的WasmPlugin资源
    // 4. 解析完整配置信息并返回
}

// UpdateExtAuth 编辑外部认证插件
func (core *APIServerCore) UpdateExtAuth(ctx csmContext.CsmContext) error {
    // 1. 获取实例ID和插件ID
    // 2. 参数验证和绑定
    // 3. 重新解析路由规则（如果是路由维度）
    // 4. 更新WasmPlugin资源
}

// DeleteExtAuth 删除外部认证插件
func (core *APIServerCore) DeleteExtAuth(ctx csmContext.CsmContext) error {
    // 1. 获取实例ID和插件ID
    // 2. 检查资源存在性和类型验证
    // 3. 执行删除操作
}
```

## 7. 配置管理

### 7.1 配置文件更新

**文件**: `conf/csm_local.yaml`

```yaml
aigw:
  plugins:
    ext_auth_plugin:
      name: "ext-auth"
      url: "oci://registry.baidubce.com/csm-offline/wasm-go/ext-auth:1.0.0"
      template_path: "templates/higress/wasm/ext-auth.tmpl"
```

### 7.2 常量定义

**文件**: `pkg/util/constants/constants.go`

```go
// 外部认证插件常量
extAuthPluginNameKey   = "aigw.plugins.ext_auth_plugin.name"
extAuthPluginURLKey    = "aigw.plugins.ext_auth_plugin.url"
extAuthTemplatePathKey = "aigw.plugins.ext_auth_plugin.template_path"

// WasmPlugin标签常量
WasmPluginTypeExtAuth = "ext-auth"

// Getter函数
func GetExtAuthPluginName() string {
    return viper.GetString(extAuthPluginNameKey)
}

func GetExtAuthPluginURL() string {
    return viper.GetString(extAuthPluginURLKey)
}

func GetExtAuthTemplatePath() string {
    return viper.GetString(extAuthTemplatePathKey)
}

func GetExtAuthPluginLabelSelector() string {
    return WasmPluginLabelPluginType + "=" + WasmPluginTypeExtAuth
}
```

## 8. API路由定义

**文件**: `cmd/csm/app/router/aigateway.go`

```go
// 外部认证插件接口
group.POST("/v1/aigateway/:instanceId/extAuth", server.CsmHandler(c.CreateExtAuth),
    middleware.CheckIAMSignature(),
    middleware.CheckAndSetRegion(),
    middleware.FilterEmptyQueryParam(),
)

group.GET("/v1/aigateway/:instanceId/extAuthList", server.CsmHandler(c.ListExtAuths),
    middleware.CheckIAMSignature(),
    middleware.CheckAndSetRegion(),
    middleware.FilterEmptyQueryParam(),
)

group.GET("/v1/aigateway/:instanceId/extAuth/:id", server.CsmHandler(c.GetExtAuthDetail),
    middleware.CheckIAMSignature(),
    middleware.CheckAndSetRegion(),
    middleware.FilterEmptyQueryParam(),
)

group.PUT("/v1/aigateway/:instanceId/extAuth/:id", server.CsmHandler(c.UpdateExtAuth),
    middleware.CheckIAMSignature(),
    middleware.CheckAndSetRegion(),
    middleware.FilterEmptyQueryParam(),
)

group.DELETE("/v1/aigateway/:instanceId/extAuth/:id", server.CsmHandler(c.DeleteExtAuth),
    middleware.CheckIAMSignature(),
    middleware.CheckAndSetRegion(),
    middleware.FilterEmptyQueryParam(),
)
```

## 9. 核心工作流程

### 9.1 网关维度插件创建流程

```mermaid
graph TD
    A[接收创建请求] --> B[参数验证]
    B --> C{scope=gateway?}
    C -->|是| D[使用用户传入的name作为插件名称]
    D --> E[准备模板数据<br/>不包含matchType和matchList]
    E --> F[渲染模板]
    F --> G[创建WasmPlugin资源]
    G --> H[返回响应]
```

### 9.2 路由维度插件创建流程

```mermaid
graph TD
    A[接收创建请求] --> B[参数验证]
    B --> C{scope=route?}
    C -->|是| D[验证routeList参数]
    D --> E[查询VirtualService资源]
    E --> F[解析匹配规则]
    F --> G[使用用户传入的name作为插件名称]
    G --> H[准备模板数据<br/>包含matchType和matchList]
    H --> I[渲染模板]
    I --> J[创建WasmPlugin资源]
    J --> K[返回响应]
```

## 10. 使用示例

### 10.1 网关维度外部认证

```json
{
  "enabled": true,
  "name": "my-gateway-auth",
  "description": "网关级别认证",
  "scope": "gateway",
  "httpService": {
    "endpointMode": "envoy",
    "endpoint": {
      "serviceName": "auth-service.default.svc.cluster.local",
      "servicePort": 8080,
      "pathPrefix": "/auth"
    },
    "timeout": 2000
  },
  "statusOnError": 403
}
```

### 10.2 路由维度外部认证

```json
{
  "enabled": true,
  "name": "my-route-auth",
  "description": "路由级别认证",
  "scope": "route",
  "matchType": "blacklist",
  "routeList": ["api-route-v1", "user-route"],
  "httpService": {
    "endpointMode": "envoy",
    "endpoint": {
      "serviceName": "auth-service.default.svc.cluster.local",
      "servicePort": 8080,
      "pathPrefix": "/validate"
    },
    "timeout": 2000
  },
  "statusOnError": 401
}
```

## 11. 生成的WasmPlugin示例

### 11.1 网关维度插件

```yaml
apiVersion: extensions.higress.io/v1alpha1
kind: WasmPlugin
metadata:
  name: my-gateway-auth
  namespace: istio-system-instance123
  labels:
    aigw.plugin.type: "ext-auth"
  annotations:
    aigw.rule.name: "my-gateway-auth"
    aigw.rule.description: "网关级别认证"
    aigw.rule.scope: "gateway"
spec:
  defaultConfig:
    http_service:
      endpoint_mode: envoy
      endpoint:
        service_name: auth-service.default.svc.cluster.local
        service_port: 8080
        path_prefix: /auth
      timeout: 2000
    status_on_error: 403
  defaultConfigDisable: false
  url: oci://registry.baidubce.com/csm-offline/wasm-go/ext-auth:1.0.0
```

### 11.2 路由维度插件

```yaml
apiVersion: extensions.higress.io/v1alpha1
kind: WasmPlugin
metadata:
  name: my-route-auth
  namespace: istio-system-instance123
  labels:
    aigw.plugin.type: "ext-auth"
  annotations:
    aigw.rule.name: "my-route-auth"
    aigw.rule.description: "路由级别认证"
    aigw.rule.scope: "route"
    aigw.rule.match.type: "blacklist"
spec:
  defaultConfig:
    match_type: blacklist
    match_list:
      - match_rule_path: /api/v1
        match_rule_type: prefix
      - match_rule_path: /auth/login
        match_rule_type: exact
    http_service:
      endpoint_mode: envoy
      endpoint:
        service_name: auth-service.default.svc.cluster.local
        service_port: 8080
        path_prefix: /validate
      timeout: 2000
    status_on_error: 401
  defaultConfigDisable: false
  url: oci://registry.baidubce.com/csm-offline/wasm-go/ext-auth:1.0.0
```

## 12. 数据结构确认

### 12.1 需要确认的数据结构格式

**ExtAuthHTTPService结构**：
```json
{
  "endpointMode": "envoy",
  "endpoint": {
    "serviceName": "service-name.namespace.svc.cluster.local",
    "servicePort": 8080,
    "pathPrefix": "/auth"
  },
  "timeout": 2000
}
```

**MatchList结构**（从VirtualService解析）：
```json
[
  {"path": "/api/v1", "type": "prefix"},
  {"path": "/auth/login", "type": "exact"}
]
```

### 12.2 技术实现要点

1. **插件命名**：使用用户传入的name参数作为WasmPlugin的metadata.name
2. **生效粒度**：通过scope字段区分gateway和route两种维度
3. **路由解析**：路由维度时根据routeList查询VirtualService并解析匹配规则
4. **配置管理**：插件URL完全配置化，避免硬编码

## 13. 测试策略

### 13.1 单元测试要点（TODO）

- 参数验证逻辑测试
- VirtualService解析逻辑测试
- 模板渲染功能测试
- HTTP服务配置生成测试

### 13.2 集成测试要点（TODO）

- 完整的CRUD操作测试
- 网关维度和路由维度功能测试
- 错误处理和边界条件测试

## 14. 实现计划

1. **第一阶段**：数据模型定义和参数验证
2. **第二阶段**：VirtualService解析逻辑实现
3. **第三阶段**：WasmPlugin模板和配置管理
4. **第四阶段**：API接口实现和路由注册
5. **第五阶段**：测试和文档完善
