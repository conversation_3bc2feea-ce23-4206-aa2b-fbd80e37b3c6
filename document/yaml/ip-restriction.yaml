apiVersion: extensions.higress.io/v1alpha1
kind: WasmPlugin
metadata:
  name: ip-restriction-blacklist
  namespace: istio-system-aigw-wdv5qgx7
spec:
  defaultConfig:
    ip_source_type: "header"
    ip_header_name: "x-forwarded-for"
    deny:
      - "*************"
      - "**********/12"
    status: 403
    message: "Your IP address is blocked"
  url: oci://registry.baidubce.com/csm-offline/wasm-go/ip-restriction:20250127
---
apiVersion: extensions.higress.io/v1alpha1
kind: WasmPlugin
metadata:
  name: ip-restriction-custom-header
  namespace: istio-system-aigw-wdv5qgx7
spec:
  defaultConfig:
    ip_source_type: "header"
    ip_header_name: "x-real-ip"
    deny:
      - "************"
    status: 451
    message: "Access denied by IP restriction"
  url: oci://registry.baidubce.com/csm-offline/wasm-go/ip-restriction:20250127
