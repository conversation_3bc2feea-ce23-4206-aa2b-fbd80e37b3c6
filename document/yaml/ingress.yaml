apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-example
  namespace: default
  annotations:
    ### 1.1 [done] add custom headers
    gateway.io/response-header-control-add: "X-Custom-Header Flask"

    ### 3.1 [done] rewrite target-普通匹配
    nginx.ingress.kubernetes.io/rewrite-target: /path/$2

spec:
  ingressClassName: istio-ingressclass
  rules:
    - host: httpbin.example.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: flask-service
                port:
                  number: 80
    - host: hello.example.com
      http:
        paths:
          - path: '/instance-id(/|$)(.*)'
            backend:
              service:
                name: flask-service
                port:
                  number: 80
            pathType: ImplementationSpecific