你是一个后端开发工程师 现在有一个新的需求 需要给消费者添加一个配额管理的功能 请你参考 接口文档/消费者管理 目录下的创建消费者、查询消费者列表、查询消费者详情、编辑消费者这四个接口文档 以上几个文档是更新过后的接口文档 主要是添加了配额管理相关的操作 请你根据更新后的接口文档修改项目中相应的接口代码

下面是具体各个函数的新增逻辑部分：
1 创建消费者
* 前端会传过来unlimitedQuota值 你需要将该值赋值给key-auth插件的unlimitedQuota字段
* 若unlimitedQuota值为true 则前端不会传quotaValue 则需要将redis中对应的key设置为math.MaxInt64 相当于无限配额
* 若unlimitedQuota值为false 则需要将redis中对应的key设置为前端传过来的quotaValue(可以为0 表示限制该消费者访问)

2 编辑消费者
* 前端会传过来unlimitedQuota值 你需要将该值赋值给key-auth插件的unlimitedQuota字段
* 若unlimitedQuota值为true 则前端不会传quotaValue 则需要将redis中对应的key设置为math.MaxInt64 相当于无限配额
* 若unlimitedQuota值为false 则需要将redis中对应的key设置为前端传过来的quotaValue

3 查询消费者/查询消费者列表
* 对于每一个消费者详情 现在需要向前端多返回两个字段 一个是unlimitedQuota 一个是quotaValue
    * 首先你需要从key-auth插件中获取该消费者的unlimitedQuota值 若不存在则设为false
    * 若unlimitedQuota值为true 则向前端返回的uotaValue值为-1
    * 若unlimitedQuota值为false 则向前端返回的uotaValue值为redis中对应的key的value

4 删除消费者
* 需要删除redis中对应的key

5 下发ai-quota插件
你还需要将配额插件ai-quota使用其他插件类似的方式配置到conf/csm_local.yaml中 并且实现下面功能：
* 网关创建的时候将该插件创建出来 和keyauth插件类似 当有操作使得ingress列表为空的时候使用添加一条虚拟的路由
* 在创建、修改路由的时候如果该路由开启了消费者认证则需要将其加入该插件的ingress列表中 若路由被删除或者关闭了消费者认证同样的需要从ingress列表中移除

遵守原则：尽量不要使用硬编码，循序后端开发规范
* 配额值的key请使用如下格式：chat_quota_<instanceID>:<consumerName>
* 配额管理中的unlimitedQuota字段只存储在keyauth插件中 因此你需要更新templates/higress/wasm/consumer-jwt-auth.tmpl文件 在消费者详情添加相应的字段
* redis只保存配额值 这个配额值会被其他的插件调用进而减少 因此若有必要会设置为math.MaxInt64 这样就相当于无限配额了
* redis使用配置文件中的开发环境 
* 尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉。关键功能点前后输出日志信息，方便调试
* 最后形成接口更新总结放入 接口实现总结/消费者管理目录下