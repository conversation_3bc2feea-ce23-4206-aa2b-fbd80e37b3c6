# 消费者配额功能totalQuota更新总结

## 更新概述

根据产品需求，对消费者配额管理功能进行了重要更新：

1. **新增原始配额值概念**：`totalQuota` - 用户设置的原始配额值，存储在keyAuth插件中
2. **重新定义剩余配额**：`quotaValue` - 表示剩余配额，存储在Redis中
3. **接口字段变更**：创建和编辑消费者接口的字段从 `quotaValue` 改为 `totalQuota`
4. **数据存储分离**：原始配额存储在插件中，剩余配额存储在Redis中

## 数据结构更新

### 1. 请求结构体更新

#### CreateConsumerRequest
```go
type CreateConsumerRequest struct {
    ConsumerName   string   `json:"consumerName"`
    Description    string   `json:"description"`
    AuthType       string   `json:"authType"`
    RouteNames     []string `json:"routeNames"`
    UnlimitedQuota bool     `json:"unlimitedQuota"`
    TotalQuota     *int     `json:"totalQuota"`  // 改为totalQuota
}
```

#### UpdateConsumerRequest
```go
type UpdateConsumerRequest struct {
    Description    string   `json:"description"`
    RouteNames     []string `json:"routeNames"`
    UnlimitedQuota *bool    `json:"unlimitedQuota"`
    TotalQuota     *int     `json:"totalQuota"`  // 改为totalQuota
}
```

### 2. 响应结构体更新

#### ConsumerDetailInfo
```go
type ConsumerDetailInfo struct {
    ConsumerID     string              `json:"consumerId"`
    ConsumerName   string              `json:"consumerName"`
    Description    string              `json:"description"`
    AuthType       string              `json:"authType"`
    AuthInfo       ConsumerAuthInfo    `json:"authInfo"`
    Routes         []ConsumerRouteInfo `json:"routes"`
    UnlimitedQuota bool                `json:"unlimitedQuota"`
    TotalQuota     int64               `json:"totalQuota"`  // 新增：原始配额值
    QuotaValue     int64               `json:"quotaValue"`  // 剩余配额
}
```

#### Consumer (列表响应)
```go
type Consumer struct {
    ConsumerId     string `json:"consumerId"`
    ConsumerName   string `json:"consumerName"`
    Description    string `json:"description"`
    CreateTime     string `json:"createTime"`
    UnlimitedQuota bool   `json:"unlimitedQuota"`
    TotalQuota     int64  `json:"totalQuota"`  // 新增：原始配额值
    QuotaValue     int64  `json:"quotaValue"`  // 剩余配额
}
```

### 3. 模板数据结构更新

#### ConsumerTemplateData
```go
type ConsumerTemplateData struct {
    Namespace      string
    ConsumerID     string
    ConsumerName   string
    Description    string
    Credential     string
    AuthType       string
    RouteNames     []string
    CreateTime     string
    UnlimitedQuota bool
    TotalQuota     *int  // 新增：原始配额值
    // 插件相关字段
    PluginName string
    PluginURL  string
}
```

## 模板文件更新

### keyAuth插件模板 (templates/higress/wasm/consumer-jwt-auth.tmpl)

```yaml
apiVersion: extensions.higress.io/v1alpha1
kind: WasmPlugin
metadata:
  name: {{ .PluginName }}
  namespace: {{ .Namespace }}
spec:
  defaultConfig:
    consumers:
    {{- if .ConsumerName }}
      - credential: {{ .Credential }}
        name: {{ .ConsumerName }}
        id: {{ .ConsumerID }}
        description: {{ .Description }}
        authType: {{ .AuthType }}
        createTime: {{ .CreateTime }}
        unlimitedQuota: {{ .UnlimitedQuota }}
        {{- if .TotalQuota }}
        totalQuota: {{ .TotalQuota }}  # 新增字段
        {{- end }}
    {{- else }}
      []
    {{- end }}
    # ... 其他配置
```

## 核心逻辑更新

### 1. 创建消费者逻辑

```go
// 添加新的consumer
newConsumer := map[string]interface{}{
    "credential":     credential,
    "name":           createConsumerReq.ConsumerName,
    "id":             consumerId,
    "description":    createConsumerReq.Description,
    "authType":       createConsumerReq.AuthType,
    "createTime":     createTime,
    "unlimitedQuota": createConsumerReq.UnlimitedQuota,
}

// 添加totalQuota字段到插件中
if !createConsumerReq.UnlimitedQuota && createConsumerReq.TotalQuota != nil {
    newConsumer["totalQuota"] = *createConsumerReq.TotalQuota
}

// 设置Redis中的配额值（剩余配额）
err = core.setConsumerQuota(ctx, instanceId, createConsumerReq.ConsumerName, 
    createConsumerReq.UnlimitedQuota, createConsumerReq.TotalQuota)
```

### 2. 编辑消费者逻辑

```go
// 更新配额字段
if updateReq.UnlimitedQuota != nil {
    consumer["unlimitedQuota"] = *updateReq.UnlimitedQuota
    
    // 如果设置为限制配额且提供了totalQuota，则更新totalQuota字段
    if !*updateReq.UnlimitedQuota && updateReq.TotalQuota != nil {
        consumer["totalQuota"] = *updateReq.TotalQuota
    } else if *updateReq.UnlimitedQuota {
        // 如果设置为无限配额，移除totalQuota字段
        delete(consumer, "totalQuota")
    }
}

// 更新Redis中的配额值
err = core.setConsumerQuota(ctx, instanceId, consumerName, 
    *updateReq.UnlimitedQuota, updateReq.TotalQuota)
```

### 3. 获取消费者信息逻辑

```go
// 获取配额信息
unlimitedQuota, _, _ := unstructured.NestedBool(consumer, "unlimitedQuota")
var totalQuota int64 = 0  // 原始配额值
var quotaValue int64 = -1 // 剩余配额值

if unlimitedQuota {
    totalQuota = -1  // 无限配额的原始配额为-1
    quotaValue = -1  // 无限配额的剩余配额为-1
} else {
    // 从插件获取原始配额值
    if totalQuotaFloat, exists, _ := unstructured.NestedFloat64(consumer, "totalQuota"); exists {
        totalQuota = int64(totalQuotaFloat)
    }
    
    // 从Redis获取当前剩余配额
    currentQuota, err := core.getConsumerQuota(ctx, instanceId, consumerName)
    if err != nil {
        ctx.CsmLogger().Warnf("获取消费者 %s 配额失败: %v", consumerName, err)
        quotaValue = 0 // 获取失败时返回0
    } else {
        quotaValue = currentQuota
    }
}
```

## 验证逻辑更新

### CreateConsumerRequest.Validate()
```go
// 验证配额相关参数
if !r.UnlimitedQuota {
    // 当不是无限配额时，totalQuota必须提供且大于等于0
    if r.TotalQuota == nil {
        return errors.New("当unlimitedQuota为false时，totalQuota不能为空")
    }
    if *r.TotalQuota < 0 {
        return errors.New("totalQuota必须大于等于0")
    }
}
```

### UpdateConsumerRequest.Validate()
```go
// 验证配额相关参数
if r.UnlimitedQuota != nil && !*r.UnlimitedQuota {
    // 当不是无限配额时，totalQuota必须提供且大于等于0
    if r.TotalQuota == nil {
        return errors.New("当unlimitedQuota为false时，totalQuota不能为空")
    }
    if *r.TotalQuota < 0 {
        return errors.New("totalQuota必须大于等于0")
    }
}
```

## 接口示例

### 1. 创建消费者请求示例

```json
{
    "consumerName": "test-consumer",
    "description": "测试消费者",
    "authType": "KeyAuth",
    "routeNames": ["test-route"],
    "unlimitedQuota": false,
    "totalQuota": 1000
}
```

### 2. 编辑消费者请求示例

```json
{
    "description": "更新后的描述",
    "routeNames": ["test-route", "new-route"],
    "unlimitedQuota": false,
    "totalQuota": 2000
}
```

### 3. 消费者详情响应示例

```json
{
    "consumerId": "consumer-1234567890-123456",
    "consumerName": "test-consumer",
    "description": "测试消费者",
    "authType": "KeyAuth",
    "authInfo": {
        "token": "Bearer f881a93b-678b4177979-a4f4b7270ada"
    },
    "routes": [
        {
            "routeName": "test-route",
            "createTime": "2024-01-15 10:30:00",
            "authEnabled": true
        }
    ],
    "unlimitedQuota": false,
    "totalQuota": 1000,
    "quotaValue": 850
}
```

### 4. 消费者列表响应示例

```json
{
    "orderBy": "createTime",
    "order": "desc",
    "pageNo": 1,
    "pageSize": 10,
    "totalCount": 2,
    "result": [
        {
            "consumerId": "consumer-1234567890-123456",
            "consumerName": "test-consumer",
            "description": "测试消费者",
            "createTime": "2024-01-15 10:30:00",
            "unlimitedQuota": false,
            "totalQuota": 1000,
            "quotaValue": 850
        },
        {
            "consumerId": "consumer-1234567890-123457",
            "consumerName": "unlimited-consumer",
            "description": "无限配额消费者",
            "createTime": "2024-01-15 09:30:00",
            "unlimitedQuota": true,
            "totalQuota": -1,
            "quotaValue": -1
        }
    ]
}
```

## 数据存储说明

### 1. keyAuth插件存储
- **位置**：Kubernetes WasmPlugin资源的 `spec.defaultConfig.consumers[].totalQuota` 字段
- **内容**：用户设置的原始配额值
- **特点**：持久化存储，不会因为配额消耗而变化

### 2. Redis存储
- **位置**：Redis键值对，键格式为 `chat_quota_<instanceID>:<consumerName>`
- **内容**：当前剩余配额值
- **特点**：动态变化，随着API调用消耗而减少

## 配额值说明

### 1. 无限配额模式 (unlimitedQuota: true)
- `totalQuota`: -1
- `quotaValue`: -1
- Redis中存储：`math.MaxInt64`

### 2. 限制配额模式 (unlimitedQuota: false)
- `totalQuota`: 用户设置的原始配额值（如1000）
- `quotaValue`: 当前剩余配额值（如850）
- Redis中存储：实际剩余配额数值

## 兼容性说明

1. **向后兼容**：现有的消费者数据会自动适配新的字段结构
2. **字段迁移**：原有的配额数据会保持在Redis中作为剩余配额
3. **默认值处理**：未设置totalQuota的消费者默认为0配额

## 注意事项

1. **数据一致性**：确保插件中的totalQuota与Redis中的初始值保持一致
2. **错误处理**：Redis连接失败时，配额相关操作不影响消费者的创建和编辑
3. **性能考虑**：获取消费者列表时会批量查询Redis，注意性能优化
4. **监控告警**：建议对配额相关的Redis操作添加监控和告警 