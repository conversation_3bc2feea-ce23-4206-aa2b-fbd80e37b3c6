# AI配额插件实现总结

## 概述
按照prompt文档要求，已完成ai-quota插件的下发和CRUD操作实现，包括网关创建时的插件初始化、路由CRUD操作中的插件管理等功能。

## 实现内容

### 1. 配置文件更新
**文件**: `conf/csm_local.yaml`
- 在`aigw.plugins`节点下添加了`ai_quota_plugin`配置
- 包含插件名称、URL和模板路径配置

```yaml
ai_quota_plugin:
  name: "ai-quota"
  url: "oci://registry.baidubce.com/csm-offline/wasm-go/ai-quota:1.0.7"
  template_path: "templates/higress/wasm/ai-quota.tmpl"
```

### 2. 常量定义
**文件**: `pkg/util/constants/constants.go`
- 添加了AI配额插件相关常量：
  - `aiQuotaPluginNameKey`、`aiQuotaPluginURLKey`、`aiQuotaTemplatePathKey` - 配置键名
  - `AIQuotaPluginPriority` - 插件优先级(750)
  - 相应的getter函数：`GetAIQuotaPluginName`、`GetAIQuotaPluginURL`、`GetAIQuotaPluginPriority`、`GetAIQuotaTemplatePath`

### 3. 数据模型定义
**文件**: `pkg/model/meta/airoute.go`
- 添加了`AIQuotaTemplateData`结构体，包含：
  - `Namespace` - 命名空间
  - `RouteNames` - 路由名称列表
  - `RedisKeyPrefix` - Redis键前缀
  - `PluginName`、`PluginURL` - 插件基本信息
  - `RedisServiceName`、`RedisServicePort` - Redis服务配置

**文件**: `pkg/model/meta/consumer.go`
- 更新了`ConsumerTemplateData`结构体，移除了`Priority`字段（使用固定优先级）

### 4. 模板文件更新
**文件**: `templates/higress/wasm/ai-quota.tmpl`
- 更新模板使用动态配置而非硬编码值
- 支持动态插件名称、URL
- 使用固定优先级750（硬编码）
- 支持动态Redis服务配置
- 支持动态路由列表和Redis键前缀

**文件**: `templates/higress/wasm/consumer-jwt-auth.tmpl`
- 使用固定优先级310（硬编码）

**文件**: `templates/higress/wasm/token-rate-limit.tmpl`
- 使用固定优先级600（硬编码）

### 5. 网关创建时的插件初始化
**文件**: `cmd/csm/app/core/aigateway.go`

#### 5.1 createBaseAIQuotaPlugin函数
- 在网关创建时自动创建基础AI配额插件
- 包含虚拟路由作为默认ingress，避免空列表
- 使用模板渲染生成插件配置
- 支持动态Redis配置

#### 5.2 网关创建流程集成
- 在`CreateAIGateway`函数中，在创建keyauth插件后添加AI配额插件创建
- 确保插件创建失败不影响网关创建主流程

### 6. 路由CRUD操作中的插件管理

#### 6.1 updateAIQuotaPluginIngress函数
- 支持"add"和"remove"操作来管理插件的ingress列表
- 自动创建插件（如果不存在）
- 智能处理空ingress列表（添加虚拟路由占位）
- 支持动态Redis配置

#### 6.2 CreateRoute集成
- 当路由开启认证时，自动将路由添加到AI配额插件的ingress列表

#### 6.3 UpdateRoute集成
- 根据认证开关状态决定添加或移除路由
- 开启认证时添加路由到ingress列表
- 关闭认证时从ingress列表移除路由

#### 6.4 DeleteRoute集成
- 路由删除时自动从AI配额插件的ingress列表中移除
- 确保插件状态与路由状态保持一致

### 7. 核心实现逻辑

#### 7.1 插件查找和创建
- 动态查找AI配额插件
- 如果不存在则自动创建基础插件
- 支持插件配置的动态更新

#### 7.2 ingress列表管理
- 获取现有matchRules并更新ingress列表
- **存储路由名称**：ingress列表中存储的是路由名称（routeName），而非路由ID
- 添加操作：将路由名称加入现有规则或创建新规则
- 移除操作：从所有规则中移除指定路由名称
- 空列表处理：添加虚拟路由作为占位符

#### 7.3 Redis配置管理
- 动态生成Redis键前缀：`chat_quota_{instanceId}:`
- 支持配置化的Redis服务名称和端口
- 集成现有的Redis服务配置

## 技术特点

### 1. 遵循现有模式
- 完全按照keyauth插件的实现模式
- 保持代码风格和架构的一致性
- 复用现有的常量管理和配置模式
- 使用固定的插件优先级，确保插件执行顺序的一致性

### 2. 错误处理
- 插件操作失败不影响主业务流程
- 详细的错误日志记录
- 优雅的错误恢复机制

### 3. 动态配置
- 支持配置文件驱动的插件参数
- 模板化的插件配置生成
- 环境相关的Redis配置

### 4. 状态一致性
- 路由状态与插件配置保持同步
- 自动处理插件不存在的情况
- 智能的空列表处理

## 验证结果
- 代码编译通过，无语法错误
- 遵循Go语言最佳实践
- 符合项目现有代码规范
- 完整实现了prompt文档的所有要求

## 使用流程
1. **网关创建**：自动创建AI配额插件，包含虚拟路由
2. **路由创建**：开启认证的路由自动加入插件ingress列表
3. **路由更新**：根据认证状态动态调整插件配置
4. **路由删除**：自动从插件ingress列表中移除
5. **插件管理**：支持动态创建和配置更新

整个实现确保了AI配额插件与路由生命周期的完全集成，实现了自动化的插件管理和配置同步。

## 插件优先级说明

为确保插件执行顺序的一致性，所有插件都使用固定的优先级：

- **key-auth插件**: 优先级 310 (认证阶段)
- **ai-token-limit插件**: 优先级 600 (限流阶段)  
- **ai-quota插件**: 优先级 750 (配额管理阶段)

优先级数值越小，执行顺序越靠前。这样确保了：
1. 先进行身份认证 (key-auth)
2. 再进行Token限流 (ai-token-limit)
3. 最后进行配额管理 (ai-quota)

这种设计保证了插件的逻辑执行顺序符合业务需求。

## 重要设计说明

### ingress列表存储机制
所有插件（key-auth、ai-token-limit、ai-quota）的ingress列表中存储的都是**路由名称（routeName）**，而不是路由ID。

**原因说明**：
1. **Kubernetes资源标识**：VirtualService资源的`metadata.name`就是路由名称
2. **插件匹配机制**：Higress插件通过ingress列表中的路由名称来识别和匹配需要应用插件的流量
3. **资源操作一致性**：所有Kubernetes资源操作（创建、更新、删除）都基于资源名称
4. **路由ID用途**：路由ID（如`rt-xxx`）仅用于API响应，为前端提供唯一标识符

**实现一致性**：
- CreateRoute：使用`routeRequest.RouteName`
- UpdateRoute：使用路径参数`routeName`  
- DeleteRoute：使用路径参数`routeName`
- 所有插件的ingress列表都遵循相同的命名规则 