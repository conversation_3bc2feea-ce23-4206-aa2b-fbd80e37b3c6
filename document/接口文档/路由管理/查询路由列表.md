# 查询路由列表接口

## 接口基本信息

- **接口说明**：查询指定实例下的路由列表
- **接口地址**：`/api/aigw/v1/aigateway/cluster/{instanceId}/route`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

### 查询参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| routeName | String | 否 | 路由名称（模糊搜索） | inference |
| pageNo | Integer | 否 | 页码，默认为1 | 1 |
| pageSize | Integer | 否 | 每页条数，默认为10 | 10 |
| orderBy | String | 否 | 排序字段，默认为createTime | createTime |
| order | String | 否 | 排序方式，默认为desc | desc |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "page": {
        "orderBy": "createTime",
        "order": "desc",
        "pageNo": 1,
        "pageSize": 10,
        "totalCount": 2,
        "result": [
            {
                "routeName": "inference-api",
                "routeStatus": "PUBLISHED",
                "matchPath": {
                    "matchType": "prefix",
                    "value": "/v1/models/"
                },
                "targetService": [
                    {
                        "serviceName": "mnist-inference",
                        "namespace": "default",
                        "servicePort": 8080,
                        "requestRatio": 100
                    }
                ],
                "createTime": "2025-04-18 15:20:35"
            },
            {
                "routeName": "inference-chat",
                "routeStatus": "PUBLISHED",
                "matchPath": {
                    "matchType": "exact",
                    "value": "/v1/chat/completions"
                },
                "targetService": [
                    {
                        "serviceName": "gpt-4-service",
                        "namespace": "default",
                        "servicePort": 9000,
                        "modelName": "gpt-4"
                    },
                    {
                        "serviceName": "gpt-3-service",
                        "namespace": "default",
                        "servicePort": 9000,
                        "modelName": "gpt-3.5-turbo"
                    }
                ],
                "createTime": "2025-04-17 10:15:20"
            }
        ]
    }
}
```

### 错误响应体结构

```json
{
    "success": false,
    "status": 404,
    "message": "实例不存在"
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| page | Object | 分页查询结果 |
| page.orderBy | String | 排序字段 |
| page.order | String | 排序方式，desc-降序，asc-升序 |
| page.pageNo | Integer | 当前页码 |
| page.pageSize | Integer | 每页条数 |
| page.totalCount | Integer | 总记录数 |
| page.result | Array[Object] | 路由列表 |
| page.result[].routeName | String | 路由名称 |
| page.result[].routeStatus | String | 路由状态，PUBLISHED-已发布 |
| page.result[].matchPath | Object | 路径匹配规则 |
| page.result[].matchPath.matchType | String | 匹配方式，prefix-前缀匹配，exact-精确匹配 |
| page.result[].matchPath.value | String | A路径匹配值 |
| page.result[].targetService | Array[Object] | 目标服务信息数组 |
| page.result[].targetService[].serviceName | String | 服务名称 |
| page.result[].targetService[].namespace | String | 服务所在命名空间 |
| page.result[].targetService[].servicePort | Integer | 服务端口 |
| page.result[].targetService[].requestRatio | Integer | 请求比例，按比例分发时返回 |
| page.result[].targetService[].modelName | String | 模型名称，按模型名称分发时返回 |
| page.result[].createTime | String | 创建时间，格式：YYYY-MM-DD HH:mm:ss |
| message | String | 错误信息，仅在失败时返回 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数格式是否正确 |
| 401 | 未授权 | 确认用户是否有查询路由列表的权限 |
| 404 | 实例不存在 | 确认实例ID是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/cluster/gw-ist9vvin/route?routeName=inference&pageNo=1&pageSize=10
X-Region: gz
```

## 响应示例

### 成功响应示例

```json
{
    "success": true,
    "status": 200,
    "page": {
        "orderBy": "createTime",
        "order": "desc",
        "pageNo": 1,
        "pageSize": 10,
        "totalCount": 3,
        "result": [
            {
                "routeName": "inference-api-single",
                "routeStatus": "PUBLISHED",
                "matchPath": {
                    "matchType": "prefix",
                    "value": "/v1/models/"
                },
                "targetService": [
                    {
                        "serviceName": "mnist-inference",
                        "namespace": "default",
                        "servicePort": 8080,
                        "requestRatio": 100
                    }
                ],
                "createTime": "2025-04-18 15:20:35"
            },
            {
                "routeName": "inference-api-ratio",
                "routeStatus": "PUBLISHED",
                "matchPath": {
                    "matchType": "prefix",
                    "value": "/v1/inference/"
                },
                "targetService": [
                    {
                        "serviceName": "mnist-inference-1",
                        "namespace": "default",
                        "servicePort": 8080,
                        "requestRatio": 70
                    },
                    {
                        "serviceName": "mnist-inference-2",
                        "namespace": "default",
                        "servicePort": 8080,
                        "requestRatio": 30
                    }
                ],
                "createTime": "2025-04-18 14:10:20"
            },
            {
                "routeName": "inference-chat",
                "routeStatus": "PUBLISHED",
                "matchPath": {
                    "matchType": "exact",
                    "value": "/v1/chat/completions"
                },
                "targetService": [
                    {
                        "serviceName": "gpt-4-service",
                        "namespace": "default",
                        "servicePort": 9000,
                        "modelName": "gpt-4"
                    },
                    {
                        "serviceName": "gpt-3-service",
                        "namespace": "default",
                        "servicePort": 9000,
                        "modelName": "gpt-3.5-turbo"
                    }
                ],
                "createTime": "2025-04-17 10:15:20"
            }
        ]
    }
}
```

### 错误响应示例

```json
{
    "success": false,
    "status": 404,
    "message": "实例不存在"
}
```

## 注意事项

1. 实例ID必须符合规范格式，通常以特定前缀（如`gw-`）开头
2. 路由名称查询支持模糊匹配，可以只输入路由名称的一部分进行搜索
3. 列表默认按创建时间倒序排列，显示最新创建的路由在前
4. 路由状态目前仅显示已发布(PUBLISHED)状态
5. 分页参数可以不传，系统会使用默认值（pageNo=1, pageSize=10）
6. targetService现在返回数组格式，支持单服务和多服务场景
7. requestRatio字段仅在按比例分发的路由中返回
8. modelName字段仅在按模型名称分发的路由中返回 