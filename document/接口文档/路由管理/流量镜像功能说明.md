# AI网关流量镜像功能说明

## 功能概述

流量镜像功能允许将生产环境的真实流量复制到测试环境或新版本服务，用于：
- 新版本服务的生产验证
- 性能测试和压力测试
- 问题排查和调试
- A/B测试和灰度发布

## 技术原理

流量镜像基于Istio的VirtualService和DestinationRule实现：
- **DestinationRule**：定义服务的不同版本（subset）
- **VirtualService**：配置主流量路由和镜像流量路由
- **镜像模式**：基于同一服务的不同版本进行镜像

## 配置说明

### 基本配置结构

```json
{
  "targetService": {
    "serviceName": "ai-model-service",
    "namespace": "ai-prod",
    "servicePort": 8080,
    "subset": "v1",
    "labels": {
      "version": "v1",
      "app": "ai-model-service"
    }
  },
  "trafficMirror": {
    "enabled": true,
    "mirror": {
      "subset": "v2",
      "labels": {
        "version": "v2",
        "app": "ai-model-service"
      }
    },
    "percentage": 50.0
  }
}
```

### 字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| targetService.subset | String | 否 | 主服务版本标识 |
| targetService.labels | Object | 否 | 主服务版本标签选择器 |
| trafficMirror.enabled | Boolean | 是 | 是否启用流量镜像 |
| trafficMirror.mirror.subset | String | 是 | 镜像目标版本标识 |
| trafficMirror.mirror.labels | Object | 是 | 镜像目标标签选择器 |
| trafficMirror.percentage | Float | 否 | 镜像流量百分比(0-100) |

## 使用场景

### 场景1：单服务流量镜像

```json
{
  "routeName": "ai-chat-route",
  "multiService": false,
  "targetService": {
    "serviceName": "ai-chat-service",
    "namespace": "production",
    "servicePort": 8080,
    "subset": "stable",
    "labels": {
      "version": "stable",
      "app": "ai-chat-service"
    }
  },
  "trafficMirror": {
    "enabled": true,
    "mirror": {
      "subset": "canary",
      "labels": {
        "version": "canary",
        "app": "ai-chat-service"
      }
    },
    "percentage": 30.0
  }
}
```

### 场景2：多服务模式流量镜像

```json
{
  "routeName": "ai-multi-route",
  "multiService": true,
  "trafficDistributionStrategy": "ratio",
  "targetService": [
    {
      "serviceName": "ai-service-v1",
      "namespace": "production",
      "servicePort": 8080,
      "requestRatio": 70,
      "subset": "stable",
      "labels": {
        "version": "stable",
        "app": "ai-service-v1"
      }
    },
    {
      "serviceName": "ai-service-v2",
      "namespace": "production",
      "servicePort": 8080,
      "requestRatio": 30
    }
  ],
  "trafficMirror": {
    "enabled": true,
    "mirror": {
      "subset": "test",
      "labels": {
        "version": "test",
        "app": "ai-service-v1"
      }
    },
    "percentage": 100.0
  }
}
```

## 生成的Istio资源

### DestinationRule示例

```yaml
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: ai-chat-route-ai-chat-service-dr
  namespace: istio-system
spec:
  host: ai-chat-service.production.svc.cluster.local
  subsets:
  - name: stable
    labels:
      version: stable
      app: ai-chat-service
  - name: canary
    labels:
      version: canary
      app: ai-chat-service
```

### VirtualService示例

```yaml
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: ai-chat-route
  namespace: istio-system
  annotations:
    traffic-mirror-enabled: "true"
    mirror-subset: "canary"
    mirror-percentage: "30.0"
spec:
  hosts:
  - "*"
  gateways:
  - istio-system/gateway-internal
  http:
  - name: ai-chat-route
    match:
    - uri:
        prefix: /api/v1/chat
    route:
    - destination:
        host: ai-chat-service.production.svc.cluster.local
        port:
          number: 8080
        subset: stable
      weight: 100
    mirror:
      host: ai-chat-service.production.svc.cluster.local
      port:
        number: 8080
      subset: canary
    mirrorPercentage:
      value: 30.0
```

## 注意事项

1. **版本管理**：确保目标服务的不同版本都已正确部署并打上相应标签
2. **资源消耗**：镜像流量会增加目标服务的负载，需要合理设置镜像百分比
3. **网络开销**：镜像流量会增加网络传输量，建议在同一集群内使用
4. **监控告警**：建议对镜像目标服务设置独立的监控和告警
5. **数据隔离**：镜像服务应使用独立的数据存储，避免影响生产数据
6. **安全考虑**：镜像流量包含真实用户数据，需要确保镜像环境的安全性

## 最佳实践

1. **渐进式镜像**：从小比例开始，逐步增加镜像流量百分比
2. **环境隔离**：镜像目标服务使用独立的命名空间和资源配置
3. **监控对比**：对比主服务和镜像服务的性能指标和错误率
4. **自动化测试**：结合自动化测试验证镜像服务的功能正确性
5. **回滚准备**：准备快速禁用镜像功能的应急方案

## 故障排查

### 常见问题

1. **镜像流量未生效**
   - 检查DestinationRule中的subset配置是否正确
   - 验证目标服务的标签是否与配置匹配
   - 确认VirtualService的mirror配置是否正确

2. **镜像服务无响应**
   - 检查镜像目标服务是否正常运行
   - 验证服务端口和网络连通性
   - 查看镜像服务的日志和监控指标

3. **主服务性能下降**
   - 检查镜像百分比设置是否过高
   - 监控网络和CPU资源使用情况
   - 考虑调整镜像策略或暂时禁用镜像功能
