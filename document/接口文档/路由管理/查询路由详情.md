# 查询路由详情接口

## 接口基本信息

- **接口说明**：根据实例ID和路由名称查询路由详情
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/{routeName}/route/detail`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |
| routeName | String | 是 | 路由名称 | inference-api |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 响应体结构

#### 单服务模式响应

```json
{
    "success": true,
    "status": 200,
    "result": {
        "routeName": "inference-api",
        "createTime": "2025-04-18 15:20:35",
        "updateTime": "2025-04-19 10:15:20",
        "multiService": false,
        "matchRules": {
            "pathRule": {
                "matchType": "prefix",
                "value": "/v1/models/",
                "caseSensitive": true
            },
            "methods": ["GET", "POST"],
            "headers": [
                {
                    "key": "Content-Type",
                    "matchType": "exact",
                    "value": "application/json"
                }
            ],
            "queryParams": [
                {
                    "key": "version",
                    "matchType": "exact",
                    "value": "v1"
                }
            ]
        },
        "targetService": {
            "serviceSource": "CCE",
            "serviceName": "mnist-inference",
            "namespace": "default",
            "servicePort": 8080,
            "loadBalanceAlgorithm": "consistent-hash",
            "hashType": "header",
            "hashKey": "X-Request-Id",
            "subset": "v1",
            "labels": {
                "version": "v1",
                "app": "mnist-inference"
            }
        },
        "timeoutPolicy": {
            "enabled": true,
            "timeout": 5
        },
        "retryPolicy": {
            "enabled": true,
            "retryConditions": "gateway-error,5xx",
            "numRetries": 2
        },
        "authEnabled": true,
        "allowedConsumers": ["consumer-1", "consumer-2"],
        "tokenRateLimit": {
            "rule_name": "rate-limit-a1b2c3",
            "enabled": true,
            "rule_items": [
                {
                    "match_condition": {
                        "type": "consumer",
                        "key": "",
                        "value": "consumer_name"
                    },
                    "limit_config": {
                        "time_unit": "minute",
                        "token_amount": 1000
                    }
                }
            ]
        },
        "trafficMirror": {
            "enabled": true,
            "mirror": {
                "subset": "v2",
                "labels": {
                    "version": "v2",
                    "app": "mnist-inference"
                }
            },
            "percentage": 50.0
        }
    }
}
```

#### 多服务模式响应（按比例分发）

```json
{
    "success": true,
    "status": 200,
    "result": {
        "routeName": "inference-api-multi",
        "createTime": "2025-04-18 15:20:35",
        "updateTime": "2025-04-19 10:15:20",
        "multiService": true,
        "trafficDistributionStrategy": "ratio",
        "matchRules": {
            "pathRule": {
                "matchType": "prefix",
                "value": "/v1/models/",
                "caseSensitive": true
            },
            "methods": ["GET", "POST"]
        },
            "rewrite": {
                "enabled": false
        },
        "targetService": [
            {
                "serviceSource": "CCE",
                "serviceName": "mnist-inference-1",
                "namespace": "default",
                "servicePort": 8080,
                "loadBalanceAlgorithm": "round-robin",
                "requestRatio": 70,
                "subset": "stable",
                "labels": {
                    "version": "stable",
                    "app": "mnist-inference-1"
                }
            },
            {
                "serviceSource": "CCE",
                "serviceName": "mnist-inference-2",
                "namespace": "default",
                "servicePort": 8080,
                "loadBalanceAlgorithm": "round-robin",
                "requestRatio": 30
            }
        ],
        "authEnabled": false,
        "tokenRateLimit": {
            "rule_name": "rate-limit-a1b2c3",
            "enabled": false
        },
        "trafficMirror": {
            "enabled": true,
            "mirror": {
                "subset": "canary",
                "labels": {
                    "version": "canary",
                    "app": "mnist-inference-1"
                }
            },
            "percentage": 30.0
        }
    }
}
```

#### 多服务模式响应（按模型名称分发）

```json
{
    "success": true,
    "status": 200,
    "result": {
        "routeName": "inference-api-model",
        "createTime": "2025-04-18 15:20:35",
        "updateTime": "2025-04-19 10:15:20",
        "multiService": true,
        "trafficDistributionStrategy": "model_name",
        "matchRules": {
            "pathRule": {
                "matchType": "prefix",
                "value": "/v1/models/",
                "caseSensitive": true
            },
            "methods": ["GET", "POST"]
        },
            "rewrite": {
                "enabled": false
        },
        "targetService": [
            {
                "serviceSource": "CCE",
                "serviceName": "gpt-4-service",
                "namespace": "default",
                "servicePort": 8080,
                "loadBalanceAlgorithm": "round-robin",
                "modelName": "gpt-4"
            },
            {
                "serviceSource": "CCE",
                "serviceName": "gpt-3-service",
                "namespace": "default",
                "servicePort": 8080,
                "loadBalanceAlgorithm": "round-robin",
                "modelName": "gpt-3.5-turbo"
            }
        ],
        "authEnabled": false,
        "tokenRateLimit": {
            "rule_name": "rate-limit-a1b2c3",
            "enabled": false
        }
    }
}
```

### 错误响应体结构

```json
{
    "success": false,
    "status": 404,
    "message": "路由不存在"
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Object | 路由详情信息 |
| result.routeName | String | 路由名称 |
| result.createTime | String | 创建时间，格式：YYYY-MM-DD HH:mm:ss |
| result.updateTime | String | 更新时间，格式：YYYY-MM-DD HH:mm:ss |
| result.multiService | Boolean | 是否开启多服务，true-开启，false-关闭 |
| result.trafficDistributionStrategy | String | 流量分发策略，仅当multiService=true时返回，ratio-按比例，model_name-按模型名称 |
| result.matchRules | Object | 匹配规则 |
| result.matchRules.pathRule | Object | 路径匹配规则 |
| result.matchRules.pathRule.matchType | String | 匹配方式，prefix-前缀匹配，exact-精确匹配，regex-正则匹配 |
| result.matchRules.pathRule.value | String | 路径匹配值 |
| result.matchRules.pathRule.caseSensitive | Boolean | 是否大小写敏感，true-区分大小写，false-不区分大小写 |
| result.matchRules.methods | Array[String] | 请求方法列表 |
| result.matchRules.headers | Array[Object] | 请求头匹配规则 |
| result.matchRules.headers[].key | String | 请求头名称 |
| result.matchRules.headers[].matchType | String | 匹配规则，prefix-前缀匹配，exact-精确匹配 |
| result.matchRules.headers[].value | String | 请求头值 |
| result.matchRules.queryParams | Array[Object] | 请求参数匹配规则 |
| result.matchRules.queryParams[].key | String | 参数名 |
| result.matchRules.queryParams[].matchType | String | 匹配规则，prefix-前缀匹配，exact-精确匹配 |
| result.matchRules.queryParams[].value | String | 参数值 |
| result.rewrite | Object | 路径重写配置 |
| result.rewrite.enabled | Boolean | 是否启用路径重写，true-启用路径重写，false-不重写路径 |
| result.rewrite.path | String | 重写后的路径，转发到目标服务的路径 |
| result.targetService | Object/Array | 目标服务信息，单服务时为Object，多服务时为Array |
| result.targetService.serviceSource | String | 服务来源，如CCE |
| result.targetService.serviceName | String | 服务名称 |
| result.targetService.namespace | String | 服务所在命名空间 |
| result.targetService.servicePort | Integer | 服务端口 |
| result.targetService.loadBalanceAlgorithm | String | 负载均衡算法 |
| result.targetService.hashType | String | 哈希一致性类型 |
| result.targetService.hashKey | String | 哈希一致性参数 |
| result.targetService.requestRatio | Integer | 请求比例，仅当trafficDistributionStrategy=ratio时返回 |
| result.targetService.modelName | String | 模型名称，仅当trafficDistributionStrategy=model_name时返回 |
| result.targetService.subset | String | 主服务版本subset名称 |
| result.targetService.labels | Object | 主服务版本标签选择器 |
| result.trafficMirror | Object | 流量镜像策略 |
| result.trafficMirror.enabled | Boolean | 是否启用流量镜像 |
| result.trafficMirror.mirror | Object | 镜像目标配置 |
| result.trafficMirror.mirror.subset | String | 镜像目标subset名称 |
| result.trafficMirror.mirror.labels | Object | 镜像目标标签选择器 |
| result.trafficMirror.percentage | Float | 镜像流量百分比 |
| result.authEnabled | Boolean | 是否开启消费者认证，true-开启，false-关闭 |
| result.allowedConsumers | Array[String] | 可访问的消费者列表 |
| result.tokenRateLimit | Object | Token限流配置 |
| result.tokenRateLimit.rule_name | String | 限流规则名称，由后端自动生成 |
| result.tokenRateLimit.enabled | Boolean | 是否开启Token限流，true-开启，false-关闭 |
| result.tokenRateLimit.rule_items | Array[Object] | 限流策略列表 |
| result.tokenRateLimit.rule_items[].match_condition | Object | 限流规则 |
| result.tokenRateLimit.rule_items[].match_condition.type | String | 限流类型：consumer(按消费者)、header(按请求头)、query_param(按请求参数) |
| result.tokenRateLimit.rule_items[].match_condition.key | String | 限流键名 |
| result.tokenRateLimit.rule_items[].match_condition.value | String | 限流键值 |
| result.tokenRateLimit.rule_items[].limit_config | Object | 限流配置 |
| result.tokenRateLimit.rule_items[].limit_config.time_unit | String | 时间单位：second(每秒)、minute(每分钟)、hour(每小时)、day(每天) |
| result.tokenRateLimit.rule_items[].limit_config.token_amount | Integer | Token总数 |
| result.timeoutPolicy | Object | 超时策略 |
| result.timeoutPolicy.enabled | Boolean | 是否开启超时策略 |
| result.timeoutPolicy.timeout | Integer | 超时时长（秒） |
| result.retryPolicy | Object | 重试策略 |
| result.retryPolicy.enabled | Boolean | 是否开启重试策略 |
| result.retryPolicy.retryConditions | String | 重试条件 |
| result.retryPolicy.numRetries | Integer | 重试次数 |
| message | String | 错误信息，仅在失败时返回 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数格式是否正确 |
| 401 | 未授权 | 确认用户是否有查询路由详情的权限 |
| 404 | 路由不存在 | 确认路由名称是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/gw-ist9vvin/inference-api/route/detail
X-Region: gz
```

## 响应示例

### 成功响应示例（单服务）

```json
{
    "success": true,
    "status": 200,
    "result": {
        "routeName": "inference-api",
        "createTime": "2025-04-18 15:20:35",
        "updateTime": "2025-04-19 10:15:20",
        "multiService": false,
        "matchRules": {
            "pathRule": {
                "matchType": "prefix",
                "value": "/v1/models/",
                "caseSensitive": true
            },
            "methods": ["GET", "POST"],
            "headers": [
                {
                    "key": "Content-Type",
                    "matchType": "exact",
                    "value": "application/json"
                }
            ],
            "queryParams": [
                {
                    "key": "version",
                    "matchType": "exact",
                    "value": "v1"
                }
            ]
        },
        "targetService": {
            "serviceSource": "CCE",
            "serviceName": "mnist-inference",
            "namespace": "default",
            "servicePort": 8080,
            "loadBalanceAlgorithm": "consistent-hash",
            "hashType": "header",
            "hashKey": "X-Request-Id",
            "subset": "v1",
            "labels": {
                "version": "v1",
                "app": "mnist-inference"
            }
        },
        "timeoutPolicy": {
            "enabled": true,
            "timeout": 5
        },
        "retryPolicy": {
            "enabled": true,
            "retryConditions": "gateway-error,5xx",
            "numRetries": 2
        },
        "authEnabled": true,
        "allowedConsumers": ["consumer-1", "consumer-2"],
        "tokenRateLimit": {
            "rule_name": "rate-limit-a1b2c3",
            "enabled": true,
            "rule_items": [
                {
                    "match_condition": {
                        "type": "consumer",
                        "key": "",
                        "value": "consumer_name"
                    },
                    "limit_config": {
                        "time_unit": "minute",
                        "token_amount": 1000
                    }
                }
            ]
        },
        "trafficMirror": {
            "enabled": true,
            "mirror": {
                "subset": "v2",
                "labels": {
                    "version": "v2",
                    "app": "mnist-inference"
                }
            },
            "percentage": 50.0
        }
    }
}
```

### 成功响应示例（多服务-按比例）

```json
{
    "success": true,
    "status": 200,
    "result": {
        "routeName": "inference-api-multi",
        "createTime": "2025-04-18 15:20:35",
        "updateTime": "2025-04-19 10:15:20",
        "multiService": true,
        "trafficDistributionStrategy": "ratio",
        "matchRules": {
            "pathRule": {
                "matchType": "prefix",
                "value": "/v1/models/",
                "caseSensitive": true
            },
            "methods": ["GET", "POST"]
        },
        "targetService": [
            {
                "serviceSource": "CCE",
                "serviceName": "mnist-inference-1",
                "namespace": "default",
                "servicePort": 8080,
                "loadBalanceAlgorithm": "round-robin",
                "requestRatio": 70
            },
            {
                "serviceSource": "CCE",
                "serviceName": "mnist-inference-2",
                "namespace": "default",
                "servicePort": 8080,
                "loadBalanceAlgorithm": "round-robin",
                "requestRatio": 30
            }
        ],
        "authEnabled": false,
        "tokenRateLimit": {
            "rule_name": "rate-limit-a1b2c3",
            "enabled": false
        },
        "trafficMirror": {
            "enabled": true,
            "mirror": {
                "subset": "canary",
                "labels": {
                    "version": "canary",
                    "app": "mnist-inference-1"
                }
            },
            "percentage": 30.0
        }
    }
}
```

### 错误响应示例

```json
{
    "success": false,
    "status": 404,
    "message": "路由不存在"
}
```

## 注意事项

1. 实例ID必须符合规范格式，通常以特定前缀（如`gw-`）开头
2. 路由名称必须是实例中已存在的路由
3. 返回信息包含路由的完整配置信息，包括匹配规则、目标服务和认证设置等
4. 如果开启了消费者认证（authEnabled=true），结果中会返回允许访问的消费者列表 
5. 如果开启了Token限流（tokenRateLimit.enabled=true），结果中会返回完整的限流配置
6. 单服务模式下，targetService为Object；多服务模式下，targetService为Array
7. 多服务模式时，会返回流量分发策略（trafficDistributionStrategy）
8. 按比例分发时，每个服务会返回requestRatio字段
9. 按模型名称分发时，每个服务会返回modelName字段
10. 响应体包含超时策略(timeoutPolicy)、重试策略(retryPolicy)和哈希一致性负载均衡相关字段，若开启则相关字段必有返回
11. 如果启用了流量镜像（trafficMirror.enabled=true），响应中会包含完整的镜像配置信息
12. 流量镜像基于同一服务的不同版本（subset）实现，通过labels标签选择器区分版本
13. 主服务版本信息通过targetService.subset和targetService.labels字段返回
14. 镜像目标版本信息通过trafficMirror.mirror.subset和trafficMirror.mirror.labels字段返回