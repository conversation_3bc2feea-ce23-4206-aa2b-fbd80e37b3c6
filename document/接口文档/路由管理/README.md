# 路由管理接口文档

本目录包含AI网关路由管理相关的接口文档。

## 接口列表

### 1. 创建路由
- **接口路径**: `POST /api/aigw/v1/aigateway/{instanceId}/route`
- **功能描述**: 创建新的路由配置
- **详细文档**: [创建路由.md](./创建路由.md)

### 2. 查询路由列表
- **接口路径**: `GET /api/aigw/v1/aigateway/{instanceId}/route`
- **功能描述**: 查询路由列表（基础版本）
- **详细文档**: [查询路由列表.md](./查询路由列表.md)

### 3. 查询路由列表（含外部认证状态）
- **接口路径**: `GET /api/aigw/v1/aigateway/{instanceId}/routes`
- **功能描述**: 查询路由列表，包含外部认证插件关联状态
- **详细文档**: [查询路由列表（含外部认证状态）.md](./查询路由列表（含外部认证状态）.md)

### 4. 查询路由详情
- **接口路径**: `GET /api/aigw/v1/aigateway/{instanceId}/route/{routeName}`
- **功能描述**: 查询指定路由的详细信息
- **详细文档**: [查询路由详情.md](./查询路由详情.md)

### 5. 更新路由
- **接口路径**: `PUT /api/aigw/v1/aigateway/{instanceId}/route/{routeName}`
- **功能描述**: 更新现有路由配置
- **详细文档**: [更新路由.md](./更新路由.md)

### 6. 删除路由
- **接口路径**: `DELETE /api/aigw/v1/aigateway/{instanceId}/route/{routeName}`
- **功能描述**: 删除指定路由
- **详细文档**: [删除路由.md](./删除路由.md)

## 接口特性

### 路由类型支持
- **单服务路由**: 流量转发到单个后端服务
- **多服务路由**: 支持按比例分配和按模型名称分配两种策略
- **重写功能**: 支持动态路径重写配置

### 性能优化
- **批量查询**: 使用Kubernetes标签选择器进行高效查询
- **缓存友好**: 查询结果结构化，适合缓存
- **并发处理**: 支持并发操作，提升处理效率

### 安全特性
- **IAM认证**: 所有接口都需要通过IAM签名验证
- **参数验证**: 严格的参数格式和范围验证
- **错误处理**: 统一的错误码和错误信息格式

## 数据模型

### 路由配置
- **基础信息**: 路由名称、描述、创建时间等
- **匹配规则**: URI匹配、Header匹配等
- **目标服务**: 后端服务配置和负载均衡策略
- **重写配置**: 路径重写规则

### 外部认证关联
- **关联状态**: 路由是否配置了外部认证插件
- **插件信息**: 关联的外部认证插件详情
- **启用状态**: 插件是否处于启用状态

## 使用建议

### 创建路由
1. 先确认后端服务的可用性
2. 合理配置匹配规则，避免冲突
3. 根据业务需求选择合适的负载均衡策略

### 查询路由
1. 使用带外部认证状态的查询接口获取完整信息
2. 合理使用分页参数，避免一次查询过多数据
3. 利用排序功能提升用户体验

### 更新路由
1. 更新前先查询当前配置，避免覆盖重要设置
2. 分步更新复杂配置，降低出错风险
3. 更新后验证路由是否正常工作

### 删除路由
1. 删除前确认路由不再被使用
2. 考虑先禁用路由，观察一段时间后再删除
3. 删除操作不可逆，请谨慎操作

## 错误处理

### 常见错误码
- `MissingParametersException`: 缺少必要参数
- `InvalidParameterValueException`: 参数值无效
- `ResourceNotFoundException`: 资源不存在
- `DBOperationException`: 数据库操作失败

### 错误排查
1. 检查参数格式和必填项
2. 确认实例ID和路由名称的正确性
3. 验证IAM签名和权限设置
4. 查看详细错误信息进行定位

## 相关文档
- [外部认证插件文档](../外部认证插件/README.md)
- [插件市场文档](../插件市场/README.md)
