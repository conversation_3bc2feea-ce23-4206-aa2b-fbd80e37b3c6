# 外部认证插件管理接口

## 概述
外部认证插件提供了完整的CRUD管理功能，支持网关维度和路由维度两种生效粒度。

## 接口列表

### 1. 创建外部认证插件
- **接口路径**: `POST /api/aigw/v1/aigateway/{instanceId}/extAuth`
- **功能描述**: 创建外部认证插件，支持网关维度和路由维度
- **详细文档**: [创建外部认证插件.md](./创建外部认证插件.md)

### 2. 查询外部认证插件列表
- **接口路径**: `GET /api/aigw/v1/aigateway/{instanceId}/extAuthList`
- **功能描述**: 查询指定AI网关实例下的外部认证插件列表，支持分页、过滤和维度筛选
- **详细文档**: [查询外部认证插件列表.md](./查询外部认证插件列表.md)

### 3. 查询外部认证插件详情
- **接口路径**: `GET /api/aigw/v1/aigateway/{instanceId}/extAuth/{id}`
- **功能描述**: 查询指定外部认证插件的详细信息
- **详细文档**: [查询外部认证插件详情.md](./查询外部认证插件详情.md)

### 4. 更新外部认证插件
- **接口路径**: `PUT /api/aigw/v1/aigateway/{instanceId}/extAuth/{id}`
- **功能描述**: 更新指定外部认证插件的配置信息
- **详细文档**: [更新外部认证插件.md](./更新外部认证插件.md)

### 5. 删除外部认证插件
- **接口路径**: `DELETE /api/aigw/v1/aigateway/{instanceId}/extAuth/{id}`
- **功能描述**: 删除指定的外部认证插件
- **详细文档**: [删除外部认证插件.md](./删除外部认证插件.md)

### 6. 查询插件市场已安装插件
- **接口路径**: `GET /api/aigw/v1/aigateway/{instanceId}/pluginMarket`
- **功能描述**: 查询插件市场已安装的插件列表，包含规则数量统计
- **详细文档**: [查询插件市场已安装插件.md](./查询插件市场已安装插件.md)

### 7. 卸载插件
- **接口路径**: `DELETE /api/aigw/v1/aigateway/{instanceId}/plugin/{pluginName}`
- **功能描述**: 根据插件名称卸载插件，全量删除所有相关规则
- **详细文档**: [卸载插件.md](./卸载插件.md)

## 核心特性

### 生效粒度
- **网关维度** (`scope: "gateway"`): 对整个网关实例生效，不需要指定路由信息
- **路由维度** (`scope: "route"`): 对指定路由生效，需要提供路由列表并自动解析匹配规则

### 匹配类型（仅路由维度）
- **blacklist**: 黑名单模式，匹配的请求进行认证
- **whitelist**: 白名单模式，不匹配的请求进行认证

### HTTP服务配置
- **端点模式**: 目前仅支持 `envoy`
- **服务发现**: 支持Kubernetes服务名格式
- **超时配置**: 可配置认证服务的超时时间
- **路径前缀**: 支持自定义认证服务的路径前缀

## 使用示例

### 网关维度认证
```json
{
  "enabled": true,
  "name": "gateway-auth",
  "description": "网关级别认证",
  "scope": "gateway",
  "httpService": "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service.default.svc.cluster.local\n  service_port: 8080\n  path_prefix: /auth\ntimeout: 2000",
  "statusOnError": 403
}
```

### 路由维度认证
```json
{
  "enabled": true,
  "name": "route-auth",
  "description": "路由级别认证",
  "scope": "route",
  "matchType": "blacklist",
  "routeList": ["api-route-v1", "user-route"],
  "httpService": "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service.default.svc.cluster.local\n  service_port: 8080\n  path_prefix: /validate\ntimeout: 2000",
  "statusOnError": 401
}
```

## 错误处理

### 通用错误码
- `MissingParametersException`: 缺少必要参数
- `InvalidParameterValueException`: 参数值无效
- `ResourceNotFoundException`: 资源不存在
- `ResourceConflictException`: 资源已存在
- `InternalServerError`: 服务器内部错误

### 错误响应格式
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "InvalidParameterValueException",
  "message": {
    "global": "One of the parameters in the request is invalid"
  }
}
```

## 技术实现

### 底层资源
- 基于Higress WasmPlugin资源实现
- 使用标签 `aigw.plugin.type: "ext-auth"` 进行资源标识
- 通过annotations存储插件元数据信息

### 路由解析
- 自动查询VirtualService资源
- 提取路径匹配规则（prefix/exact）
- 支持多路由的规则合并

### 配置管理
- 插件镜像地址配置化管理
- 模板文件路径可配置
- 支持动态配置更新

## 注意事项

1. **插件命名**: 使用用户传入的name参数作为插件标识
2. **路由依赖**: 路由维度插件依赖VirtualService资源的存在
3. **认证服务**: 需要确保认证服务的可用性和正确配置
4. **权限控制**: 所有接口都集成了IAM签名验证
5. **资源清理**: 删除插件会立即停止相关的认证功能
