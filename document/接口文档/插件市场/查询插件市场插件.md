# 查询插件市场已安装插件

## 接口描述
查询指定AI网关实例下插件市场已安装的插件列表，目前支持外部认证插件。

## 请求信息

### 请求路径
```
GET /api/aigw/v1/aigateway/{instanceId}/pluginMarket
```

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | string | 是 | AI网关实例ID |

#### 查询参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| pageNo | integer | 否 | 页码，默认1 |
| pageSize | integer | 否 | 每页数量，默认10，最大100 |
| orderBy | string | 否 | 排序字段，默认createTime |
| order | string | 否 | 排序方式，desc/asc，默认desc |

## 响应信息

### 响应参数
| 参数名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| status | integer | HTTP状态码 |
| page | object | 分页信息 |
| page.orderBy | string | 排序字段 |
| page.order | string | 排序方式 |
| page.pageNo | integer | 页码 |
| page.pageSize | integer | 每页数量 |
| page.totalCount | integer | 总数 |
| page.result | array | 插件列表 |
| page.result[].pluginName | string | 插件名称（固定：ext-auth） |
| page.result[].pluginType | string | 插件类型（auth） |
| page.result[].installStatus | string | 安装状态：installed/uninstalled |
| page.result[].routeEnabledCount | integer | 路由维度启用规则数量 |
| page.result[].gatewayEnabledCount | integer | 网关维度启用规则数量 |
| page.result[].ruleCount | object | 规则数量统计 |
| page.result[].ruleCount.total | integer | 规则总数 |
| page.result[].ruleCount.routeRuleCount | integer | 路由维度规则数量 |
| page.result[].ruleCount.gatewayRuleCount | integer | 网关维度规则数量 |

### 响应示例

#### 成功响应
```json
{
  "success": true,
  "status": 200,
  "page": {
    "orderBy": "createTime",
    "order": "desc",
    "pageNo": 1,
    "pageSize": 10,
    "totalCount": 1,
    "result": [
      {
        "pluginName": "ext-auth",
        "pluginType": "auth",
        "installStatus": "installed",
        "routeEnabledCount": 1,
        "gatewayEnabledCount": 1,
        "ruleCount": {
          "total": 3,
          "routeRuleCount": 2,
          "gatewayRuleCount": 1
        }
      }
    ]
  }
}
```

#### 无插件时响应
```json
{
  "success": true,
  "status": 200,
  "page": {
    "orderBy": "createTime",
    "order": "desc",
    "pageNo": 1,
    "pageSize": 10,
    "totalCount": 0,
    "result": []
  }
}
```

## 错误码

### 通用错误码
- `MissingParametersException`: 缺少必要参数
- `InvalidParameterValueException`: 参数值无效

### 错误响应示例
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "MissingParametersException",
  "message": {
    "global": "instanceId is required"
  }
}
```

## 使用说明

1. **插件识别**: 通过标签 `aigw.plugin.tag=plugin-market` 识别插件市场插件
2. **规则统计**: 统计外部认证插件的规则数量，包括总数、路由维度和网关维度
3. **分页支持**: 支持标准的分页查询
4. **插件类型**: 目前仅支持外部认证插件（ext-auth）

## 注意事项

1. 该接口仅返回已安装的插件市场插件
2. 规则数量统计基于实际配置的插件实例
3. 插件名称固定为 "ext-auth"，插件类型固定为 "auth"
4. 如果没有安装任何插件市场插件，返回空列表
