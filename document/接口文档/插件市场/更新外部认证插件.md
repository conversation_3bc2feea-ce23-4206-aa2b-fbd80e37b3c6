# 更新外部认证插件

## 接口描述
更新指定外部认证插件的配置信息。

## 请求信息

### 请求路径
```
PUT /api/aigw/v1/aigateway/{instanceId}/extAuth/{ruleName}
```

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述         |
|--------|------|------|------------|
| instanceId | string | 是 | AI网关实例ID   |
| ruleName | string | 是 | 外部认证插件规则名称 |

#### 请求体参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| enabled | boolean | 是 | 是否启用插件 |
| description | string | 否 | 插件描述，最大200个字符 |
| scope | string | 否 | 生效粒度，可选值：gateway、route，默认gateway |
| matchType | string | 否 | 匹配类型，可选值：blacklist、whitelist，仅路由维度时必填 |
| routeList | array[string] | 否 | 路由名称列表，仅路由维度时必填 |
| httpService | string | 是 | HTTP服务配置（YAML格式字符串） |
| statusOnError | integer | 否 | 认证失败时的HTTP状态码，默认403，范围400-599 |

### 请求示例

#### 更新网关维度插件
```json
{
  "enabled": false,
  "description": "更新后的网关级别认证",
  "scope": "gateway",
  "httpService": "endpoint_mode: envoy\nendpoint:\n  service_name: new-auth-service.default.svc.cluster.local\n  service_port: 9090\n  path_prefix: /new-auth\ntimeout: 3000",
  "statusOnError": 401
}
```

#### 更新路由维度插件
```json
{
  "enabled": true,
  "description": "更新后的路由级别认证",
  "scope": "route",
  "matchType": "blacklist",
  "routeList": ["new-api-route", "admin-route"],
  "httpService": "endpoint_mode: envoy\nendpoint:\n  service_name: new-auth-service.default.svc.cluster.local\n  service_port: 9090\n  path_prefix: /validate-new\ntimeout: 3000",
  "statusOnError": 401
}
```

## 响应信息

### 响应参数
| 参数名 | 类型 | 描述 |
|--------|------|------|
| id | string | 插件ID |
| enabled | boolean | 是否启用 |
| description | string | 插件描述 |
| scope | string | 生效粒度 |
| matchType | string | 匹配类型（仅路由维度） |
| routeList | array[string] | 路由名称列表（仅路由维度） |
| httpService | string | HTTP服务配置（YAML格式字符串） |
| statusOnError | integer | 认证失败状态码 |
| updateTime | string | 更新时间 |

### 响应示例

#### 成功响应
```json
{
  "success": true,
  "status": 200,
  "result": {
    "id": "my-gateway-auth",
    "enabled": false,
    "name": "updated-gateway-auth",
    "description": "更新后的网关级别认证",
    "scope": "gateway",
    "httpService": "endpoint_mode: envoy\nendpoint:\n  service_name: new-auth-service.default.svc.cluster.local\n  service_port: 9090\n  path_prefix: /new-auth\ntimeout: 3000",
    "statusOnError": 401,
    "updateTime": "2024-01-15 14:20:00"
  }
}
```

#### 失败响应
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "ResourceNotFoundException",
  "message": {
    "global": "The specified resource does not exist"
  }
}
```

## 错误码
| 错误码 | 描述 |
|--------|------|
| MissingParametersException | 缺少必要参数 |
| InvalidParameterValueException | 参数值无效 |
| ResourceNotFoundException | 资源不存在 |
| InternalServerError | 服务器内部错误 |
