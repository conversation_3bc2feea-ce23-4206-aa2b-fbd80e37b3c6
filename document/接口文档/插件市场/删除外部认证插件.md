# 删除外部认证插件

## 接口描述
删除指定的外部认证插件。

## 请求信息

### 请求路径
```
DELETE /api/aigw/v1/aigateway/{instanceId}/extAuth/{ruleName}
```

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述         |
|--------|------|------|------------|
| instanceId | string | 是 | AI网关实例ID   |
| ruleName | string | 是 | 外部认证插件规则名称 |

### 请求示例
```
DELETE /api/aigw/v1/aigateway/instance123/extAuth/my-gateway-auth
```

## 响应信息

### 响应参数
| 参数名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| status | integer | HTTP状态码 |
| message | string | 删除结果消息 |

### 响应示例

#### 成功响应
```json
{
  "success": true,
  "status": 200,
  "result": {
    "success": true,
    "status": 200,
    "message": "外部认证插件删除成功"
  }
}
```

#### 失败响应
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "ResourceNotFoundException",
  "message": {
    "global": "The specified resource does not exist"
  }
}
```

## 错误码
| 错误码 | 描述 |
|--------|------|
| MissingParametersException | 缺少必要参数 |
| ResourceNotFoundException | 资源不存在 |
| InternalServerError | 服务器内部错误 |

## 注意事项
1. 删除操作不可逆，请谨慎操作
2. 删除插件后，相关的认证规则将立即失效
3. 建议在删除前先停用插件，观察业务影响
