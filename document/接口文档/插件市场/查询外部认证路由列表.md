# 查询路由列表（含外部认证状态）

## 接口描述
查询AI网关实例下的路由列表，包含每个路由是否关联外部认证插件的状态信息。

## 请求信息

### 请求路径
```
GET /api/aigw/v1/aigateway/{instanceId}/routes
```

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | string | 是 | AI网关实例ID |


### 请求示例
```
GET /api/aigw/v1/aigateway/instance123/routes
```

## 响应信息

### 响应参数
| 参数名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| status | integer | HTTP状态码 |
| result | array | 路由列表 |
| result[].routeName | string | 路由名称 |
| result[].hasExtAuth | boolean | 是否关联外部认证插件 |

### 响应示例

#### 成功响应
```json
{
  "success": true,
  "status": 200,
  "result": [
    {
      "routeName": "api-route-1",
      "hasExtAuth": true
    },
    {
      "routeName": "api-route-2",
      "hasExtAuth": false
    },
    {
      "routeName": "web-route-1",
      "hasExtAuth": true
    },
    {
      "routeName": "web-route-2",
      "hasExtAuth": false
    },
    {
      "routeName": "admin-route",
      "hasExtAuth": true
    }
  ]
}
```

#### 无路由时响应
```json
{
  "success": true,
  "status": 200,
  "result": []
}
```

## 错误码

### 通用错误码
- `MissingParametersException`: 缺少必要参数
- `InvalidParameterValueException`: 参数值无效

### 错误响应示例
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "MissingParametersException",
  "message": {
    "global": "instanceId is required"
  }
}
```

## 使用说明

1. **路由来源**: 查询所有标记为 `aigw.route.tag=route-management` 的VirtualService资源
2. **外部认证关联**: 通过检查路由维度的外部认证插件配置来确定关联状态
3. **性能优化**: 使用批量查询和内存映射，避免多次API调用
4. **排序支持**: 支持按路由名称和外部认证状态排序
5. **分页支持**: 支持标准分页参数

## 技术实现

### 性能优化特性
1. **批量查询**: 一次性查询所有VirtualService和WasmPlugin资源
2. **内存映射**: 构建路由与外部认证插件的关联映射表
3. **标签过滤**: 使用Kubernetes标签选择器精确过滤资源
4. **缓存友好**: 查询结果适合缓存，减少重复查询

### 查询流程
1. 验证实例ID和分页参数
2. 批量查询所有路由（VirtualService）
3. 批量查询所有外部认证插件（WasmPlugin）
4. 构建路由与插件的关联映射
5. 组装路由列表并应用排序和分页
6. 返回格式化的响应结果

## 注意事项

1. **权限要求**: 需要通过IAM签名验证
2. **实例存在性**: 不验证实例是否存在，直接查询对应命名空间
3. **关联逻辑**: 只检查路由维度的外部认证插件关联
4. **排序字段**: 仅支持 `routeName` 和 `hasExtAuth` 两个排序字段
5. **分页限制**: 最大每页100条记录
