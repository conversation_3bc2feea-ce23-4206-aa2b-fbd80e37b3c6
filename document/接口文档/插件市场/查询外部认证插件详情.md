# 查询外部认证插件详情

## 接口描述
查询指定外部认证插件的详细信息。

## 请求信息

### 请求路径
```
GET /api/aigw/v1/aigateway/{instanceId}/extAuth/{ruleName}
```

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | string | 是 | AI网关实例ID |
| ruleName string | 是 | 外部认证插件ID |

### 请求示例
```
GET /api/aigw/v1/aigateway/instance123/extAuth/my-gateway-auth
```

## 响应信息

### 响应参数
| 参数名 | 类型 | 描述 |
|--------|------|------|
| enabled | boolean | 是否启用 |
| name | string | 插件名称 |
| description | string | 插件描述 |
| scope | string | 生效粒度 |
| matchType | string | 匹配类型（仅路由维度） |
| routeList | array[string] | 路由名称列表（仅路由维度） |
| matchList | array[object] | 匹配规则列表（仅路由维度） |
| matchList[].path | string | 匹配路径 |
| matchList[].type | string | 匹配类型：prefix、exact |
| httpService | string | HTTP服务配置（YAML格式字符串） |
| statusOnError | integer | 认证失败状态码 |
| createTime | string | 创建时间 |
| updateTime | string | 更新时间 |

### 响应示例

#### 网关维度插件详情
```json
{
  "success": true,
  "status": 200,
  "result": {
    "enabled": true,
    "name": "my-gateway-auth",
    "description": "网关级别认证",
    "scope": "gateway",
    "httpService": "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service.default.svc.cluster.local\n  service_port: 8080\n  path_prefix: /auth\ntimeout: 2000",
    "statusOnError": 403,
    "createTime": "2024-01-15 10:30:00",
    "updateTime": "2024-01-15 10:30:00"
  }
}
```

#### 路由维度插件详情
```json
{
  "success": true,
  "status": 200,
  "result": {
    "enabled": true,
    "name": "my-route-auth",
    "description": "路由级别认证",
    "scope": "route",
    "matchType": "blacklist",
    "routeList": ["api-route-v1", "user-route"],
    "matchList": [
      {
        "path": "/api/v1",
        "type": "prefix"
      },
      {
        "path": "/auth/login",
        "type": "exact"
      }
    ],
    "httpService": "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service.default.svc.cluster.local\n  service_port: 8080\n  path_prefix: /validate\ntimeout: 2000",
    "statusOnError": 401,
    "createTime": "2024-01-15 09:15:00",
    "updateTime": "2024-01-15 11:20:00"
  }
}
```

#### 失败响应
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "ResourceNotFoundException",
  "message": {
    "global": "The specified resource does not exist"
  }
}
```

## 错误码
| 错误码 | 描述 |
|--------|------|
| MissingParametersException | 缺少必要参数 |
| ResourceNotFoundException | 资源不存在 |
| InternalServerError | 服务器内部错误 |
