# 查询网关扩缩容状态

## 接口描述
查询指定AI网关实例的Higress Gateway Deployment的扩缩容状态信息，用于监控网关实例的健康状况和部署进度。

## 请求信息

### 请求路径
```
GET /api/aigw/v1/aigateway/{instanceId}/gateway/status
```

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | string | 是 | AI网关实例ID |

### 请求示例
```
GET /api/aigw/v1/aigateway/instance123/gateway/status
```

## 响应信息

### 响应参数
| 参数名 | 类型 | 描述 |
|--------|------|------|
| ready | integer | 当前就绪的副本数（对应Kubernetes Deployment的READY状态） |
| upToDate | integer | 已更新到最新版本的副本数（对应UP-TO-DATE状态） |
| available | integer | 当前可用的副本数（对应AVAILABLE状态） |
| replicas | integer | 期望的副本数（总副本数） |

### 状态字段说明
- **ready**: 当前处于就绪状态的Pod副本数，表示已经通过健康检查并可以接收流量的副本
- **upToDate**: 已经更新到最新版本的Pod副本数，在滚动更新过程中此值会逐步增加
- **available**: 当前可用的Pod副本数，表示可以正常提供服务的副本
- **replicas**: 期望的总副本数，由Deployment配置中的replicas字段决定

### 响应示例

#### 正常运行状态
```json
{
  "ready": 3,
  "upToDate": 3,
  "available": 3,
  "replicas": 3
}
```

#### 扩容进行中
```json
{
  "ready": 3,
  "upToDate": 3,
  "available": 3,
  "replicas": 5
}
```

#### 滚动更新进行中
```json
{
  "ready": 2,
  "upToDate": 1,
  "available": 2,
  "replicas": 3
}
```

#### 部分副本异常
```json
{
  "ready": 2,
  "upToDate": 3,
  "available": 2,
  "replicas": 3
}
```

## 错误码

### 通用错误码
- `MissingParametersException`: 缺少必要参数
- `ResourceNotFoundException`: 网关Deployment不存在
- `DBOperationException`: Kubernetes API操作失败

### 错误响应示例

#### 实例ID缺失
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "MissingParametersException",
  "message": {
    "global": "instanceId is required"
  }
}
```

#### 网关Deployment不存在
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "ResourceNotFoundException",
  "message": {
    "global": "higress-gateway deployment not found"
  }
}
```

## 使用说明

### 状态判断逻辑
1. **健康状态**: `ready == replicas && available == replicas`
2. **扩缩容中**: `ready != replicas` 或 `available != replicas`
3. **更新中**: `upToDate != replicas`
4. **异常状态**: `ready < available` 或长时间状态不一致

### 监控建议
1. **定期轮询**: 建议每30秒查询一次状态
2. **状态变化**: 关注状态字段的变化趋势
3. **异常告警**: 当状态长时间不一致时触发告警
4. **扩缩容监控**: 在执行扩缩容操作后密切监控状态变化

### 典型使用场景
1. **健康检查**: 定期检查网关实例是否正常运行
2. **扩缩容监控**: 监控扩缩容操作的进度和结果
3. **滚动更新**: 监控版本更新的进度
4. **故障诊断**: 分析网关实例的异常状态

## 技术实现

### 查询目标
- **资源类型**: Kubernetes Deployment
- **资源名称**: `higress-gateway`
- **命名空间**: `istio-system-{instanceId}`

### 状态字段映射
```
ready ← deployment.status.readyReplicas
upToDate ← deployment.status.updatedReplicas
available ← deployment.status.availableReplicas
replicas ← deployment.spec.replicas
```

### 性能特性
1. **直接查询**: 直接查询Kubernetes API，获取实时状态
2. **轻量级**: 只查询单个Deployment资源，响应快速
3. **无缓存**: 每次查询都获取最新状态，确保数据准确性

## 注意事项

1. **权限要求**: 需要通过IAM签名验证
2. **实例存在性**: 不验证实例是否存在，直接查询对应命名空间的Deployment
3. **资源命名**: 固定查询名为`higress-gateway`的Deployment
4. **状态延迟**: Kubernetes状态更新可能有几秒延迟
5. **网络依赖**: 依赖与Kubernetes集群的网络连接

## 相关文档
- [路由管理文档](../路由管理/README.md)
- [插件市场文档](../插件市场/README.md)
