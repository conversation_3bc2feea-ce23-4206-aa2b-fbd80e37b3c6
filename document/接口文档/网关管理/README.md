# 网关管理接口文档

本目录包含AI网关管理相关的接口文档，主要用于监控和管理网关实例的运行状态。

## 接口列表

### 1. 查询网关扩缩容状态
- **接口路径**: `GET /api/aigw/v1/aigateway/{instanceId}/gateway/status`
- **功能描述**: 查询指定AI网关实例的Higress Gateway Deployment扩缩容状态
- **详细文档**: [查询网关扩缩容状态.md](./查询网关扩缩容状态.md)

## 功能特性

### 实时监控
- **状态查询**: 实时获取网关Deployment的运行状态
- **健康检查**: 监控网关实例的健康状况
- **扩缩容监控**: 跟踪扩缩容操作的进度

### 状态信息
- **就绪副本数**: 当前可以接收流量的Pod数量
- **更新副本数**: 已更新到最新版本的Pod数量
- **可用副本数**: 当前可以提供服务的Pod数量
- **期望副本数**: 配置的目标Pod数量

### 使用场景
1. **运维监控**: 实时监控网关实例的运行状态
2. **扩缩容管理**: 监控扩缩容操作的执行进度
3. **故障诊断**: 分析网关实例的异常状态
4. **容量规划**: 基于状态信息进行容量规划

## 技术架构

### 查询机制
- **直接查询**: 直接查询Kubernetes API获取实时状态
- **轻量级**: 单次查询单个Deployment资源
- **高性能**: 响应时间通常在100ms以内

### 状态映射
```
Kubernetes Deployment Status → API Response
├── spec.replicas → replicas (期望副本数)
├── status.readyReplicas → ready (就绪副本数)
├── status.updatedReplicas → upToDate (更新副本数)
└── status.availableReplicas → available (可用副本数)
```

### 安全特性
- **IAM认证**: 所有接口都需要通过IAM签名验证
- **权限控制**: 基于用户权限控制访问范围
- **参数验证**: 严格验证输入参数的格式和有效性

## 状态解读指南

### 正常状态
```json
{
  "ready": 3,
  "upToDate": 3,
  "available": 3,
  "replicas": 3
}
```
- 所有字段值相等，表示网关运行正常

### 扩容状态
```json
{
  "ready": 3,
  "upToDate": 3,
  "available": 3,
  "replicas": 5
}
```
- `replicas > ready`，表示正在扩容

### 缩容状态
```json
{
  "ready": 2,
  "upToDate": 2,
  "available": 2,
  "replicas": 2
}
```
- 副本数减少，表示已完成缩容

### 更新状态
```json
{
  "ready": 2,
  "upToDate": 1,
  "available": 2,
  "replicas": 3
}
```
- `upToDate < replicas`，表示正在进行滚动更新

### 异常状态
```json
{
  "ready": 1,
  "upToDate": 3,
  "available": 1,
  "replicas": 3
}
```
- `ready < replicas` 且状态持续，可能存在Pod启动问题

## 监控最佳实践

### 轮询策略
1. **正常状态**: 每60秒查询一次
2. **操作期间**: 每10秒查询一次
3. **异常状态**: 每5秒查询一次

### 告警规则
1. **副本不足**: `ready < replicas * 0.8` 持续5分钟
2. **更新超时**: `upToDate != replicas` 持续30分钟
3. **完全不可用**: `available == 0` 立即告警

### 性能优化
1. **批量查询**: 如需查询多个实例，考虑并发查询
2. **缓存策略**: 对于展示用途，可以缓存30秒
3. **错误处理**: 实现重试机制和降级策略

## 集成示例

### 前端监控页面
```javascript
// 查询网关状态
async function getGatewayStatus(instanceId) {
  const response = await fetch(`/api/aigw/v1/aigateway/${instanceId}/gateway/status`);
  const status = await response.json();
  
  // 判断状态
  if (status.ready === status.replicas && status.available === status.replicas) {
    return 'healthy';
  } else if (status.ready < status.replicas) {
    return 'scaling';
  } else if (status.upToDate < status.replicas) {
    return 'updating';
  } else {
    return 'abnormal';
  }
}
```

### 运维脚本
```bash
#!/bin/bash
# 检查网关状态
INSTANCE_ID="your-instance-id"
STATUS=$(curl -s "/api/aigw/v1/aigateway/${INSTANCE_ID}/gateway/status")

READY=$(echo $STATUS | jq '.ready')
REPLICAS=$(echo $STATUS | jq '.replicas')

if [ "$READY" -eq "$REPLICAS" ]; then
  echo "Gateway is healthy"
else
  echo "Gateway is not ready: $READY/$REPLICAS"
fi
```

## 错误处理

### 常见错误
1. **实例不存在**: 检查instanceId是否正确
2. **权限不足**: 验证IAM签名和权限配置
3. **网络超时**: 检查与Kubernetes集群的连接
4. **资源不存在**: 确认网关Deployment是否已部署

### 故障排查
1. 检查网关实例是否正确部署
2. 验证Kubernetes集群连接状态
3. 确认IAM权限配置正确
4. 查看详细错误日志

## 相关文档
- [路由管理文档](../路由管理/README.md)
- [插件市场文档](../插件市场/README.md)
- [外部认证插件文档](../外部认证插件/README.md)
