# 删除消费者接口

## 接口基本信息

- **接口说明**：删除指定的消费者
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/consumer/{consumerId}`
- **请求方式**：DELETE
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
| instanceId | String | 是 | 实例ID |
| consumerId | String | 是 | 消费者ID |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "result": null,
    "status": 200
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Null | 删除操作无返回数据 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 404 | 实例或消费者不存在 | 确认实例ID和消费者ID是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
DELETE /api/aigw/v1/aigateway/i-a1b2c3d4/consumer/consumer-001
```

## 响应示例

```json
{
    "success": true,
    "result": null,
    "status": 200
}
``` 