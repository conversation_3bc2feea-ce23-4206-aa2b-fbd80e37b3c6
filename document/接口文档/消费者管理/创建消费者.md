# 创建消费者接口

## 接口基本信息

- **接口说明**：为实例创建消费者
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/consumer` 
- **请求方式**：POST
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
| instanceId | String | 是 | 实例ID |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

### 请求体参数

| 参数名 | 类型 | 是否必填 | 描述                                            | 示例值 |
| --- | --- | --- |-----------------------------------------------| --- |
| consumerName | String | 是 | 消费者名称，长度为2-64个字符，支持英文、数字、中划线、下划线，同一实例下不可重名    | "test-consumer" |
| description | String | 否 | 消费者的描述信息，最多255个字符                             | "测试用消费者" |
| srcProduct | String | 否 | 来源产品标识，可选值：aibox（开发机）、pom（推理机） |
| authType | String | 是 | 认证方式，目前仅支持KeyAuth认证，固定值为"KeyAuth"             | "KeyAuth" |
| routeNames | Array | 否 | 授权路由范围，可选择允许该消费者访问的路由，支持多条路由                  | ["route-001", "route-002"] |
| unlimitedQuota | Boolean | 是 | 是否开启不限制配额，true表示不限额                           | true |
| totalQuota | Integer | 否 | 总配额值，若未开启不限额则该值为必填，表示分配给该消费者的总token额度，默认为3000 | 3000 |

## 请求体示例

```json
{
    "consumerName": "test-consumer",
    "description": "测试用消费者",
    "authType": "KeyAuth",
  	"srcProduct": "pom",
    "routeNames": ["route-001", "route-002"],
    "unlimitedQuota": false,
    "totalQuota": 5000
}
```

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "result": {
        "consumerId": "consumer-1703123456-123456",
        "credential": "Bearer f881a93b-678b4177979-a4f4b7270ada",
      	"success": true,
      	"status": 200
    },
    "status": 200
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Object | 创建结果信息 |
| result.consumerId | String | 创建的消费者ID |
| result.credential | String | 消费者认证凭证 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 404 | 实例不存在 | 确认实例ID是否正确 |
| 409 | 消费者名称已存在 | 更换消费者名称 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
限制配额 总配额值设置为5000
POST /api/aigw/v1/aigateway/i-a1b2c3d4/consumer

{
    "consumerName": "test-consumer",
    "description": "测试用消费者",
    "authType": "KeyAuth",
    "srcProduct": "pom",
    "routeNames": ["route-001", "route-002"],
    "unlimitedQuota": true
}
```

```
不限额
POST /api/aigw/v1/aigateway/i-a1b2c3d4/consumer

{
    "consumerName": "test-consumer",
    "description": "测试用消费者",
    "authType": "KeyAuth",
    "routeNames": ["route-001", "route-002"],
    "unlimitedQuota": true
}
```

## 响应示例

```json
{
    "success": true,
    "result": {
        "consumerId": "consumer-1703123456-123456",
        "credential": "Bearer f881a93b-678b4177979-a4f4b7270ada",
      	"success": true,
      	"status": 200
    },
    "status": 200
}
```