---
apiVersion: v1
kind: ConfigMap
metadata:
  name: provider-dubbo-resolve-config
  namespace: dubbo-demo
data:
  dubbo-resolve.properties: org.apache.dubbo.springboot.demo.ServerService=dubbo://dubbo-springboot-demo-server:20880
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: provider-demo-deployment-v1
  namespace: dubbo-demo
  labels:
    app: provider-demo
    name: provider-demo
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: provider-demo
      version: v1
  template:
    metadata:
      annotations:
        sidecar.istio.io/bootstrapOverride: aeraki-bootstrap-config
      labels:
        app: provider-demo
        version: v1
    spec:
      restartPolicy: Always
      containers:
        - name: provider-demo
          image: registry.baidubce.com/bms/dubbo-spring-boot-demo-provider:dev
          imagePullPolicy: Always
          ports:
            - containerPort: 20880
          resources:
            limits:
              cpu: 260m
              memory: 600Mi
          volumeMounts:
            - mountPath: /home/<USER>/dubbo-resolve.properties
              name: provider-dubbo-resolve-config
              readOnly: true
              subPath: dubbo-resolve.properties
      volumes:
        - name: provider-dubbo-resolve-config
          configMap:
            name: provider-dubbo-resolve-config
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: provider-demo-deployment-v2
  namespace: dubbo-demo
  labels:
    app: provider-demo
    name: provider-demo
    version: v2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: provider-demo
      version: v2
  template:
    metadata:
      annotations:
        sidecar.istio.io/bootstrapOverride: aeraki-bootstrap-config
      labels:
        app: provider-demo
        version: v2
    spec:
      restartPolicy: Always
      containers:
        - name: provider-demo
          image: registry.baidubce.com/bms/dubbo-spring-boot-demo-provider:dev
          imagePullPolicy: Always
          ports:
            - containerPort: 20880
          resources:
            limits:
              cpu: 260m
              memory: 600Mi
          volumeMounts:
            - mountPath: /home/<USER>/dubbo-resolve.properties
              name: provider-dubbo-resolve-config
              readOnly: true
              subPath: dubbo-resolve.properties
      volumes:
        - name: provider-dubbo-resolve-config
          configMap:
            name: provider-dubbo-resolve-config
---
apiVersion: v1
kind: Service
metadata:
  name: dubbo-springboot-demo-provider
  namespace: dubbo-demo
spec:
  selector:
    app: provider-demo
  ports:
    - name: tcp-metaprotocol-dubbo
      protocol: TCP
      port: 20880
      targetPort: 20880
---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: dubbo-springboot-demo-provider
  namespace: dubbo-demo
spec:
  host: dubbo-springboot-demo-provider.dubbo-demo.svc.cluster.local
  subsets:
    - labels:
        version: v1
      name: v1
    - labels:
        version: v2
      name: v2