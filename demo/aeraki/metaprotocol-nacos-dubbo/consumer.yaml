---
apiVersion: v1
kind: ConfigMap
metadata:
  name: consumer-dubbo-resolve-config
  namespace: dubbo-demo
data:
  dubbo-resolve.properties: org.apache.dubbo.springboot.demo.DemoService=dubbo://dubbo-springboot-demo-provider:20880
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: consumer-demo-deployment
  namespace: dubbo-demo
  labels:
    app: consumer-demo
    name: consumer-demo
spec:
  replicas: 1
  selector:
    matchLabels:
      app: consumer-demo
  template:
    metadata:
      annotations:
        sidecar.istio.io/bootstrapOverride: aeraki-bootstrap-config
      labels:
        app: consumer-demo
    spec:
      restartPolicy: Always
      containers:
        - name: consumer-demo
          image: registry.baidubce.com/bms/dubbo-spring-boot-demo-consumer:dev
          imagePullPolicy: Always
          ports:
            - containerPort: 20880
          resources:
            limits:
              cpu: 260m
              memory: 600Mi
          volumeMounts:
            - mountPath: /home/<USER>/dubbo-resolve.properties
              name: consumer-dubbo-resolve-config
              readOnly: true
              subPath: dubbo-resolve.properties
      volumes:
        - name: consumer-dubbo-resolve-config
          configMap:
            name: consumer-dubbo-resolve-config
---
apiVersion: v1
kind: Service
metadata:
  name: dubbo-springboot-demo-consumer
  namespace: dubbo-demo
spec:
  selector:
    app: consumer-demo
  ports:
    - name: tcp-metaprotocol-dubbo
      protocol: TCP
      port: 20880
      targetPort: 20880
    - name: http
      port: 9999
      targetPort: 9999
