apiVersion: metaprotocol.aeraki.io/v1alpha1
kind: MetaRouter
metadata:
  name: test-metaprotocol-dubbo-route
  namespace: meta-dubbo
spec:
  hosts:
    - org.apache.dubbo.samples.basic.api.demoservice
  routes:
    - name: v1
      match:
        attributes:
          interface:
            exact: org.apache.dubbo.samples.basic.api.DemoService
          method:
            exact: sayHello
          foo:
            exact: bar
      route:
        - destination:
            host: org.apache.dubbo.samples.basic.api.demoservice
            subset: v1
