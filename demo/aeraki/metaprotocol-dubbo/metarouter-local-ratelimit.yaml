apiVersion: metaprotocol.aeraki.io/v1alpha1
kind: MetaRouter
metadata:
  name: test-metaprotocol-dubbo-route
  namespace: meta-dubbo
spec:
  hosts:
    - org.apache.dubbo.samples.basic.api.demoservice
  localRateLimit:
    tokenBucket:
      fillInterval: 60s
      maxTokens: 10
      tokensPerFill: 10
    conditions:
    - tokenBucket:
        fillInterval: 10s
        maxTokens: 2
        tokensPerFill: 2
      match:
        attributes:
          method:
            exact: sayHello
  routes:
    - name: v1
      route:
        - destination:
            host: org.apache.dubbo.samples.basic.api.demoservice
            subset: v1
