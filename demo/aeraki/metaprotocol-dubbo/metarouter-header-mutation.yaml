---
apiVersion: metaprotocol.aeraki.io/v1alpha1
kind: MetaRouter
metadata:
  name: test-metaprotocol-dubbo-route
  namespace: meta-dubbo
spec:
  hosts:
    - org.apache.dubbo.samples.basic.api.demoservice
  routes:
    - name: header-mutation
      route:
        - destination:
            host: org.apache.dubbo.samples.basic.api.demoservice
      requestMutation:
        - key: foo
          value: bar
        - key: foo1
          value: bar1
