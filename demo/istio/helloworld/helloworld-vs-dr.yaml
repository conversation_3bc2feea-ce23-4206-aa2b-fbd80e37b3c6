apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: helloworld-vs
spec:
  hosts:
    - helloworld
  http:
    - route:
        - destination:
            host: helloworld
            subset: v1
          weight: 0
        - destination:
            host: helloworld
            subset: v2
          weight: 100
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: helloworld-dr
spec:
  host: helloworld
  subsets:
    - labels:
        version: v1
      name: v1
    - labels:
        version: v2
      name: v2