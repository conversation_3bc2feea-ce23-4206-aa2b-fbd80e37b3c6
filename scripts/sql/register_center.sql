CREATE TABLE `t_register_instance`
(
    `id`                  bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `instance_id`         varchar(64)  NOT NULL DEFAULT '' COMMENT '注册中心实例UUID',
    `instance_name`       varchar(64)  NOT NULL DEFAULT '' COMMENT '注册中心实例名',
    `region`              varchar(8)   NOT NULL DEFAULT '' COMMENT '地域',
    `account_id`          varchar(64)  NOT NULL DEFAULT '0' COMMENT '主账号ID',
    `version`             varchar(12)  NOT NULL DEFAULT '' COMMENT '注册中心版本',
    `args`                varchar(255) NOT NULL DEFAULT '{}' COMMENT '注册中心启动参数 json ',
    `vpc_id`              varchar(64)  NOT NULL DEFAULT '' COMMENT 'VPC网络ID',
    `subnet_id`           varchar(64)  NOT NULL DEFAULT '' COMMENT '子网ID',
    `status`              tinyint      NOT NULL DEFAULT 0 COMMENT '状态, 0 - 初始化，1 - 创建中，2 - 运行中，3 - 调整中，4 - 释放中，5 - 运行异常，6 - 创建失败',
    `server_protocol`     varchar(12)  NOT NULL DEFAULT 'eureka' COMMENT 'server 端访问协议',
    `server_port`         varchar(12)  NOT NULL DEFAULT '8761' COMMENT 'server 端访问端口',
    `load_balance_list`   varchar(255) NOT NULL DEFAULT '[]' COMMENT '负载均衡列表 []json',
    `monitor_enabled`     tinyint NULL DEFAULT 0 COMMENT '是否开启监控 0 - 未开启，1 - 已开启',
    `monitor_instance_id` varchar(64) NULL DEFAULT '' COMMENT 'CProm监控实例ID',
    `monitor_agent_id`    varchar(64) NULL DEFAULT '' COMMENT 'CProm部署在用户侧k8s集群中的代理组件ID',
    `monitor_region`      varchar(8) NULL DEFAULT '' COMMENT 'CProm地域',
    `extend_data`         varchar(255) NOT NULL DEFAULT '{}' COMMENT '后期扩展字段 json ',
    `create_time`         timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         timestamp    NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '更新时间',
    `delete_time`         timestamp    NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '删除时间',
    `deleted`             tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除 1删除 0否',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '注册中心实例列表';

ALTER TABLE t_register_instance
    ADD INDEX idx_account_instance (account_id, instance_id);

INSERT INTO `t_register_instance` (`instance_id`,
                                   `instance_name`,
                                   `region`,
                                   `account_id`,
                                   `version`,
                                   `args`,
                                   `vpc_id`,
                                   `subnet_id`,
                                   `status`,
                                   `load_balance_list`,
                                   `monitor_enabled`,
                                   `monitor_instance_id`,
                                   `monitor_agent_id`,
                                   `monitor_region`,
                                   `extend_data`,
                                   `create_time`,
                                   `update_time`,
                                   `delete_time`,
                                   `deleted`)
VALUES ('uuid1', 'Instance1', 'bj', '1001', '1.0', '{}', 'vpc-001', 'subnet-001', 1, '[]', 0, '', '', '', '{}',
        NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid2', 'Instance2', 'gz', '1002', '1.1', '{}', 'vpc-002', 'subnet-002', 2, '[]', 1, '', '', '', '{}',
        NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid3', 'Instance3', 'gz', '1003', '2.0', '{}', 'vpc-003', 'subnet-003', 3, '[]', 0, '', '', '', '{}',
        NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid4', 'Instance4', 'gz', '1004', '1.2', '{}', 'vpc-004', 'subnet-004', 0, '[]', 1, '', '', '', '{}',
        NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid5', 'Instance5', 'bj', '1005', '1.3', '{}', 'vpc-005', 'subnet-005', 1, '[]', 0, '', '', '', '{}',
        NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid6', 'Instance6', 'gz', '1006', '1.4', '{}', 'vpc-006', 'subnet-006', 2, '[]', 1, '', '', '', '{}',
        NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid7', 'Instance7', 'gz', '1007', '1.5', '{}', 'vpc-007', 'subnet-007', 3, '[]', 0, '', '', '', '{}',
        NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid8', 'Instance8', 'gz', '1008', '2.1', '{}', 'vpc-008', 'subnet-008', 1, '[]', 1, '', '', '', '{}',
        NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid9', 'Instance9', 'gz', '1009', '1.6', '{}', 'vpc-009', 'subnet-009', 2, '[]', 0, '', '', '', '{}',
        NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid10', 'Instance10', 'gz', '1010', '1.7', '{}', 'vpc-010', 'subnet-010', 0, '[]', 1, '', '', '',
        '{}', NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid11', 'Instance11', 'gz', '1011', '2.2', '{}', 'vpc-011', 'subnet-011', 1, '[]', 0, '', '', '', '{}',
        NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid12', 'Instance12', 'gz', '1012', '1.8', '{}', 'vpc-012', 'subnet-012', 2, '[]', 1, '', '', '',
        '{}', NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid13', 'Instance13', 'bj', '1013', '1.9', '{}', 'vpc-013', 'subnet-013', 3, '[]', 0, '', '', '',
        '{}', NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid14', 'Instance14', 'gz', '1014', '2.3', '{}', 'vpc-014', 'subnet-014', 0, '[]', 1, '', '', '', '{}',
        NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid15', 'Instance15', 'gz', '1015', '1.0', '{}', 'vpc-015', 'subnet-015', 1, '[]', 0, '', '', '',
        '{}', NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid16', 'Instance16', 'gz', '1016', '1.1', '{}', 'vpc-016', 'subnet-016', 2, '[]', 1, '', '', '',
        '{}', NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid17', 'Instance17', 'gz', '1017', '2.4', '{}', 'vpc-017', 'subnet-017', 3, '[]', 0, '', '', '', '{}',
        NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid18', 'Instance18', 'gz', '1018', '1.2', '{}', 'vpc-018', 'subnet-018', 0, '[]', 1, '', '', '',
        '{}', NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid19', 'Instance19', 'gz', '1019', '1.3', '{}', 'vpc-019', 'subnet-019', 1, '[]', 0, '', '', '',
        '{}', NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0),
       ('uuid20', 'Instance20', 'gz', '1020', '1.9', '{}', 'vpc-020', 'subnet-020', 1, '[]', 0, '', '', '',
        '{}', NOW(), '1971-01-01 00:00:01', '1971-01-01 00:00:01', 0);