USE `bce_csm_logic`;

-- ----------------------------
-- Table structure for t_service_mesh_instance
-- ----------------------------
CREATE TABLE `t_service_mesh_instance`
(
    `id`                         bigint                                                                   NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `instance_uuid`              varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '服务网格实例UUID',
    `instance_name`              varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '服务网格实例名称',
    `instance_type`              enum ('standalone','hosting','cnap-hosting') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '服务网格实例类型standalone/hosting',
    `istio_version`              varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '服务网格istio版本',
    `region`                     varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '地域',
    `account_id`                 varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '账号account id',
    `vpc_network_id`             varchar(255)                                                             NOT NULL DEFAULT '' COMMENT 'VPC网络ID',
    `subnet_id`                  varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '子网ID',
    `status`                     varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '服务网格实例状态',
    `public_enabled`             tinyint                                                                  NOT NULL DEFAULT '0' COMMENT '是否开启 kube config 公网访问，1 表示开启，0 表示关闭',
    `monitor_enabled`            tinyint                                                                  NOT NULL DEFAULT '0' COMMENT '是否开启监控，1 表示开启，0 表示关闭',
    `discovery_selector_enabled` tinyint                                                                  NOT NULL DEFAULT '0' COMMENT '是否开启命名空间级别服务网格实例，1 表示开启，0 表示关闭',
    `discovery_selector_labels`  varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '控制面服务发现的命名空间标签',
    `istio_install_namespace`    varchar(255)                                                             NOT NULL DEFAULT 'istio-system' COMMENT '服务网格实例安装的命名空间',
    `instance_manage_scope`      enum ('cluster','namespace')                                             NOT NULL DEFAULT 'cluster' COMMENT '服务网格实例级别 cluster(集群)/namespace(命名空间)',
    `metadata`                   blob COMMENT '可扩展的metadata，json的形式存储',
    `create_time`                timestamp                                                                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                timestamp                                                                NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '更新时间',
    `delete_time`                timestamp                                                                         DEFAULT '1971-01-01 00:00:01' COMMENT '删除时间',
    `deleted`                    tinyint                                                                  NOT NULL DEFAULT '0' COMMENT '是否删除 1删除 0否',
    `multi_protocol_enabled`     tinyint                                                                  NOT NULL DEFAULT '0' COMMENT '是否支持多协议，1 表示支持，0 表示不支持',
    `api_server_eip`             tinyint                                                                  NOT NULL DEFAULT '0' COMMENT '托管网格api server是否挂载eip，1 表示挂载，0 表示不挂载',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='服务网格实例表';

-- ----------------------------
-- Table structure for t_service_mesh_cluster
-- ----------------------------
CREATE TABLE `t_service_mesh_cluster`
(
    `id`                      bigint                                                  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `instance_uuid`           varchar(255)                                            NOT NULL DEFAULT '' COMMENT '服务网格实例UUID',
    `cluster_uuid`            varchar(255)                                            NOT NULL DEFAULT '' COMMENT 'CCE集群UUID',
    `cluster_name`            varchar(255)                                            NOT NULL DEFAULT '' COMMENT 'CCE集群名称',
    `cluster_type`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '服务网格实例中集群类型primary/remote/external',
    `region`                  varchar(255)                                            NOT NULL DEFAULT '' COMMENT '地域',
    `account_id`              varchar(255)                                            NOT NULL DEFAULT '0' COMMENT '账号account id',
    `istio_install_namespace` varchar(255)                                            NOT NULL DEFAULT 'istio-system' COMMENT '服务网格实例安装的命名空间',
    `monitor_instance_id`     varchar(255)                                            NOT NULL DEFAULT '' COMMENT '集群关联的 CProm 实例 id',
    `monitor_region`          varchar(100)                                            NOT NULL DEFAULT '' COMMENT 'CProm 实例地域',
    `monitor_job_ids`         varchar(100)                                            NOT NULL DEFAULT '' COMMENT '监控采集 job id，逗号分隔',
    `monitor_agent_id`        varchar(255)                                                NULL DEFAULT '' COMMENT 'CProm部署在用户侧k8s集群中的代理组件ID',
    `connection_state`        varchar(255)                                            NOT NULL DEFAULT '0' COMMENT '集群联通状态',
    `create_time`             timestamp                                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`             timestamp                                               NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '更新时间',
    `delete_time`             timestamp                                                        DEFAULT '1971-01-01 00:00:01' COMMENT '删除时间',
    `deleted`                 tinyint                                                 NOT NULL DEFAULT '0' COMMENT '是否删除 1删除 0否',
    `ingress_sync_enabled`    tinyint                                                     NULL DEFAULT '0' COMMENT '当前集群是否开启了ingress同步，1 表示开启，0 表示关闭',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='服务网格实例集群表';

-- ----------------------------
-- Table structure for t_service_mesh_cert
-- ----------------------------
CREATE TABLE `t_service_mesh_cert`
(
    `id`             bigint       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `cluster_uuid`   varchar(255) NOT NULL DEFAULT '' COMMENT 'CCE集群UUID',
    `region`         varchar(255) NOT NULL DEFAULT '' COMMENT '地域',
    `account_id`     varchar(255) NOT NULL DEFAULT '0' COMMENT '账号account id',
    `ca_cert_pem`    blob         NOT NULL COMMENT '集群证书 ca_cert_pem',
    `ca_key_pem`     blob         NOT NULL COMMENT '集群证书 ca_key_pem',
    `cert_chain_pem` blob         NOT NULL COMMENT '集群证书 cert_chain_pem',
    `root_cert_pem`  blob         NOT NULL COMMENT '集群证书 root_cert_pem',
    `create_time`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    timestamp    NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '更新时间',
    `delete_time`    timestamp             DEFAULT '1971-01-01 00:00:01' COMMENT '删除时间',
    `deleted`        tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除 1删除 0否',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='服务网格实例集群证书表';

-- ----------------------------
-- Table structure for t_service_mesh_gateway
-- ----------------------------
CREATE TABLE `t_service_mesh_gateway`
(
    `id`             bigint                                                                   NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `instance_uuid`  varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '服务网格实例UUID',
    `istio_version`  varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '控制面istio版本',
    `cluster_uuid`   varchar(255)                                                             NOT NULL DEFAULT '' COMMENT 'CCE集群UUID',
    `cluster_name`   varchar(255)                                                             NOT NULL DEFAULT '' COMMENT 'CCE集群名称',
    `gateway_uuid`   varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '网关实例ID',
    `gateway_name`   varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '网关实例名称',
    `region`         varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '地域',
    `account_id`     varchar(255)                                                             NOT NULL DEFAULT '0' COMMENT '账号ID',
    `namespace`      varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '托管网关安装的命名空间',
    `resource_quota` varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '网关资源配额如2c4g/4c8g等',
    `vpc_network_id` varchar(255)                                                             NOT NULL DEFAULT '' COMMENT 'VPC网络ID',
    `subnet_id`      varchar(255)                                                             NOT NULL DEFAULT '' COMMENT '子网ID',
    `deploy_mode`    enum ('standalone','hosting') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '网关部署方式standalone/hosting',
    `gateway_type`   enum ('ingress','egress') CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '网关类型ingress/egress',
    `replicas`       int                                                                      NOT NULL DEFAULT '0' COMMENT '网关副本数',
    `monitor_enabled` tinyint                                                                     NULL DEFAULT '0' COMMENT '是否开启监控',
    `monitor_instance_id` varchar(255)                                                            NULL DEFAULT ''  COMMENT 'CProm监控实例ID',
    `monitor_agent_id` varchar(255)                                                               NULL DEFAULT '' COMMENT 'CProm部署在用户侧k8s集群中的代理组件ID',
    `monitor_region` varchar(255)                                                                 NULL DEFAULT '' COMMENT 'CProm地域',
    `monitor_job_id` varchar(255)                                                                 NULL DEFAULT '' COMMENT 'CProm采集任务ID',
    `create_time`    timestamp                                                                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    timestamp                                                                NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '更新时间',
    `delete_time`    timestamp                                                                         DEFAULT '1971-01-01 00:00:01' COMMENT '删除时间',
    `deleted`        tinyint                                                                  NOT NULL DEFAULT '0' COMMENT '是否删除 1删除 0否',
    `remote_ingress_enabled` tinyint                                                              NULL DEFAULT '0' COMMENT '是否开启remote集群ingress监听功能，1 表示开启，0 表示关闭',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '服务网格实例网关表';

-- ----------------------------
-- Table structure for t_service_mesh_lane_group
-- ----------------------------
CREATE TABLE `t_service_mesh_lane_group`
(
    `id`               bigint(20)        NOT NULL AUTO_INCREMENT COMMENT '主键',
    `instance_uuid`    varchar(255)      NOT NULL COMMENT 'CSM实例ID',
    `group_id`         varchar(255)      NOT NULL COMMENT '泳道组ID',
    `account_id`       varchar(255)      NOT NULL DEFAULT '0' COMMENT '账号ID',
    `group_name`       varchar(255)      NOT NULL COMMENT '泳道组名称',
    `trace_header`     varchar(255)      NOT NULL COMMENT '链路透传请求头',
    `route_header`     varchar(255)      NOT NULL COMMENT '引流请求头',
    `service_list`     text              NOT NULL COMMENT '泳道组下关联的服务列表，取值为JSON数组。单个服务的格式为：$集群地域/$集群名称/$集群id/$namespace/$serviceName。[\"bj/sh01/cce-sh1/default/mockc\"]',
    `create_time`      timestamp         NOT NULL COMMENT '创建时间',
    `update_time`      timestamp         NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_time`      timestamp         NULL DEFAULT '1971-01-01 00:00:01' COMMENT '删除时间',
    `deleted`          tinyint(4)        NOT NULL DEFAULT '0' COMMENT '是否删除 1删除 0否',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='服务网格泳道组表';

-- ----------------------------
-- Table structure for t_service_mesh_lane
-- ----------------------------
CREATE TABLE `t_service_mesh_lane`
(
    `id`                     bigint(20)      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `instance_uuid`          varchar(255)    NOT NULL COMMENT 'CSM实例ID',
    `group_id`               varchar(255)    NOT NULL COMMENT '泳道组ID',
    `lane_id`                varchar(255)    NOT NULL COMMENT '泳道ID',
    `lane_name`              varchar(255)    NOT NULL COMMENT '泳道名称',
    `account_id`             varchar(255)    NOT NULL DEFAULT '0' COMMENT '账号ID',
    `label_selector_key`     varchar(255)    NOT NULL COMMENT '工作负载对应的标签key值',
    `label_selector_value`   varchar(255)    NOT NULL COMMENT '工作负载对应的标签value值',
    `service_list`           text            NOT NULL COMMENT '泳道关联的服务列表，取值为JSON数组。单个服务的格式为：$集群地域/$集群名称/$集群id/$namespace/$serviceName。[\"bj/sh01/cce-sh1/default/mockc\"]',
    `is_base`                tinyint         NOT NULL DEFAULT '0' COMMENT '是否基准泳道，1为基准泳道',
    `create_time`            timestamp       NOT NULL COMMENT '创建时间',
    `update_time`            timestamp       NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_time`            timestamp       NULL DEFAULT '1971-01-01 00:00:01' COMMENT '删除时间',
    `deleted`                tinyint(4)      NOT NULL DEFAULT '0' COMMENT '是否删除 1删除 0否',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='服务网格泳道表';
