---
apiVersion: v1
kind: Namespace
metadata:
  name: {{.Namespace}}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: gateway-{{.Namespace}}
rules:
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "watch", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: gateway-{{.Namespace}}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: gateway-{{.Namespace}}
subjects:
  - kind: ServiceAccount
    name: higress-gateway
    namespace: {{.Namespace}}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: higress-config
  namespace: {{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-gateway
    higress: {{.Namespace}}-higress-gateway
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-gateway
data:
  higress: |-
    downstream:
      connectionBufferLimits: 32768
      http2:
        initialConnectionWindowSize: 1048576
        initialStreamWindowSize: 65535
        maxConcurrentStreams: 100
      idleTimeout: 180
      maxRequestHeadersKb: 60
      routeTimeout: 0
    upstream:
      connectionBufferLimits: ********
      idleTimeout: 10
  # Configuration file for the mesh networks to be used by the Split Horizon EDS.
  meshNetworks: |-
    networks: {}
  mesh: |-
    accessLogEncoding: JSON
    accessLogFile: /dev/stdout
    accessLogFormat: |
      {"ai_log":"%FILTER_STATE(wasm.ai_log:PLAIN)%","authority":"%REQ(X-ENVOY-ORIGINAL-HOST?:AUTHORITY)%","bytes_received":"%BYTES_RECEIVED%","bytes_sent":"%BYTES_SENT%","downstream_local_address":"%DOWNSTREAM_LOCAL_ADDRESS%","downstream_remote_address":"%DOWNSTREAM_REMOTE_ADDRESS%","duration":"%DURATION%","istio_policy_status":"%DYNAMIC_METADATA(istio.mixer:status)%","method":"%REQ(:METHOD)%","path":"%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%","protocol":"%PROTOCOL%","request_id":"%REQ(X-REQUEST-ID)%","requested_server_name":"%REQUESTED_SERVER_NAME%","response_code":"%RESPONSE_CODE%","response_flags":"%RESPONSE_FLAGS%","route_name":"%ROUTE_NAME%","start_time":"%START_TIME%","trace_id":"%REQ(X-B3-TRACEID)%","upstream_cluster":"%UPSTREAM_CLUSTER%","upstream_host":"%UPSTREAM_HOST%","upstream_local_address":"%UPSTREAM_LOCAL_ADDRESS%","upstream_service_time":"%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%","upstream_transport_failure_reason":"%UPSTREAM_TRANSPORT_FAILURE_REASON%","user_agent":"%REQ(USER-AGENT)%","x_forwarded_for":"%REQ(X-FORWARDED-FOR)%","response_code_details":"%RESPONSE_CODE_DETAILS%"}
    configSources:
    - address: xds://127.0.0.1:15051
    - address: k8s://
    defaultConfig:
      discoveryAddress: higress-controller.{{.Namespace}}.svc:15012
      proxyStatsMatcher:
        inclusionRegexps:
        - .*
      tracing: {}
    dnsRefreshRate: 200s
    enableAutoMtls: false
    enablePrometheusMerge: true
    ingressControllerMode: "OFF"
    mseIngressGlobalConfig:
      enableH3: false
      enableProxyProtocol: false
    protocolDetectionTimeout: 100ms
    rootNamespace: {{.Namespace}}
    trustDomain: cluster.local
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: controller-{{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-controller
    higress: higress-controller
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-controller
rules:
  # ingress controller
  - apiGroups: ["extensions", "networking.k8s.io"]
    resources: ["ingresses"]
    verbs: ["create", "get", "list", "watch", "update", "delete", "patch"]
  - apiGroups: ["extensions", "networking.k8s.io"]
    resources: ["ingresses/status"]
    verbs: ["*"]
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingresses", "ingressclasses"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingresses/status"]
    verbs: ["*"]
  # required for CA's namespace controller
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["create", "get", "list", "watch", "update"]
  # Use for Kubernetes Service APIs
  - apiGroups: ["networking.x-k8s.io"]
    resources: ["*"]
    verbs: ["get", "watch", "list"]
  - apiGroups: ["networking.x-k8s.io"]
    resources: ["*"]
    verbs: ["update"]
  # Gateway api controller
  - apiGroups: ["gateway.networking.k8s.io"]
    resources: ["*"]
    verbs: ["get", "watch", "list", "create", "update", "delete", "patch"]
  # Needed for multicluster secret reading, possibly ingress certs in the future
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "watch", "list", "create", "update", "delete", "patch"]
  - apiGroups: ["networking.higress.io"]
    resources: ["mcpbridges"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  - apiGroups: ["extensions.higress.io"]
    resources: ["wasmplugins"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["networking.higress.io"]
    resources: ["http2rpcs"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  - apiGroups: [""]
    resources: ["services"]
    verbs: ["get", "watch", "list", "update", "patch", "create", "delete"]
  # auto-detect installed CRD definitions
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "list", "watch"]
  # discovery and routing
  - apiGroups: [""]
    resources: ["pods", "nodes", "services", "namespaces", "endpoints", "deployments"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["discovery.k8s.io"]
    resources: ["endpointslices"]
    verbs: ["get", "list", "watch"]
  # Istiod and bootstrap.
  - apiGroups: ["certificates.k8s.io"]
    resources:
      - "certificatesigningrequests"
      - "certificatesigningrequests/approval"
      - "certificatesigningrequests/status"
    verbs: ["update", "create", "get", "delete", "watch"]
  - apiGroups: ["certificates.k8s.io"]
    resources:
      - "signers"
    resourceNames:
      - "kubernetes.io/legacy-unknown"
    verbs: ["approve"]
  # Used by Istiod to verify the JWT tokens
  - apiGroups: ["authentication.k8s.io"]
    resources: ["tokenreviews"]
    verbs: ["create"]
  # Used by Istiod to verify gateway SDS
  - apiGroups: ["authorization.k8s.io"]
    resources: ["subjectaccessreviews"]
    verbs: ["create"]
  # Used for MCS serviceexport management
  - apiGroups: ["multicluster.x-k8s.io"]
    resources: ["serviceexports"]
    verbs: ["get", "watch", "list", "create", "delete"]
  # Used for MCS serviceimport management
  - apiGroups: ["multicluster.x-k8s.io"]
    resources: ["serviceimports"]
    verbs: ["get", "watch", "list"]
  # sidecar injection controller
  - apiGroups: ["admissionregistration.k8s.io"]
    resources: ["mutatingwebhookconfigurations"]
    verbs: ["get", "list", "watch", "update", "patch"]
  # configuration validation webhook controller
  - apiGroups: ["admissionregistration.k8s.io"]
    resources: ["validatingwebhookconfigurations"]
    verbs: ["get", "list", "watch", "update"]
  # istio configuration
  # removing CRD permissions can break older versions of Istio running alongside this control plane (https://github.com/istio/istio/issues/29382)
  # please proceed with caution
  - apiGroups: ["config.istio.io", "security.istio.io", "networking.istio.io", "authentication.istio.io", "rbac.istio.io", "telemetry.istio.io", "extensions.istio.io"]
    verbs: ["get", "watch", "list"]
    resources: ["*"]
  # knative KIngress configuration
  - apiGroups: ["networking.internal.knative.dev"]
    verbs: ["get", "list", "watch"]
    resources: ["ingresses"]
  - apiGroups: ["networking.internal.knative.dev"]
    resources: ["ingresses/status"]
    verbs: ["get", "patch", "update"]
  # gateway api need
  - apiGroups: ["apps"]
    verbs: ["get", "watch", "list", "update", "patch", "create", "delete"]
    resources: ["deployments"]
  - apiGroups: [""]
    verbs: ["get", "watch", "list", "update", "patch", "create", "delete"]
    resources: ["serviceaccounts"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: controller-{{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-controller
    higress: higress-controller
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: controller-{{.Namespace}}
subjects:
  - kind: ServiceAccount
    name: higress-controller
    namespace: {{.Namespace}}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: higress-controller
  namespace: {{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-controller
    higress: higress-controller
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-controller
spec:
  replicas: 1
  selector:
    matchLabels:
      app: higress-controller
      higress: higress-controller
  template:
    metadata:
      annotations:
        # 必选项，对端 VPC 的用户 ID
        cross-vpc-eni.cce.io/userID: "{{.AccountId}}"
        # 必选项，跨 VPC 弹性网卡所属的子网
        cross-vpc-eni.cce.io/subnetID: "{{.SubnetId}}"
        # 必选项，跨 VPC 弹性网卡绑定的安全组合集，多个安全组使用逗号分隔
        cross-vpc-eni.cce.io/securityGroupIDs: "{{.SecurityGroupIds}}"
        # 必选项，跨 VPC 弹性网卡所属 VPC 的网段
        cross-vpc-eni.cce.io/vpcCidr: "{{.VpcCidr}}"
      labels:
        app: higress-controller
        higress: higress-controller
    spec:
      serviceAccountName: higress-controller
      securityContext:
        seccompProfile:
          type: RuntimeDefault
      containers:
        - name: higress-core
          securityContext:
            runAsUser: 1337
            runAsGroup: 1337
            runAsNonRoot: true
            allowPrivilegeEscalation: false
          image: "registry.baidubce.com/csm-offline/higress/higress:2.1.0"
          args:
            - "serve"
            - --gatewaySelectorKey=higress
            - --gatewaySelectorValue={{.Namespace}}-higress-gateway
            - --gatewayHttpPort=80
            - --gatewayHttpsPort=443
            - --ingressClass=higress
            - --watchNamespace={{.Namespace}}
            - --enableAutomaticHttps=true
            - --automaticHttpsEmail=
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.serviceAccountName
            - name: DOMAIN_SUFFIX
              value: cluster.local
            - name: GATEWAY_NAME
              value: higress-gateway
            - name: PILOT_ENABLE_GATEWAY_API
              value: "true"
            - name: PILOT_ENABLE_ALPHA_GATEWAY_API
              value: "true"
          ports:
            - name: http
              containerPort: 8888
              protocol: TCP
            - name: http-solver
              containerPort: 8889
              protocol: TCP
            - name: grpc
              containerPort: 15051
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /ready
              port: 8888
            initialDelaySeconds: 1
            periodSeconds: 3
            timeoutSeconds: 5
          resources:
            limits:
              cpu: '500m'
              memory: 1Gi
            requests:
              cpu: 500m
              memory: 1Gi
          volumeMounts:
            - name: log
              mountPath: /var/log
        - name: discovery
          image: "registry.baidubce.com/csm-offline/higress/pilot:2.1.0"
          args:
            - "discovery"
            - --monitoringAddr=:15014
            - --log_output_level=default:info
            - --domain
            - cluster.local
            - --keepaliveMaxServerConnectionAge
            - "30m"
          ports:
            - containerPort: 8080
              protocol: TCP
            - containerPort: 15010
              protocol: TCP
            - containerPort: 15017
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /ready
              port: 8080
            initialDelaySeconds: 1
            periodSeconds: 3
            timeoutSeconds: 5
          env:
            - name: PILOT_ENABLE_QUIC_LISTENERS
              value: "true"
            - name: VALIDATION_WEBHOOK_CONFIG_NAME
              value: ""
            - name: ISTIO_DUAL_STACK
              value: "false"
            - name: PILOT_ENABLE_HEADLESS_SERVICE_POD_LISTENERS
              value: "false"
            - name: PILOT_ENABLE_ALPN_FILTER
              value: "false"
            - name: ENABLE_OPTIMIZED_CONFIG_REBUILD
              value: "false"
            - name: PILOT_ENABLE_K8S_SELECT_WORKLOAD_ENTRIES
              value: "false"
            - name: HIGRESS_SYSTEM_NS
              value: "{{.Namespace}}"
            - name: DEFAULT_UPSTREAM_CONCURRENCY_THRESHOLD
              value: "10000"
            - name: ISTIO_GPRC_MAXRECVMSGSIZE
              value: "********0"
            - name: ENBALE_SCOPED_RDS
              value: "true"
            - name: ON_DEMAND_RDS
              value: "false"
            - name: HOST_RDS_MERGE_SUBSET
              value: "false"
            - name: PILOT_FILTER_GATEWAY_CLUSTER_CONFIG
              value: "false"
            - name: HIGRESS_CONTROLLER_SVC
              value: "127.0.0.1"
            - name: HIGRESS_CONTROLLER_PORT
              value: "15051"
            - name: REVISION
              value: "default"
            - name: JWT_POLICY
              value: third-party-jwt
            - name: PILOT_CERT_PROVIDER
              value: "istiod"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.serviceAccountName
            - name: KUBECONFIG
              value: /var/run/secrets/remote/config
            - name: PRIORITIZED_LEADER_ELECTION
              value: "false"
            - name: INJECT_ENABLED
              value: "false"
            - name: PILOT_ENABLE_CROSS_CLUSTER_WORKLOAD_ENTRY
              value: "false"
            - name: PILOT_ENABLE_METADATA_EXCHANGE
              value: "false"
            - name: PILOT_SCOPE_GATEWAY_TO_NAMESPACE
              value: "false"
            - name: VALIDATION_ENABLED
              value: "false"
            - name: PILOT_TRACE_SAMPLING
              value: "1"
            - name: PILOT_ENABLE_PROTOCOL_SNIFFING_FOR_OUTBOUND
              value: "true"
            - name: PILOT_ENABLE_PROTOCOL_SNIFFING_FOR_INBOUND
              value: "true"
            - name: ISTIOD_ADDR
              value: istiod.{{.Namespace}}.svc:15012
            - name: PILOT_ENABLE_ANALYSIS
              value: "false"
            - name: CLUSTER_ID
              value: "Kubernetes"
            # HIGRESS_ENABLE_ISTIO_API is only used to restart the controller pod after the config change
            - name: HIGRESS_ENABLE_ISTIO_API
              value: "true"
            - name: PILOT_ENABLE_GATEWAY_API
              value: "false"
            - name: PILOT_ENABLE_ALPHA_GATEWAY_API
              value: "false"
            - name: PILOT_ENABLE_GATEWAY_API_STATUS
              value: "false"
            - name: PILOT_ENABLE_GATEWAY_API_DEPLOYMENT_CONTROLLER
              value: "false"
            - name: CUSTOM_CA_CERT_NAME
              value: "higress-ca-root-cert"
          securityContext:
            readOnlyRootFilesystem: true
            runAsUser: 1337
            runAsGroup: 1337
            runAsNonRoot: true
          volumeMounts:
            - name: config
              mountPath: /etc/istio/config
            - name: istio-token
              mountPath: /var/run/secrets/tokens
              readOnly: true
            - name: local-certs
              mountPath: /var/run/secrets/istio-dns
            - name: cacerts
              mountPath: /etc/cacerts
              readOnly: true
            - name: istio-kubeconfig
              mountPath: /var/run/secrets/remote
              readOnly: true
      volumes:
        - name: log
          emptyDir: {}
        - name: config
          configMap:
            name: higress-config
        # Technically not needed on this pod - but it helps debugging/testing SDS
        # Should be removed after everything works.
        - emptyDir:
            medium: Memory
          name: local-certs
        - name: istio-token
          projected:
            sources:
              - serviceAccountToken:
                  audience: istio-ca
                  expirationSeconds: 43200
                  path: istio-token
        # Optional: user-generated root
        - name: cacerts
          secret:
            secretName: cacerts
            optional: true
        - name: istio-kubeconfig
          secret:
            secretName: istio-kubeconfig
            optional: true
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: higress-controller
  namespace: {{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-controller
    higress: higress-controller
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-controller
rules:
  # For storing CA secret
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["create", "get", "watch", "list", "update", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: higress-controller
  namespace: {{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-controller
    higress: higress-controller
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: higress-controller
subjects:
  - kind: ServiceAccount
    name: higress-controller
    namespace: {{.Namespace}}
---
apiVersion: v1
kind: Service
metadata:
  name: higress-controller
  namespace: {{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-controller
    higress: higress-controller
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-controller
spec:
  type: ClusterIP
  ports:
    - name: http
      port: 8888
      protocol: TCP
      targetPort: 8888
    - name: http-solver
      port: 8889
      protocol: TCP
      targetPort: 8889
    - name: grpc
      port: 15051
      protocol: TCP
      targetPort: 15051
    - port: 15010
      name: grpc-xds # plaintext
      protocol: TCP
    - port: 15012
      name: https-dns # mTLS with k8s-signed cert
      protocol: TCP
    - port: 443
      name: https-webhook # validation and injection
      targetPort: 15017
      protocol: TCP
    - port: 15014
      name: http-monitoring # prometheus stats
      protocol: TCP
  selector:
    app: higress-controller
    higress: higress-controller
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: higress-controller
  namespace: {{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-controller
    higress: higress-controller
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-controller
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: higress-gateway
  namespace: {{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-gateway
    higress: {{.Namespace}}-higress-gateway
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-gateway
  annotations: {}
spec:
  replicas: {{.Replicas}}
  selector:
    matchLabels:
      app: higress-gateway
      higress: {{.Namespace}}-higress-gateway
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 100%
  template:
    metadata:
      annotations:
        # 必选项，对端 VPC 的用户 ID
        cross-vpc-eni.cce.io/userID: "{{.AccountId}}"
        # 必选项，跨 VPC 弹性网卡所属的子网
        cross-vpc-eni.cce.io/subnetID: "{{.SubnetId}}"
        # 必选项，跨 VPC 弹性网卡绑定的安全组合集，多个安全组使用逗号分隔
        cross-vpc-eni.cce.io/securityGroupIDs: "{{.SecurityGroupIds}}"
        # 必选项，跨 VPC 弹性网卡所属 VPC 的网段
        cross-vpc-eni.cce.io/vpcCidr: "{{.VpcCidr}}"
        prometheus.io/path: /stats/prometheus
        prometheus.io/port: "15020"
        prometheus.io/scrape: "true"
        sidecar.istio.io/inject: "false"
      labels:
        sidecar.istio.io/inject: "false"
        app: higress-gateway
        higress: {{.Namespace}}-higress-gateway
    spec:
      serviceAccountName: higress-gateway
      securityContext:
        seccompProfile:
          type: RuntimeDefault
      containers:
        - name: higress-gateway
          image: "registry.baidubce.com/csm-offline/higress/gateway:2.1.0"
          args:
            - proxy
            - router
            - --domain
            - $(POD_NAMESPACE).svc.cluster.local
            - --proxyLogLevel=warning
            - --proxyComponentLogLevel=misc:error
            - --log_output_level=all:info
            - --serviceCluster=higress-gateway
          lifecycle:
            preStop:
              exec:
                command: ["sleep", "60"]
          securityContext:
            runAsUser: 1337
            runAsGroup: 1337
            runAsNonRoot: true
            allowPrivilegeEscalation: false
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: INSTANCE_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  fieldPath: spec.serviceAccountName
            - name: PROXY_XDS_VIA_AGENT
              value: "true"
            - name: ENABLE_INGRESS_GATEWAY_SDS
              value: "false"
            - name: JWT_POLICY
              value: third-party-jwt
            - name: ISTIO_META_HTTP10
              value: "1"
            - name: ISTIO_META_CLUSTER_ID
              value: "Kubernetes"
            - name: INSTANCE_NAME
              value: "higress-gateway"
            - name: LITE_METRICS
              value: "on"
          ports:
            - containerPort: 15020
              protocol: TCP
              name: istio-prom
            - containerPort: 15090
              protocol: TCP
              name: http-envoy-prom
            - containerPort: 8080
              name: http
              protocol: TCP
            - containerPort: 8443
              name: https
              protocol: TCP
          readinessProbe:
            failureThreshold: 30
            httpGet:
              path: /healthz/ready
              port: 15021
              scheme: HTTP
            initialDelaySeconds: 1
            periodSeconds: 2
            successThreshold: 1
            timeoutSeconds: 3
          resources:
            limits:
              cpu: '500m'
              memory: 1Gi
            requests:
              cpu: '500m'
              memory: 1Gi
          volumeMounts:
            - mountPath: /var/run/secrets/workload-spiffe-uds
              name: workload-socket
            - mountPath: /var/run/secrets/credential-uds
              name: credential-socket
            - mountPath: /var/run/secrets/workload-spiffe-credentials
              name: workload-certs
            - name: istio-token
              mountPath: /var/run/secrets/tokens
              readOnly: true
            - name: config
              mountPath: /etc/istio/config
            - name: higress-ca-root-cert
              mountPath: /var/run/secrets/istio
            - name: istio-data
              mountPath: /var/lib/istio/data
            - name: podinfo
              mountPath: /etc/istio/pod
            - name: proxy-socket
              mountPath: /etc/istio/proxy
      volumes:
        - emptyDir: {}
          name: workload-socket
        - emptyDir: {}
          name: credential-socket
        - emptyDir: {}
          name: workload-certs
        - name: istio-token
          projected:
            sources:
              - serviceAccountToken:
                  audience: istio-ca
                  expirationSeconds: 43200
                  path: istio-token
        - name: higress-ca-root-cert
          configMap:
            name: higress-ca-root-cert
        - name: config
          configMap:
            name: higress-config
        - name: istio-data
          emptyDir: {}
        - name: proxy-socket
          emptyDir: {}
        - name: podinfo
          downwardAPI:
            defaultMode: 420
            items:
              - fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels
                path: labels
              - fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.annotations
                path: annotations
              - path: cpu-request
                resourceFieldRef:
                  containerName: higress-gateway
                  divisor: 1m
                  resource: requests.cpu
              - path: cpu-limit
                resourceFieldRef:
                  containerName: higress-gateway
                  divisor: 1m
                  resource: limits.cpu
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: higress-gateway-global-custom-response
  namespace: {{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-gateway
    higress: {{.Namespace}}-higress-gateway
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-gateway
spec:
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        listener:
          filterChain:
            filter:
              name: envoy.filters.network.http_connection_manager
      patch:
        operation: INSERT_FIRST
        value:
          name: envoy.filters.http.custom_response
          typed_config:
            "@type": type.googleapis.com/envoy.extensions.filters.http.custom_response.v3.CustomResponse
---
apiVersion: networking.k8s.io/v1
kind: IngressClass
metadata:
  name: higress
spec:
  controller: higress.io/higress-controller
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: higress-gateway
  namespace: {{.Namespace}}
rules:
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "watch", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: higress-gateway
  namespace: {{.Namespace}}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: higress-gateway
subjects:
  - kind: ServiceAccount
    name: higress-gateway
---
apiVersion: v1
kind: Service
metadata:
  name: higress-gateway
  namespace: {{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-gateway
    higress: {{.Namespace}}-higress-gateway
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-gateway
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true"
    service.beta.kubernetes.io/cce-load-balancer-backend-type: "eni"
spec:
  type: LoadBalancer
  ports:
    - name: http2
      port: 80
      protocol: TCP
      targetPort: 8080
    - name: https
      port: 443
      protocol: TCP
      targetPort: 8443
  selector:
    app: higress-gateway
    higress: {{.Namespace}}-higress-gateway
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: higress-gateway
  namespace: {{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-gateway
    higress: {{.Namespace}}-higress-gateway
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-gateway

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: istiod-{{.Namespace}}
  labels:
    app: istiod
    release: istio
rules:
  # sidecar injection controller
  - apiGroups: ["admissionregistration.k8s.io"]
    resources: ["mutatingwebhookconfigurations"]
    verbs: ["get", "list", "watch", "update", "patch"]
  # configuration validation webhook controller
  - apiGroups: ["admissionregistration.k8s.io"]
    resources: ["validatingwebhookconfigurations"]
    verbs: ["get", "list", "watch", "update"]
  # istio configuration
  # removing CRD permissions can break older versions of Istio running alongside this control plane (https://github.com/istio/istio/issues/29382)
  # please proceed with caution
  - apiGroups: ["config.istio.io", "security.istio.io", "networking.istio.io", "authentication.istio.io", "rbac.istio.io", "telemetry.istio.io"]
    verbs: ["get", "watch", "list"]
    resources: ["*"]
  - apiGroups: ["networking.istio.io"]
    verbs: ["get", "watch", "list", "update", "patch", "create", "delete"]
    resources: ["workloadentries"]
  - apiGroups: ["networking.istio.io"]
    verbs: ["get", "watch", "list", "update", "patch", "create", "delete"]
    resources: ["workloadentries/status"]
  # auto-detect installed CRD definitions
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "list", "watch"]
  # discovery and routing
  - apiGroups: [""]
    resources: ["pods", "nodes", "services", "namespaces", "endpoints"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["discovery.k8s.io"]
    resources: ["endpointslices"]
    verbs: ["get", "list", "watch"]
  # ingress controller
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingresses", "ingressclasses"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingresses/status"]
    verbs: ["*"]
  # required for CA's namespace controller
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["create", "get", "list", "watch", "update"]
  # Istiod and bootstrap.
  - apiGroups: ["certificates.k8s.io"]
    resources:
      - "certificatesigningrequests"
      - "certificatesigningrequests/approval"
      - "certificatesigningrequests/status"
    verbs: ["update", "create", "get", "delete", "watch"]
  - apiGroups: ["certificates.k8s.io"]
    resources:
      - "signers"
    resourceNames:
      - "kubernetes.io/legacy-unknown"
    verbs: ["approve"]
  # Used by Istiod to verify the JWT tokens
  - apiGroups: ["authentication.k8s.io"]
    resources: ["tokenreviews"]
    verbs: ["create"]
  # Used by Istiod to verify gateway SDS
  - apiGroups: ["authorization.k8s.io"]
    resources: ["subjectaccessreviews"]
    verbs: ["create"]
  # Use for Kubernetes Service APIs
  - apiGroups: ["networking.x-k8s.io", "gateway.networking.k8s.io"]
    resources: ["*"]
    verbs: ["get", "watch", "list"]
  - apiGroups: ["networking.x-k8s.io", "gateway.networking.k8s.io"]
    resources: ["*"]
    verbs: ["update"]
  - apiGroups: ["gateway.networking.k8s.io"]
    resources: ["gatewayclasses"]
    verbs: ["create", "update", "patch", "delete"]
  # Needed for multicluster secret reading, possibly ingress certs in the future
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "watch", "list"]
  # Used for MCS serviceexport management
  - apiGroups: ["multicluster.x-k8s.io"]
    resources: ["serviceexports"]
    verbs: ["get", "watch", "list", "create", "delete"]
  # Used for MCS serviceimport management
  - apiGroups: ["multicluster.x-k8s.io"]
    resources: ["serviceimports"]
    verbs: ["get", "watch", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: istio-reader-{{.Namespace}}
  labels:
    app: istio-reader
    release: istio
rules:
  - apiGroups:
      - "config.istio.io"
      - "security.istio.io"
      - "networking.istio.io"
      - "authentication.istio.io"
      - "rbac.istio.io"
    resources: ["*"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["endpoints", "pods", "services", "nodes", "replicationcontrollers", "namespaces", "secrets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["networking.istio.io"]
    verbs: ["get", "watch", "list"]
    resources: ["workloadentries"]
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["discovery.k8s.io"]
    resources: ["endpointslices"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["apps"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["authentication.k8s.io"]
    resources: ["tokenreviews"]
    verbs: ["create"]
  - apiGroups: ["authorization.k8s.io"]
    resources: ["subjectaccessreviews"]
    verbs: ["create"]
  - apiGroups: ["multicluster.x-k8s.io"]
    resources: ["serviceexports"]
    verbs: ["get", "watch", "list"]
  - apiGroups: ["multicluster.x-k8s.io"]
    resources: ["serviceimports"]
    verbs: ["get", "watch", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: istio-reader-{{.Namespace}}
  labels:
    app: istio-reader
    release: istio
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: istio-reader-{{.Namespace}}
subjects:
  - kind: ServiceAccount
    name: istio-reader-service-account
    namespace: {{.Namespace}}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: istiod-{{.Namespace}}
  labels:
    app: istiod
    release: istio
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: istiod-{{.Namespace}}
subjects:
  - kind: ServiceAccount
    name: istiod-service-account
    namespace: {{.Namespace}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: istio-reader-service-account
  namespace: {{.Namespace}}
  labels:
    app: istio-reader
    release: istio
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: istiod-{{.Namespace}}
  namespace: {{.Namespace}}
  labels:
    app: istiod
    release: istio
rules:
  # permissions to verify the webhook is ready and rejecting
  # invalid config. We use --server-dry-run so no config is persisted.
  - apiGroups: ["networking.istio.io"]
    verbs: ["create"]
    resources: ["gateways"]
  # For storing CA secret
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["create", "get", "watch", "list", "update", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: istiod-{{.Namespace}}
  namespace: {{.Namespace}}
  labels:
    app: istiod
    release: istio
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: istiod-{{.Namespace}}
subjects:
  - kind: ServiceAccount
    name: istiod-service-account
    namespace: {{.Namespace}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: istiod-service-account
  namespace: {{.Namespace}}
  labels:
    app: istiod
    release: istio
---
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: gateway-internal
  namespace: {{.Namespace}}
spec:
  selector:
    higress: {{.Namespace}}-higress-gateway
  servers:
    - port:
        number: 80
        name: http
        protocol: HTTP
      hosts:
        - "*"
---
apiVersion: telemetry.istio.io/v1alpha1
kind: Telemetry
metadata:
  name: enable-telemetry
  namespace: {{.Namespace}}
spec:
  metrics:
    - providers:
        - name: prometheus