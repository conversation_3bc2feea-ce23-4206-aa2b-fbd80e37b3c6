package middleware

import (
	"fmt"
	"github.com/spf13/viper"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/registercenter"

	"github.com/labstack/echo/v4"

	csmError "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

func CheckAndSetRegion() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			cc := c.(context.CsmContext)
			region := c.Request().Header.Get(constants.RegionHeaderKey)
			if len(region) == 0 {
				return csmError.NewMissingParametersException(fmt.Sprintf("Missing Header %s", constants.RegionHeaderKey))
			}
			cc.Set(reg.ContextRegion, region)

			return next(cc)
		}
	}
}

// OpenAPICheckAndSetRegion just for cnap
func OpenAPICheckAndSetRegion() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			cc := c.(context.CsmContext)
			region := c.Request().Header.Get(constants.RegionHeaderKey)
			if len(region) == 0 {
				cc.CsmLogger().Infof("OpenAPICheckAndSetRegion Missing Header %s", constants.RegionHeaderKey)
				iamBackendRegion := viper.GetString("iam.profile.staging.region")
				cc.Set(reg.ContextRegion, iamBackendRegion)
				return next(cc)
			}
			cc.Set(reg.ContextRegion, region)
			return next(cc)
		}
	}
}

func CheckAndSetRegion4Registry() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			cc := c.(context.CsmContext)
			region := c.Request().Header.Get(constants.RegionHeaderKey)
			if len(region) == 0 {
				return csmError.NewMissingParametersException(fmt.Sprintf("Missing Header %s", constants.RegionHeaderKey))
			}

			backendRegion := viper.GetString(registercenter.Region)
			if backendRegion != region {
				return csmError.NewMissingParametersException(fmt.Sprintf("Current region %s does not support", region))
			}

			cc.Set(reg.ContextRegion, region)
			return next(cc)
		}
	}
}
