package cnap

import (
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/internal/pkg/request"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/validate"
)

func TestInstanceConvertAndCheck(t *testing.T) {
	// Setup
	validate.Validator.RegisterStructValidation(InstanceStructLevelValidation, Instance{})

	testCases := []struct {
		name           string
		input          string
		expected       Instance
		expectedErrMsg string
	}{
		{
			name:  "simple",
			input: `{"name":"mesh-test", "type":"standalone", "scope":"cluster", "clusterId":"cce-test"}`,
			expected: Instance{
				Type:         "standalone",
				Scope:        "cluster",
				IstioVersion: "1.13.2",
				ClusterId:    "cce-test",
				Name:         "mesh-test",
			},
			expectedErrMsg: "",
		},
		{
			name:           "standalone type without cluster id",
			input:          `{"name":"mesh-test", "type":"standalone", "scope":"cluster"}`,
			expected:       Instance{},
			expectedErrMsg: "clusterId_required_if_standalone",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", strings.NewReader(tc.input))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			u := new(Instance)
			err := request.ConvertAndCheck(c, u)
			if err != nil {
				assert.NotEmpty(t, tc.expectedErrMsg)
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			} else {
				if !reflect.DeepEqual(*u, tc.expected) {
					t.Errorf("expected: %v, got %v", tc.expected, *u)
				}
			}
		})
	}
}
