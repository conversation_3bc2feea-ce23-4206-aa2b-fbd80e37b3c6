package cnap

import "github.com/go-playground/validator/v10"

const (
	MeshClusterRegionInQueryParam = "region"
	MeshClusterIDInQueryParam     = "clusterID"
)

type Clusters struct {
	Clusters []Cluster `json:"clusters" validate:"required"`
}

type Cluster struct {
	ClusterId   string `json:"clusterId" validate:"required"`
	ClusterName string `json:"clusterName"`
	Region      string `json:"region" validate:"required"`
}

func (c *Clusters) FillDefault() {}

func ClustersStructLevelValidation(sl validator.StructLevel) {
	c := sl.Current().Interface().(Clusters)
	if len(c.Clusters) == 0 {
		sl.ReportError(c.Clusters, "clusters", "Clusters", "require clusters to manage", "")
	}
}
