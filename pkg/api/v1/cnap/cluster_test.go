package cnap

import (
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/internal/pkg/request"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/ptrutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/validate"
)

func TestClusterConvertAndCheck(t *testing.T) {
	// Setup
	validate.Validator.RegisterStructValidation(ClustersStructLevelValidation, Clusters{})

	testCases := []struct {
		name           string
		input          string
		expected       *Clusters
		expectedErrMsg *string
	}{
		{
			name:           "simple",
			input:          `{"clusters":[]}`,
			expected:       &Clusters{},
			expectedErrMsg: ptrutil.String("require clusters to manage"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", strings.NewReader(tc.input))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			u := new(Clusters)
			err := request.ConvertAndCheck(c, u)
			if err != nil {
				assert.NotEmpty(t, tc.expectedErrMsg)
				assert.Containsf(t, err.Error(), *tc.expectedErrMsg,
					"expected error: %v, got %v", *tc.expectedErrMsg, err.Error())
			} else {
				if !reflect.DeepEqual(*u, tc.expected) {
					t.Errorf("expected: %v, got %v", tc.expected, *u)
				}
			}
		})
	}
}
