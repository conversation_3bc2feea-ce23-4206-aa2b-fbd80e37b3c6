package cnap

import (
	"strings"

	"github.com/go-playground/validator/v10"
)

type MeshInstanceType string

const (
	TypeStandalone  MeshInstanceType = "standalone"
	TypeHosting     MeshInstanceType = "hosting"
	TypeCnapHosting MeshInstanceType = "cnap-hosting"
)

type MeshInstanceScope string

const (
	ScopeCluster   MeshInstanceScope = "cluster"
	ScopeNamespace MeshInstanceScope = "namespace"
)

const (
	DefaultIstioVersion = "1.13.2"
	BusinessKey         = "eks.baidu-int.com/workspaceID"
)

type Instance struct {
	Type              string             `json:"type" validate:"oneof=standalone hosting cnap-hosting"`
	Scope             string             `json:"scope" validate:"oneof=cluster namespace"`
	Name              string             `json:"name" validate:"required"`
	IstioVersion      string             `json:"istioVersion"`
	ClusterId         string             `json:"clusterId"`
	DiscoverySelector *DiscoverySelector `json:"discoverySelector"`
	// TraceInfo 链路追踪相关参数
	TraceInfo *TraceInfo `json:"traceInfo"`
}

type InstanceStatusResponse struct {
	Status string `json:"status"`
}

type DiscoverySelector struct {
	Enabled     bool              `json:"enabled"`
	MatchLabels map[string]string `json:"matchLabels"`
}

type CreateInstanceResponse struct {
	MeshInstanceId string `json:"meshInstanceId"`
}

// TraceInfo 链路追踪信息
type TraceInfo struct {
	TraceEnabled bool    `json:"traceEnabled"`
	SamplingRate float64 `json:"samplingRate"`
	Service      string  `json:"service"`
	Address      string  `json:"address"`
}

func (i *Instance) FillDefault() {
	if len(i.Type) == 0 {
		i.Type = string(TypeCnapHosting)
	}

	if len(i.Scope) == 0 {
		i.Scope = string(ScopeCluster)
	}

	if len(i.IstioVersion) == 0 {
		i.IstioVersion = DefaultIstioVersion
	}
}

func InstanceStructLevelValidation(sl validator.StructLevel) {
	i := sl.Current().Interface().(Instance)
	if strings.EqualFold(i.Type, string(TypeStandalone)) && len(i.ClusterId) == 0 {
		sl.ReportError(i.Type, "type", "Type", "clusterId_required_if_standalone", "")
		sl.ReportError(i.ClusterId, "clusterId", "ClusterId", "clusterId_required_if_standalone", "")
	}

	if strings.EqualFold(i.Scope, string(ScopeNamespace)) && (i.DiscoverySelector == nil || !i.DiscoverySelector.Enabled ||
		i.DiscoverySelector.MatchLabels == nil || len(i.DiscoverySelector.MatchLabels) == 0) {
		sl.ReportError(i.DiscoverySelector, "discoverySelector", "DiscoverySelector",
			"discoverySelector_required_if_namespace_scope", "")
	}
}
