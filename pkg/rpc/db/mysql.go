package db

import (
	"errors"
	"fmt"
	"time"

	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmflag"
)

var dsnOptions = "mysql.dsnOptions"
var driver = "mysql.driver"
var user = "mysql.user"
var password = "mysql.password"
var host = "mysql.host"
var database = "mysql.database"

type DbConfig struct {
	Driver     string
	Host       string
	User       string
	Password   string
	Database   string
	DsnOptions string
}

func InitMysqlConfig() {
	viper.SetDefault(dsnOptions, "charset=utf8&parseTime=true&loc=Local&timeout=3s&readTimeout=8s&writeTimeout=3s")
	viper.SetDefault(driver, "mysql")
	viper.SetDefault(user, "root")
	viper.SetDefault(password, "vm531@Vrp")
	viper.SetDefault(host, "gzns-inf-matrix0.gzns.baidu.com:8306")
	viper.SetDefault(database, "bce_csm_logic")
}

func NewDbConfig() *DbConfig {
	return &DbConfig{
		DsnOptions: viper.GetString(dsnOptions),
		Driver:     viper.GetString(driver),
		User:       viper.GetString(user),
		Password:   viper.GetString(password),
		Host:       viper.GetString(host),
		Database:   viper.GetString(database),
	}
}

func (c *DbConfig) AddFlags(fs *csmflag.FlagSet) {
	fs.StringVar(&c.Driver, "mysql.driver", "mysql", "db user")
	fs.StringVar(&c.DsnOptions, "mysql.dsnOptions", c.DsnOptions, "db dsn")
	fs.StringVar(&c.User, "mysql.user", c.User, "db user")
	fs.StringVar(&c.Password, "mysql.password", c.Password, "db password")
	fs.StringVar(&c.Host, "mysql.host", c.Host, "db host")
	fs.StringVar(&c.Database, "mysql.database", c.Database, "db database")
}

func NewMysqlEngine(dbconf *DbConfig) (*gorm.DB, error) {
	if dbconf == nil {
		return nil, errors.New("dbconf is nil")
	}
	if len(dbconf.Host) == 0 {
		return nil, errors.New("db host is nil")
	}

	mysqlDSN := fmt.Sprintf("%s:%s@tcp(%s)/%s?%s", dbconf.User, dbconf.Password, dbconf.Host, dbconf.Database, dbconf.DsnOptions)

	engine, err := gorm.Open(dbconf.Driver, mysqlDSN)
	if err != nil {
		time.Sleep(1 * time.Second)
		engine, err = gorm.Open(dbconf.Driver, mysqlDSN)
	}
	engine.DB().SetMaxIdleConns(100)
	engine.DB().SetMaxOpenConns(1000)
	engine.DB().SetConnMaxLifetime(10 * time.Minute) //按照xdb的wait_timeout=700换算为10m
	logger := newGormLogger(dbconf)
	engine.SetLogger(logger)
	engine.LogMode(true)

	return engine, err
}
