package db

import (
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"time"
	"unicode"

	log "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

var (
	sqlRegexp                = regexp.MustCompile(`\?`)
	numericPlaceHolderRegexp = regexp.MustCompile(`\$\d+`)
)

type gormLogger struct {
	Conf   *DbConfig
	logger *log.Logger
}

func newGormLogger(conf *DbConfig) *gormLogger {
	return &gormLogger{
		Conf: conf,
		logger: log.WithFields(log.Fields{
			"user":     conf.User,
			"host":     conf.Host,
			"database": conf.Database,
		}).AddCallerSkip(6),
	}
}

func isPrintable(s string) bool {
	for _, r := range s {
		if !unicode.IsPrint(r) {
			return false
		}
	}
	return true
}

func (l *gormLogger) Print(values ...interface{}) {
	if len(values) > 1 {
		var (
			formattedValues []string
			sql             string
			level           = values[0]
		)
		if level == "sql" {
			// duration
			duration := float64(values[2].(time.Duration).Nanoseconds()/1e4) / 100.0
			// affectedOrReturned
			affectedOrReturned := strconv.FormatInt(values[5].(int64), 10)
			// sql
			for _, value := range values[4].([]interface{}) {
				indirectValue := reflect.Indirect(reflect.ValueOf(value))
				if indirectValue.IsValid() {
					value = indirectValue.Interface()
					if t, ok := value.(time.Time); ok {
						formattedValues = append(formattedValues, fmt.Sprintf("'%v'", t.Format("2006-01-02 15:04:05")))
					} else if b, ok := value.([]byte); ok {
						if str := string(b); isPrintable(str) {
							formattedValues = append(formattedValues, fmt.Sprintf("'%v'", str))
						} else {
							formattedValues = append(formattedValues, "'<binary>'")
						}
					} else {
						formattedValues = append(formattedValues, fmt.Sprintf("'%v'", value))
					}
				} else {
					formattedValues = append(formattedValues, "NULL")
				}
			}

			// differentiate between $n placeholders or else treat like ?
			if numericPlaceHolderRegexp.MatchString(values[3].(string)) {
				sql = values[3].(string)
				for index, value := range formattedValues {
					placeholder := fmt.Sprintf(`\$%d([^\d]|$)`, index+1)
					sql = regexp.MustCompile(placeholder).ReplaceAllString(sql, value+"$1")
				}
			} else {
				formattedValuesLength := len(formattedValues)
				for index, value := range sqlRegexp.Split(values[3].(string), -1) {
					sql += value
					if index < formattedValuesLength {
						sql += formattedValues[index]
					}
				}
			}
			l.logger.WithFields(log.Fields{
				"duration": duration,
				"rows":     affectedOrReturned,
			}).Infof(sql)

		} else {
			l.logger.Infof("", values[2:]...)
		}
	}
}
