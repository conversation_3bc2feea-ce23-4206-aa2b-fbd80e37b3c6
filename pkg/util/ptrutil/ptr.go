package ptrutil

import (
	"reflect"
)

func EnsureValue(i interface{}) interface{} {
	t := reflect.TypeOf(i)
	if t == nil {
		return nil
	}
	v := reflect.ValueOf(i)
	if t.Kind() == reflect.Ptr {
		if !v.Elem().IsValid() {
			return nil
		}
		return v.Elem().Interface()
	}
	return i
}

func GetFieldPtrByName(i interface{}, name string, fieldType reflect.Type) (interface{}, bool) {
	t := reflect.TypeOf(i)
	v := reflect.ValueOf(i)
	if t.Kind() == reflect.Ptr {
		v = v.Elem()
		t = reflect.TypeOf(v)
	}
	if t.Kind() != reflect.Struct {
		return nil, false
	}
	f := v.<PERSON>ame(name)
	if !f.<PERSON>ali<PERSON>() {
		return nil, false
	}
	if f.Type() == fieldType {
		if f.<PERSON>ddr() == false {
			return nil, false
		}
		return f.Addr().Interface(), true
	}
	return nil, false
}
