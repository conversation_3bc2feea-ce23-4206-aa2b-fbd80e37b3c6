package ptrutil

import "time"

func String(s string) *string {
	return &s
}

func Bool(b bool) *bool {
	return &b
}

func Int(t int) *int {
	return &t
}

func Int32(t int32) *int32 {
	return &t
}

func Int64(t int64) *int64 {
	return &t
}

func Int16(t int16) *int16 {
	return &t
}

func Int8(t int8) *int8 {
	return &t
}

func Uint32(t uint32) *uint32 {
	return &t
}

func Time(t time.Time) *time.Time {
	return &t
}

func Duration(d time.Duration) *time.Duration {
	return &d
}
