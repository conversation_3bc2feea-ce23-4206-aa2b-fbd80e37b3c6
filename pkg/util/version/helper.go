package version

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

// istio与k8s版本配对列表
type IstioSupportK8sVersion struct {
	IstioVersion                string   `json:"istioVersion"`
	SupportedClusterVersionList []string `json:"supportedClusterVersionList"`
}

func GetIstioSupportK8sVersion() []IstioSupportK8sVersion {
	versionPairingLists := []IstioSupportK8sVersion{
		{
			IstioVersion: constants.IstioVersion13,
			SupportedClusterVersionList: []string{
				constants.K8sVersion20,
				constants.K8sVersion22,
			},
		},
		{
			IstioVersion: constants.IstioVersion14,
			SupportedClusterVersionList: []string{
				constants.K8sVersion22,
				constants.K8sVersion24,
			},
		},
		{
			IstioVersion: constants.IstioVersion16,
			SupportedClusterVersionList: []string{
				constants.K8sVersion22,
				constants.K8sVersion24,
			},
		},
	}
	return versionPairingLists
}
