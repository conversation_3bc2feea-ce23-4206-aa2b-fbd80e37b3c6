package version

import (
	"fmt"
	"io"
	"runtime"
)

// Version contains versioning information.
// how we'll want to distribute that information.
type Version struct {
	GitVersion   string `json:"gitVersion"`
	GitCommit    string `json:"gitCommit"`
	GitTreeState string `json:"gitTreeState"`
	BuildDate    string `json:"buildDate"`
	GoVersion    string `json:"goVersion"`
	Compiler     string `json:"compiler"`
	Platform     string `json:"platform"`
}

// Get returns the overall codebase version. It's for detecting
// what code a binary was built from.
func Get() *Version {
	return &Version{
		GitVersion:   gitVersion,
		GitCommit:    gitCommit,
		GitTreeState: gitTreeState,
		BuildDate:    buildDate,
		GoVersion:    runtime.Version(),
		Compiler:     runtime.Compiler,
		Platform:     fmt.Sprintf("%s/%s", runtime.GOOS, runtime.GOARCH),
	}
}

// PrintPretty xxx
func PrintPretty(w io.Writer) {
	fmt.Printf("GitVersion:   %s\n", gitVersion)
	fmt.Printf("GitCommit:    %s\n", gitCommit)
	fmt.Printf("GitTreeState: %s\n", gitTreeState)
	fmt.Printf("BuildDate:    %s\n", buildDate)
	fmt.Printf("GoVersion:    %s\n", runtime.Version())
	fmt.Printf("Compiler:     %s\n", runtime.Compiler)
	fmt.Printf("Platform:     %s/%s\n", runtime.GOOS, runtime.GOARCH)
}

// PrintStruct xxx
func PrintStruct(w io.Writer) {
	fmt.Printf("%#v\n", Get())
}
