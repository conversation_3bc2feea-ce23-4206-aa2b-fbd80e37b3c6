package util

import (
	"fmt"
	"io"
	"net"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/davecgh/go-spew/spew"
	"github.com/stretchr/testify/assert"
)

type TestServerConfig struct {
	t                  *testing.T
	ass                *assert.Assertions
	RequestMethod      string
	RequestURLPath     string
	RequestBody        []byte
	RequestHeaders     map[string]string
	RequestQueryParams map[string]string
	ResponseHeaders    map[string]string
	ResponseBody       []byte
	ResponseBodyFunc   func(t *testing.T, actualBody []byte)
	ResponseStatusCode int
	HookAfterResponse  func()
	Debug              bool
}

func (config *TestServerConfig) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ass := config.ass
	defer r.Body.Close()

	if config.RequestMethod != "" {
		if config.Debug {
			fmt.Fprintf(os.Stderr, "Method: ")
			spew.Fdump(os.<PERSON>, r.<PERSON>)
		}
		ass.Equal(config.RequestMethod, r.Method, "request method mismatch")
	}

	if config.RequestURLPath != "" {
		if config.Debug {
			fmt.Fprintf(os.Stderr, "Path: ")
			spew.Fdump(os.Stderr, r.URL.Path)
		}
		ass.Equal(config.RequestURLPath, r.URL.Path, "request path mismatch")
	}

	if config.Debug && len(config.RequestHeaders) > 0 {
		if config.Debug {
			fmt.Fprintf(os.Stderr, "Header: ")
			spew.Fdump(os.Stderr, r.Header)
		}
	}
	reqHeaders := r.Header
	for k, want := range config.RequestHeaders {
		actual := reqHeaders.Get(k)
		ass.Equal(want, actual, fmt.Sprintf("header '%s' mismatch", k))
	}

	if len(config.RequestQueryParams) > 0 {
		if config.Debug {
			fmt.Fprintf(os.Stderr, "QueryParams: ")
			spew.Fdump(os.Stderr, r.URL.RawQuery)
		}
	}
	reqQueryParams := r.URL.Query()
	for k, want := range config.RequestQueryParams {
		actual := reqQueryParams.Get(k)
		ass.Equal(want, actual, fmt.Sprintf("query param '%s' mismatch", k))
	}

	if len(config.RequestBody) > 0 {
		body, _ := io.ReadAll(r.Body)
		if config.Debug {
			fmt.Fprintf(os.Stderr, "Body: ")
			spew.Fdump(os.Stderr, body)
		}
		if config.ResponseBodyFunc != nil {
			config.ResponseBodyFunc(config.t, body)
		} else {
			ass.Equal(config.RequestBody, body, "request body mismatch")
		}
	}

	// 返回顺序要遵守：header -> status code -> body
	rspHeaders := w.Header()
	for k, v := range config.ResponseHeaders {
		rspHeaders.Set(k, v)
	}

	if config.ResponseStatusCode > 0 {
		w.WriteHeader(config.ResponseStatusCode)
	}

	if len(config.ResponseBody) > 0 {
		w.Write(config.ResponseBody)
	}

	if config.HookAfterResponse != nil {
		config.HookAfterResponse()
	}
}

func NewTestServer(t *testing.T, config *TestServerConfig) *httptest.Server {
	config.ass = assert.New(t)
	return httptest.NewServer(config)
}

// NewUnstartedServer returns a new Server but doesn't start it.
//
// After changing its configuration, the caller should call Start or
// StartTLS.
//
// The caller should call Close when finished, to shut it down.
func NewUnstartedServer(t *testing.T, config *TestServerConfig) *httptest.Server {
	config.ass = assert.New(t)
	return httptest.NewUnstartedServer(config)
}

// StartTestServer Start starts a server from NewUnstartedServer. listening on address
func StartTestServer(server *httptest.Server, address string) {
	server.Listener = newLocalListener(address)
	server.Start()
}

func NewTestServerWithMultipleRequests(t *testing.T, configs []TestServerConfig) *httptest.Server {
	ass := assert.New(t)
	for i := range configs {
		configs[i].ass = ass
	}
	requestCount := 0
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		configs[requestCount].ServeHTTP(w, r)
		requestCount++
	}))
}

func newLocalListener(address string) net.Listener {
	if address != "" {
		l, err := net.Listen("tcp", address)
		if err != nil {
			panic(fmt.Sprintf("httptest: failed to listen on %v: %v", address, err))
		}
		return l
	}
	l, err := net.Listen("tcp", "127.0.0.1:0")
	if err != nil {
		if l, err = net.Listen("tcp6", "[::1]:0"); err != nil {
			panic(fmt.Sprintf("httptest: failed to listen on a port: %v", err))
		}
	}
	return l
}
