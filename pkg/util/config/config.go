package config

import (
	goflag "flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/spf13/pflag"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmflag"
)

const AppNameKey = "app"

// InitConfig 初始化配置，如果指定了 --config 参数，会从指定路径读取 YAML，否则会从 ./conf/ 或 ./ 下寻找 csm_local.yaml
func InitConfig() {
	pflag.CommandLine.SetNormalizeFunc(wordSepNormalizeFunc)
	pflag.CommandLine.AddGoFlagSet(goflag.CommandLine)

	configPath := pflag.StringP("config", "c", "", "custom path for configuration file")
	debugMode := pflag.Bool("debug", false, "enable debug mode")
	pflag.Parse()

	initViper(*configPath, *debugMode)
}

func initViper(configPath string, debugMode bool) {
	viper.AutomaticEnv()
	viper.SetEnvPrefix("CSM")

	viper.SetDefault(AppNameKey, filepath.Base(os.Args[0]))

	if configPath != "" {
		viper.SetConfigFile(configPath)
	} else {
		viper.SetConfigName("config")
		viper.AddConfigPath("./conf/")
		viper.AddConfigPath(".")
	}
	err := viper.ReadInConfig()
	if err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			fmt.Fprintf(os.Stderr, "Skip reading config file: %s \n", err)
		} else {
			panic(fmt.Errorf("Error with config file: %s \n", err))
		}
	} else if debugMode {
		fmt.Fprintf(os.Stderr, "Read config file from %s \n", viper.ConfigFileUsed())
	}

	err = viper.BindPFlags(pflag.CommandLine)
	if err != nil {
		panic(fmt.Errorf("Fatal error when reading flags: %s \n", err))
	}
	csmflag.Parse()
}

// WordSepNormalizeFunc changes all flags that contain "_" separators
func wordSepNormalizeFunc(f *pflag.FlagSet, name string) pflag.NormalizedName {
	if strings.Contains(name, "_") {
		return pflag.NormalizedName(strings.Replace(name, "_", "-", -1))
	}
	return pflag.NormalizedName(name)
}
