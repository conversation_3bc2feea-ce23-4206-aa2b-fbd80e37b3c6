package page

import (
	"strconv"
	"strings"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
)

// 分页的参数
type Page struct {
	OrderBy     string
	Order       string
	PageNoStr   string
	PageSizeStr string
	PageNo      int64
	PageSize    int64
}

func GetPageParam(ctx context.CsmContext, orderParam map[string]string) *Page {
	// 排序初始化
	page := &Page{}
	page.OrderBy = ctx.QueryParam("orderBy")
	page.Order = ctx.QueryParam("order")
	page.PageNoStr = ctx.QueryParam("pageNo")
	page.PageSizeStr = ctx.QueryParam("pageSize")
	orderBy := "created_at"
	// 排序字段映射
	for front, backend := range orderParam {
		if page.OrderBy == front {
			orderBy = backend
			break
		}
	}
	page.OrderBy = orderBy

	if strings.ToUpper(page.Order) == "ASC" {
		page.Order = strings.ToUpper(page.Order)
	} else {
		page.Order = "DESC"
	}

	// 分页
	pageNo, err := strconv.ParseInt(page.PageNoStr, 10, 64)
	pageNo = util.MaxInt64(1, pageNo)

	pageSize, err := strconv.ParseInt(page.PageSizeStr, 10, 64)
	if err != nil {
		pageSize = 10
	}
	pageSize = util.MaxInt64(1, pageSize)
	pageSize = util.MinInt64(10000, pageSize)

	page.PageNo = pageNo
	page.PageSize = pageSize
	page.PageNoStr = strconv.FormatInt(pageNo, 10)
	page.PageSizeStr = strconv.FormatInt(pageSize, 10)
	return page
}
