package util

import (
	"testing"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	mockCtx = csmContext.MockNewCsmContext()
)

func TestIsYAMLEqual(t *testing.T) {
	tests := []struct {
		desc   string
		in1    string
		in2    string
		expect bool
	}{
		{
			desc:   "yaml-equal",
			in1:    `foo: bar`,
			in2:    `foo: bar`,
			expect: true,
		},
		{
			desc:   "bad-yaml-1",
			in1:    "O#JF*()#",
			in2:    `foo: bar`,
			expect: false,
		},
		{
			desc:   "bad-yaml-2",
			in1:    `foo: bar`,
			in2:    "#OHJ*#()F",
			expect: false,
		},
		{
			desc: "yaml-not-equal",
			in1: `zinc: iron
stoichiometry: avagadro
`,
			in2: `i-swear: i-am
definitely-not: in1
`,
			expect: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.desc, func(t *testing.T) {
			if got := IsYAMLEqual(mockCtx, tt.in1, tt.in2); got != tt.expect {
				t.<PERSON><PERSON>rf("%v: got %v want %v", tt.desc, got, tt.expect)
			}
		})
	}
}
