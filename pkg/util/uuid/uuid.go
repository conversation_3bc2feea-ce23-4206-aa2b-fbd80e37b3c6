package uuid

import (
	"math/rand"
	"time"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

// GetUUID 使用数字与小写字母随机生成 uuid
func GetUUID() string {
	// we need to use mesh-id as an install namespace for istio, so we not use capital letters
	var letters = []rune("abcdefghijklmnopqrstuvwxyz0123456789")
	b := make([]rune, 8)
	rand.Seed(time.Now().UnixNano())
	for j := range b {
		b[j] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

// GetInstanceUUID 生成 csm- 为前缀的实例 id
func GetInstanceUUID() string {
	return constants.InstanceUUIDPrefix + GetUUID()
}

// GetGatewayUUID 生成 gw- 为前缀的实例 id
func GetGatewayUUID() string {
	return constants.GatewayUUIDPrefix + GetUUID()
}

// GetRegisterInstanceUUID 生成 rgc- 为前缀的实例 id
func GetRegisterInstanceUUID() string {
	return constants.RegisterInstanceCenterPrefix + GetUUID()
}
