package dbutil

import (
	"fmt"
	"reflect"

	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/ptrutil"
)

const searchTag = "searchable"
const wildcardSearch = "wildcard"
const prefixWildcardSearch = "prefix-wildcard"

func WithSearch(search interface{}, q *gorm.DB) *gorm.DB {
	search = ptrutil.EnsureValue(search)
	if search == nil {
		return q
	}
	t := reflect.TypeOf(search)
	if t.Kind() == reflect.Ptr || t.Kind() == reflect.Interface {
		panic("search params should not be ptr or interface")
	}
	s := reflect.ValueOf(search)
	n := s.NumField()
	for i := 0; i < n; i++ {
		fv := s.Field(i)
		// recursive calling WithSearch on exported struct field
		if fv.Type().Kind() == reflect.Struct {
			q = WithSearch(fv.Interface(), q)
		}
		if fv.IsValid() == false {
			continue
		}
		ft := t.Field(i)
		name := ft.Name
		tag, ok := parseSubTag(ft, searchTag)
		if !ok || tag == "-" {
			continue
		}

		// 卸载指针
		if fv.Kind() == reflect.Ptr || fv.Kind() == reflect.Interface {
			if fv.IsNil() {
				continue
			}
			fv = fv.Elem()
		}

		var colName string
		tagValue, isOk := parseSubTagWithSpecifiedParent(ft, gormTag, columnTag)
		if isOk && tagValue != "-" {
			colName = tagValue
		} else {
			colName = gorm.ToColumnName(name)
		}

		switch fv.Kind() {
		case reflect.Bool:
			q = q.Where(fmt.Sprintf("%s = ?", colName), fv.Bool())
			break
		case reflect.Int64:
		case reflect.Int32:
		case reflect.Int16:
		case reflect.Int8:
		case reflect.Int:
			q = q.Where(fmt.Sprintf("%s = ?", colName), fv.Int())
			break
		case reflect.String:
			switch tag {
			case wildcardSearch:
				if len(fv.String()) != 0 {
					q = q.Where(
						fmt.Sprintf("%s like ?", colName),
						fmt.Sprintf("%%%s%%", fv.String()))
				}
				break
			case prefixWildcardSearch:
				if len(fv.String()) != 0 {
					q = q.Where(
						fmt.Sprintf("%s like ?", colName),
						fmt.Sprintf("%s%%", fv.String()))
				}
				break
			default:
				if len(fv.String()) != 0 {
					q = q.Where(fmt.Sprintf("%s = ?", colName), fv.String())
				}
			}
			break
		default:
			csmlog.Warnf("unsupported search field type %s %s, ignored", name, fv.Kind())
			break
		}
	}
	return q
}
