package dbutil

import (
	"reflect"
	"testing"

	"database/sql/driver"
	"github.com/erikstmartin/go-testdb"
	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
)

type withSearchTest1 struct {
	A *string `dbutil:"searchable"`
	B *string `dbutil:"searchable:prefix-wildcard"`
	C *string `dbutil:"searchable:wildcard"`
	D *int    `dbutil:"searchable"`
	E *int    ``
	F *bool   `dbutil:"searchable"`
	G float64 `dbutil:"searchable"`
}

type WithSearchTest2 struct {
	A *string `dbutil:"searchable"`
	B *string `dbutil:"searchable:prefix-wildcard"`
	C *string `dbutil:"searchable:wildcard"`
	D *string ``
}

// test WithSearch on embedded struct field
type withSearchTest3 struct {
	E *string `dbutil:"searchable"`
	WithSearchTest2
}

func TestWithSearch(t *testing.T) {
	db, _ := gorm.Open("testdb", "")

	type args struct {
		search interface{}
		q      *gorm.DB
	}
	tests := []struct {
		name      string
		args      args
		wantQuery string
		wantArgs  []driver.Value
	}{
		{
			name:      "string, int, bool search",
			wantQuery: `SELECT * FROM "with_search_test1"  WHERE (a = ?) AND (d = ?) AND (f = ?)`,
			wantArgs: []driver.Value{
				"abc",
				int64(1),
				true,
			},
			args: args{
				search: withSearchTest1{
					A: csm.String("abc"),
					D: csm.Int(1),
					F: csm.Bool(true),
				},
				q: db,
			},
		},
		{
			name:      "support ptr search",
			wantQuery: `SELECT * FROM "with_search_test1"  WHERE (a = ?) AND (d = ?) AND (f = ?)`,
			wantArgs: []driver.Value{
				"abc",
				int64(1),
				true,
			},
			args: args{
				search: &withSearchTest1{
					A: csm.String("abc"),
					D: csm.Int(1),
					F: csm.Bool(true),
				},
				q: db,
			},
		},
		{
			name:      "nil search",
			wantQuery: `SELECT * FROM "with_search_test1"  `,
			wantArgs:  []driver.Value{},
			args: args{
				search: withSearchTest1{
					A: nil,
					D: nil,
					F: nil,
				},
				q: db,
			},
		},
		{
			name:      "wildcard search",
			wantQuery: `SELECT * FROM "with_search_test1"  WHERE (b like ?) AND (c like ?)`,
			wantArgs: []driver.Value{
				"b%",
				"%c%",
			},
			args: args{
				search: withSearchTest1{
					B: csm.String("b"),
					C: csm.String("c"),
				},
				q: db,
			},
		},
		{
			name:      "ignore unsupported search",
			wantQuery: `SELECT * FROM "with_search_test1"  WHERE (b like ?) AND (c like ?)`,
			wantArgs: []driver.Value{
				"b%",
				"%c%",
			},
			args: args{
				search: withSearchTest1{
					B: csm.String("b"),
					C: csm.String("c"),
					G: 1.1,
				},
				q: db,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdb.SetQueryWithArgsFunc(func(query string, args []driver.Value) (driver.Rows, error) {
				if !reflect.DeepEqual(args, tt.wantArgs) {
					t.Errorf("WithSearch() Args = %v, want %v", args, tt.wantArgs)
				}
				if query != tt.wantQuery {
					t.Errorf("WithSearch() Query = %v, want %v", query, tt.wantQuery)
				}
				columns := []string{"A", "B", "C"}
				result := `a,b,c`
				return testdb.RowsFromCSVString(columns, result), nil
			})

			err := WithSearch(tt.args.search, tt.args.q).Find(&withSearchTest1{}).Error
			if err != nil {
				t.Errorf("WithSearch() err = %v", err)
			}
		})
	}
}

func TestWithSearch2(t *testing.T) {
	db, _ := gorm.Open("testdb", "")

	type args struct {
		search interface{}
		q      *gorm.DB
	}
	tests := []struct {
		name      string
		args      args
		wantQuery string
		wantArgs  []driver.Value
	}{
		{
			name:      "string search",
			wantQuery: `SELECT * FROM "with_search_test3"  WHERE (e = ?)`,
			wantArgs: []driver.Value{
				"abc",
			},
			args: args{
				search: withSearchTest3{
					E: csm.String("abc"),
				},
				q: db,
			},
		},
		{
			name:      "embedded struct field search",
			wantQuery: `SELECT * FROM "with_search_test3"  WHERE (e = ?) AND (a = ?) AND (b like ?) AND (c like ?)`,
			wantArgs: []driver.Value{
				"abc",
				"foo",
				"bar%",
				"%hoo%",
			},
			args: args{
				search: withSearchTest3{
					E: csm.String("abc"),
					WithSearchTest2: WithSearchTest2{
						A: csm.String("foo"),
						B: csm.String("bar"),
						C: csm.String("hoo"),
						D: csm.String("mar"),
					},
				},
				q: db,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdb.SetQueryWithArgsFunc(func(query string, args []driver.Value) (driver.Rows, error) {
				if !reflect.DeepEqual(args, tt.wantArgs) {
					t.Errorf("WithSearch() Args = %v, want %v", args, tt.wantArgs)
				}
				if query != tt.wantQuery {
					t.Errorf("WithSearch() Query = %v, want %v", query, tt.wantQuery)
				}
				columns := []string{"A", "B", "C", "D", "E"}
				result := `aa, bb, cc, dd, ee`
				return testdb.RowsFromCSVString(columns, result), nil
			})

			err := WithSearch(tt.args.search, tt.args.q).Find(&withSearchTest3{}).Error
			if err != nil {
				t.Errorf("WithSearch() err = %v", err)
			}
		})
	}
}
