package dbutil

import (
	"reflect"
	"testing"

	"github.com/magiconair/properties/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
)

type withSortTest2 struct{}

type withSortTest3 interface{}

type withSortTest1 struct {
	A *string
	B int
	C string
	D withSortTest2
	E withSortTest3
}

func TestWithSort(t *testing.T) {
	type args struct {
		data     []withSortTest1
		orderBy  string
		order    DBOrder
		pageNo   int64
		pageSize int64
	}

	tests := []struct {
		name       string
		args       args
		wantResult []withSortTest1
	}{
		{
			name: "normal sort by string pointer",
			args: args{
				data: []withSortTest1{
					{
						A: csm.String("aaa"),
						B: 1,
					},
					{
						A: csm.String("ccc"),
						B: 2,
					},
					{
						A: csm.String("bbb"),
						B: 3,
					},
				},
				orderBy:  "A",
				order:    DBOrderDESC,
				pageNo:   1,
				pageSize: 5,
			},
			wantResult: []withSortTest1{
				{
					A: csm.String("ccc"),
					B: 2,
				},
				{
					A: csm.String("bbb"),
					B: 3,
				},
				{
					A: csm.String("aaa"),
					B: 1,
				},
			},
		},
		{
			name: "normal sort by int",
			args: args{
				data: []withSortTest1{
					{
						A: csm.String("aaa"),
						B: 1,
					},
					{
						A: csm.String("ccc"),
						B: 2,
					},
					{
						A: csm.String("bbb"),
						B: 3,
					},
				},
				orderBy:  "B",
				order:    DBOrderDESC,
				pageNo:   1,
				pageSize: 5,
			},
			wantResult: []withSortTest1{
				{
					A: csm.String("bbb"),
					B: 3,
				},
				{
					A: csm.String("ccc"),
					B: 2,
				},
				{
					A: csm.String("aaa"),
					B: 1,
				},
			},
		},
		{
			name: "no ops sort",
			args: args{
				data: []withSortTest1{
					{
						A: csm.String("ccc"),
						B: 2,
					},
					{
						A: csm.String("aaa"),
						B: 1,
					},
					{
						A: csm.String("bbb"),
						B: 3,
					},
				},
				orderBy:  "D",
				order:    DBOrderDESC,
				pageNo:   1,
				pageSize: 5,
			},
			wantResult: []withSortTest1{
				{
					A: csm.String("ccc"),
					B: 2,
				},
				{
					A: csm.String("aaa"),
					B: 1,
				},
				{
					A: csm.String("bbb"),
					B: 3,
				},
			},
		},
		{
			name: "normal sort and page",
			args: args{
				data: []withSortTest1{
					{
						A: csm.String("aaa"),
						B: 1,
					},
					{
						A: csm.String("ccc"),
						B: 2,
					},
					{
						A: csm.String("bbb"),
						B: 3,
					},
					{
						A: csm.String("eee"),
						B: 4,
					},
					{
						A: csm.String("ddd"),
						B: 5,
					},
				},
				orderBy:  "A",
				order:    DBOrderDESC,
				pageNo:   2,
				pageSize: 2,
			},
			wantResult: []withSortTest1{
				{
					A: csm.String("ccc"),
					B: 2,
				},
				{
					A: csm.String("bbb"),
					B: 3,
				},
			},
		},
		{
			name: "normal sort and page",
			args: args{
				data: []withSortTest1{
					{
						A: csm.String("aaa"),
						B: 1,
					},
					{
						A: csm.String("ccc"),
						B: 2,
					},
					{
						A: csm.String("bbb"),
						B: 3,
					},
					{
						A: csm.String("eee"),
						B: 4,
					},
					{
						A: csm.String("ddd"),
						B: 5,
					},
				},
				orderBy:  "A",
				order:    DBOrderDESC,
				pageNo:   3,
				pageSize: 2,
			},
			wantResult: []withSortTest1{
				{
					A: csm.String("aaa"),
					B: 1,
				},
			},
		},
		{
			name: "normal sort and page",
			args: args{
				data: []withSortTest1{
					{
						A: csm.String("aaa"),
						B: 1,
					},
					{
						A: csm.String("ccc"),
						B: 2,
					},
					{
						A: csm.String("bbb"),
						B: 3,
					},
					{
						A: csm.String("eee"),
						B: 4,
					},
					{
						A: csm.String("ddd"),
						B: 5,
					},
				},
				orderBy:  "A",
				order:    DBOrderDESC,
				pageNo:   1,
				pageSize: 2,
			},
			wantResult: []withSortTest1{
				{
					A: csm.String("eee"),
					B: 4,
				},
				{
					A: csm.String("ddd"),
					B: 5,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &tt.args.data
			SortByField(a, tt.args.orderBy, tt.args.order, tt.args.pageNo, tt.args.pageSize)
			for i := 0; i < len(tt.wantResult); i++ {
				assert.Equal(t, (*a)[i], tt.wantResult[i])
			}
			assert.Equal(t, *a, tt.wantResult)
		})
	}
}

func TestIsSortableField(t *testing.T) {
	type args struct {
		t    reflect.Type
		name string
	}
	tests := []struct {
		name         string
		args         args
		expectResult bool
	}{
		{
			name: "normal string pointer field",
			args: args{
				t:    reflect.TypeOf(withSortTest1{}),
				name: "A",
			},
			expectResult: true,
		},
		{
			name: "normal int field",
			args: args{
				t:    reflect.TypeOf(withSortTest1{}),
				name: "B",
			},
			expectResult: true,
		},
		{
			name: "normal string field",
			args: args{
				t:    reflect.TypeOf(withSortTest1{}),
				name: "C",
			},
			expectResult: true,
		},
		{
			name: "not match struct field",
			args: args{
				t:    reflect.TypeOf(withSortTest1{}),
				name: "D",
			},
			expectResult: false,
		},
		{
			name: "not match interface field",
			args: args{
				t:    reflect.TypeOf(withSortTest1{}),
				name: "E",
			},
			expectResult: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := IsSortableField(tt.args.t, tt.args.name)
			assert.Equal(t, r, tt.expectResult)
		})
	}
}
