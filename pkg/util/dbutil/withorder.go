package dbutil

import (
	"fmt"
	"reflect"

	"github.com/jinzhu/gorm"
)

const orderTag = "orderable"

type DBOrder string

const (
	DBOrderASC  DBOrder = "ASC"
	DBOrderDESC DBOrder = "DESC"
)

func WithOrder(t reflect.Type, orderBy string, order DBOrder, q *gorm.DB) *gorm.DB {

	if t.Kind() == reflect.Ptr || t.Kind() == reflect.Interface {
		panic("t should not be ptr or interface")
	}
	var fOrderBy *string
	for i := 0; i < t.NumField(); i++ {
		ft := t.Field(i)
		name := ft.Name
		tag, ok := parseSubTag(ft, orderTag)
		if !ok || tag == "-" {
			if ft.Type.Kind() == reflect.Struct {
				q = WithOrder(ft.Type, orderBy, order, q)
			} else {
				continue
			}
		}
		if gorm.ToColumnName(orderBy) == gorm.ToColumnName(name) {
			colName := gorm.ToColumnName(orderBy)
			fOrderBy = &colName
			break
		}
	}
	if fOrderBy != nil {
		q = q.Order(fmt.Sprintf("%s %s", *fOrderBy, order))
	}
	return q
}
