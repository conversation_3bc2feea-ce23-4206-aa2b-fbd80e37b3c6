package dbutil

import (
	"reflect"
	"time"

	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/ptrutil"
)

const updateTag = "updatable"

func WithUpdate(update interface{}, q *gorm.DB) *gorm.DB {
	update = ptrutil.EnsureValue(update)
	if update == nil {
		return q
	}
	updateMap := make(map[string]interface{})
	t := reflect.TypeOf(update)
	if t.Kind() == reflect.Ptr || t.Kind() == reflect.Interface {
		panic("update params should not be ptr or interface")
	}
	s := reflect.ValueOf(update)
	n := s.NumField()
	for i := 0; i < n; i++ {
		fv := s.Field(i)
		if fv.IsValid() == false {
			continue
		}
		ft := t.Field(i)
		name := ft.Name
		tag, ok := parseSubTag(ft, updateTag)
		if !ok || tag == "-" {
			continue
		}

		// 卸载指针
		if fv.Kind() == reflect.Ptr || fv.Kind() == reflect.Interface {
			if fv.IsNil() {
				continue
			}
			fv = fv.Elem()
		}

		var colName string
		tag, ok = parseSubTagWithSpecifiedParent(ft, gormTag, columnTag)
		if ok && tag != "-" {
			colName = tag
		} else {
			colName = gorm.ToColumnName(name)
		}
		if fv.Type() == reflect.TypeOf(time.Time{}) {
			updateMap[colName] = fv.Interface()
		} else {
			switch fv.Kind() {
			case reflect.Struct:
				q = WithUpdate(fv.Interface(), q)
			default:
				updateMap[colName] = fv.Interface()
			}
		}
	}
	return q.Updates(updateMap)
}
