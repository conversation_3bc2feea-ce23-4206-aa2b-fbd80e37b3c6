package dbutil

import (
	"reflect"
	"testing"

	"database/sql/driver"
	"github.com/erikstmartin/go-testdb"
	"github.com/jinzhu/gorm"
)

type withOrderTest1 struct {
	A *string `dbutil:"orderable"`
	B *int
	innerStruct
}

type innerStruct struct {
	D *string `dbutil:"orderable"`
}

func TestWithOrder(t *testing.T) {
	db, _ := gorm.Open("testdb", "")

	type args struct {
		order   DBOrder
		orderBy string
		t       reflect.Type
		q       *gorm.DB
	}
	tests := []struct {
		name      string
		args      args
		wantQuery string
		wantArgs  []driver.Value
	}{
		{
			name:      "asc order",
			wantQuery: `SELECT * FROM "with_order_test1"   ORDER BY a ASC`,
			args: args{
				t:       reflect.TypeOf(withOrderTest1{}),
				order:   DBOrderASC,
				orderBy: "a",
				q:       db,
			},
		},
		{
			name:      "cap asc order",
			wantQuery: `SELECT * FROM "with_order_test1"   ORDER BY a ASC`,
			args: args{
				t:       reflect.TypeOf(withOrderTest1{}),
				order:   DBOrderASC,
				orderBy: "A",
				q:       db,
			},
		},
		{
			name:      "desc order",
			wantQuery: `SELECT * FROM "with_order_test1"   ORDER BY a DESC`,
			args: args{
				t:       reflect.TypeOf(withOrderTest1{}),
				order:   DBOrderDESC,
				orderBy: "a",
				q:       db,
			},
		},
		{
			name:      "ignored order by",
			wantQuery: `SELECT * FROM "with_order_test1"  `,
			args: args{
				t:       reflect.TypeOf(withOrderTest1{}),
				order:   DBOrderASC,
				orderBy: "b",
				q:       db,
			},
		},
		{
			name:      "not exist order by",
			wantQuery: `SELECT * FROM "with_order_test1"  `,
			args: args{
				t:       reflect.TypeOf(withOrderTest1{}),
				order:   DBOrderASC,
				orderBy: "c",
				q:       db,
			},
		},
		{
			name:      "inner struct order by",
			wantQuery: `SELECT * FROM "with_order_test1"   ORDER BY d ASC`,
			args: args{
				t:       reflect.TypeOf(withOrderTest1{}),
				order:   DBOrderASC,
				orderBy: "d",
				q:       db,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdb.SetQueryWithArgsFunc(func(query string, args []driver.Value) (driver.Rows, error) {
				if query != tt.wantQuery {
					t.Errorf("WithOrder() Query = %v, want %v", query, tt.wantQuery)
				}
				columns := []string{"A", "B", "C", "D"}
				result := `a,b,c,d`
				return testdb.RowsFromCSVString(columns, result), nil
			})

			err := WithOrder(tt.args.t, tt.args.orderBy, tt.args.order, tt.args.q).Find(&withOrderTest1{}).Error
			if err != nil {
				t.Errorf("WithOrder() err = %v", err)
			}
		})
	}
}
