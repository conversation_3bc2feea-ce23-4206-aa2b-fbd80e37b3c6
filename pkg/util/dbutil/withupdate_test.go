package dbutil

import (
	"reflect"
	"testing"
	"time"

	"database/sql/driver"
	"github.com/erikstmartin/go-testdb"
	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
)

type withUpdateTest1 struct {
	A *string `dbutil:"updatable"`
	B *int    `dbutil:"updatable"`
	C *bool   `dbutil:"updatable"`
	D *bool
	E *time.Time `dbutil:"updatable"`
}

func TestWithUpdate(t *testing.T) {
	db, _ := gorm.Open("testdb", "")

	type args struct {
		search interface{}
		q      *gorm.DB
	}
	eTime := time.Now().UTC()
	tests := []struct {
		name      string
		args      args
		wantQuery string
		wantArgs  []driver.Value
	}{
		{
			name:      "string, int, bool update",
			wantQuery: `UPDATE "with_update_test1" SET "a" = ?, "b" = ?, "c" = ?  `,
			wantArgs: []driver.Value{
				"abc",
				int64(1),
				true,
			},
			args: args{
				search: withUpdateTest1{
					A: csm.String("abc"),
					B: csm.Int(1),
					C: csm.Bool(true),
					E: nil,
				},
				q: db,
			},
		},
		{
			name:      "support ptr update",
			wantQuery: `UPDATE "with_update_test1" SET "a" = ?, "b" = ?, "c" = ?  `,
			wantArgs: []driver.Value{
				"abc",
				int64(1),
				true,
			},
			args: args{
				search: &withUpdateTest1{
					A: csm.String("abc"),
					B: csm.Int(1),
					C: csm.Bool(true),
					E: nil,
				},
				q: db,
			},
		},
		{
			name:      "nil update",
			wantQuery: `UPDATE "with_update_test1" SET "a" = ?  `,
			wantArgs: []driver.Value{
				"abc",
			},
			args: args{
				search: withUpdateTest1{
					A: csm.String("abc"),
					B: nil,
					C: nil,
					E: nil,
				},
				q: db,
			},
		},
		{
			name:      "ignore not updatable",
			wantQuery: `UPDATE "with_update_test1" SET "a" = ?  `,
			wantArgs: []driver.Value{
				"abc",
			},
			args: args{
				search: withUpdateTest1{
					A: csm.String("abc"),
					D: csm.Bool(true),
					E: nil,
				},
				q: db,
			},
		},
		{
			name:      "updatable time",
			wantQuery: `UPDATE "with_update_test1" SET "e" = ?  `,
			wantArgs: []driver.Value{
				eTime,
			},
			args: args{
				search: withUpdateTest1{
					A: nil,
					B: nil,
					E: &eTime,
				},
				q: db,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdb.SetExecWithArgsFunc(func(query string, args []driver.Value) (driver.Result, error) {
				if !reflect.DeepEqual(args, tt.wantArgs) {
					t.Errorf("WithSearch() Args = %v, want %v", args, tt.wantArgs)
				}
				if query != tt.wantQuery {
					t.Errorf("WithSearch() Query = %v, want %v", query, tt.wantQuery)
				}
				return testdb.NewResult(1, nil, 1, nil), nil
			})

			err := WithUpdate(tt.args.search, tt.args.q.Model(&withUpdateTest1{})).Error
			if err != nil {
				t.Errorf("WithSearch() err = %v", err)
			}
		})
	}
}
