package dbutil

import (
	"reflect"
	"sort"
)

// 判断一个字段是否是sortable字段
func IsSortableField(t reflect.Type, name string) bool {
	for i := 0; i < t.NumField(); i++ {
		if t.Field(i).Name == name {
			switch t.Field(i).Type.Kind() {
			case reflect.Struct, reflect.Slice, reflect.Map, reflect.Func, reflect.Chan,
				reflect.Array, reflect.Uintptr, reflect.Bool, reflect.Interface:
				return false
			}
			return true
		}
	}
	return false
}

// SortByField按照数据库的结构体字段对slice进行排序
// 如果orderBy对应的是struct，则需要调用方提供传入lessFunc，否则通过反射解析字段，
// 因为涉及到slice内部数据的写入，所以slicePtr参数必须是指向Slice的指针，否则会导致Panic
func SortByField(slicePtr interface{}, orderBy string, order DBOrder, pageNo int64, pageSize int64, lessFunc ...func(i, j interface{}) bool) {
	// data must be a pointer to slice
	p := reflect.TypeOf(slicePtr).Kind()
	if p != reflect.Ptr {
		panic("slicePtr must be pointer")
	}
	data := reflect.ValueOf(slicePtr).Elem().Interface()
	k := reflect.TypeOf(data).Kind()
	orderFieldIndex := -1
	var orderFieldKind reflect.Kind
	switch k {
	case reflect.Slice:
		s := reflect.ValueOf(data)
		eleType := reflect.TypeOf(data).Elem()
		if s.Len() == 0 {
			return
		}
		if lessFunc != nil && len(lessFunc) == 1 && lessFunc[0] != nil {
			sort.Slice(data, func(i, j int) bool {
				vi := reflect.ValueOf(data).Index(i).Interface()
				vj := reflect.ValueOf(data).Index(j).Interface()
				less := false
				less = lessFunc[0](vi, vj)
				if order == DBOrderDESC {
					return !less
				}
				return less
			})
		} else {
			if !IsSortableField(eleType, orderBy) {
				return
			}
			// use first element as template
			v := s.Index(0)
			for i := 0; i < v.NumField(); i++ {
				ft := eleType.Field(i)
				if ft.Name == orderBy {
					orderFieldIndex = i
					orderFieldKind = v.Field(i).Kind()
					break
				}
			}
			// exit directly if can not find the field
			if orderFieldIndex == -1 {
				return
			}
			sort.Slice(data, func(i, j int) bool {
				vi := reflect.ValueOf(data).Index(i)
				vj := reflect.ValueOf(data).Index(j)
				var fi, fj reflect.Value
				switch orderFieldKind {
				case reflect.Struct:
					return false
				case reflect.Ptr:
					fi = vi.Field(orderFieldIndex).Elem()
					fj = vj.Field(orderFieldIndex).Elem()
				default:
					fi = vi.Field(orderFieldIndex)
					fj = vj.Field(orderFieldIndex)
				}
				less := false
				switch fi.Kind() {
				case reflect.String, reflect.Invalid:
					less = fi.String() < fj.String()
				case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
					less = fi.Uint() < fj.Uint()
				case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
					less = fi.Int() < fj.Int()
				case reflect.Float32, reflect.Float64:
					less = fi.Float() < fj.Float()
				default:
					return false
				}
				if order == DBOrderDESC {
					return !less
				}
				return less
			})
		}

		pageData := reflect.MakeSlice(reflect.SliceOf(eleType), 0, int(pageSize))
		offset := pageSize * (pageNo - 1)
		if offset > int64(s.Len()-1) { // return empty page data
			n := reflect.Copy(s, pageData)
			reflect.ValueOf(slicePtr).Elem().SetLen(n)
			return
		}
		limit := offset + pageSize
		if limit > int64(s.Len()) {
			limit = int64(s.Len())
		}
		for i := offset; i < limit; i++ {
			pageData = reflect.Append(pageData, s.Index(int(i)))
		}
		n := reflect.Copy(s, pageData)
		reflect.ValueOf(slicePtr).Elem().SetLen(n)
		return
	default:
		panic("slicePtr must be pointer to a slice")
	}
}
