package dbutil

import (
	"errors"
	"reflect"
	"strings"

	"github.com/jinzhu/gorm"
)

const UtilTag = "dbutil"
const gormTag = "gorm"
const columnTag = "column"

var UsedBeforeLoadErr error

func init() {
	UsedBeforeLoadErr = errors.New("used before load")
}

type DB struct {
	*gorm.DB
}

func NewDB(db *gorm.DB) *DB {
	return &DB{
		DB: db,
	}
}

func (b *DB) WithModel(m interface{}) *DB {
	return &DB{
		DB: b.DB.Model(m),
	}
}

func (b *DB) WithSearch(search interface{}) *DB {
	return &DB{
		DB: WithSearch(search, b.DB),
	}
}

func (b *DB) WithUpdate(update interface{}) *DB {
	return &DB{
		DB: WithUpdate(update, b.DB),
	}
}

func (b *DB) WithOrder(t reflect.Type, orderBy string, order DBOrder) *DB {
	return &DB{
		DB: WithOrder(t, orderBy, order, b.DB),
	}
}

// BeginWithLogger 开启事务，便于追踪操作数据库的日志
func (b *DB) BeginWithLogger(logger gorm.Logger) *gorm.DB {
	tx := b.Begin()
	tx.SetLogger(logger)
	return tx
}

func parseSubTag(f reflect.StructField, subTagKey string) (string, bool) {
	return parseSubTagWithSpecifiedParent(f, UtilTag, subTagKey)
}

func parseSubTagWithSpecifiedParent(f reflect.StructField, tagKey, subTagKey string) (string, bool) {
	tag, ok := f.Tag.Lookup(tagKey)
	if !ok {
		return "", ok
	}
	subTags := strings.Split(tag, ",")
	for _, subTag := range subTags {
		k := strings.Split(strings.TrimSpace(subTag), ":")
		if k[0] == subTagKey {
			if len(k) == 2 {
				return strings.TrimSpace(k[1]), true
			}
			return "", true
		}
	}
	return "", false
}
