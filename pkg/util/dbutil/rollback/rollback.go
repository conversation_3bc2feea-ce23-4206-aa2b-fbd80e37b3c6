package rollback

import (
	"bytes"
	"runtime"
	"strings"

	"github.com/jinzhu/gorm"
	"github.com/labstack/echo/v4"
	csmError "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

// Rollback 根据传入的runErr和r决定是否执行回滚
// 如果参数runErr内部错误不为空，那么返回值为回滚结果，回滚成功则为nil
// 如果程序出现panic导致回滚，如果回滚失败，返回值为回滚错误；如果回滚成功，返回值为封装了panic的error
func Rollback(ctx context.CsmContext, tx *gorm.DB, runErr error, r interface{}) error {
	if r != nil {
		ctx.CsmLogger().Warnf("Rollback with panic: %v", r)
		stack := make([]byte, 8<<10)
		length := runtime.Stack(stack, false)
		// 在一行内打印panic信息, <lf> 表示换行
		withoutNewLine := strings.Replace(bytes.NewBuffer(stack[:length]).String(), "\n", "<lf>", -1)
		ctx.CsmLogger().Errorf("[PANIC RECOVER] %v %s\n", r, withoutNewLine)
		err := tx.Rollback().Error
		if err != nil {
			ctx.CsmLogger().Warnf("Rollback failed with error: %v", err)
			return csmError.NewDBOperationException(err)
		}
		return csmError.NewServiceException("service running exception", err)
	} else if runErr != nil {
		var internal error
		switch he := runErr.(type) {
		case *echo.HTTPError:
			// there may be root causes from internal
			if he.Internal != nil {
				internal = he.Internal
			}
		}
		var internalMsg string
		if internal != nil {
			internalMsg = internal.Error()
		}
		logFields := csmlog.Fields{
			"errmsg":   runErr.Error(),
			"internal": internalMsg,
		}
		ctx.CsmLogger().WithFields(logFields).Warnf("Rollback since error")
		err := tx.Rollback().Error
		if err != nil {
			ctx.CsmLogger().Warnf("Rollback failed with error: %v", err)
			return csmError.NewDBOperationException(err)
		}
		return nil
	}
	return nil
}
