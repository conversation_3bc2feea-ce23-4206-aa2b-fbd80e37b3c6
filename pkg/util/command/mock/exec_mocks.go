// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockExecInterface is a mock of ExecInterface interface.
type MockExecInterface struct {
	ctrl     *gomock.Controller
	recorder *MockExecInterfaceMockRecorder
}

// MockExecInterfaceMockRecorder is the mock recorder for MockExecInterface.
type MockExecInterfaceMockRecorder struct {
	mock *MockExecInterface
}

// NewMockExecInterface creates a new mock instance.
func NewMockExecInterface(ctrl *gomock.Controller) *MockExecInterface {
	mock := &MockExecInterface{ctrl: ctrl}
	mock.recorder = &MockExecInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExecInterface) EXPECT() *MockExecInterfaceMockRecorder {
	return m.recorder
}

// Exec mocks base method.
func (m *MockExecInterface) Exec(ctx context.CsmContext, cmd string) ([]byte, []byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Exec", ctx, cmd)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Exec indicates an expected call of Exec.
func (mr *MockExecInterfaceMockRecorder) Exec(ctx, cmd interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exec", reflect.TypeOf((*MockExecInterface)(nil).Exec), ctx, cmd)
}
