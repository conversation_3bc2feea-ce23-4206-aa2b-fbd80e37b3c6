package command

import (
	"bytes"
	"context"
	"fmt"
	"os/exec"
	"strings"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

const (
	eksIgnore = "protected-crd"
)

// ExecCmdOut 执行 bash 命令, 后续挪到 interface(todo)
func ExecCmdOut(cc csmContext.CsmContext, cmdStr string) ([]byte, []byte, error) {
	cc.CsmLogger().Infof("command %s", cmdStr)
	ctx, cancel := context.WithTimeout(context.Background(), constants.CommandTimeOut)
	// 超时控制
	defer cancel()
	cmd := exec.CommandContext(ctx, "bash", "-c", cmdStr)
	var stdout, stderr bytes.Buffer
	// 标准输出
	cmd.Stdout = &stdout
	// 标准错误
	cmd.Stderr = &stderr
	if err := cmd.Start(); err != nil {
		errStr := stderr.Bytes()
		outStr := stdout.Bytes()
		info := fmt.Sprintf("Start() exec command: %s, outStr: %s, errString: %s, error: %v",
			cmdStr, outStr, errStr, err)
		// todo better way for removing crd in eks
		if len(errStr) > 0 && strings.Contains(string(errStr), eksIgnore) {
			cc.CsmLogger().Infof("[eks] the logs out:[%s] err:[%s]", string(outStr), string(errStr))
			return outStr, errStr, nil
		}
		cc.CsmLogger().Errorf(info)
		return nil, nil, fmt.Errorf("%v", info)
	}

	if err := cmd.Wait(); err != nil {
		errStr := stderr.Bytes()
		outStr := stdout.Bytes()
		info := fmt.Sprintf("Wait() exec command: %s, outStr: %s, errString: %s, error %v",
			cmdStr, outStr, errStr, err)
		// todo better way for removing crd in eks
		if len(errStr) > 0 && strings.Contains(string(errStr), eksIgnore) {
			cc.CsmLogger().Infof("[eks] the logs out:[%s] err:[%s]", string(outStr), string(errStr))
			return outStr, errStr, nil
		}
		cc.CsmLogger().Errorf(info)
		return nil, nil, fmt.Errorf("%v", info)
	}
	outStr, errStr := stdout.Bytes(), stderr.Bytes()
	cc.CsmLogger().Debugf("the log of executing command:[%s], out:[%s] err:[%s]", cmdStr, string(outStr), string(errStr))
	return outStr, errStr, nil
}
