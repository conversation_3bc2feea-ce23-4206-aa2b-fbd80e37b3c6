package jwt

import (
	"testing"

	"k8s.io/client-go/kubernetes/fake"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestDetectSupportedJWTPolicy(t *testing.T) {
	cli := fake.NewSimpleClientset()
	cli.Resources = []*metav1.APIResourceList{}
	t.Run("first-party-jwt", func(t *testing.T) {
		res, err := detectSupportedJWTPolicy(cli)
		if err != nil {
			t.Fatal(err)
		}
		if res != FirstPartyJWT {
			t.Fatalf("unexpected jwt type, expected %s, got %s", FirstPartyJWT, res)
		}
	})
	cli.Resources = []*metav1.APIResourceList{
		{
			APIResources: []metav1.APIResource{{Name: "serviceaccounts/token"}},
		},
	}
	t.Run("third-party-jwt", func(t *testing.T) {
		res, err := detectSupportedJWTPolicy(cli)
		if err != nil {
			t.Fatal(err)
		}
		if res != ThirdPartyJWT {
			t.Fatalf("unexpected jwt type, expected %s, got %s", ThirdPartyJWT, res)
		}
	})
}
