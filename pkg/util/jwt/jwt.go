package jwt

import (
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/discovery"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

type JWTPolicy string

const (
	FirstPartyJWT JWTPolicy = "first-party-jwt"
	ThirdPartyJWT JWTPolicy = "third-party-jwt"
)

// SupportedJWTPolicy return jwt policy
func SupportedJWTPolicy(client kubernetes.Interface) bool {
	policy, err := detectSupportedJWTPolicy(client)
	if err != nil {
		csmlog.Warnf("detectSupportedJWTPolicy error %v", err)
		return false
	}
	csmlog.Infof("detectSupportedJWTPolicy is %s", string(policy))
	if ThirdPartyJWT == policy {
		return true
	}
	return false
}

// detectSupportedJWTPolicy queries the api-server to detect whether it has TokenRequest support
func detectSupportedJWTPolicy(client kubernetes.Interface) (JWTPolicy, error) {
	_, s, err := client.Discovery().ServerGroupsAndResources()
	// This may fail if any api service is down. We should only fail if the specific API we care about failed
	if err != nil {
		if discovery.IsGroupDiscoveryFailedError(err) {
			derr := err.(*discovery.ErrGroupDiscoveryFailed)
			if _, f := derr.Groups[schema.GroupVersion{Group: "authentication.k8s.io", Version: "v1"}]; f {
				return "", err
			}
		} else {
			return "", err
		}
	}
	for _, res := range s {
		for _, api := range res.APIResources {
			// Appearance of this API indicates we do support third party jwt token
			if api.Name == "serviceaccounts/token" {
				return ThirdPartyJWT, nil
			}
		}
	}
	return FirstPartyJWT, nil
}
