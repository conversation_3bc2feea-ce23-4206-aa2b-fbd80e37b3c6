package object

import (
	"fmt"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"

	admitv1 "k8s.io/api/admissionregistration/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestGetIstioCtlWithNamespace(t *testing.T) {
	namespace := "istio-system"
	cmd := "test-cmd"
	expect := cmd + fmt.Sprintf(" --istioNamespace %s", namespace)
	got := GetIstioCtlWithNamespace(namespace, cmd)
	assert.Equal(t, got, expect)
}

func TestGetNameWithDiscoverySelectorLabels(t *testing.T) {
	data := []struct {
		desc     string
		baseName string
		labels   map[string]string
		value    string
	}{
		{
			"valid-name-labels",
			"istio-system",
			map[string]string{
				"user": "test01",
			},
			"istio-system-test01",
		},
		{
			"valid-name-labels-2",
			"istio-system",
			nil,
			"istio-system",
		},
	}
	for _, tt := range data {
		t.Run(tt.desc, func(t *testing.T) {
			got := GetNameWithDiscoverySelectorLabels(tt.baseName, tt.labels)
			if got != tt.value {
				t.Errorf("expect %s,but got %s", tt.value, got)
			}
		})
	}
}

func TestUpdateValidatingWebhookConfigurationNamespaceSelector(t *testing.T) {
	vw := admitv1.ValidatingWebhook{
		Name: "rev.validation.istio.io",
		ClientConfig: admitv1.WebhookClientConfig{
			URL: nil,
			Service: &admitv1.ServiceReference{
				Namespace: "istio-system",
				Name:      "istiod",
			},
			CABundle: []byte("xxx"),
		},
		NamespaceSelector:       nil,
		AdmissionReviewVersions: []string{"v1beta1", "v1"},
	}
	vwc := admitv1.ValidatingWebhookConfiguration{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "istio-validator-istio-system",
			Labels: map[string]string{"app": "istiod"},
		},
		Webhooks: []admitv1.ValidatingWebhook{vw},
	}

	vw2 := vw.DeepCopy()

	lsr1 := metav1.LabelSelectorRequirement{
		Key:      "user1",
		Operator: metav1.LabelSelectorOpIn,
		Values:   []string{"test01"},
	}
	vw2.NamespaceSelector = &metav1.LabelSelector{
		MatchExpressions: []metav1.LabelSelectorRequirement{lsr1},
	}

	vwc2 := admitv1.ValidatingWebhookConfiguration{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "istio-validator-istio-system",
			Labels: map[string]string{"app": "istiod"},
		},
		Webhooks: []admitv1.ValidatingWebhook{*vw2},
	}

	vw3 := vw.DeepCopy()
	lsr2 := metav1.LabelSelectorRequirement{
		Key:      "user2",
		Operator: metav1.LabelSelectorOpIn,
		Values:   []string{"test02"},
	}
	vw3.NamespaceSelector = &metav1.LabelSelector{
		MatchExpressions: []metav1.LabelSelectorRequirement{lsr1, lsr2},
	}
	vwc3 := admitv1.ValidatingWebhookConfiguration{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "istio-validator-istio-system",
			Labels: map[string]string{"app": "istiod"},
		},
		Webhooks: []admitv1.ValidatingWebhook{*vw3},
	}

	cases := []struct {
		desc   string
		labels map[string]string
		vwc    admitv1.ValidatingWebhookConfiguration
		expect admitv1.ValidatingWebhookConfiguration
	}{
		{
			"valid-nil-update-ValidatingWebhookConfiguration",
			map[string]string{"user1": "test01"},
			vwc,
			vwc2,
		},
		{
			"valid-update-ValidatingWebhookConfiguration",
			map[string]string{"user2": "test02"},
			vwc2,
			vwc3,
		},
	}

	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			res := UpdateValidatingWebhookConfigurationNamespaceSelector(tt.vwc, tt.labels)
			if !reflect.DeepEqual(res, tt.expect) {
				t.Errorf("got %v, want: %v", res, tt.expect)
			}
		})
	}
}

func TestUpdateValidatingWebhookConfigurationsNamespaceSelector(t *testing.T) {
	lsr1 := metav1.LabelSelectorRequirement{
		Key:      "app",
		Operator: metav1.LabelSelectorOpIn,
		Values:   []string{"istio"},
	}
	lsr2 := metav1.LabelSelectorRequirement{
		Key:      "app",
		Operator: metav1.LabelSelectorOpIn,
		Values:   []string{"istio"},
	}

	namespaceSelector1 := &metav1.LabelSelector{MatchExpressions: []metav1.LabelSelectorRequirement{lsr1}}
	vw := admitv1.ValidatingWebhook{
		Name: "rev.validation.istio.io",
		ClientConfig: admitv1.WebhookClientConfig{
			URL: nil,
			Service: &admitv1.ServiceReference{
				Namespace: "istio-system",
				Name:      "istiod",
			},
			CABundle: []byte("xxx"),
		},
		NamespaceSelector:       namespaceSelector1,
		AdmissionReviewVersions: []string{"v1beta1", "v1"},
	}
	vw2 := *vw.DeepCopy()
	namespaceSelector2 := &metav1.LabelSelector{MatchExpressions: []metav1.LabelSelectorRequirement{lsr2}}
	vw2.NamespaceSelector = namespaceSelector2

	vwcs := []admitv1.ValidatingWebhookConfiguration{{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "istio-validator-istio-system",
			Labels: map[string]string{"app": "istiod"},
		},
		Webhooks: []admitv1.ValidatingWebhook{vw, vw2},
	}}

	lsr3 := metav1.LabelSelectorRequirement{
		Key:      "user1",
		Operator: metav1.LabelSelectorOpIn,
		Values:   []string{"test01"},
	}
	namespaceSelectorExpect1 := &metav1.LabelSelector{MatchExpressions: []metav1.LabelSelectorRequirement{lsr1, lsr3}}
	vw.NamespaceSelector = namespaceSelectorExpect1
	namespaceSelectorExpect2 := &metav1.LabelSelector{MatchExpressions: []metav1.LabelSelectorRequirement{lsr2, lsr3}}
	vw2.NamespaceSelector = namespaceSelectorExpect2

	vwcsExpect := []admitv1.ValidatingWebhookConfiguration{{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "istio-validator-istio-system",
			Labels: map[string]string{"app": "istiod"},
		},
		Webhooks: []admitv1.ValidatingWebhook{vw, vw2},
	}}

	cases := []struct {
		desc   string
		labels map[string]string
		vwc    []admitv1.ValidatingWebhookConfiguration
		expect []admitv1.ValidatingWebhookConfiguration
	}{
		{
			"valid-UpdateValidatingWebhookConfigurationsNamespaceSelector",
			map[string]string{"user1": "test01"},
			vwcs,
			vwcsExpect,
		},
	}

	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			res := UpdateValidatingWebhookConfigurationsNamespaceSelector(tt.vwc, tt.labels)
			if !reflect.DeepEqual(res, tt.expect) {
				t.Errorf("got %v, want: %v", res, tt.expect)
			}
		})
	}

}

func TestUpdateMutatingWebhookConfigurationsNamespaceSelector(t *testing.T) {
	lsr1 := metav1.LabelSelectorRequirement{
		Key:      "app",
		Operator: metav1.LabelSelectorOpIn,
		Values:   []string{"istio"},
	}
	lsr2 := metav1.LabelSelectorRequirement{
		Key:      "app",
		Operator: metav1.LabelSelectorOpIn,
		Values:   []string{"istio"},
	}

	namespaceSelector1 := &metav1.LabelSelector{MatchExpressions: []metav1.LabelSelectorRequirement{lsr1}}
	vw := admitv1.MutatingWebhook{
		Name: "rev.validation.istio.io",
		ClientConfig: admitv1.WebhookClientConfig{
			URL: nil,
			Service: &admitv1.ServiceReference{
				Namespace: "istio-system",
				Name:      "istiod",
			},
			CABundle: []byte("xxx"),
		},
		NamespaceSelector:       namespaceSelector1,
		AdmissionReviewVersions: []string{"v1beta1", "v1"},
	}
	vw2 := *vw.DeepCopy()
	namespaceSelector2 := &metav1.LabelSelector{MatchExpressions: []metav1.LabelSelectorRequirement{lsr2}}
	vw2.NamespaceSelector = namespaceSelector2

	vwcs := []admitv1.MutatingWebhookConfiguration{{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "istio-validator-istio-system",
			Labels: map[string]string{"app": "istiod"},
		},
		Webhooks: []admitv1.MutatingWebhook{vw, vw2},
	}}

	lsr3 := metav1.LabelSelectorRequirement{
		Key:      "user1",
		Operator: metav1.LabelSelectorOpIn,
		Values:   []string{"test01"},
	}
	namespaceSelectorExpect1 := &metav1.LabelSelector{MatchExpressions: []metav1.LabelSelectorRequirement{lsr1, lsr3}}
	vw.NamespaceSelector = namespaceSelectorExpect1
	namespaceSelectorExpect2 := &metav1.LabelSelector{MatchExpressions: []metav1.LabelSelectorRequirement{lsr2, lsr3}}
	vw2.NamespaceSelector = namespaceSelectorExpect2

	vwcsExpect := []admitv1.MutatingWebhookConfiguration{{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "istio-validator-istio-system",
			Labels: map[string]string{"app": "istiod"},
		},
		Webhooks: []admitv1.MutatingWebhook{vw, vw2},
	}}

	cases := []struct {
		desc   string
		labels map[string]string
		vwc    []admitv1.MutatingWebhookConfiguration
		expect []admitv1.MutatingWebhookConfiguration
	}{
		{
			"valid-UpdateValidatingWebhookConfigurationsNamespaceSelector",
			map[string]string{"user1": "test01"},
			vwcs,
			vwcsExpect,
		},
	}

	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			res := UpdateMutatingWebhookConfigurationsNamespaceSelector(tt.vwc, tt.labels)
			if !reflect.DeepEqual(res, tt.expect) {
				t.Errorf("got %v, want: %v", res, tt.expect)
			}
		})
	}
}
