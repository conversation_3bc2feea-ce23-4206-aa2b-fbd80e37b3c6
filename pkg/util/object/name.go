package object

const (
	ClusterRoleStr = "ClusterRole"

	ClusterRoleBindingStr = "ClusterRoleBinding"

	MeshPolicyStr = "MeshPolicy"
)

// Istio API Group Names
const (
	AuthenticationAPIGroupName = "authentication.istio.io"

	ConfigAPIGroupName = "config.istio.io"

	NetworkingAPIGroupName = "networking.istio.io"

	SecurityAPIGroupName = "security.istio.io"
)

const (
	MutatingWebhookConfigurationStr = "MutatingWebhookConfiguration"

	ValidatingWebhookConfigurationStr = "ValidatingWebhookConfiguration"

	CustomResourceDefinitionStr = "apiextensions.k8s.io/CustomResourceDefinition"
)

const (
	ValidatingWebhookConfigurationGroupKindStr = "admissionregistration.k8s.io/" + ValidatingWebhookConfigurationStr

	MutatingWebhookConfigurationGroupKindStr = "admissionregistration.k8s.io/" + MutatingWebhookConfigurationStr

	EnvoyFilterNetworkingAPIGroupName = NetworkingAPIGroupName + "/EnvoyFilter"
)
