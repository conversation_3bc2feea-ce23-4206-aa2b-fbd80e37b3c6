package object

import (
	"reflect"
	"strings"
	"testing"

	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"

	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
)

var (
	mockCtx, _ = ctxCsm.NewCsmContextMock()

	manifestServiceYaml = `apiVersion: v1
kind: Service
metadata:
  name: istiod
  namespace: istio-system-csm-test01
  labels:
    app: istiod
spec:
  ports:
    - port: 15010
      name: grpc-xds # plaintext
      protocol: TCP
  selector:
    app: istiod`

	manifestHorizontalPodAutoscalerYaml = `apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  labels:
    app: istiod
  name: istiod
  namespace: istio-system-csm-test01
spec:
  maxReplicas: 5
  metrics:
  - resource:
      name: cpu
      targetAverageUtilization: 80
    type: Resource
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: istiod`

	manifestRoleYaml = `apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: istiod-istio-system-csm-test01
  namespace: istio-system-csm-test01
  labels:
    app: istiod
rules:
- apiGroups: ["networking.istio.io"]
  verbs: ["create"]
  resources: ["gateways"]
- apiGroups: [""]
  resources: ["secrets"]
  # TODO lock this down to istio-ca-cert if not using the DNS cert mesh config
  verbs: ["create", "get", "watch", "list", "update", "delete"]`

	errYaml = `apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: istiod-istio-system-csm-test01
  namespace: istio-system-csm-test01xxxxx
xxxx`

	testValidatingWebhookConfigurationYaml = `apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  labels:
    app: istiod
    install.operator.istio.io/owning-resource-namespace: istio-system
    istio: istiod
    istio.io/rev: default
    operator.istio.io/component: Pilot
    operator.istio.io/managed: Reconcile
    operator.istio.io/version: 1.13.2
    release: istio
  name: istio-validator-istio-system
webhooks:
- admissionReviewVersions:
  - v1beta1
  - v1
  clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUZDekNDQXZPZ0F3SUJBZ0lKQUxkaWVKbEEySzE0TUEwR0NTcUdTSWIzRFFFQkJRVUFNQ0l4RGpBTUJnTlYKQkFvTUJVbHpkR2x2TVJBd0RnWURWUVFEREFkU2IyOTBJRU5CTUNBWERUSXlNRFF4T1RBMk1ETTFPRm9ZRHpJeApNakl3TXpJMk1EWXdNelU0V2pBaU1RNHdEQVlEVlFRS0RBVkpjM1JwYnpFUU1BNEdBMVVFQXd3SFVtOXZkQ0JEClFUQ0NBaUl3RFFZSktvWklodmNOQVFFQkJRQURnZ0lQQURDQ0Fnb0NnZ0lCQU5DVFNOS2Q4cWNOcExWaU1zWFEKVkFtQ0ZOajh5VzJCams4TjhkMTNZUE1RU3pEdkplekNGUWhoVFJMcHVIUDh5bmYvclV4MTBlVldpWUZQdEJVYwpTcmNwbmJRbHZ1STZnWHJST0kxcks5SkpQMXFEY3QxSFZ2ZVpaYmFMeU1HME9NbVFINzc2U3lhQlNYc1J0UExBCnYwZURQT3R5M3lFSEJ4WTFKOHZ1SlhxNHQrSGN5YS9sYjhYdmpvN1EyWHphLzdrdExpRHZTall0YmZEVUdTeEkKUXJjcGplR1Fyb1g2cmhrUlUwWUFCUGQ0QTVqMmVCMVlvbFg0TkRaUmRFdzh5anEveWVuRDc3OHZ3UEJYS0ZyVwpWRjVEWDVudlVZYndINUczdURBSmUzV2cwUTRNNGxTUXdDbEFDRlI5SXR3NnNmVjZWUFBaVHNtZGlnSUdCOTVYCnhSY0ZiemtWclRLTFpLdkpyVTM0NjRtb3BiTGo3SzQ2VHVuYnc4M0poMm01WGxmeDhyTlpSdWFQMUpGVWRkdGcKUVloWDU5SEM0UXVkSmU3WE9OaW1yWUtLbXZ0aHpYR3pzaW9abFJoRWYrZDAyUG1vemVQUDhrWWUrcFM1UEZvYwpkamdtRU1mL3Bsci9LM2xZUkFUZ0VqakMxbGFncU5VNmJSUWVqSWR0eWsyOW0xakVHVENWaHc3UlYxQWJFdEJZCmF6VFFWSlRNVGRuTlUzNzlmNzFRZGxBMCtsWG9VZmxsNi9qSzJvUm5OdjhvZWZXVzAxSjFyekNPdUpOMThMWHkKOTJUbkloUkpuR3RoSUVkNEFNYnZDbE5yb05rY1hIdU9nb1YrcTl3VnlVZWpGQXBhY21JQU42NndJZWtheW9rRwo1OXhkVit0YjFIM2RJUnJGcXVrYzFkQVZBZ01CQUFHalFqQkFNQjBHQTFVZERnUVdCQlNzVHhnRVNMaDRRaEVtCnlLVThpbjJuTmZmUHR6QVBCZ05WSFJNQkFmOEVCVEFEQVFIL01BNEdBMVVkRHdFQi93UUVBd0lDNURBTkJna3EKaGtpRzl3MEJBUVVGQUFPQ0FnRUFDZ3JqS0JMYWl6eENtd0FtZDNRcEcvTytqb0VQL3VLQXJsT2JIaDhJTjdLcQp4bWVZSEhVL01Mc3QwZGQ3bHQvZUhoTkRRSzZRVzNXNjRrQ3lVYUgxSElKNUcyTkZyQ1BEQnU2d1Y2a2NveEtTCjl4SDlsREVXVlM2aVBmdytyNCs0QnN1RFZWU3U1bFhaVWFVakVvMldCeVpITW5PTmw2c1FNc2FudWhLOGo5dFoKNlJZckJLNExBWVA5OFJWcTVXLzN3eFRVQk9sS3YrK1h5Rk1NK3FDcThXNlBaS0lIdGtpeVB5UjU1WDZmZjJ6ZQprdklLZjc5UW9lbHJzZ3M0dEZsVmVOQ0kyRC9ZQkVxY1hPR2VYVEhsMSttVWZjWEN3UlFqL2pRUHk1L1NRK0FSCklMUWxkVDFDUG1QenRjRkdHS21GdUt3OGwweG9nV2QzS0UvdVg5WmpmLzNINTVrS3hKdWlvMVljWlhWZXNyWEgKd0M5OHRNT1hodS9Fbk9JdllPeVVUVUpBQTJZWHZITTA3NmRZMU44TTMvWVQxRHJJcm1oZFdoY0lnVDNqVWpUTgpKK1FHeUVoS0ZsT0pqM0lpTVpkQXdVSmlSVEsrRlZMZDRHZVJqalhFUzkxYjd0cEkwekg4U0k5ZGxqN3NyNXk1CjlBZ3o2bS9ZQjFzY2ZuVFovSytQcU1DYWR5TnpkR1ovTXo3aVJGUFZQa0ZsclJ5TksyVTRUV29SWWxBMTl2RmEKWjBnT1V6NGtjSnVQaWYzOS9jdUpqcmhxYVlid3lHTlI3WmROZEdNbXBiejFQTHRXNVRmRTFTSHBTcDAyYjZNYQpYTVJBV25vOXovUnp4cjlrVTFhTVN2MjVZeGZBTmF2Nm5RelhjM3kzc0ZvalFKTEM0cWROZVMrSXU4Yk9CZXM9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
    service:
      name: istiod
      namespace: istio-system
      path: /validate
      port: 443
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: rev.validation.istio.io
  namespaceSelector: {}
  objectSelector:
    matchExpressions:
    - key: istio.io/rev
      operator: In
      values:
      - default
  rules:
  - apiGroups:
    - security.istio.io
    - networking.istio.io
    - telemetry.istio.io
    - extensions.istio.io
    apiVersions:
    - '*'
    operations:
    - CREATE
    - UPDATE
    resources:
    - '*'
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
`

	testExpectValidatingWebhookConfigurationYaml = `apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  creationTimestamp: null
  labels:
    app: istiod
    install.operator.istio.io/owning-resource-namespace: istio-system
    istio: istiod
    istio.io/rev: default
    operator.istio.io/component: Pilot
    operator.istio.io/managed: Reconcile
    operator.istio.io/version: 1.13.2
    release: istio
  name: istio-validator-istio-system
webhooks:
- admissionReviewVersions:
  - v1beta1
  - v1
  clientConfig:
    caBundle: 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
    service:
      name: istiod
      namespace: istio-system
      path: /validate
      port: 443
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: rev.validation.istio.io
  namespaceSelector:
    matchExpressions:
    - key: user
      operator: In
      values:
      - test01
  objectSelector:
    matchExpressions:
    - key: istio.io/rev
      operator: In
      values:
      - default
  rules:
  - apiGroups:
    - security.istio.io
    - networking.istio.io
    - telemetry.istio.io
    - extensions.istio.io
    apiVersions:
    - '*'
    operations:
    - CREATE
    - UPDATE
    resources:
    - '*'
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
`

	testMutatingWebhookConfiguration = `apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  creationTimestamp: null
  labels:
    app: sidecar-injector
    install.operator.istio.io/owning-resource: unknown
    install.operator.istio.io/owning-resource-namespace: istio-system
    istio.io/rev: default
    operator.istio.io/component: Pilot
    operator.istio.io/managed: Reconcile
    operator.istio.io/version: 1.13.2
    release: istio
  name: istio-sidecar-injector
webhooks:
- admissionReviewVersions:
  - v1beta1
  - v1
  clientConfig:
    caBundle: 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
    service:
      name: istiod
      namespace: istio-system
      path: /inject
      port: 443
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: rev.namespace.sidecar-injector.istio.io
  namespaceSelector:
    matchLabels:
      istio.io/deactivated: never-match
  objectSelector:
    matchLabels:
      istio.io/deactivated: never-match
  reinvocationPolicy: Never
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
- admissionReviewVersions:
  - v1beta1
  - v1
  clientConfig:
    caBundle: 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
    service:
      name: istiod
      namespace: istio-system
      path: /inject
      port: 443
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: rev.object.sidecar-injector.istio.io
  namespaceSelector:
    matchLabels:
      istio.io/deactivated: never-match
  objectSelector:
    matchLabels:
      istio.io/deactivated: never-match
  reinvocationPolicy: Never
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
- admissionReviewVersions:
  - v1beta1
  - v1
  clientConfig:
    caBundle: 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
    service:
      name: istiod
      namespace: istio-system
      path: /inject
      port: 443
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: namespace.sidecar-injector.istio.io
  namespaceSelector:
    matchLabels:
      istio.io/deactivated: never-match
  objectSelector:
    matchLabels:
      istio.io/deactivated: never-match
  reinvocationPolicy: Never
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
- admissionReviewVersions:
  - v1beta1
  - v1
  clientConfig:
    caBundle: 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
    service:
      name: istiod
      namespace: istio-system
      path: /inject
      port: 443
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: object.sidecar-injector.istio.io
  namespaceSelector:
    matchLabels:
      istio.io/deactivated: never-match
  objectSelector:
    matchLabels:
      istio.io/deactivated: never-match
  reinvocationPolicy: Never
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10`

	testExpectMutatingWebhookConfiguration = `apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  creationTimestamp: null
  labels:
    app: sidecar-injector
    install.operator.istio.io/owning-resource: unknown
    install.operator.istio.io/owning-resource-namespace: istio-system
    istio.io/rev: default
    operator.istio.io/component: Pilot
    operator.istio.io/managed: Reconcile
    operator.istio.io/version: 1.13.2
    release: istio
  name: istio-sidecar-injector
webhooks:
- admissionReviewVersions:
  - v1beta1
  - v1
  clientConfig:
    caBundle: 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
    service:
      name: istiod
      namespace: istio-system
      path: /inject
      port: 443
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: rev.namespace.sidecar-injector.istio.io
  namespaceSelector:
    matchExpressions:
    - key: user
      operator: In
      values:
      - test01
    matchLabels:
      istio.io/deactivated: never-match
  objectSelector:
    matchLabels:
      istio.io/deactivated: never-match
  reinvocationPolicy: Never
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
- admissionReviewVersions:
  - v1beta1
  - v1
  clientConfig:
    caBundle: 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
    service:
      name: istiod
      namespace: istio-system
      path: /inject
      port: 443
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: rev.object.sidecar-injector.istio.io
  namespaceSelector:
    matchExpressions:
    - key: user
      operator: In
      values:
      - test01
    matchLabels:
      istio.io/deactivated: never-match
  objectSelector:
    matchLabels:
      istio.io/deactivated: never-match
  reinvocationPolicy: Never
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
- admissionReviewVersions:
  - v1beta1
  - v1
  clientConfig:
    caBundle: 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
    service:
      name: istiod
      namespace: istio-system
      path: /inject
      port: 443
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: namespace.sidecar-injector.istio.io
  namespaceSelector:
    matchExpressions:
    - key: user
      operator: In
      values:
      - test01
    matchLabels:
      istio.io/deactivated: never-match
  objectSelector:
    matchLabels:
      istio.io/deactivated: never-match
  reinvocationPolicy: Never
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
- admissionReviewVersions:
  - v1beta1
  - v1
  clientConfig:
    caBundle: 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
    service:
      name: istiod
      namespace: istio-system
      path: /inject
      port: 443
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: object.sidecar-injector.istio.io
  namespaceSelector:
    matchExpressions:
    - key: user
      operator: In
      values:
      - test01
    matchLabels:
      istio.io/deactivated: never-match
  objectSelector:
    matchLabels:
      istio.io/deactivated: never-match
  reinvocationPolicy: Never
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
`
)

func TestHash(t *testing.T) {
	hashTests := []struct {
		desc      string
		kind      string
		namespace string
		name      string
		want      string
	}{
		{"CalculateHashForObjectWithNormalCharacter", "Service", "default", "ingressgateway", "Service:default:ingressgateway"},
		{"CalculateHashForObjectWithDash", "Deployment", "istio-system", "istio-pilot", "Deployment:istio-system:istio-pilot"},
		{"CalculateHashForObjectWithDot", "ConfigMap", "istio-system", "my.config", "ConfigMap:istio-system:my.config"},
	}

	for _, tt := range hashTests {
		t.Run(tt.desc, func(t *testing.T) {
			got := Hash(tt.kind, tt.namespace, tt.name)
			if got != tt.want {
				t.Errorf("Hash(%s): got %s for kind %s, namespace %s, name %s, want %s", tt.desc, got, tt.kind, tt.namespace, tt.name, tt.want)
			}
		})
	}
}

func TestFromHash(t *testing.T) {
	hashTests := []struct {
		desc      string
		hash      string
		kind      string
		namespace string
		name      string
	}{
		{"ParseHashWithNormalCharacter", "Service:default:ingressgateway", "Service", "default", "ingressgateway"},
		{"ParseHashForObjectWithDash", "Deployment:istio-system:istio-pilot", "Deployment", "istio-system", "istio-pilot"},
		{"ParseHashForObjectWithDot", "ConfigMap:istio-system:my.config", "ConfigMap", "istio-system", "my.config"},
		{"InvalidHash", "test", "Bad hash string: test", "", ""},
	}

	for _, tt := range hashTests {
		t.Run(tt.desc, func(t *testing.T) {
			k, ns, name := FromHash(tt.hash)
			if k != tt.kind || ns != tt.namespace || name != tt.name {
				t.Errorf("FromHash(%s): got kind %s, namespace %s, name %s, want kind %s, namespace %s, name %s", tt.desc, k, ns, name, tt.kind, tt.namespace, tt.name)
			}
		})
	}
}

func TestHashNameKind(t *testing.T) {
	hashNameKindTests := []struct {
		desc string
		kind string
		name string
		want string
	}{
		{"CalculateHashNameKindForObjectWithNormalCharacter", "Service", "ingressgateway", "Service:ingressgateway"},
		{"CalculateHashNameKindForObjectWithDash", "Deployment", "istio-pilot", "Deployment:istio-pilot"},
		{"CalculateHashNameKindForObjectWithDot", "ConfigMap", "my.config", "ConfigMap:my.config"},
	}

	for _, tt := range hashNameKindTests {
		t.Run(tt.desc, func(t *testing.T) {
			got := HashNameKind(tt.kind, tt.name)
			if got != tt.want {
				t.Errorf("HashNameKind(%s): got %s for kind %s, name %s, want %s", tt.desc, got, tt.kind, tt.name, tt.want)
			}
		})
	}
}

func TestParseJSONToK8sObject(t *testing.T) {
	testDeploymentJSON := `{
	"apiVersion": "apps/v1",
	"kind": "Deployment",
	"metadata": {
		"name": "istio-citadel",
		"namespace": "istio-system",
		"labels": {
			"istio": "citadel"
		}
	},
	"spec": {
		"replicas": 1,
		"selector": {
			"matchLabels": {
				"istio": "citadel"
			}
		},
		"template": {
			"metadata": {
				"labels": {
					"istio": "citadel"
				}
			},
			"spec": {
				"containers": [
					{
						"name": "citadel",
						"image": "docker.io/istio/citadel:1.1.8",
						"args": [
							"--append-dns-names=true",
							"--grpc-port=8060",
							"--grpc-hostname=citadel",
							"--citadel-storage-namespace=istio-system",
							"--custom-dns-names=istio-pilot-service-account.istio-system:istio-pilot.istio-system",
							"--monitoring-port=15014",
							"--self-signed-ca=true"
					  ]
					}
				]
			}
		}
	}
}`
	testPodJSON := `{
	"apiVersion": "v1",
	"kind": "Pod",
	"metadata": {
		"name": "istio-galley-75bcd59768-hpt5t",
		"namespace": "istio-system",
		"labels": {
			"istio": "galley"
		}
	},
	"spec": {
		"containers": [
			{
				"name": "galley",
				"image": "docker.io/istio/galley:1.1.8",
				"command": [
					"/usr/local/bin/galley",
					"server",
					"--meshConfigFile=/etc/mesh-config/mesh",
					"--livenessProbeInterval=1s",
					"--livenessProbePath=/healthliveness",
					"--readinessProbePath=/healthready",
					"--readinessProbeInterval=1s",
					"--deployment-namespace=istio-system",
					"--insecure=true",
					"--validation-webhook-config-file",
					"/etc/config/validatingwebhookconfiguration.yaml",
					"--monitoringPort=15014",
					"--log_output_level=default:info"
				],
				"ports": [
					{
						"containerPort": 443,
						"protocol": "TCP"
					},
					{
						"containerPort": 15014,
						"protocol": "TCP"
					},
					{
						"containerPort": 9901,
						"protocol": "TCP"
					}
				]
			}
		]
	}
}`
	testServiceJSON := `{
	"apiVersion": "v1",
	"kind": "Service",
	"metadata": {
			"labels": {
					"app": "pilot"
			},
			"name": "istio-pilot",
			"namespace": "istio-system"
	},
	"spec": {
			"clusterIP": "*************",
			"ports": [
					{
							"name": "grpc-xds",
							"port": 15010,
							"protocol": "TCP",
							"targetPort": 15010
					},
					{
							"name": "https-xds",
							"port": 15011,
							"protocol": "TCP",
							"targetPort": 15011
					},
					{
							"name": "http-legacy-discovery",
							"port": 8080,
							"protocol": "TCP",
							"targetPort": 8080
					},
					{
							"name": "http-monitoring",
							"port": 15014,
							"protocol": "TCP",
							"targetPort": 15014
					}
			],
			"selector": {
					"istio": "pilot"
			},
			"sessionAffinity": "None",
			"type": "ClusterIP"
	}
}`

	testInvalidJSON := `invalid json`

	parseJSONToK8sObjectTests := []struct {
		desc          string
		objString     string
		wantGroup     string
		wantKind      string
		wantName      string
		wantNamespace string
		wantErr       bool
	}{
		{"ParseJsonToK8sDeployment", testDeploymentJSON, "apps", "Deployment", "istio-citadel", "istio-system", false},
		{"ParseJsonToK8sPod", testPodJSON, "", "Pod", "istio-galley-75bcd59768-hpt5t", "istio-system", false},
		{"ParseJsonToK8sService", testServiceJSON, "", "Service", "istio-pilot", "istio-system", false},
		{"ParseJsonError", testInvalidJSON, "", "", "", "", true},
	}

	for _, tt := range parseJSONToK8sObjectTests {
		t.Run(tt.desc, func(t *testing.T) {
			k8sObj, err := ParseJSONToK8sObject([]byte(tt.objString))
			if err == nil {
				if tt.wantErr {
					t.Errorf("ParseJsonToK8sObject(%s): should be error", tt.desc)
				}
				k8sObjStr := k8sObj.YAMLDebugString()
				if k8sObj.Group != tt.wantGroup {
					t.Errorf("ParseJsonToK8sObject(%s): got group %s for k8s object %s, want %s", tt.desc, k8sObj.Group, k8sObjStr, tt.wantGroup)
				}
				if k8sObj.Kind != tt.wantKind {
					t.Errorf("ParseJsonToK8sObject(%s): got kind %s for k8s object %s, want %s", tt.desc, k8sObj.Kind, k8sObjStr, tt.wantKind)
				}
				if k8sObj.Name != tt.wantName {
					t.Errorf("ParseJsonToK8sObject(%s): got name %s for k8s object %s, want %s", tt.desc, k8sObj.Name, k8sObjStr, tt.wantName)
				}
				if k8sObj.Namespace != tt.wantNamespace {
					t.Errorf("ParseJsonToK8sObject(%s): got group %s for k8s object %s, want %s", tt.desc, k8sObj.Namespace, k8sObjStr, tt.wantNamespace)
				}
			} else if !tt.wantErr {
				t.Errorf("ParseJsonToK8sObject(%s): got unexpected error: %v", tt.desc, err)
			}
		})
	}
}

func TestParseYAMLToK8sObject(t *testing.T) {
	testDeploymentYaml := `apiVersion: apps/v1
kind: Deployment
metadata:
  name: istio-citadel
  namespace: istio-system
  labels:
    istio: citadel
spec:
  replicas: 1
  selector:
    matchLabels:
      istio: citadel
  template:
    metadata:
      labels:
        istio: citadel
    spec:
      containers:
      - name: citadel
        image: docker.io/istio/citadel:1.1.8
        args:
        - "--append-dns-names=true"
        - "--grpc-port=8060"
        - "--grpc-hostname=citadel"
        - "--citadel-storage-namespace=istio-system"
        - "--custom-dns-names=istio-pilot-service-account.istio-system:istio-pilot.istio-system"
        - "--monitoring-port=15014"
        - "--self-signed-ca=true"`

	testPodYaml := `apiVersion: v1
kind: Pod
metadata:
  name: istio-galley-75bcd59768-hpt5t
  namespace: istio-system
  labels:
    istio: galley
spec:
  containers:
  - name: galley
    image: docker.io/istio/galley:1.1.8
    command:
    - "/usr/local/bin/galley"
    - server
    - "--meshConfigFile=/etc/mesh-config/mesh"
    - "--livenessProbeInterval=1s"
    - "--livenessProbePath=/healthliveness"
    - "--readinessProbePath=/healthready"
    - "--readinessProbeInterval=1s"
    - "--deployment-namespace=istio-system"
    - "--insecure=true"
    - "--validation-webhook-config-file"
    - "/etc/config/validatingwebhookconfiguration.yaml"
    - "--monitoringPort=15014"
    - "--log_output_level=default:info"
    ports:
    - containerPort: 443
      protocol: TCP
    - containerPort: 15014
      protocol: TCP
    - containerPort: 9901
      protocol: TCP`

	testServiceYaml := `apiVersion: v1
kind: Service
metadata:
  labels:
    app: pilot
  name: istio-pilot
  namespace: istio-system
spec:
  clusterIP: *************
  ports:
  - name: grpc-xds
    port: 15010
    protocol: TCP
    targetPort: 15010
  - name: https-xds
    port: 15011
    protocol: TCP
    targetPort: 15011
  - name: http-legacy-discovery
    port: 8080
    protocol: TCP
    targetPort: 8080
  - name: http-monitoring
    port: 15014
    protocol: TCP
    targetPort: 15014
  selector:
    istio: pilot
  sessionAffinity: None
  type: ClusterIP`

	parseYAMLToK8sObjectTests := []struct {
		desc          string
		objString     string
		wantGroup     string
		wantKind      string
		wantName      string
		wantNamespace string
	}{
		{"ParseYamlToK8sDeployment", testDeploymentYaml, "apps", "Deployment", "istio-citadel", "istio-system"},
		{"ParseYamlToK8sPod", testPodYaml, "", "Pod", "istio-galley-75bcd59768-hpt5t", "istio-system"},
		{"ParseYamlToK8sService", testServiceYaml, "", "Service", "istio-pilot", "istio-system"},
	}

	for _, tt := range parseYAMLToK8sObjectTests {
		t.Run(tt.desc, func(t *testing.T) {
			k8sObj, err := ParseYAMLToK8sObject([]byte(tt.objString))
			if err != nil {
				k8sObjStr := k8sObj.YAMLDebugString()
				if k8sObj.Group != tt.wantGroup {
					t.Errorf("ParseYAMLToK8sObject(%s): got group %s for k8s object %s, want %s", tt.desc, k8sObj.Group, k8sObjStr, tt.wantGroup)
				}
				if k8sObj.Group != tt.wantGroup {
					t.Errorf("ParseYAMLToK8sObject(%s): got kind %s for k8s object %s, want %s", tt.desc, k8sObj.Kind, k8sObjStr, tt.wantKind)
				}
				if k8sObj.Name != tt.wantName {
					t.Errorf("ParseYAMLToK8sObject(%s): got name %s for k8s object %s, want %s", tt.desc, k8sObj.Name, k8sObjStr, tt.wantName)
				}
				if k8sObj.Namespace != tt.wantNamespace {
					t.Errorf("ParseYAMLToK8sObject(%s): got group %s for k8s object %s, want %s", tt.desc, k8sObj.Namespace, k8sObjStr, tt.wantNamespace)
				}
			}
		})
	}
}

func TestParseK8sObjectsFromYAMLManifest(t *testing.T) {
	testDeploymentYaml := `apiVersion: apps/v1
kind: Deployment
metadata:
  name: istio-citadel
  namespace: istio-system
  labels:
    istio: citadel
spec:
  replicas: 1
  selector:
    matchLabels:
      istio: citadel
  template:
    metadata:
      labels:
        istio: citadel
    spec:
      containers:
      - name: citadel
        image: docker.io/istio/citadel:1.1.8
        args:
        - "--append-dns-names=true"
        - "--grpc-port=8060"
        - "--grpc-hostname=citadel"
        - "--citadel-storage-namespace=istio-system"
        - "--custom-dns-names=istio-pilot-service-account.istio-system:istio-pilot.istio-system"
        - "--monitoring-port=15014"
        - "--self-signed-ca=true"`

	testPodYaml := `apiVersion: v1
kind: Pod
metadata:
  name: istio-galley-75bcd59768-hpt5t
  namespace: istio-system
  labels:
    istio: galley
spec:
  containers:
  - name: galley
    image: docker.io/istio/galley:1.1.8
    command:
    - "/usr/local/bin/galley"
    - server
    - "--meshConfigFile=/etc/mesh-config/mesh"
    - "--livenessProbeInterval=1s"
    - "--livenessProbePath=/healthliveness"
    - "--readinessProbePath=/healthready"
    - "--readinessProbeInterval=1s"
    - "--deployment-namespace=istio-system"
    - "--insecure=true"
    - "--validation-webhook-config-file"
    - "/etc/config/validatingwebhookconfiguration.yaml"
    - "--monitoringPort=15014"
    - "--log_output_level=default:info"
    ports:
    - containerPort: 443
      protocol: TCP
    - containerPort: 15014
      protocol: TCP
    - containerPort: 9901
      protocol: TCP`

	testServiceYaml := `apiVersion: v1
kind: Service
metadata:
  labels:
    app: pilot
  name: istio-pilot
  namespace: istio-system
spec:
  clusterIP: *************
  ports:
  - name: grpc-xds
    port: 15010
    protocol: TCP
    targetPort: 15010
  - name: https-xds
    port: 15011
    protocol: TCP
    targetPort: 15011
  - name: http-legacy-discovery
    port: 8080
    protocol: TCP
    targetPort: 8080
  - name: http-monitoring
    port: 15014
    protocol: TCP
    targetPort: 15014
  selector:
    istio: pilot
  sessionAffinity: None
  type: ClusterIP`

	parseK8sObjectsFromYAMLManifestTests := []struct {
		desc    string
		objsMap map[string]string
	}{
		{
			"FromHybridYAMLManifest",
			map[string]string{
				"Deployment:istio-system:istio-citadel":          testDeploymentYaml,
				"Pod:istio-system:istio-galley-75bcd59768-hpt5t": testPodYaml,
				"Service:istio-system:istio-pilot":               testServiceYaml,
			},
		},
	}

	for _, tt := range parseK8sObjectsFromYAMLManifestTests {
		t.Run(tt.desc, func(t *testing.T) {
			testManifestYaml := strings.Join([]string{testDeploymentYaml, testPodYaml, testServiceYaml}, YAMLSeparator)
			gotK8sObjs, err := ParseK8sObjectsFromYAMLManifest(mockCtx, testManifestYaml)
			if err != nil {
				gotK8sObjsMap := gotK8sObjs.ToMap()
				for objHash, want := range tt.objsMap {
					if gotObj, ok := gotK8sObjsMap[objHash]; ok {
						gotObjYaml := gotObj.YAMLDebugString()
						if !util.IsYAMLEqual(mockCtx, gotObjYaml, want) {
							t.Errorf("ParseK8sObjectsFromYAMLManifest(%s): got:\n%s\n\nwant:\n%s\n", tt.desc, gotObjYaml, want)
						}
					}
				}
			}
		})
	}
}

func TestK8sObject_Equal(t *testing.T) {
	obj1 := K8sObject{
		object: &unstructured.Unstructured{Object: map[string]interface{}{
			"key": "value1",
		}},
	}
	obj2 := K8sObject{
		object: &unstructured.Unstructured{Object: map[string]interface{}{
			"key": "value2",
		}},
	}
	cases := []struct {
		desc string
		o1   *K8sObject
		o2   *K8sObject
		want bool
	}{
		{
			desc: "Equals",
			o1:   &obj1,
			o2:   &obj1,
			want: true,
		},
		{
			desc: "NotEquals",
			o1:   &obj1,
			o2:   &obj2,
			want: false,
		},
		{
			desc: "NilSource",
			o1:   nil,
			o2:   &obj2,
			want: false,
		},
		{
			desc: "NilDest",
			o1:   &obj1,
			o2:   nil,
			want: false,
		},
		{
			desc: "TwoNils",
			o1:   nil,
			o2:   nil,
			want: true,
		},
	}
	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			res := tt.o1.Equal(mockCtx, tt.o2)
			if res != tt.want {
				t.Errorf("got %v, want: %v", res, tt.want)
			}
		})
	}
}

func TestManifestK8sObject(t *testing.T) {
	cases := []struct {
		desc         string
		manifestYaml string
		expect       string
	}{
		{
			desc:         "update-ManifestK8sObject",
			manifestYaml: strings.Join([]string{manifestServiceYaml, manifestHorizontalPodAutoscalerYaml, manifestRoleYaml}, YAMLSeparator),
			expect:       strings.Join([]string{manifestRoleYaml, manifestHorizontalPodAutoscalerYaml, manifestServiceYaml}, YAMLSeparator),
		},
	}
	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			res, err := ManifestK8sObject(mockCtx, tt.manifestYaml)
			resStr := strings.Join(res, YAMLSeparator)
			if err != nil || !reflect.DeepEqual(resStr, tt.expect) {
				t.Errorf("expect: %v, but get %v\n", tt.expect, resStr)
			}
		})
	}
}

func TestGetsK8sObjectWithGroupKind(t *testing.T) {
	cases := []struct {
		desc      string
		kindGroup string
		data      []string
		expects   []string
	}{
		{
			desc:      "getsK8sObjectWithGroupKind",
			kindGroup: "/Service",
			data:      []string{manifestServiceYaml, manifestHorizontalPodAutoscalerYaml, manifestRoleYaml},
			expects:   []string{manifestServiceYaml},
		},
		{
			desc:      "nil-getsK8sObjectWithGroupKind",
			kindGroup: "/xxx",
			data:      []string{manifestServiceYaml, manifestHorizontalPodAutoscalerYaml, manifestRoleYaml},
			expects:   []string{},
		},
		{
			desc:      "error-getsK8sObjectWithGroupKind",
			kindGroup: "/Service",
			data:      []string{errYaml},
			expects:   []string{},
		},
	}
	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			res := GetsK8sObjectWithGroupKind(mockCtx, tt.data, tt.kindGroup)
			if !reflect.DeepEqual(res, tt.expects) {
				t.Errorf("expect: %v, but get %v\n", tt.expects, res)
			}
		})
	}
}

func TestGetsK8sObjectWithExcludeGroupKind(t *testing.T) {
	cases := []struct {
		desc      string
		kindGroup []string
		data      []string
		expects   []string
	}{
		{
			desc:      "getsK8sObjectWithGroupKind",
			kindGroup: []string{"/Service"},
			data:      []string{manifestServiceYaml, manifestHorizontalPodAutoscalerYaml, manifestRoleYaml},
			expects:   []string{manifestHorizontalPodAutoscalerYaml, manifestRoleYaml},
		},
		{
			desc:      "nil-getsK8sObjectWithGroupKind",
			kindGroup: []string{"/xxx"},
			data:      []string{manifestServiceYaml, manifestHorizontalPodAutoscalerYaml, manifestRoleYaml},
			expects:   []string{manifestServiceYaml, manifestHorizontalPodAutoscalerYaml, manifestRoleYaml},
		},
		{
			desc:      "error-getsK8sObjectWithGroupKind",
			kindGroup: []string{"/Service"},
			data:      []string{errYaml},
			expects:   []string{},
		},
	}
	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			res := GetsK8sObjectWithExcludeGroupKind(mockCtx, tt.data, tt.kindGroup)
			if !reflect.DeepEqual(res, tt.expects) {
				t.Errorf("expect: %v, but get %v\n", tt.expects, res)
			}
		})
	}
}

func TestUpdateValidatingWebhookConfigurationWithLabels(t *testing.T) {
	cases := []struct {
		desc     string
		testData string
		labels   map[string]string
		expect   string
		err      error
	}{
		{
			desc:     "UpdateWebhookConfiguration-01",
			labels:   map[string]string{"user": "test01"},
			testData: testValidatingWebhookConfigurationYaml,
			expect:   testExpectValidatingWebhookConfigurationYaml,
			err:      nil,
		},
	}
	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			res, err := UpdateValidatingWebhookConfigurationWithLabels(mockCtx, tt.testData, tt.labels)
			if err != tt.err || res != tt.expect {
				t.Errorf("expect: %v, but get %v\n", tt.expect, res)
			}
		})
	}
}

func TestUpdateMutatingWebhookConfigurationWithLabelsAndMeshInstanceId(t *testing.T) {
	cases := []struct {
		desc           string
		testData       string
		labels         map[string]string
		meshInstanceId string
		expect         string
		err            error
	}{
		{
			desc:           "UpdateWebhookConfiguration-01",
			labels:         map[string]string{"user": "test01"},
			testData:       testValidatingWebhookConfigurationYaml,
			meshInstanceId: "xxxxx",
			expect:         testExpectValidatingWebhookConfigurationYaml,
			err:            nil,
		},
	}
	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			res, err := UpdateMutatingWebhookConfigurationWithLabelsAndMeshInstanceId(mockCtx, tt.testData, tt.labels, tt.meshInstanceId)
			if err != tt.err || res != tt.expect {
				t.Errorf("expect: %v, but get %v\n", tt.expect, res)
			}
		})
	}
}

func TestIstioCustomResources(t *testing.T) {
	cases := []struct {
		desc   string
		data   string
		expect bool
	}{
		{
			desc:   "ok-ConfigAPIGroupName",
			data:   ConfigAPIGroupName,
			expect: true,
		},
		{
			desc:   "ok-ConfigAPIGroupName",
			data:   NetworkingAPIGroupName,
			expect: true,
		},
		{
			desc:   "ok-ConfigAPIGroupName",
			data:   SecurityAPIGroupName,
			expect: true,
		},
		{
			desc:   "ok-ConfigAPIGroupName",
			data:   AuthenticationAPIGroupName,
			expect: true,
		},
		{
			desc:   "false-xxx",
			data:   "xxx",
			expect: false,
		},
	}
	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			res := istioCustomResources(tt.data)
			if res != tt.expect {
				t.Errorf("expect: %v, but get %v\n", tt.expect, res)
			}
		})
	}
}
