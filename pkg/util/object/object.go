package object

import (
	"bufio"
	"bytes"
	"fmt"
	"sort"
	"strings"

	admitv1 "k8s.io/api/admissionregistration/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	apimachinery_runtime "k8s.io/apimachinery/pkg/runtime"
	k8syaml "k8s.io/apimachinery/pkg/util/yaml"
	"sigs.k8s.io/yaml"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/sliceutil"
)

const (
	// YAMLSeparator is a separator for multi-document YAML files.
	YAMLSeparator = "\n---\n"
)

// K8sObject is an in-memory representation of a k8s object, used for moving between different representations
// (Unstructured, JSON, YAML) with cached rendering.
type K8sObject struct {
	object *unstructured.Unstructured

	Group     string
	Kind      string
	Name      string
	Namespace string

	json []byte
	yaml []byte
}

// NewK8sObject creates a new K8sObject and returns a ptr to it.
func NewK8sObject(u *unstructured.Unstructured, json, yaml []byte) *K8sObject {
	o := &K8sObject{
		object: u,
		json:   json,
		yaml:   yaml,
	}

	gvk := u.GetObjectKind().GroupVersionKind()
	o.Group = gvk.Group
	o.Kind = gvk.Kind
	o.Name = u.GetName()
	o.Namespace = u.GetNamespace()

	return o
}

// Valid checks returns true if Kind of K8sObject is not empty.
func (o *K8sObject) Valid() bool {
	return o.Kind != ""
}

// JSON returns a JSON representation of the K8sObject, using an internal cache.
func (o *K8sObject) JSON() ([]byte, error) {
	if o.json != nil {
		return o.json, nil
	}

	b, err := o.object.MarshalJSON()
	if err != nil {
		return nil, err
	}
	return b, nil
}

// Hash returns a unique hash for the K8sObject
func (o *K8sObject) Hash() string {
	return Hash(o.Kind, o.Namespace, o.Name)
}

// Equal returns true if o and other are both valid and equal to each other.
func (o *K8sObject) Equal(ctx csmContext.CsmContext, other *K8sObject) bool {
	if o == nil {
		return other == nil
	}
	if other == nil {
		return o == nil
	}

	ay, err := o.YAML()
	if err != nil {
		return false
	}
	by, err := other.YAML()
	if err != nil {
		return false
	}

	return util.IsYAMLEqual(ctx, string(ay), string(by))
}

// FromHash parses kind, namespace and name from a hash.
func FromHash(hash string) (kind, namespace, name string) {
	hv := strings.Split(hash, ":")
	if len(hv) != 3 {
		return "Bad hash string: " + hash, "", ""
	}
	kind, namespace, name = hv[0], hv[1], hv[2]
	return
}

// HashNameKind returns a unique, insecure hash based on kind and name.
func HashNameKind(kind, name string) string {
	return strings.Join([]string{kind, name}, ":")
}

// Hash returns a unique, insecure hash based on kind, namespace and name.
func Hash(kind, namespace, name string) string {
	switch kind {
	case ClusterRoleStr, ClusterRoleBindingStr, MeshPolicyStr:
		namespace = ""
	}
	return strings.Join([]string{kind, namespace, name}, ":")
}

// YAML returns a YAML representation of the K8sObject, using an internal cache.
func (o *K8sObject) YAML() ([]byte, error) {
	if o == nil {
		return nil, nil
	}
	if o.yaml != nil {
		return o.yaml, nil
	}
	oj, err := o.JSON()
	if err != nil {
		return nil, err
	}
	o.json = oj
	y, err := yaml.JSONToYAML(oj)
	if err != nil {
		return nil, err
	}
	o.yaml = y
	return y, nil
}

// ParseJSONToK8sObject parses JSON to an K8sObject.
func ParseJSONToK8sObject(json []byte) (*K8sObject, error) {
	o, _, err := unstructured.UnstructuredJSONScheme.Decode(json, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("error parsing json into unstructured object: %v", err)
	}

	u, ok := o.(*unstructured.Unstructured)
	if !ok {
		return nil, fmt.Errorf("parsed unexpected type %T", o)
	}

	return NewK8sObject(u, json, nil), nil
}

// ParseYAMLToK8sObject parses YAML to an Object.
func ParseYAMLToK8sObject(yaml []byte) (*K8sObject, error) {
	r := bytes.NewReader(yaml)
	decoder := k8syaml.NewYAMLOrJSONDecoder(r, 1024)

	out := &unstructured.Unstructured{}
	err := decoder.Decode(out)
	if err != nil {
		return nil, fmt.Errorf("error decoding object %v: %v", string(yaml), err)
	}
	return NewK8sObject(out, nil, yaml), nil
}

// YAMLDebugString returns a YAML representation of the K8sObject, or an error string if the K8sObject cannot be rendered to YAML.
func (o *K8sObject) YAMLDebugString() string {
	y, err := o.YAML()
	if err != nil {
		return err.Error()
	}
	return string(y)
}

// UnstructuredObject exposes the raw object, primarily for testing
func (o *K8sObject) UnstructuredObject() *unstructured.Unstructured {
	return o.object
}

// Unstructured exposes the raw object content, primarily for testing
func (o *K8sObject) Unstructured() map[string]interface{} {
	return o.UnstructuredObject().UnstructuredContent()
}

// K8sObjects holds a collection of k8s objects, so that we can filter / sequence them
type K8sObjects []*K8sObject

// UnstructuredItems returns the list of items of unstructured.Unstructured.
func (os K8sObjects) UnstructuredItems() []unstructured.Unstructured {
	var usList []unstructured.Unstructured
	for _, obj := range os {
		usList = append(usList, *obj.UnstructuredObject())
	}
	return usList
}

// ToMap returns a map of K8sObject hash to K8sObject.
func (os K8sObjects) ToMap() map[string]*K8sObject {
	ret := make(map[string]*K8sObject)
	for _, oo := range os {
		if oo.Valid() {
			ret[oo.Hash()] = oo
		}
	}
	return ret
}

// ParseK8sObjectsFromYAMLManifest returns a K8sObjects representation of manifest.
func ParseK8sObjectsFromYAMLManifest(ctx csmContext.CsmContext, manifest string) (K8sObjects, error) {
	return ParseK8sObjectsFromYAMLManifestFailOption(ctx, manifest, true)
}

// ParseK8sObjectsFromYAMLManifestFailOption returns a K8sObjects representation of manifest.
// Continues parsing when a bad object is found if failOnError is set to false.
func ParseK8sObjectsFromYAMLManifestFailOption(ctx csmContext.CsmContext, manifest string,
	failOnError bool) (K8sObjects, error) {
	var b bytes.Buffer

	var yamls []string
	scanner := bufio.NewScanner(strings.NewReader(manifest))
	for scanner.Scan() {
		line := scanner.Text()
		if strings.HasPrefix(line, "---") {
			// yaml separator
			yamls = append(yamls, b.String())
			b.Reset()
		} else {
			if _, err := b.WriteString(line); err != nil {
				return nil, err
			}
			if _, err := b.WriteString("\n"); err != nil {
				return nil, err
			}
		}
	}
	yamls = append(yamls, b.String())

	var objects K8sObjects

	for _, yaml := range yamls {
		yaml = removeNonYAMLLines(yaml)
		if yaml == "" {
			continue
		}
		o, err := ParseYAMLToK8sObject([]byte(yaml))
		if err != nil {
			e := fmt.Errorf("failed to parse YAML to a k8s object: %s", err)
			if failOnError {
				return nil, e
			}
			ctx.CsmLogger().Errorf(err.Error())
			continue
		}
		if o.Valid() {
			objects = append(objects, o)
		}
	}

	return objects, nil
}

func removeNonYAMLLines(yms string) string {
	var b strings.Builder
	for _, s := range strings.Split(yms, "\n") {
		if strings.HasPrefix(s, "#") {
			continue
		}
		b.WriteString(s)
		b.WriteString("\n")
	}

	// helm charts sometimes emits blank objects with just a "disabled" comment.
	return strings.TrimSpace(b.String())
}

// DefaultObjectOrder is default sorting function used to sort k8s objects.
func DefaultObjectOrder() func(o *K8sObject) int {
	return func(o *K8sObject) int {
		gk := o.Group + "/" + o.Kind
		switch {
		// Create CRDs asap - both because they are slow and because we will likely create instances of them soon
		case gk == "apiextensions.k8s.io/CustomResourceDefinition":
			return -1000

			// We need to create ServiceAccounts, Roles before we bind them with a RoleBinding
		case gk == "/ServiceAccount" || gk == "rbac.authorization.k8s.io/ClusterRole":
			return 1
		case gk == "rbac.authorization.k8s.io/ClusterRoleBinding":
			return 2

			// validatingwebhookconfiguration is configured to FAIL-OPEN in the default install. For the
			// re-install case we want to apply the validatingwebhookconfiguration first to reset any
			// orphaned validatingwebhookconfiguration that is FAIL-CLOSE.
		case gk == "admissionregistration.k8s.io/ValidatingWebhookConfiguration":
			return 3

		case istioCustomResources(o.Group):
			return 4

			// Pods might need configmap or secrets - avoid backoff by creating them first
		case gk == "/ConfigMap" || gk == "/Secrets":
			return 100

			// Create the pods after we've created other things they might be waiting for
		case gk == "extensions/Deployment" || gk == "app/Deployment":
			return 1000

			// Autoscalers typically act on a deployment
		case gk == "autoscaling/HorizontalPodAutoscaler":
			return 1001

			// Create services late - after pods have been started
		case gk == "/Service":
			return 10000

		default:
			return 1000
		}
	}
}

func istioCustomResources(group string) bool {
	switch group {
	case ConfigAPIGroupName,
		SecurityAPIGroupName,
		AuthenticationAPIGroupName,
		NetworkingAPIGroupName:
		return true
	}
	return false
}

// Sort will order the items in K8sObjects in order of score, group, kind, name.
// The intent is to have a deterministic ordering in which K8sObjects are applied.
func (os K8sObjects) Sort(score func(o *K8sObject) int) {
	sort.Slice(os, func(i, j int) bool {
		iScore := score(os[i])
		jScore := score(os[j])
		return iScore < jScore ||
			(iScore == jScore &&
				os[i].Group < os[j].Group) ||
			(iScore == jScore &&
				os[i].Group == os[j].Group &&
				os[i].Kind < os[j].Kind) ||
			(iScore == jScore &&
				os[i].Group == os[j].Group &&
				os[i].Kind == os[j].Kind &&
				os[i].Name < os[j].Name)
	})
}

// ManifestK8sObject generate k8sObject []string for manifest string
func ManifestK8sObject(ctx csmContext.CsmContext, manifest string) ([]string, error) {
	objects, err := ParseK8sObjectsFromYAMLManifest(ctx, manifest)
	if err != nil {
		return nil, err
	}
	var output []string
	// For a given group of objects, sort in order to avoid missing dependencies, such as creating CRDs first
	objects.Sort(DefaultObjectOrder())
	for _, obj := range objects {
		yml, yamlErr := obj.YAML()
		if yamlErr != nil {
			return nil, yamlErr
		}
		output = append(output, string(yml))
	}
	return output, nil
}

// GetsK8sObjectWithGroupKind gets objects equal to groupKind
func GetsK8sObjectWithGroupKind(ctx csmContext.CsmContext, objects []string, groupKind string) []string {
	groupKindObjects := make([]string, 0)
	for _, o := range objects {
		obj, parseErr := ParseYAMLToK8sObject([]byte(o))
		if parseErr != nil {
			ctx.CsmLogger().Errorf("ParseYAMLToK8sObject err %v", parseErr)
			return []string{}
		}
		gk := obj.Group + "/" + obj.Kind
		if groupKind == gk {
			groupKindObjects = append(groupKindObjects, o)
		}
	}
	return groupKindObjects
}

// GetsK8sObjectWithExcludeGroupKind gets objects exclude object equal to groupKind
func GetsK8sObjectWithExcludeGroupKind(ctx csmContext.CsmContext, objects []string, groupKind []string) []string {
	var groupKindObjects []string
	for _, o := range objects {
		obj, parseErr := ParseYAMLToK8sObject([]byte(o))
		if parseErr != nil {
			ctx.CsmLogger().Errorf("ParseYAMLToK8sObject err %v", parseErr)
			return []string{}
		}
		gk := obj.Group + "/" + obj.Kind
		if sliceutil.StringContains(groupKind, gk) {
			ctx.CsmLogger().Debugf("exclude gk %s", gk)
			continue
		}
		groupKindObjects = append(groupKindObjects, o)
	}
	return groupKindObjects
}

// UpdateValidatingWebhookConfigurationWithLabels updates validatingWebhook with labels
func UpdateValidatingWebhookConfigurationWithLabels(ctx csmContext.CsmContext, content string,
	labels map[string]string) (string, error) {
	obj, parseErr := ParseYAMLToK8sObject([]byte(content))
	if parseErr != nil {
		return "", parseErr
	}
	gk := obj.Group + "/" + obj.Kind
	ctx.CsmLogger().Infof("start update %s", gk)
	str := ""
	var vwc admitv1.ValidatingWebhookConfiguration
	convertErr := apimachinery_runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Unstructured(), &vwc)
	if convertErr != nil {
		return str, convertErr
	}
	vwc2 := UpdateValidatingWebhookConfigurationNamespaceSelector(vwc, labels)
	vwcStr, vmcError := yaml.Marshal(vwc2)
	if vmcError != nil {
		return str, vmcError
	}
	ctx.CsmLogger().Infof("update %s success", gk)
	str = string(vwcStr)
	return str, nil
}

func UpdateMutatingWebhookConfigurationWithLabelsAndMeshInstanceId(ctx csmContext.CsmContext, content string,
	labels map[string]string, meshInstanceId string) (string, error) {
	obj, parseErr := ParseYAMLToK8sObject([]byte(content))
	if parseErr != nil {
		return "", parseErr
	}
	gk := obj.Group + "/" + obj.Kind
	ctx.CsmLogger().Infof("start update %s", gk)
	str := ""
	var mwc admitv1.MutatingWebhookConfiguration
	ctx.CsmLogger().Infof("start update %s", gk)
	convertErr := apimachinery_runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Unstructured(), &mwc)
	if convertErr != nil {
		return str, convertErr
	}
	mwc2 := UpdateMutatingWebhookConfigurationNamespaceSelector(mwc, labels)
	vmcStr, vmcError := yaml.Marshal(mwc2)
	if vmcError != nil {
		return str, vmcError
	}
	ctx.CsmLogger().Infof("update %s success", gk)
	str = string(vmcStr)
	return str, nil
}
