package object

import (
	"fmt"

	admitv1 "k8s.io/api/admissionregistration/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// GetIstioCtlWithNamespace compose namespace with cmd string
func GetIstioCtlWithNamespace(namespace, cmd string) string {
	return cmd + fmt.Sprintf(" --istioNamespace %s", namespace)
}

// GetNameWithDiscoverySelectorLabels splices baseName with labels
func GetNameWithDiscoverySelectorLabels(baseName string, labels map[string]string) string {
	str := baseName
	if labels == nil {
		return str
	}
	for _, value := range labels {
		str = str + "-" + value
	}
	return str
}

// UpdateValidatingWebhookConfigurationNamespaceSelector updates ValidatingWebhookConfiguration with labels
func UpdateValidatingWebhookConfigurationNamespaceSelector(validatingWebhookConfiguration admitv1.ValidatingWebhookConfiguration,
	labels map[string]string) admitv1.ValidatingWebhookConfiguration {
	labelSelectors := make([]metav1.LabelSelectorRequirement, 0)
	for k, v := range labels {
		labelValue := metav1.LabelSelectorRequirement{
			Key:      k,
			Operator: metav1.LabelSelectorOpIn,
			Values:   []string{v},
		}
		labelSelectors = append(labelSelectors, labelValue)
	}
	return UpdateValidatingWebhookConfiguration(validatingWebhookConfiguration, labelSelectors)
}

// UpdateValidatingWebhookConfigurationsNamespaceSelector updates []ValidatingWebhookConfigurations with labels
func UpdateValidatingWebhookConfigurationsNamespaceSelector(validatingWebhookConfigurations []admitv1.ValidatingWebhookConfiguration,
	labels map[string]string) []admitv1.ValidatingWebhookConfiguration {
	var mwcs []admitv1.ValidatingWebhookConfiguration
	for _, validatingWebhookConfiguration := range validatingWebhookConfigurations {
		mwk := UpdateValidatingWebhookConfigurationNamespaceSelector(validatingWebhookConfiguration, labels)
		mwcs = append(mwcs, mwk)
	}
	return mwcs
}

// UpdateValidatingWebhookConfiguration updates ValidatingWebhookConfiguration with labels
func UpdateValidatingWebhookConfiguration(validatingWebhookConfiguration admitv1.ValidatingWebhookConfiguration,
	labelSelectors []metav1.LabelSelectorRequirement) admitv1.ValidatingWebhookConfiguration {
	webhooks := validatingWebhookConfiguration.Webhooks
	for i, webhook := range webhooks {
		namespaceSelector := webhook.NamespaceSelector
		if namespaceSelector == nil {
			namespaceSelector = new(metav1.LabelSelector)
		}
		namespaceSelector.MatchExpressions = append(namespaceSelector.MatchExpressions, labelSelectors...)
		webhook.NamespaceSelector = namespaceSelector
		webhooks[i] = webhook
	}
	validatingWebhookConfiguration.Webhooks = webhooks
	return validatingWebhookConfiguration
}

// UpdateMutatingWebhookConfigurationNamespaceSelector updates mutatingWebhookConfiguration with labels
func UpdateMutatingWebhookConfigurationNamespaceSelector(mutatingWebhookConfiguration admitv1.MutatingWebhookConfiguration,
	labels map[string]string) admitv1.MutatingWebhookConfiguration {
	labelSelectors := make([]metav1.LabelSelectorRequirement, 0)
	for k, v := range labels {
		labelValue := metav1.LabelSelectorRequirement{
			Key:      k,
			Operator: metav1.LabelSelectorOpIn,
			Values:   []string{v},
		}
		labelSelectors = append(labelSelectors, labelValue)
	}
	mwk := UpdateMutatingWebhookConfiguration(mutatingWebhookConfiguration, labelSelectors)
	return mwk
}

// UpdateMutatingWebhookConfigurationsNamespaceSelector updates MutatingWebhookConfiguration[] with labels
func UpdateMutatingWebhookConfigurationsNamespaceSelector(mutatingWebhookConfigurations []admitv1.MutatingWebhookConfiguration,
	labels map[string]string) []admitv1.MutatingWebhookConfiguration {
	var mutatingWebhookConfigurationss []admitv1.MutatingWebhookConfiguration
	for _, mutatingWebhookConfiguration := range mutatingWebhookConfigurations {
		mwk := UpdateMutatingWebhookConfigurationNamespaceSelector(mutatingWebhookConfiguration, labels)
		mutatingWebhookConfigurationss = append(mutatingWebhookConfigurationss, mwk)
	}
	return mutatingWebhookConfigurationss
}

// UpdateMutatingWebhookConfiguration updates MutatingWebhookConfiguration with labels
func UpdateMutatingWebhookConfiguration(mutatingWebhookConfiguration admitv1.MutatingWebhookConfiguration,
	labelSelectors []metav1.LabelSelectorRequirement) admitv1.MutatingWebhookConfiguration {
	webhooks := mutatingWebhookConfiguration.Webhooks
	for i, webhook := range webhooks {
		namespaceSelector := webhook.NamespaceSelector
		if namespaceSelector == nil {
			namespaceSelector = new(metav1.LabelSelector)
		}
		namespaceSelector.MatchExpressions = append(namespaceSelector.MatchExpressions, labelSelectors...)
		webhook.NamespaceSelector = namespaceSelector
		webhooks[i] = webhook
	}
	mutatingWebhookConfiguration.Webhooks = webhooks
	return mutatingWebhookConfiguration
}
