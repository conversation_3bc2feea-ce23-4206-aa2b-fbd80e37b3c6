package sliceutil

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestStringContains(t *testing.T) {
	ass := assert.New(t)
	tests := []struct {
		name   string
		values []string
		str    string
		result bool
	}{
		{
			name:   "test01",
			values: []string{},
			str:    "a",
			result: false,
		},
		{
			name:   "test02",
			values: []string{"a"},
			str:    "a",
			result: true,
		}, {
			name:   "test03",
			values: []string{"a", "b", "c", "d"},
			str:    "d",
			result: true,
		},
		{
			name:   "test04",
			values: []string{"a", "b", "c", "d"},
			str:    "f",
			result: false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			res := StringContains(test.values, test.str)
			ass.Equal(test.result, res, test.name)
		})
	}
}
