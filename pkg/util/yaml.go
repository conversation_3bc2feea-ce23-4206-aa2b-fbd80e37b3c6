package util

import (
	"bytes"
	"encoding/json"
	"strings"

	"sigs.k8s.io/yaml"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

func StructToYamlString(source interface{}) (string, error) {
	jsonBytes, err := json.Marshal(source)
	if err != nil {
		return "", err
	}
	yamlBytes, err := yaml.JSONToYAML(jsonBytes)
	if err != nil {
		return "", err
	}

	return string(yamlBytes), nil
}

func YamlStringToStruct(yamlContent string, dst interface{}) error {
	jsonBytes, err := yaml.YAMLToJSON([]byte(yamlContent))
	if err != nil {
		return err
	}

	err = json.Unmarshal(jsonBytes, dst)
	if err != nil {
		return err
	}

	return nil
}

// IsYAMLEqual reports whether the YAML in strings a and b are equal.
func IsYAMLEqual(ctx csmContext.CsmContext, a, b string) bool {
	if strings.TrimSpace(a) == "" && strings.TrimSpace(b) == "" {
		return true
	}
	ajb, err := yaml.YAMLToJSON([]byte(a))
	if err != nil {
		ctx.CsmLogger().Errorf("bad YAML in isYAMLEqual:\n%s", a)
		return false
	}
	bjb, err := yaml.YAMLToJSON([]byte(b))
	if err != nil {
		ctx.CsmLogger().Errorf("bad YAML in isYAMLEqual:\n%s", b)
		return false
	}

	return bytes.Equal(ajb, bjb)
}
