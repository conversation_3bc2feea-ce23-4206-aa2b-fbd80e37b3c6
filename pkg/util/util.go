package util

import (
	"fmt"
	"runtime"
	"strings"

	corev1 "k8s.io/api/core/v1"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

// GetNamespaceWithCsmInstanceId use split to splice namespace and csmInstanceId
func GetNamespaceWithCsmInstanceId(namespace, csmInstanceId, split string) string {
	return GetStrWithSplit([]string{namespace, csmInstanceId}, split)
}

// GetStrWithSplit use split to splice []string values
func GetStrWithSplit(values []string, split string) string {
	return strings.Join(values, split)
}

// GetIstioCtl 根据运行平台获取 istioctl 命名工具
func GetIstioCtl(ctx csmContext.CsmContext) string {
	istioctl := constants.BaseIstioName
	sysType := runtime.GOOS
	if sysType == constants.Darwin {
		istioctl = istioctl + "_arm"
	}
	ctx.CsmLogger().Infof("the istio command is %s", istioctl)
	return istioctl
}

// GetHelmCtl 根据运行平台获取 istioctl 命名工具
func GetHelmCtl(ctx csmContext.CsmContext) string {
	helm := constants.BaseHelmName
	sysType := runtime.GOOS
	if sysType == constants.Darwin {
		helm = helm + "_arm"
	}
	ctx.CsmLogger().Infof("the helm command is %s", helm)
	return helm
}

// CheckPodReady returns nil if the given pod and all of its containers are ready.
func CheckPodReady(pod *corev1.Pod) error {
	switch pod.Status.Phase {
	case corev1.PodRunning:
		// Wait until all containers are ready.
		for _, containerStatus := range pod.Status.InitContainerStatuses {
			if !containerStatus.Ready {
				return fmt.Errorf("init container not ready: '%s'", containerStatus.Name)
			}
		}
		for _, containerStatus := range pod.Status.ContainerStatuses {
			if !containerStatus.Ready {
				return fmt.Errorf("container not ready: '%s'", containerStatus.Name)
			}
		}
		if len(pod.Status.Conditions) > 0 {
			for _, condition := range pod.Status.Conditions {
				if condition.Type == corev1.PodReady && condition.Status != corev1.ConditionTrue {
					return fmt.Errorf("pod not ready, condition message: %v", condition.Message)
				}
			}
		}
		return nil
	default:
		return fmt.Errorf("%s", pod.Status.Phase)
	}
}
