package restclient

import (
	"errors"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/ext"
	"github.com/spf13/viper"
	"gopkg.in/resty.v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/auth"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

const BLBClient = "blb"

type (
	bceAuthContextKeyType struct{}
	bceSecurityTokenKey   struct{}
	bceSubAccountKey      struct{}
)

var (
	BceAuthContextKey   bceAuthContextKeyType
	BceSecurityTokenKey bceSecurityTokenKey
	BceSubAccountKey    bceSubAccountKey
)

var signerPool = sync.Pool{
	New: func() interface{} {
		return auth.NewSigner()
	},
}

func NewBCERestClient(name string) *resty.Client {
	client := resty.New()

	client.SetDebug(viper.GetBool("debug"))
	client.SetLogger(csmlog.NewLogger().Named(name).AddCallerSkip(7))
	client.SetTimeout(10 * time.Second)
	client.SetHeader(http.CanonicalHeaderKey("User-Agent"), "CSM Client")
	client.SetError(&csm.GenericError{})
	// TODO 是否需要配置一下重试机制

	// add headers
	client.OnBeforeRequest(func(c *resty.Client, req *resty.Request) error {
		req.SetHeader("x-bce-date", time.Now().Format(time.RFC3339))

		// add request id, this can be omitted when using tracing span below
		requestId, ok := req.Context().Value(BceRequestIdKey).(string)
		if ok && requestId != "" {
			req.SetHeader("x-bce-request-id", requestId)
		}

		// add security-token
		sessionToken, ok := req.Context().Value(BceSecurityTokenKey).(string)
		if ok && sessionToken != "" {
			req.SetHeader("x-bce-security-token", sessionToken)
		}

		// add sub-account-id
		accountId, ok := req.Context().Value(BceSubAccountKey).(string)
		if ok && sessionToken != "" {
			req.SetHeader("x-bce-sub-account-id", accountId)
		}

		span, ok := req.Context().Value(Span).(opentracing.Span)
		if ok && span != nil {
			if err := span.Tracer().Inject(span.Context(), opentracing.HTTPHeaders, req.Header); err != nil {
				return err
			}
			ext.SpanKindRPCClient.Set(span)
			ext.HTTPUrl.Set(span, req.URL)
			ext.HTTPMethod.Set(span, req.Method)
		}

		return nil
	})

	// add IAM auth
	client.SetPreRequestHook(func(c *resty.Client, req *resty.Request) error {
		// grab auth key from request context
		authKey, ok := req.RawRequest.Context().Value(BceAuthContextKey).(auth.BceAuthKey)
		if ok {
			signer := signerPool.Get().(*auth.Signer)
			defer signerPool.Put(signer)

			headers := req.Header
			if headers.Get("Host") == "" {
				headers.Set("Host", req.RawRequest.Host)
			}
			if headers.Get("Content-Type") == "" {
				headers.Set("Content-Type", "application/json")
			}

			s := signer.Reset()
			if name == BLBClient {
				s = s.AddIgnoredHeader("x-bce-request-id")
			}
			signature := s.AuthKey(&authKey).
				Method(req.Method).
				Path(req.RawRequest.URL.Path).
				Params(req.QueryParam).
				Headers(headers).
				WithSignedHeader().
				GetSign()

			req.Header.Set("Authorization", signature)
		}
		return nil
	})

	client.OnAfterResponse(func(c *resty.Client, resp *resty.Response) error {
		req := resp.Request.RawRequest
		span, ok := req.Context().Value(Span).(opentracing.Span)
		if ok && span != nil {
			ext.HTTPStatusCode.Set(span, uint16(resp.StatusCode()))
			if resp.IsError() {
				ext.Error.Set(span, true)
			}
		}
		return nil
	})

	return client
}

func BceResponseError(resp *resty.Response) (*csm.ResponseError, error) {
	if !resp.IsError() {
		return nil, errors.New("no error")
	}

	bceError, ok := resp.Error().(*csm.GenericError)
	if !ok {
		return nil, fmt.Errorf("cannot get bce.Error from response error: %v", resp)
	}

	err := csm.ResponseError{
		GenericError: *bceError,
		StatusCode:   resp.StatusCode(),
	}

	return &err, nil
}
