package restclient

import (
	"context"
	auth2 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/auth"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/opentracing/opentracing-go"
	"gopkg.in/resty.v1"
)

func TestBCERestClient(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("x-bce-request-id") != "request_id" {
			t.Fatalf("request id wrong, get %s should `request_id`", r.<PERSON>er.Get("x-bce-request-id"))
		}

		if r.Header.Get("x-bce-security-token") != "security_token" {
			t.Fatalf("security token wrong, get %s should `security_token`", r.Header.Get("x-bce-security-token"))
		}

		if r.<PERSON><PERSON>.Get("x-bce-sub-account-id") != "sub_account_id" {
			t.Fatalf("sub account id wrong, get %s should `sub_account_id`", r.<PERSON>er.Get("x-bce-sub-account-id"))
		}
	}))
	defer ts.Close()

	client := NewBCERestClient("test")

	ctx := context.WithValue(context.TODO(), BceRequestIdKey, "request_id")
	ctx = context.WithValue(ctx, Span, opentracing.GlobalTracer().StartSpan("test_span"))
	ctx = context.WithValue(ctx, BceSubAccountKey, "sub_account_id")
	ctx = context.WithValue(ctx, BceSecurityTokenKey, "security_token")
	ctx = context.WithValue(ctx, BceAuthContextKey, *auth2.NewBceAuthKey("ak", "sk"))

	_, err := client.R().SetContext(ctx).Get(ts.URL)
	if err != nil {
		t.Fatal(err)
	}
}

func TestBceResponseError(t *testing.T) {
	// error
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Add("Content-Type", "application/json")
		w.WriteHeader(400)
		w.Write([]byte(`{"requestId":"REQUEST_ID", "code": "CODE", "message": "MESSAGE"}`))
	}))
	defer ts.Close()

	client := NewBCERestClient("test")

	ctx := context.WithValue(context.TODO(), BceAuthContextKey, *auth2.NewBceAuthKey("ak", "sk"))

	resp, err := client.R().SetContext(ctx).Get(ts.URL)
	if err != nil {
		t.Fatal(err)
	}

	if !resp.IsError() {
		t.Fatal("should receive error")
	}

	bceErr, err := BceResponseError(resp)
	if err != nil {
		t.Fatal(err)
	}

	if bceErr.StatusCode != 400 {
		t.Fatalf("status code wrong, should be 400 but got %d", bceErr.StatusCode)
	}

	if bceErr.String() != "MESSAGE" {
		t.Fatalf("error message wrong, should be `MESSAGE` but got %s", bceErr)
	}

	// no error
	resp = &resty.Response{
		Request:     &resty.Request{},
		RawResponse: &http.Response{},
	}
	bceErr, err = BceResponseError(resp)
	if bceErr != nil {
		t.Fatalf("expected nil *bce.ResponseError, got %v", bceErr)
	}
	if err == nil {
		t.Fatal("expected err, but got nil")
	}

	// wrong error type
	resp.RawResponse.StatusCode = 400
	bceErr, err = BceResponseError(resp)
	if bceErr != nil {
		t.Fatalf("expected nil *bce.ResponseError, got %v", bceErr)
	}
	if err == nil {
		t.Fatal("expected err, but got nil")
	}
}
