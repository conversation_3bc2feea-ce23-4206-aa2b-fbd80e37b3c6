package csmflag

import (
	"fmt"
	"os"
	"time"

	"github.com/alecthomas/units"
	"github.com/prometheus/common/model"
	"github.com/spf13/pflag"
	"github.com/spf13/viper"
)

var CommandLine *FlagSet
var stringFlagMaps map[string]*string
var intFlagMaps map[string]*int
var boolFlagMaps map[string]*bool

var stringSliceFlagMaps map[string]*[]string
var durationFlagMaps map[string]*time.Duration

var base2bytesFlagMaps map[string]*units.Base2Bytes

func init() {
	CommandLine = &FlagSet{
		s: pflag.CommandLine,
	}
	stringFlagMaps = make(map[string]*string)
	intFlagMaps = make(map[string]*int)
	boolFlagMaps = make(map[string]*bool)

	stringSliceFlagMaps = make(map[string]*[]string)
	durationFlagMaps = make(map[string]*time.Duration)

	base2bytesFlagMaps = make(map[string]*units.Base2Bytes)
}

type FlagSet struct {
	s *pflag.FlagSet
}

func (b *FlagSet) StringVar(p *string, name, value, usage string) {
	viper.SetDefault(name, value)
	b.s.StringVar(p, name, value, usage)
	stringFlagMaps[name] = p
}

func (b *FlagSet) IntVar(p *int, name string, value int, usage string) {
	viper.SetDefault(name, value)
	b.s.IntVar(p, name, value, usage)
	intFlagMaps[name] = p
}

func (b *FlagSet) BoolVar(p *bool, name string, value bool, usage string) {
	viper.SetDefault(name, value)
	b.s.BoolVar(p, name, value, usage)
	boolFlagMaps[name] = p
}

func (b *FlagSet) DurationVar(p *time.Duration, name string, value time.Duration, usage string) {
	viper.SetDefault(name, value)
	b.s.DurationVar(p, name, value, usage)
	durationFlagMaps[name] = p
}

func (b *FlagSet) StringSliceVar(p *[]string, name string, value []string, usage string) {
	viper.SetDefault(name, value)
	b.s.StringSliceVar(p, name, value, usage)
	stringSliceFlagMaps[name] = p
}

func (b *FlagSet) Base2BytesVar(p *units.Base2Bytes, name string, value units.Base2Bytes, usage string) {
	viper.SetDefault(name, value)
	// ps是为了能显示帮助信息
	var ps string
	b.s.StringVar(&ps, name, value.String(), usage)
	base2bytesFlagMaps[name] = p
}

func (b *FlagSet) PromethteusDurationVar(p *model.Duration, name string, value model.Duration, usage string) {
	b.DurationVar((*time.Duration)(p), name, time.Duration(value), usage)
}

func Parse() {
	for name := range stringFlagMaps {
		v := viper.GetString(name)
		*stringFlagMaps[name] = v
	}
	for name := range intFlagMaps {
		v := viper.GetInt(name)
		*intFlagMaps[name] = v
	}
	for name := range boolFlagMaps {
		v := viper.GetBool(name)
		*boolFlagMaps[name] = v
	}

	for name := range stringSliceFlagMaps {
		v := viper.GetStringSlice(name)
		*stringSliceFlagMaps[name] = v
	}

	for name := range durationFlagMaps {
		v := viper.GetDuration(name)
		*durationFlagMaps[name] = v
	}

	for name := range base2bytesFlagMaps {
		vs := viper.GetString(name)
		v, err := units.ParseBase2Bytes(vs)
		if err != nil {
			fmt.Fprintf(os.Stderr, "config: parse base2bytes flag '%s' failed: %s", name, err)
			continue
		}
		*base2bytesFlagMaps[name] = v
	}
}
