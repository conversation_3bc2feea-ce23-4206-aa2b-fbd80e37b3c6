package util

import (
	"testing"
)

func TestGetNamespaceWithCsmInstanceId(t *testing.T) {
	tests := []struct {
		name          string
		namespace     string
		csmInstanceId string
		split         string
		expect        string
	}{
		{
			name:          "test-GetNamespaceWithCsmInstanceId",
			namespace:     "test",
			csmInstanceId: "csm-123456",
			split:         "-",
			expect:        "test-csm-123456",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetNamespaceWithCsmInstanceId(tt.namespace, tt.csmInstanceId, tt.split); got != tt.expect {
				t.<PERSON>rf("%v: got %v want %v", tt.name, got, tt.expect)
			}
		})
	}
}
