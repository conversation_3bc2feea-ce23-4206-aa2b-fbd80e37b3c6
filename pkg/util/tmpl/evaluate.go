package tmpl

import (
	"os"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/file"
)

// Evaluate parses the template and then executes it with the given parameters.
func Evaluate(_ csmContext.CsmContext, tpl string, data interface{}) (string, error) {
	t, err := Parse(tpl)
	if err != nil {
		return "", err
	}
	return Execute(t, data)
}

func EvaluateIstiodService(ctx csmContext.CsmContext, tplPath string, data interface{}) (string, error) {
	tplValue, err := os.ReadFile(tplPath)
	if err != nil {
		ctx.CsmLogger().Errorf("ReadFile %s error %v", tplPath, err)
		return "", err
	}
	t, err := Parse(string(tplValue))
	if err != nil {
		return "", err
	}
	return Execute(t, data)
}

func EvaluatePathTmpl(ctx csmContext.CsmContext, srcTmplPath, desTmplPath string, data interface{}) error {
	tempValue, err := os.ReadFile(srcTmplPath)
	if err != nil {
		ctx.CsmLogger().Errorf("ReadFile %s error %v", srcTmplPath, err)
		return err
	}
	res, err := Evaluate(ctx, string(tempValue), data)
	if err != nil {
		ctx.CsmLogger().Errorf("Evaluate error %v", err)
		return err
	}
	err = file.RewriteFile(ctx, desTmplPath, []byte(res))
	if err != nil {
		ctx.CsmLogger().Errorf("RewriteFile %s error %v", desTmplPath, err)
		return err
	}
	return nil
}
