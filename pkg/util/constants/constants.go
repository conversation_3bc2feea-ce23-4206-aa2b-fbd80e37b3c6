package constants

import (
	"os"
	"strings"
	"time"

	"github.com/spf13/viper"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
)

const (
	PollInternal   = 2 * time.Second
	Internal       = 600 * time.Second
	CommandTimeOut = 5 * time.Minute

	RetryInternal      = 5 * time.Second
	MaxRetryNums       = 6
	DefaultDiscoveryIP = "*******"
)

const (
	// EksIopTemplate is eks template
	EksIopTemplate = "eks"

	// EksNetWork is network mode for eks
	EksNetWork = "appspace"

	// EksIopTemplateTmplName is template name to install istio for eks
	EksIopTemplateTmplName = "iop-template-eks.tmpl"

	// EksEastWestGatewayTmplName is template name to install istio gateway for eks
	EksEastWestGatewayTmplName = "eastwest-gateway-eks.tmpl"

	// ExposeIstiodTmplName is the name of istiod
	ExposeIstiodTmplName = "expose-istiod.tmpl"

	// IopTemplateTmplName is cce template
	IopTemplateTmplName = "iop-template.tmpl"

	// EastWestGatewayTmplName is template name to install istio gateway for cce
	EastWestGatewayTmplName = "eastwest-gateway.tmpl"

	// EastWestGatewayName is the name of eastwestgateway for istio
	EastWestGatewayName = "eastwestgateway"

	// SplitSlash for `-`
	SplitSlash = "-"

	// IstiodGatewayName is istiod-gateway template name
	IstiodGatewayName = "istiod-gateway"

	// IstiodVsName is istiod-vs template name
	IstiodVsName = "istiod-vs"

	// IstiodDeploymentName is the deployment name for istio
	IstiodDeploymentName = "istiod"

	// IstiodServiceName is the service name for istio
	IstiodServiceName = "istiod"

	IstioENVClusterID        = "CLUSTER_ID"
	IstioCRDNAMESPACENAME    = "ISTIO_CRD_NAMESPACE_NAME"
	IstioContainerName       = "discovery"
	IstioConfigMapInjectName = "istio-sidecar-injector"

	// IngressGatewayServiceName is the service name for istio-ingressgateway
	IngressGatewayResourceName = "istio-ingressgateway"

	IstioRemoteSecretPrefix     = "istio-remote-secret-"
	IstioRemoteConfigSecretName = "istio-kubeconfig"
)

const (
	GlobalRegion        = "global"
	InstanceRegionField = "region"
	InstanceStatusField = "status"
)

const (
	RegionHeaderKey = "X-Region"

	KubeConfigType       = "kubeConfigType"
	KubeConfigTypeVPC    = "vpc"
	KubeConfigTypePublic = "public"
	VPCEndpointTagKey    = "csm-hosting"
	InstanceUUIDPrefix   = "csm-"

	EIPInstanceTypeSNIC = "SNIC"

	CsmHostingAPIServerVpcEndpointDescribe = "the endpoint for %s csm instance APIServer"

	// RegisterInstanceCenterPrefix register center
	RegisterInstanceCenterPrefix = "cse-"

	GatewayUUIDPrefix = "aigw-"

	EastWestGateway = "istio-eastwestgateway"

	BillingModel = "free"

	BLBId = "service.beta.kubernetes.io/cce-load-balancer-id"

	BLBRunning = "running"

	BLBPending = "pending"

	BLBNotExist = "notExist"

	TopologyIstioIoNetWork = "topology.istio.io/network"

	NamespacePrefix = "istio-system-"

	MeshInstanceId = "mesh-instance-id"

	// MeshAccountId 实例账户信息
	// nolint
	MeshAccountId = "service.csm/user-account-id"

	MeshMutatingWebhookConfigurationInstanceId = "mesh.instance-id.istio.io"

	Darwin = "darwin"

	ConfigClusterEXTERNAL = "EXTERNAL"
	ConfigClusterREMOTE   = "REMOTE"
)

const (
	VpcSecurityGroupDefaultName = "default"

	CsmBlbServiceDefaultDescribe = "the default blb service of CSM"

	CsmVpcEndpointDefaultDescribe = "the default endpoint of vpc of csm"

	CceLoadBalancerID = "service.beta.kubernetes.io/cce-load-balancer-id"

	CsmCPromInstanceID         = "service.beta.kubernetes.io/csm-prometheus-instance-id"
	CsmCPromScrapeJobID        = "service.beta.kubernetes.io/csm-prometheus-scrape-job-id"
	CsmCPromAgentID            = "service.beta.kubernetes.io/csm-prometheus-agent-id"
	CSMHostingMonitor          = "service.beta.kubernetes.io/csm-hosting-monitor"
	CSMHostingBlbAccountID     = "service.beta.kubernetes.io/cce-master-blb-account-id"
	CSMHostingMonitorIstioType = "istio"

	CsmCPromAgentStatusUnknown = "Unknown"
	CsmCPromAgentStatusRunning = "Running"

	CertBasePath = "mesh_certs"

	BaseIstioTemplate = "istio/template/standalone"

	BaseHigressTemplate = "higress"

	HigressCrdTemplate = "crds/higress-crd.yaml"

	HigressPreTemplate = "crds/higress-pre.yaml"

	HigressTemplate        = "template/higress-template-list.tmpl"
	HigressInstallTemplate = "template/higress-template-list.yaml"

	StandaloneToolCertsCertMakePath = "/tools/certs/Makefile.selfsigned.mk"

	Templates = "templates"

	HostingBaseIstioTemplate = "istio/template/hosting"

	HostingTmpl = "tmpl"

	StandaloneTemplate = "template"

	StandaloneTemplateList = "iop-template-list.tmpl"

	// HostingCceIopTmplName is cce template
	HostingCceIopTmplName = "istio_iop_cce.tmpl"

	// HostingCceRemoteIopName is cce user remote template
	HostingCceRemoteIopName = "remote_iop_cce.tmpl"

	// HostingEksRemoteIopTmplName is eks template
	HostingEksRemoteIopTmplName = "remote_iop_eks.tmpl"

	// HostingCceRemoteConfigIopName is cce user remote config template
	HostingCceRemoteConfigIopName = "config_remote_iop_cce.tmpl"

	// HostingCceIstiodServiceTmplName is cce istiod service name
	HostingCceIstiodServiceTmplName = "istiod_service.tmpl"

	// HostingEksIopTmplName is eks template
	HostingEksIopTmplName = "istio_iop_eks.tmpl"

	FilePerm = os.FileMode(0755)

	BaseIstioBin = "bin"

	BaseIstioName = "istioctl"

	EastWestGatewaySvc = "istio-eastwestgateway"

	EastWestGatewayYaml = "eastwest-gateway.yaml"

	SuffixCaCertName = "-cacerts"

	CaName = "cacerts"

	CaCertPemName = "ca-cert.pem"

	CaKeyPemName = "ca-key.pem"

	CertChainPemName = "cert-chain.pem"

	RootCertPemName = "root-cert.pem"

	BaseGatewayTemplate = "gateway/template"

	IngressGatewayYaml = "ingress-gateway.yaml"

	IngressGatewayTmpl = "ingress-gateway.tmpl"

	HpaYaml = "hpa.yaml"

	HpaTmpl = "hpa.tmpl"

	GwVsYaml = "gw-vs.yaml"

	GwVsTmpl = "gw-vs.tmpl"

	TelemetryYaml = "telemetry.yaml"

	TelemetryTmpl = "telemetry.tmpl"
)

// VirtualService 相关常量与时间格式
const (
	// 标准时间格式
	TimeFormatStd = "2006-01-02 15:04:05"

	// VirtualService 注解键
	AnnUpdateTime                  = "update-time"
	AnnPathCaseSensitive           = "path-case-sensitive"
	AnnAuthEnabled                 = "auth.higress.io/enabled"
	AnnMultiService                = "multi-service"
	AnnTrafficDistributionStrategy = "traffic-distribution-strategy"
	AnnRewriteEnabled              = "rewrite-enabled"
	AnnSrcProduct                  = "src-product"
	AnnTrafficMirrorEnabled        = "traffic-mirror-enabled"
	AnnMirrorPercentage            = "mirror-percentage"
	AnnLBAlgorithm                 = "load-balance-algorithm"

	// 流量分发策略
	TrafficStrategyRatio     = "ratio"
	TrafficStrategyModelName = "model_name"

	// 特殊 Header
	HeaderModelKey = "x-model-header"
)

// for higress
const (
	ServiceNameHigress          = "higress-controller"
	DeployMentHigress           = "higress-controller"
	AnnotationEnableSyncIngress = "service.beta.kubernetes.io/csm-sync-ingress-enable"
	// AnnotationSyncClusterIDs syncClusterIDs remote集群ID以","分割，示例："cluster1,clusters2"
	AnnotationSyncClusterIDs = "service.beta.kubernetes.io/csm-sync-ingress-clusters"

	RestartIstioAnnotation = "kubectl.kubernetes.io/restartedAt"

	BaseHigressPath = "gateway/higress"

	BaseHelmBin      = "bin"
	BaseHigressChart = "helm"
	BaseHigressName  = "higress"

	BaseHelmName = "helm"

	CrossVpcUserID                    = "cross-vpc-eni\\\\.cce\\\\.io/userID"
	CrossVpcSubnetID                  = "cross-vpc-eni\\\\.cce\\\\.io/subnetID"
	CrossVpcSecurityGroupIDs          = "cross-vpc-eni\\\\.cce\\\\.io/securityGroupIDs"
	CrossVpcVpcCidr                   = "cross-vpc-eni\\\\.cce\\\\.io/vpcCidr"
	CrossVpcPrivateIPAddress          = "cross-vpc-eni\\\\.cce\\\\.io/privateIPAddress"
	CrossVpcDefaultRouteExcludedCidrs = "cross-vpc-eni\\\\.cce\\\\.io/defaultRouteExcludedCidrs"

	// ValueDefaultRouteExcludedCidrs 老集群的托管 master 是特殊的，需要通过弹性网卡访问
	ValueDefaultRouteExcludedCidrs = "************/24"

	RbacAPIGroups               = "networking.k8s.io"
	RbacResourcesIngress        = "ingresses"
	RbacResourcesIngressclasses = "ingressclasses"

	RemoteClusterRolePrefic = "istio-reader-clusterrole"

	// PortHigressXds 与helm模板中.value.controller.ports中的端口值保持一致
	PortHigressXds = 15051

	HigressLabelSelector = "app=higress-controller"
)

// for lane
const (
	BaseLanePath = "templates/lane"

	LaneEnvoyFilterTmpl            = "ef-lane.tmpl"
	LaneVirtualServiceTmpl         = "vs-lane.tmpl"
	LaneVirtualServiceDelegateTmpl = "vs-delegate.tmpl"
	LaneDestinationRuleTmpl        = "dr-lane.tmpl"

	ExactMatchMode  = "exact"
	RegexMatchMode  = "regex"
	PrefixMatchMode = "prefix"

	APIVersion16 = "networking.istio.io/v1alpha3"

	EFNameInboundSuffix    = "in"
	EFNameOutboundSuffix   = "out"
	EFPatchContextIn       = "SIDECAR_INBOUND"
	EFPatchContextOut      = "SIDECAR_OUTBOUND"
	EFOperationIn          = "INSERT_BEFORE"
	EFOperationOut         = "INSERT_FIRST"
	EFWorkSelectorLabelKey = "app"
)

const (
	ClusterDomain = "svc.cluster.local"

	MaxPageSize = 20
	MinPageSize = 1
	MinPageNo   = 1

	ExportToAllNameSpace = "ALL"

	DBOrderASC = "ASC"

	IstioNamespace = "istio-system"

	HigressNamespace = "istio-system-ai"

	ConfigMapName = "istio-sidecar-injector"

	IstioLabelSelector = "app=istiod"

	RunningStatus = "status.phase=Running"

	DefaultPrometheusPort = 15014

	DefaultLocalAddress = "localhost"

	IngressGatewayLabelSelector = "app=istio-ingressgateway"
	GatewayTagKey               = "gatewayId"

	// ServiceMeshInstance Status(in lower case)
	SmiRunning   = "running"
	SmiDeploying = "deploying"
	SmiAbnormal  = "abnormal"
	SmiUnknown   = "unknown"
	SmiEvicted   = "evicted"

	// AI Gateway Instance Status(in lower case)
	AIGatewayStatusRunning  = "running"
	AIGatewayStatusCreating = "creating"

	// AI Gateway Product Type
	AIGatewayProductAibox = "aibox"

	// Cluster Status
	CluRunning = "running"

	// Service Type
	KsService = "K8sService"
	SeService = "ServiceEntry"

	KubeTimeout = 3 * time.Second

	KubeClientDefaultTimeout = 3 * time.Second

	// "where" when select in instance list
	InstanceName = "instanceName"
	InstanceId   = "instanceId"

	// instanceID in path parameter
	InstanceIDPathParam = "instanceUUID"

	// gatewayID in path parameter
	GatewayIDPathParam = "gatewayUUID"

	LaneGroupIDParam = "laneGroupID"

	LaneIDParam = "laneID"
)

const (
	Int64QuantityExpectedBytes = 18
)

const (
	MetricsCommand = "curl http://127.0.0.1:15014/metrics | grep -w 'pilot_xds'| grep -v '#' | awk '{print $2}'"
	ChannelBuffer  = 30
	MetricsTimeout = 3 * time.Second
)

const (
	Desc = "desc"

	Asc = "asc"

	ClusterId = "clusterId"

	ClusterName = "clusterName"

	Namespace = "namespace"
)

const (
	KubeNodeLease = "kube-node-lease"

	KubePublic = "kube-public"

	KubeSystem = "kube-system"
)

const (
	Cpu = "cpu"

	Mem = "mem"

	Memory = "memory"

	CpuQuotaSuf = "m"

	MemQuotaSuf = "Mi"

	Gi = "Gi"

	Value = "values"

	ClusterUuid = "clusterUuid"

	SidecarInject = "istio-injection"

	Disabled = "disabled"

	Enabled = "enabled"
)

// constants for discovery selector
const (
	// IstioDiscoveryAddressPort for istio configmap
	IstioDiscoveryAddressPort = ":15012"

	// IstiodefaultConfigName for istio configmap
	IstiodefaultConfigName = "defaultConfig"

	// IstiodiscoveryAddressName for istio configmap
	IstiodiscoveryAddressName = "discoveryAddress"

	// IstioConfigMapMeshName for istio configmap
	IstioConfigMapMeshName = "mesh"

	// DiscoverySelectorsName for discoverySelectors
	DiscoverySelectorsName = "discoverySelectors"

	// IstioSidecarInjectorValidatingWebhookConfiguration for validatingwebhookconfiguration
	IstioSidecarInjectorValidatingWebhookConfiguration = "istio-validator"

	// IstioSidecarInjectorMutatingWebhookConfiguration for mutatingwebhookconfiguration
	IstioSidecarInjectorMutatingWebhookConfiguration = "istio-sidecar-injector"

	// IstioConfimapName for istio configmap name
	IstioConfimapName = "istio"

	// IstioInjection for istio injection
	IstioInjection = "istio-injection"

	// IstioIoRev for istio istio.io/rev
	IstioIoRev = "istio.io/rev"
)

// constants for gateway resource
const (
	// GwSecretNamePrefix gateway secrets name's prefix
	GwSecretNamePrefix = "cert-"
	// GwStatusPort gateway's status port
	GwStatusPort = uint32(15021)
	// GwCrdName crd name of gateway
	GwCrdName = "gateway-gw"
	// GwTelemetryCrdName crd name of gateway's telemetry
	GwTelemetryCrdName = "gateway-log"
	// GwTelemetryLabelKey a label name of telemetry crd
	GwTelemetryLabelKey = "taskId"
	// LogBeatName the name of logbeat-ds
	LogBeatName = "logbeat-ds"
	// LogBeatNamespace the namespace of logbeat-ds
	LogBeatNamespace = "default"
	// LogBeatTaskEnv a env name of logbeat-ds
	LogBeatTaskEnv = "BLS_TASKS"
	// ProtocolHTTP name of http protocol
	ProtocolHTTP = "HTTP"
	// ProtocolHTTPS name of https protocol
	ProtocolHTTPS = "HTTPS"
	// TLSAccelerationAnnotationKey
	TLSAccelerationAnnotationKey = "proxy.istio.io/config"
	// TLSAccelerationAnnotationVal
	TLSAccelerationAnnotationVal = "privateKeyProvider:\n  cryptomb:\n    pollDelay: 1ms"
)

const Timeout = "timeout"

const (
	EnvName  = "ENABLE_ENHANCED_RESOURCE_SCOPING"
	EnvValue = "true"
)

// constants for version
const (
	// CSM支持版本
	IstioVersion13        = "1.13.2"
	IstioVersion14        = "1.14.6"
	IstioVersion16        = "1.16.5"
	IstioHostingVersion14 = "1.14.6-baidu"
	IstioVersion22        = "1.22.6"
	IstioVersion20        = "1.20.8"
	// CCE支持版本
	K8sVersion18 = "1.18"
	K8sVersion20 = "1.20"
	K8sVersion22 = "1.22"
	K8sVersion24 = "1.24"
	K8sVersion26 = "1.26"
	K8sVersion28 = "1.28"
	K8sVersion30 = "1.30"
)

func IsAcceptExceptionForCProm(cpromErr error) bool {
	if strings.Contains(cpromErr.Error(), csmErr.CPromScrapeJobNotFoundException) ||
		strings.Contains(cpromErr.Error(), csmErr.CPromAgentNotFoundException) ||
		strings.Contains(cpromErr.Error(), csmErr.CPromInstanceNotFoundException) {
		return true
	}
	return false
}

const (
	// Tracing for istio configmap
	Tracing = "tracing"

	// Zipkin for istio configmap
	Zipkin = "zipkin"

	// Sampling for istio configmap
	Sampling = "sampling"

	// Address for istio configmap
	Address = "address"

	// Port default port for trace
	Port = ":9411"
)

const (
	// VPC 配置
	vpcIDKey = "aigw.vpc.hostingId"
	// AI Token Rate Limit Plugin Constants - 配置键名
	aiTokenLimitPluginNameKey = "aigw.plugins.token_limit_plugin.name"
	aiTokenLimitPluginURLKey  = "aigw.plugins.token_limit_plugin.url"

	// Token Rate Limit Template Path - 配置键名
	tokenRateLimitTemplatePathKey = "aigw.plugins.token_limit_plugin.template_path"

	// Key Auth Plugin Constants - 配置键名
	keyAuthPluginNameKey   = "aigw.plugins.key_auth_plugin.name"
	keyAuthPluginURLKey    = "aigw.plugins.key_auth_plugin.url"
	keyAuthTemplatePathKey = "aigw.plugins.key_auth_plugin.template_path"

	// AI Quota Plugin Constants - 配置键名
	aiQuotaPluginNameKey   = "aigw.plugins.ai_quota_plugin.name"
	aiQuotaPluginURLKey    = "aigw.plugins.ai_quota_plugin.url"
	aiQuotaTemplatePathKey = "aigw.plugins.ai_quota_plugin.template_path"

	// IP 黑白名单插件常量
	IPSourceType                 = "origin-source"
	ipRestrictionPluginURLKey    = "aigw.plugins.ip_restriction_plugin.url"
	ipRestrictionTemplatePathKey = "aigw.plugins.ip_restriction_plugin.template_path"

	// AI 可观测插件常量
	aiStatisticsPluginNameKey   = "aigw.plugins.ai_statistics_plugin.name"
	aiStatisticsPluginURLKey    = "aigw.plugins.ai_statistics_plugin.url"
	aiStatisticsTemplatePathKey = "aigw.plugins.ai_statistics_plugin.template_path"

	// 外部认证插件常量
	extAuthPluginNameKey   = "aigw.plugins.ext_auth_plugin.name"
	extAuthPluginURLKey    = "aigw.plugins.ext_auth_plugin.url"
	extAuthTemplatePathKey = "aigw.plugins.ext_auth_plugin.template_path"

	// WasmPlugin标签常量
	WasmPluginLabelPluginType   = "aigw.plugin.type"
	WasmPluginTypeIPRestriction = "ip-restriction"
	WasmPluginTypeExtAuth       = "ext-auth"

	// 插件优先级常量 - 硬编码默认值
	AITokenLimitPluginPriority = 600
	KeyAuthPluginPriority      = 310
	AIQuotaPluginPriority      = 750

	// 虚拟消费者名称 - 用于当allow数组为空时的占位
	VirtualDenyAllConsumerName = "__SYSTEM_DENY_ALL__"

	// 环境配置
	DefaultRedisEnvironment = "offline" // 默认使用offline环境

	// 托管集群配置
	HostingMeshEndpoint      = "http://cce.%s.baidubce.com"
	HostingRegion            = "cloud.hostingRegion"
	HostingRegionClusterID   = "clusterid"
	HostingRegionClusterName = "clustername"
)

// AI网关配置获取函数
// GetVpcHostingID 获取VPC ID
func GetVpcHostingID() string {
	return viper.GetString(vpcIDKey)
}

// GetAITokenLimitPluginName 获取AI Token限流插件名称
func GetAITokenLimitPluginName() string {
	return viper.GetString(aiTokenLimitPluginNameKey)
}

// GetAITokenLimitPluginURL 获取AI Token限流插件URL
func GetAITokenLimitPluginURL() string {
	return viper.GetString(aiTokenLimitPluginURLKey)
}

// GetIpRestrictionPluginURL 获取IP黑白名单插件URL
func GetIpRestrictionPluginURL() string {
	return viper.GetString(ipRestrictionPluginURLKey)
}

// GetIpRestrictionTemplatePath 获取IP黑白名单模板路径
func GetIpRestrictionTemplatePath() string {
	return viper.GetString(ipRestrictionTemplatePathKey)
}

// GetAIStatisticsPluginName 获取AI可观测插件名称
func GetAIStatisticsPluginName() string {
	return viper.GetString(aiStatisticsPluginNameKey)
}

// GetAIStatisticsPluginURL 获取AI可观测插件URL
func GetAIStatisticsPluginURL() string {
	return viper.GetString(aiStatisticsPluginURLKey)
}

// GetAIStatisticsTemplatePath 获取AI可观测模板路径
func GetAIStatisticsTemplatePath() string {
	return viper.GetString(aiStatisticsTemplatePathKey)
}

// GetAITokenLimitPluginPriority 获取AI Token限流插件优先级
func GetAITokenLimitPluginPriority() int {
	return AITokenLimitPluginPriority
}

// GetRedisServiceName 获取Redis服务名称
func GetRedisServiceName() string {
	env := GetRedisEnvironment()
	return viper.GetString("aigw.redis." + env + ".service_name")
}

// GetRedisServicePort 获取Redis服务端口
func GetRedisServicePort() int {
	env := GetRedisEnvironment()
	return viper.GetInt("aigw.redis." + env + ".service_port")
}

// GetRedisPassword 获取Redis密码
func GetRedisPassword() string {
	env := GetRedisEnvironment()
	return viper.GetString("aigw.redis." + env + ".password")
}

// GetRedisEnvironment 获取Redis环境配置，默认为offline
func GetRedisEnvironment() string {
	env := viper.GetString("aigw.redis.environment")
	if env == "" {
		return DefaultRedisEnvironment
	}
	return env
}

// GetTokenRateLimitTemplatePath 获取Token限流模板路径
func GetTokenRateLimitTemplatePath() string {
	return viper.GetString(tokenRateLimitTemplatePathKey)
}

// GetKeyAuthPluginName 获取Key Auth插件名称
func GetKeyAuthPluginName() string {
	return viper.GetString(keyAuthPluginNameKey)
}

// GetKeyAuthPluginURL 获取Key Auth插件URL
func GetKeyAuthPluginURL() string {
	return viper.GetString(keyAuthPluginURLKey)
}

// GetKeyAuthPluginPriority 获取Key Auth插件优先级
func GetKeyAuthPluginPriority() int {
	return KeyAuthPluginPriority
}

// GetKeyAuthTemplatePath 获取Key Auth模板路径
func GetKeyAuthTemplatePath() string {
	return viper.GetString(keyAuthTemplatePathKey)
}

// GetVirtualDenyAllConsumerName 获取虚拟拒绝所有消费者名称
func GetVirtualDenyAllConsumerName() string {
	return VirtualDenyAllConsumerName
}

// GetAIQuotaPluginName 获取AI配额插件名称
func GetAIQuotaPluginName() string {
	return viper.GetString(aiQuotaPluginNameKey)
}

// GetAIQuotaPluginURL 获取AI配额插件URL
func GetAIQuotaPluginURL() string {
	return viper.GetString(aiQuotaPluginURLKey)
}

// GetAIQuotaPluginPriority 获取AI配额插件优先级
func GetAIQuotaPluginPriority() int {
	return AIQuotaPluginPriority
}

// GetAIQuotaTemplatePath 获取AI配额插件模板路径
func GetAIQuotaTemplatePath() string {
	return viper.GetString(aiQuotaTemplatePathKey)
}

// GetIPRestrictionPluginLabelSelector 获取IP黑白名单插件的标签选择器
func GetIPRestrictionPluginLabelSelector() string {
	return WasmPluginLabelPluginType + "=" + WasmPluginTypeIPRestriction
}

// GetExtAuthPluginName 获取外部认证插件名称
func GetExtAuthPluginName() string {
	return viper.GetString(extAuthPluginNameKey)
}

// GetExtAuthPluginURL 获取外部认证插件URL
func GetExtAuthPluginURL() string {
	return viper.GetString(extAuthPluginURLKey)
}

// GetExtAuthTemplatePath 获取外部认证插件模板路径
func GetExtAuthTemplatePath() string {
	return viper.GetString(extAuthTemplatePathKey)
}

// GetExtAuthPluginLabelSelector 获取外部认证插件的标签选择器
func GetExtAuthPluginLabelSelector() string {
	return WasmPluginLabelPluginType + "=" + WasmPluginTypeExtAuth
}

// GetHostingClusterByRegion 根据区域获取托管集群ID
func GetHostingClusterByRegion(region string) string {
	value := viper.GetStringMap(HostingRegion)
	clusterInfo, ok := value[region].(map[string]interface{})
	if !ok {
		return ""
	}

	clusterID, ok1 := clusterInfo[HostingRegionClusterID].(string)
	if !ok1 {
		return ""
	}
	return clusterID
}
