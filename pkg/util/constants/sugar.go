package constants

const (
	AccountID = "account_id"

	User = "\u7528\u6237"

	ID = "ID"

	UserName = "Name"

	InstanceScale = "\u7f51\u683c\u89c4\u6a21"

	StandaloneInstance = "standaloneInstance"

	HostingInstance = "hostingInstance"

	InstanceSum = "instanceSum"

	SidecarScale = "sidecar\u89c4\u6a21"

	StandaloneSidecar = "standaloneSidecar"

	HostingSidecar = "hostingSidecar"

	SidecarSum = "sidecarSum"

	Categories = "categories"

	Columns = "columns"

	SuperHeaders = "superHeaders"

	Rows = "rows"

	Series = "series"

	Standalone = "\u72ec\u7acb\u7f51\u683c"

	Hosting = "\u6258\u7ba1\u7f51\u683c"

	Sum = "\u603b\u8ba1"

	AKSKNotNull = "ak/sk is null"

	ComponentValue = "componentValue"
	Name           = "name"
	Code           = "code"
	AccountUUID    = "accountUuid"

	InstanceID = "\u7f51\u683c\u5b9e\u4f8bID"
	// CNAP用户中文名
	CnapName = "CNAP\u8d26\u6237\u4e2d\u6587\u540d"
	// CNAP用户英文名
	CnapCode        = "CNAP\u8d26\u6237\u82f1\u6587\u540d"
	UserAccountUUID = "\u4e91\u4e0a\u767e\u5ea6\u8d26\u53f7ID"

	InstanceID1 = "csm-hydlh994"
	InstanceID2 = "csm-iif0ggfl"
	InstanceID3 = "csm-6tegf0u8"
	Code1       = "crm-account-38a13"
	Code2       = "test-crm"
	Code3       = "crm-test-demo"

	ClusterType = "cluster_type"
)
