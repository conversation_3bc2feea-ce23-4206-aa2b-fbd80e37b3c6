package file

import (
	"io/fs"
	"os"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// filePerm 默认的文件权限
const filePerm = os.FileMode(0755)

// RemoveFile 删除文件
func RemoveFile(filepath string) error {
	_, err := os.Stat(filepath)
	if err == nil {
		return os.RemoveAll(filepath)
	}
	return nil
}

// RewriteFile 删除文件并且重新写入内容
func RewriteFile(ctx csmContext.CsmContext, path string, content []byte) error {
	return RewriteFileWithFilePerm(ctx, path, content, filePerm)
}

// RewriteFileWithFilePerm 删除文件并且携带文件属性重新写入内容
func RewriteFileWithFilePerm(ctx csmContext.CsmContext, path string, content []byte, perm fs.FileMode) error {
	ctx.CsmLogger().Infof("RewriteFileWithFilePerm path %s", path)
	_, err := os.Stat(path)
	if err == nil {
		ctx.CsmLogger().Infof("remove file %s", path)
		err = os.RemoveAll(path)
		if err != nil {
			return err
		}
	}
	return os.WriteFile(path, content, perm)
}

// AsString is a convenience wrapper around os.ReadFile that converts the content to a string.
func AsString(filename string) (string, error) {
	b, err := AsBytes(filename)
	if err != nil {
		return "", err
	}
	return string(b), nil
}

// AsBytes is a simple wrapper around os.ReadFile provided for completeness.
func AsBytes(filename string) ([]byte, error) {
	return os.ReadFile(filename)
}
