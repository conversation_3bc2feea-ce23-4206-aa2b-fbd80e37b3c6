package kube

import (
	"testing"

	"github.com/golang/mock/gomock"
	kubeVersion "k8s.io/apimachinery/pkg/version"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube/mock"
)

func TestIsAtLeastVersion(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	tests := []struct {
		name         string
		info         *kubeVersion.Info
		minorVersion uint
		want         bool
	}{
		{
			name: "exact match",
			info: &kubeVersion.Info{
				Major: "1",
				Minor: "20",
			},
			minorVersion: 20,
			want:         true,
		},
		{
			name: "too old",
			info: &kubeVersion.Info{
				Major: "1",
				Minor: "18",
			},
			minorVersion: 20,
			want:         false,
		},
		{
			name: "newer",
			info: &kubeVersion.Info{
				Major: "1",
				Minor: "21",
			},
			minorVersion: 20,
			want:         true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mock.NewMockClient(ctl)
			mock.EXPECT().GetKubernetesVersion().Return(tt.info, nil)
			if got := IsAtLeastVersion(mock, tt.minorVersion); got != tt.want {
				t.Errorf("IsAtLeastVersion() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsLessThanVersionVersion(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	tests := []struct {
		name         string
		info         *kubeVersion.Info
		minorVersion uint
		want         bool
	}{
		{
			name: "exact match",
			info: &kubeVersion.Info{
				Major: "1",
				Minor: "20",
			},
			minorVersion: 20,
			want:         false,
		},
		{
			name: "too old",
			info: &kubeVersion.Info{
				Major: "1",
				Minor: "18",
			},
			minorVersion: 20,
			want:         true,
		},
		{
			name: "newer",
			info: &kubeVersion.Info{
				Major: "1",
				Minor: "21",
			},
			minorVersion: 20,
			want:         false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mock.NewMockClient(ctl)
			mock.EXPECT().GetKubernetesVersion().Return(tt.info, nil)
			if got := IsLessThanVersion(mock, tt.minorVersion); got != tt.want {
				t.Errorf("IsLessThanVersion() = %v, want %v", got, tt.want)
			}
		})
	}
}
