package kube

import (
	"context"
	"fmt"
	"testing"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/rest"
	cr_client "sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/object"
)

var (
	mockCtx = csmContext.MockNewCsmContext()

	serviceYaml = `apiVersion: v1
kind: Service
metadata:
  name: istiod
  namespace: istio-system-csm-test01
  labels:
    app: istiod
spec:
  ports:
    - port: 15010
      name: grpc-xds # plaintext
      protocol: TCP
  selector:
    app: istiod`

	horizontalPodAutoscalerYaml = `apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  labels:
    app: istiod
  name: istiod
  namespace: istio-system-csm-test01
spec:
  maxReplicas: 5
  metrics:
  - resource:
      name: cpu
      targetAverageUtilization: 80
    type: Resource
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: istiod`

	roleYaml = `apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: istiod-istio-system-csm-test01
  namespace: istio-system-csm-test01
  labels:
    app: istiod
rules:
- apiGroups: ["networking.istio.io"]
  verbs: ["create"]
  resources: ["gateways"]
- apiGroups: [""]
  resources: ["secrets"]
  # TODO lock this down to istio-ca-cert if not using the DNS cert mesh config
  verbs: ["create", "get", "watch", "list", "update", "delete"]`
)

func TestExample(t *testing.T) {
	c := NewFakeClient()

	name := "test"
	testNamespace := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
		},
	}
	_, err := c.Kube().CoreV1().Namespaces().Create(context.TODO(), testNamespace, metav1.CreateOptions{})
	if err != nil {
		t.Errorf("create namespace %v error %v", testNamespace, err)
	}

	ns, err := c.Kube().CoreV1().Namespaces().Get(context.TODO(), "test", metav1.GetOptions{})
	if err != nil || ns.Name != testNamespace.Name {
		t.Errorf("want %s, but get error %v", name, err)
	}
}

func TestCreateOrUpdateK8sResource(t *testing.T) {
	testYaml := []string{serviceYaml, horizontalPodAutoscalerYaml, roleYaml}
	cases := []struct {
		desc    string
		data    []string
		wantErr bool
	}{
		{
			desc:    "CreateOrUpdateResources-succeed",
			data:    testYaml,
			wantErr: false,
		},
	}
	fc := NewFakeClient()
	obj, _ := object.ParseYAMLToK8sObject([]byte(serviceYaml))
	_ = fc.ControllerRuntimeClient().Create(context.TODO(), obj.UnstructuredObject(), &cr_client.CreateOptions{})

	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			err := CreateOrUpdateK8sResource(mockCtx, fc, tt.data)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOrUpdateResources() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCreateResources(t *testing.T) {
	testYaml := []string{serviceYaml, horizontalPodAutoscalerYaml, roleYaml}
	cases := []struct {
		desc    string
		data    []string
		wantErr bool
	}{
		{
			desc:    "CreateResources_fail",
			data:    testYaml,
			wantErr: true,
		},
	}
	fc := NewFakeClient()

	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			err := CreateResources(mockCtx, fc, tt.data)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateResources() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDeleteResources(t *testing.T) {
	testYaml := []string{serviceYaml, horizontalPodAutoscalerYaml, roleYaml}
	cases := []struct {
		desc    string
		data    []string
		wantErr bool
	}{
		{
			desc:    "CreateResources_fail",
			data:    testYaml,
			wantErr: true,
		},
	}
	fc := NewFakeClient()

	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			err := DeleteResources(mockCtx, fc, tt.data)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateResources() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDeleteResourceError(t *testing.T) {
	cases := []struct {
		desc   string
		error  string
		kind   string
		expect bool
	}{
		{
			desc:   "true",
			kind:   "envoyfilter",
			error:  fmt.Sprintf("no matches for kind \"envoyfilter\""),
			expect: true,
		},
		{
			desc:   "false",
			kind:   "envoyfilter",
			error:  fmt.Sprintf("no matches for kind \"xxx\""),
			expect: false,
		},
	}
	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			res := deleteResourceError(tt.error, tt.kind)
			if res != tt.expect {
				t.Errorf("deleteResourceError() got = %v, want %v", res, tt.expect)
			}
		})
	}
}

func TestWaitServiceReady(t *testing.T) {
	c := NewFakeClient()
	name := "istiod"
	namespace := "istio-system"
	service := createService(name, namespace, map[string]string{"app": "test"}, []int32{8080}, map[string]string{"app": "prod-app"})
	_, err := c.Kube().CoreV1().Services(namespace).Create(context.TODO(), service, metav1.CreateOptions{})
	if err != nil {
		t.Fatalf("Cannot create service %s in namespace %s (error: %v)", name, namespace, err)
	}
	cases := []struct {
		desc         string
		resourceName string
		namespace    string
		expect       bool
	}{
		{
			desc:         "ok-WaitServiceReady",
			resourceName: name,
			namespace:    namespace,
			expect:       true,
		},
		{
			desc:         "false-WaitServiceReady",
			resourceName: name,
			namespace:    "test",
			expect:       false,
		},
	}
	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			res := WaitServiceReady(mockCtx, c, tt.resourceName, tt.namespace)
			if (res == nil) != tt.expect {
				t.Errorf("WaitServiceReady() got error = %v, want %v", res, tt.expect)
			}
		})
	}
}

func createService(name, namespace string, annotations map[string]string,
	ports []int32, selector map[string]string) *corev1.Service {
	svcPorts := make([]corev1.ServicePort, 0)
	for _, p := range ports {
		svcPorts = append(svcPorts, corev1.ServicePort{
			Name:     "tcp-port",
			Port:     p,
			Protocol: "http",
		})
	}
	service := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:        name,
			Namespace:   namespace,
			Annotations: annotations,
		},
		Spec: corev1.ServiceSpec{
			ClusterIP: "********",
			Ports:     svcPorts,
			Selector:  selector,
			Type:      corev1.ServiceTypeClusterIP,
		},
	}
	return service
}

func createDeployment(name string, namespace string) *appsv1.Deployment {
	return &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: csm.Int32(2),
		},
	}
}

func TestWaitDeploymentReady(t *testing.T) {
	c := NewFakeClient()
	name := "istiod"
	namespace := "istio-system"

	deploy := createDeployment(name, namespace)
	_, err := c.Kube().AppsV1().Deployments(namespace).Create(context.TODO(), deploy, metav1.CreateOptions{})
	if err != nil {
		t.Fatalf("Cannot create service %s in namespace %s (error: %v)", name, namespace, err)
	}
	cases := []struct {
		desc         string
		resourceName string
		namespace    string
		expect       bool
	}{
		{
			desc:         "false-WaitDeploymentReady",
			resourceName: name,
			namespace:    "test",
			expect:       false,
		},
	}
	for _, tt := range cases {
		t.Run(tt.desc, func(t *testing.T) {
			res := WaitDeploymentReady(mockCtx, c, tt.resourceName, tt.namespace)
			if (res == nil) != tt.expect {
				t.Errorf("WaitDeploymentReady() got error = %v, want %v", res, tt.expect)
			}
		})
	}
}

func TestNewClientWithConfig(t *testing.T) {
	mockConfig := &rest.Config{
		Timeout: resyncInterval,
	}
	_, _ = NewClientWithConfig(mockConfig)

}
