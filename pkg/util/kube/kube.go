package kube

import (
	"context"
	"fmt"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	cr_client "sigs.k8s.io/controller-runtime/pkg/client"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/object"
)

// DeleteResources deletes kubernetes object with arrays
func DeleteResources(ctx csmContext.CsmContext, c Client, k8sResource []string) error {
	var errs util.Errors
	crClient, err := cr_client.New(c.RESTConfig(), cr_client.Options{})
	if err != nil {
		return err
	}
	for _, str := range k8sResource {
		obj, err := object.ParseYAMLToK8sObject([]byte(str))
		if err != nil {
			return err
		}
		deleteErr := crClient.Delete(context.TODO(), obj.UnstructuredObject(), cr_client.PropagationPolicy(metav1.DeletePropagationBackground))
		ctx.CsmLogger().Infof("Deleting %s", obj.Hash())
		if deleteErr != nil {
			if !kubeErrors.IsNotFound(deleteErr) && !deleteResourceError(deleteErr.Error(), obj.Kind) {
				errs = util.AppendErr(errs, deleteErr)
			} else {
				// do not return error if resources are not found
				ctx.CsmLogger().Warnf("object: %s is not being deleted because it no longer exists", obj.Hash())
				continue
			}
		}
		ctx.CsmLogger().Infof("Removed %s.", obj.Hash())

		// todo we can use better way to do rather than sleep 200 time.Millisecond
		time.Sleep(200 * time.Millisecond)
	}
	return errs.ToError()
}

// deleteResourceError deal with err with k8s object kind
func deleteResourceError(err, kind string) bool {
	value := fmt.Sprintf("no matches for kind \"%s\"", kind)
	return strings.Contains(err, value)
}

// DeleteK8sResource 删除 k8s 资源
func DeleteK8sResource(ctx csmContext.CsmContext, c Client, k8sResource []string) error {
	var errs util.Errors
	for _, str := range k8sResource {
		obj, parseErr := object.ParseYAMLToK8sObject([]byte(str))
		if parseErr != nil {
			return parseErr
		}
		deleteErr := c.ControllerRuntimeClient().Delete(context.TODO(), obj.UnstructuredObject())
		ctx.CsmLogger().Infof("Deleting %s", obj.Hash())
		if deleteErr != nil {
			if !kubeErrors.IsNotFound(deleteErr) {
				errs = util.AppendErr(errs, deleteErr)
			} else {
				ctx.CsmLogger().Debugf("object: %s not exist", obj.Hash())
				continue
			}
		}
		ctx.CsmLogger().Infof("Deleted %s.", obj.Hash())
		time.Sleep(500 * time.Millisecond)
	}
	return errs.ToError()
}

func CreateOrUpdateK8sResource(ctx csmContext.CsmContext, c Client, k8sResource []string) error {
	var errs util.Errors
	for _, str := range k8sResource {
		obj, parseErr := object.ParseYAMLToK8sObject([]byte(str))
		if parseErr != nil {
			return parseErr
		}
		createErr := c.ControllerRuntimeClient().Create(context.TODO(), obj.UnstructuredObject(), &cr_client.CreateOptions{})
		if createErr != nil {
			if kubeErrors.IsAlreadyExists(createErr) {
				ctx.CsmLogger().Warnf("object: %s has existed, trying to update it", obj.Hash())
				updateErr := c.ControllerRuntimeClient().Update(context.TODO(), obj.UnstructuredObject(), &cr_client.UpdateOptions{})
				if updateErr != nil {
					ctx.CsmLogger().Warnf("object: %s update failed: %v", obj.Hash(), updateErr)
				}
				continue
			} else {
				// do not return error if resources are not found
				errs = util.AppendErr(errs, createErr)
			}
		}
		ctx.CsmLogger().Infof("Created %s.", obj.Hash())
		time.Sleep(500 * time.Millisecond)
	}
	return errs.ToError()
}

// CreateResources creates kubernetes object with arrays
func CreateResources(ctx csmContext.CsmContext, c Client, k8sResource []string) error {
	var errs util.Errors
	crClient, err := cr_client.New(c.RESTConfig(), cr_client.Options{})
	if err != nil {
		return err
	}
	for _, str := range k8sResource {
		obj, parseErr := object.ParseYAMLToK8sObject([]byte(str))
		if parseErr != nil {
			return parseErr
		}
		// todo maybe we can merge create/update and add retry
		createErr := crClient.Create(context.TODO(), obj.UnstructuredObject(), &cr_client.CreateOptions{})
		ctx.CsmLogger().Infof("Creating %s", obj.Hash())
		if createErr != nil {
			if !kubeErrors.IsAlreadyExists(createErr) {
				errs = util.AppendErr(errs, createErr)
			} else {
				// do not return error if resources are not found
				ctx.CsmLogger().Warnf("object: %s has exists", obj.Hash())
				continue
			}
		}
		ctx.CsmLogger().Infof("Created %s.", obj.Hash())
		// todo we can use better way to do rather than sleep 200 time.Millisecond
		time.Sleep(200 * time.Millisecond)
	}
	return errs.ToError()
}

// waitEventReady waits the event func() ready
func waitEventReady(event func() (bool, error)) error {
	return wait.PollImmediate(constants.PollInternal, constants.Internal, event)
}

// WaitMutatingWebhookConfigurationReady waits the service ready util timeout
func WaitMutatingWebhookConfigurationReady(ctx csmContext.CsmContext, c Client, resourceName string) error {
	mwc := func() (bool, error) {
		svc, err := c.Kube().AdmissionregistrationV1().MutatingWebhookConfigurations().Get(context.TODO(), resourceName, metav1.GetOptions{})
		ctx.CsmLogger().Infof("waiting MutatingWebhookConfigurations=%s ready", resourceName)
		if err != nil || svc == nil {
			return false, err
		}
		return true, nil
	}
	return waitEventReady(mwc)
}

// WaitServiceReady waits the service ready util timeout
func WaitServiceReady(ctx csmContext.CsmContext, c Client, resourceName, namespace string) error {
	service := func() (bool, error) {
		svc, err := c.Kube().CoreV1().Services(namespace).Get(context.TODO(), resourceName, metav1.GetOptions{})
		if err != nil || svc == nil {
			ctx.CsmLogger().Infof("waiting service=%s/namespace=%s ready", resourceName, namespace)
			return false, err
		}
		if svc.Spec.Type == corev1.ServiceTypeLoadBalancer {
			ingress := svc.Status.LoadBalancer.Ingress
			if len(ingress) == 0 {
				ctx.CsmLogger().Infof("waiting cce-lb-controller sync LoadBalancer for service=%s/namespace=%s",
					resourceName, namespace)
				return false, err
			}
		}
		ctx.CsmLogger().Infof("namespace=%s, ingress %v", namespace, svc.Status.LoadBalancer.Ingress)
		return true, nil
	}
	return waitEventReady(service)
}

// WaitDeploymentReady waits the deployment ready util timeout
func WaitDeploymentReady(ctx csmContext.CsmContext, c Client, resourceName, namespace string) error {
	deployment := func() (bool, error) {
		deploy, err := c.Kube().AppsV1().Deployments(namespace).Get(context.TODO(), resourceName, metav1.GetOptions{})
		ctx.CsmLogger().Infof("waiting deployment=%s/namespace=%s ready", resourceName, namespace)
		if err != nil {
			return false, err // stop wait with error
		}
		if deploy.Status.ReadyReplicas > 0 {
			return true, nil
		}
		return false, nil
	}
	return waitEventReady(deployment)
}

// WaitNamespaceReady return true util namespace is ready
func WaitNamespaceReady(ctx csmContext.CsmContext, c Client, namespace string) error {
	err := wait.PollImmediate(constants.PollInternal, constants.Internal, func() (bool, error) {
		ctx.CsmLogger().Infof("wait %s namespace ready", namespace)
		_, err := c.Kube().CoreV1().Namespaces().Get(context.TODO(), namespace, metav1.GetOptions{})
		if err == nil {
			return true, nil // done
		}
		if err != nil {
			return false, err // stop wait with error
		}
		return false, nil
	})
	if err != nil {
		return err
	}
	return nil
}
