// Code generated by MockGen. DO NOT EDIT.
// Source: client.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	versioned "istio.io/client-go/pkg/clientset/versioned"
	externalversions "istio.io/client-go/pkg/informers/externalversions"
	clientset "k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	version "k8s.io/apimachinery/pkg/version"
	discovery "k8s.io/client-go/discovery"
	dynamic "k8s.io/client-go/dynamic"
	dynamicinformer "k8s.io/client-go/dynamic/dynamicinformer"
	informers "k8s.io/client-go/informers"
	kubernetes "k8s.io/client-go/kubernetes"
	metadata "k8s.io/client-go/metadata"
	metadatainformer "k8s.io/client-go/metadata/metadatainformer"
	rest "k8s.io/client-go/rest"
	testing "k8s.io/client-go/testing"
	client "sigs.k8s.io/controller-runtime/pkg/client"
	versioned0 "sigs.k8s.io/gateway-api/pkg/client/clientset/versioned"
	externalversions0 "sigs.k8s.io/gateway-api/pkg/client/informers/externalversions"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// ControllerRuntimeClient mocks base method.
func (m *MockClient) ControllerRuntimeClient() client.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ControllerRuntimeClient")
	ret0, _ := ret[0].(client.Client)
	return ret0
}

// ControllerRuntimeClient indicates an expected call of ControllerRuntimeClient.
func (mr *MockClientMockRecorder) ControllerRuntimeClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ControllerRuntimeClient", reflect.TypeOf((*MockClient)(nil).ControllerRuntimeClient))
}

// Discovery mocks base method.
func (m *MockClient) Discovery() discovery.DiscoveryInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Discovery")
	ret0, _ := ret[0].(discovery.DiscoveryInterface)
	return ret0
}

// Discovery indicates an expected call of Discovery.
func (mr *MockClientMockRecorder) Discovery() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Discovery", reflect.TypeOf((*MockClient)(nil).Discovery))
}

// Dynamic mocks base method.
func (m *MockClient) Dynamic() dynamic.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Dynamic")
	ret0, _ := ret[0].(dynamic.Interface)
	return ret0
}

// Dynamic indicates an expected call of Dynamic.
func (mr *MockClientMockRecorder) Dynamic() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Dynamic", reflect.TypeOf((*MockClient)(nil).Dynamic))
}

// DynamicInformer mocks base method.
func (m *MockClient) DynamicInformer() dynamicinformer.DynamicSharedInformerFactory {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DynamicInformer")
	ret0, _ := ret[0].(dynamicinformer.DynamicSharedInformerFactory)
	return ret0
}

// DynamicInformer indicates an expected call of DynamicInformer.
func (mr *MockClientMockRecorder) DynamicInformer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DynamicInformer", reflect.TypeOf((*MockClient)(nil).DynamicInformer))
}

// Ext mocks base method.
func (m *MockClient) Ext() clientset.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ext")
	ret0, _ := ret[0].(clientset.Interface)
	return ret0
}

// Ext indicates an expected call of Ext.
func (mr *MockClientMockRecorder) Ext() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ext", reflect.TypeOf((*MockClient)(nil).Ext))
}

// GatewayAPI mocks base method.
func (m *MockClient) GatewayAPI() versioned0.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GatewayAPI")
	ret0, _ := ret[0].(versioned0.Interface)
	return ret0
}

// GatewayAPI indicates an expected call of GatewayAPI.
func (mr *MockClientMockRecorder) GatewayAPI() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GatewayAPI", reflect.TypeOf((*MockClient)(nil).GatewayAPI))
}

// GatewayAPIInformer mocks base method.
func (m *MockClient) GatewayAPIInformer() externalversions0.SharedInformerFactory {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GatewayAPIInformer")
	ret0, _ := ret[0].(externalversions0.SharedInformerFactory)
	return ret0
}

// GatewayAPIInformer indicates an expected call of GatewayAPIInformer.
func (mr *MockClientMockRecorder) GatewayAPIInformer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GatewayAPIInformer", reflect.TypeOf((*MockClient)(nil).GatewayAPIInformer))
}

// GetKubernetesVersion mocks base method.
func (m *MockClient) GetKubernetesVersion() (*version.Info, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKubernetesVersion")
	ret0, _ := ret[0].(*version.Info)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKubernetesVersion indicates an expected call of GetKubernetesVersion.
func (mr *MockClientMockRecorder) GetKubernetesVersion() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKubernetesVersion", reflect.TypeOf((*MockClient)(nil).GetKubernetesVersion))
}

// Istio mocks base method.
func (m *MockClient) Istio() versioned.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Istio")
	ret0, _ := ret[0].(versioned.Interface)
	return ret0
}

// Istio indicates an expected call of Istio.
func (mr *MockClientMockRecorder) Istio() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Istio", reflect.TypeOf((*MockClient)(nil).Istio))
}

// IstioInformer mocks base method.
func (m *MockClient) IstioInformer() externalversions.SharedInformerFactory {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IstioInformer")
	ret0, _ := ret[0].(externalversions.SharedInformerFactory)
	return ret0
}

// IstioInformer indicates an expected call of IstioInformer.
func (mr *MockClientMockRecorder) IstioInformer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IstioInformer", reflect.TypeOf((*MockClient)(nil).IstioInformer))
}

// Kube mocks base method.
func (m *MockClient) Kube() kubernetes.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Kube")
	ret0, _ := ret[0].(kubernetes.Interface)
	return ret0
}

// Kube indicates an expected call of Kube.
func (mr *MockClientMockRecorder) Kube() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Kube", reflect.TypeOf((*MockClient)(nil).Kube))
}

// KubeInformer mocks base method.
func (m *MockClient) KubeInformer() informers.SharedInformerFactory {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KubeInformer")
	ret0, _ := ret[0].(informers.SharedInformerFactory)
	return ret0
}

// KubeInformer indicates an expected call of KubeInformer.
func (mr *MockClientMockRecorder) KubeInformer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KubeInformer", reflect.TypeOf((*MockClient)(nil).KubeInformer))
}

// Metadata mocks base method.
func (m *MockClient) Metadata() metadata.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Metadata")
	ret0, _ := ret[0].(metadata.Interface)
	return ret0
}

// Metadata indicates an expected call of Metadata.
func (mr *MockClientMockRecorder) Metadata() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Metadata", reflect.TypeOf((*MockClient)(nil).Metadata))
}

// MetadataInformer mocks base method.
func (m *MockClient) MetadataInformer() metadatainformer.SharedInformerFactory {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MetadataInformer")
	ret0, _ := ret[0].(metadatainformer.SharedInformerFactory)
	return ret0
}

// MetadataInformer indicates an expected call of MetadataInformer.
func (mr *MockClientMockRecorder) MetadataInformer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MetadataInformer", reflect.TypeOf((*MockClient)(nil).MetadataInformer))
}

// RESTConfig mocks base method.
func (m *MockClient) RESTConfig() *rest.Config {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RESTConfig")
	ret0, _ := ret[0].(*rest.Config)
	return ret0
}

// RESTConfig indicates an expected call of RESTConfig.
func (mr *MockClientMockRecorder) RESTConfig() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RESTConfig", reflect.TypeOf((*MockClient)(nil).RESTConfig))
}

// RunAndWait mocks base method.
func (m *MockClient) RunAndWait(stop <-chan struct{}) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RunAndWait", stop)
}

// RunAndWait indicates an expected call of RunAndWait.
func (mr *MockClientMockRecorder) RunAndWait(stop interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunAndWait", reflect.TypeOf((*MockClient)(nil).RunAndWait), stop)
}

// MockExtendedClient is a mock of ExtendedClient interface.
type MockExtendedClient struct {
	ctrl     *gomock.Controller
	recorder *MockExtendedClientMockRecorder
}

// MockExtendedClientMockRecorder is the mock recorder for MockExtendedClient.
type MockExtendedClientMockRecorder struct {
	mock *MockExtendedClient
}

// NewMockExtendedClient creates a new mock instance.
func NewMockExtendedClient(ctrl *gomock.Controller) *MockExtendedClient {
	mock := &MockExtendedClient{ctrl: ctrl}
	mock.recorder = &MockExtendedClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExtendedClient) EXPECT() *MockExtendedClientMockRecorder {
	return m.recorder
}

// ControllerRuntimeClient mocks base method.
func (m *MockExtendedClient) ControllerRuntimeClient() client.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ControllerRuntimeClient")
	ret0, _ := ret[0].(client.Client)
	return ret0
}

// ControllerRuntimeClient indicates an expected call of ControllerRuntimeClient.
func (mr *MockExtendedClientMockRecorder) ControllerRuntimeClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ControllerRuntimeClient", reflect.TypeOf((*MockExtendedClient)(nil).ControllerRuntimeClient))
}

// Discovery mocks base method.
func (m *MockExtendedClient) Discovery() discovery.DiscoveryInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Discovery")
	ret0, _ := ret[0].(discovery.DiscoveryInterface)
	return ret0
}

// Discovery indicates an expected call of Discovery.
func (mr *MockExtendedClientMockRecorder) Discovery() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Discovery", reflect.TypeOf((*MockExtendedClient)(nil).Discovery))
}

// Dynamic mocks base method.
func (m *MockExtendedClient) Dynamic() dynamic.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Dynamic")
	ret0, _ := ret[0].(dynamic.Interface)
	return ret0
}

// Dynamic indicates an expected call of Dynamic.
func (mr *MockExtendedClientMockRecorder) Dynamic() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Dynamic", reflect.TypeOf((*MockExtendedClient)(nil).Dynamic))
}

// DynamicInformer mocks base method.
func (m *MockExtendedClient) DynamicInformer() dynamicinformer.DynamicSharedInformerFactory {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DynamicInformer")
	ret0, _ := ret[0].(dynamicinformer.DynamicSharedInformerFactory)
	return ret0
}

// DynamicInformer indicates an expected call of DynamicInformer.
func (mr *MockExtendedClientMockRecorder) DynamicInformer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DynamicInformer", reflect.TypeOf((*MockExtendedClient)(nil).DynamicInformer))
}

// Ext mocks base method.
func (m *MockExtendedClient) Ext() clientset.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ext")
	ret0, _ := ret[0].(clientset.Interface)
	return ret0
}

// Ext indicates an expected call of Ext.
func (mr *MockExtendedClientMockRecorder) Ext() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ext", reflect.TypeOf((*MockExtendedClient)(nil).Ext))
}

// GatewayAPI mocks base method.
func (m *MockExtendedClient) GatewayAPI() versioned0.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GatewayAPI")
	ret0, _ := ret[0].(versioned0.Interface)
	return ret0
}

// GatewayAPI indicates an expected call of GatewayAPI.
func (mr *MockExtendedClientMockRecorder) GatewayAPI() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GatewayAPI", reflect.TypeOf((*MockExtendedClient)(nil).GatewayAPI))
}

// GatewayAPIInformer mocks base method.
func (m *MockExtendedClient) GatewayAPIInformer() externalversions0.SharedInformerFactory {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GatewayAPIInformer")
	ret0, _ := ret[0].(externalversions0.SharedInformerFactory)
	return ret0
}

// GatewayAPIInformer indicates an expected call of GatewayAPIInformer.
func (mr *MockExtendedClientMockRecorder) GatewayAPIInformer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GatewayAPIInformer", reflect.TypeOf((*MockExtendedClient)(nil).GatewayAPIInformer))
}

// GetKubernetesVersion mocks base method.
func (m *MockExtendedClient) GetKubernetesVersion() (*version.Info, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKubernetesVersion")
	ret0, _ := ret[0].(*version.Info)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKubernetesVersion indicates an expected call of GetKubernetesVersion.
func (mr *MockExtendedClientMockRecorder) GetKubernetesVersion() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKubernetesVersion", reflect.TypeOf((*MockExtendedClient)(nil).GetKubernetesVersion))
}

// Istio mocks base method.
func (m *MockExtendedClient) Istio() versioned.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Istio")
	ret0, _ := ret[0].(versioned.Interface)
	return ret0
}

// Istio indicates an expected call of Istio.
func (mr *MockExtendedClientMockRecorder) Istio() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Istio", reflect.TypeOf((*MockExtendedClient)(nil).Istio))
}

// IstioInformer mocks base method.
func (m *MockExtendedClient) IstioInformer() externalversions.SharedInformerFactory {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IstioInformer")
	ret0, _ := ret[0].(externalversions.SharedInformerFactory)
	return ret0
}

// IstioInformer indicates an expected call of IstioInformer.
func (mr *MockExtendedClientMockRecorder) IstioInformer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IstioInformer", reflect.TypeOf((*MockExtendedClient)(nil).IstioInformer))
}

// Kube mocks base method.
func (m *MockExtendedClient) Kube() kubernetes.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Kube")
	ret0, _ := ret[0].(kubernetes.Interface)
	return ret0
}

// Kube indicates an expected call of Kube.
func (mr *MockExtendedClientMockRecorder) Kube() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Kube", reflect.TypeOf((*MockExtendedClient)(nil).Kube))
}

// KubeInformer mocks base method.
func (m *MockExtendedClient) KubeInformer() informers.SharedInformerFactory {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KubeInformer")
	ret0, _ := ret[0].(informers.SharedInformerFactory)
	return ret0
}

// KubeInformer indicates an expected call of KubeInformer.
func (mr *MockExtendedClientMockRecorder) KubeInformer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KubeInformer", reflect.TypeOf((*MockExtendedClient)(nil).KubeInformer))
}

// Metadata mocks base method.
func (m *MockExtendedClient) Metadata() metadata.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Metadata")
	ret0, _ := ret[0].(metadata.Interface)
	return ret0
}

// Metadata indicates an expected call of Metadata.
func (mr *MockExtendedClientMockRecorder) Metadata() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Metadata", reflect.TypeOf((*MockExtendedClient)(nil).Metadata))
}

// MetadataInformer mocks base method.
func (m *MockExtendedClient) MetadataInformer() metadatainformer.SharedInformerFactory {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MetadataInformer")
	ret0, _ := ret[0].(metadatainformer.SharedInformerFactory)
	return ret0
}

// MetadataInformer indicates an expected call of MetadataInformer.
func (mr *MockExtendedClientMockRecorder) MetadataInformer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MetadataInformer", reflect.TypeOf((*MockExtendedClient)(nil).MetadataInformer))
}

// RESTConfig mocks base method.
func (m *MockExtendedClient) RESTConfig() *rest.Config {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RESTConfig")
	ret0, _ := ret[0].(*rest.Config)
	return ret0
}

// RESTConfig indicates an expected call of RESTConfig.
func (mr *MockExtendedClientMockRecorder) RESTConfig() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RESTConfig", reflect.TypeOf((*MockExtendedClient)(nil).RESTConfig))
}

// RunAndWait mocks base method.
func (m *MockExtendedClient) RunAndWait(stop <-chan struct{}) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RunAndWait", stop)
}

// RunAndWait indicates an expected call of RunAndWait.
func (mr *MockExtendedClientMockRecorder) RunAndWait(stop interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunAndWait", reflect.TypeOf((*MockExtendedClient)(nil).RunAndWait), stop)
}

// MockreflectInformerSync is a mock of reflectInformerSync interface.
type MockreflectInformerSync struct {
	ctrl     *gomock.Controller
	recorder *MockreflectInformerSyncMockRecorder
}

// MockreflectInformerSyncMockRecorder is the mock recorder for MockreflectInformerSync.
type MockreflectInformerSyncMockRecorder struct {
	mock *MockreflectInformerSync
}

// NewMockreflectInformerSync creates a new mock instance.
func NewMockreflectInformerSync(ctrl *gomock.Controller) *MockreflectInformerSync {
	mock := &MockreflectInformerSync{ctrl: ctrl}
	mock.recorder = &MockreflectInformerSyncMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockreflectInformerSync) EXPECT() *MockreflectInformerSyncMockRecorder {
	return m.recorder
}

// WaitForCacheSync mocks base method.
func (m *MockreflectInformerSync) WaitForCacheSync(stopCh <-chan struct{}) map[reflect.Type]bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WaitForCacheSync", stopCh)
	ret0, _ := ret[0].(map[reflect.Type]bool)
	return ret0
}

// WaitForCacheSync indicates an expected call of WaitForCacheSync.
func (mr *MockreflectInformerSyncMockRecorder) WaitForCacheSync(stopCh interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitForCacheSync", reflect.TypeOf((*MockreflectInformerSync)(nil).WaitForCacheSync), stopCh)
}

// MockdynamicInformerSync is a mock of dynamicInformerSync interface.
type MockdynamicInformerSync struct {
	ctrl     *gomock.Controller
	recorder *MockdynamicInformerSyncMockRecorder
}

// MockdynamicInformerSyncMockRecorder is the mock recorder for MockdynamicInformerSync.
type MockdynamicInformerSyncMockRecorder struct {
	mock *MockdynamicInformerSync
}

// NewMockdynamicInformerSync creates a new mock instance.
func NewMockdynamicInformerSync(ctrl *gomock.Controller) *MockdynamicInformerSync {
	mock := &MockdynamicInformerSync{ctrl: ctrl}
	mock.recorder = &MockdynamicInformerSyncMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockdynamicInformerSync) EXPECT() *MockdynamicInformerSyncMockRecorder {
	return m.recorder
}

// WaitForCacheSync mocks base method.
func (m *MockdynamicInformerSync) WaitForCacheSync(stopCh <-chan struct{}) map[schema.GroupVersionResource]bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WaitForCacheSync", stopCh)
	ret0, _ := ret[0].(map[schema.GroupVersionResource]bool)
	return ret0
}

// WaitForCacheSync indicates an expected call of WaitForCacheSync.
func (mr *MockdynamicInformerSyncMockRecorder) WaitForCacheSync(stopCh interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitForCacheSync", reflect.TypeOf((*MockdynamicInformerSync)(nil).WaitForCacheSync), stopCh)
}

// MockfakeClient is a mock of fakeClient interface.
type MockfakeClient struct {
	ctrl     *gomock.Controller
	recorder *MockfakeClientMockRecorder
}

// MockfakeClientMockRecorder is the mock recorder for MockfakeClient.
type MockfakeClientMockRecorder struct {
	mock *MockfakeClient
}

// NewMockfakeClient creates a new mock instance.
func NewMockfakeClient(ctrl *gomock.Controller) *MockfakeClient {
	mock := &MockfakeClient{ctrl: ctrl}
	mock.recorder = &MockfakeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockfakeClient) EXPECT() *MockfakeClientMockRecorder {
	return m.recorder
}

// PrependReactor mocks base method.
func (m *MockfakeClient) PrependReactor(verb, resource string, reaction testing.ReactionFunc) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "PrependReactor", verb, resource, reaction)
}

// PrependReactor indicates an expected call of PrependReactor.
func (mr *MockfakeClientMockRecorder) PrependReactor(verb, resource, reaction interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrependReactor", reflect.TypeOf((*MockfakeClient)(nil).PrependReactor), verb, resource, reaction)
}

// PrependWatchReactor mocks base method.
func (m *MockfakeClient) PrependWatchReactor(resource string, reaction testing.WatchReactionFunc) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "PrependWatchReactor", resource, reaction)
}

// PrependWatchReactor indicates an expected call of PrependWatchReactor.
func (mr *MockfakeClientMockRecorder) PrependWatchReactor(resource, reaction interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrependWatchReactor", reflect.TypeOf((*MockfakeClient)(nil).PrependWatchReactor), resource, reaction)
}

// Tracker mocks base method.
func (m *MockfakeClient) Tracker() testing.ObjectTracker {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Tracker")
	ret0, _ := ret[0].(testing.ObjectTracker)
	return ret0
}

// Tracker indicates an expected call of Tracker.
func (mr *MockfakeClientMockRecorder) Tracker() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Tracker", reflect.TypeOf((*MockfakeClient)(nil).Tracker))
}
