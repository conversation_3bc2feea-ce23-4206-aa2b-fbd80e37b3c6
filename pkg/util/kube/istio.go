package kube

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

const configMapName = "istio-ca-root-cert"

type PatchStringValue struct {
	Op    string      `json:"op"`
	Path  string      `json:"path"`
	Value interface{} `json:"value"`
}

// DeleteNamespaceWithFinalizers 强制删除命名空间
func DeleteNamespaceWithFinalizers(ctx csmContext.CsmContext, client Client, ns *corev1.Namespace) error {
	ctx.CsmLogger().Infof("the namespace is %s with %v", ns.Name, ns)
	ns.Spec.Finalizers = nil
	payloadBytes, err := json.Marshal(ns)
	if err != nil {
		return err
	}
	err = client.Discovery().RESTClient().Put().AbsPath(fmt.Sprintf("/api/v1/namespaces/%s/finalize", ns.Name)).
		Body(payloadBytes).Do(context.TODO()).Error()
	return err
}

// DeleteAllIstioCARootCert 删除所有由 istio 生成的 "istio-ca-root-cert"
func DeleteAllIstioCARootCert(ctx csmContext.CsmContext, client Client) error {
	allNamespace, err := client.Kube().CoreV1().Namespaces().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return err
	}
	var wg sync.WaitGroup
	for _, ns := range allNamespace.Items {
		wg.Add(1)
		go func(ns corev1.Namespace) {
			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Infof("DeleteAllIstioCARootCert %v", e)
				}
				wg.Done()
			}()
			cmErr := client.Kube().CoreV1().ConfigMaps(ns.Name).Delete(context.TODO(), configMapName, metav1.DeleteOptions{})
			if cmErr != nil && !errors.IsNotFound(cmErr) {
				ctx.CsmLogger().Errorf("remove configmap %s in %s namespace error %v", configMapName, ns.Name, cmErr)
			} else {
				ctx.CsmLogger().Infof("remove configmap %s in %s namespace success", configMapName, ns.Name)
			}
		}(ns)
	}
	wg.Wait()
	return err
}
