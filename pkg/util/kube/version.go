package kube

import (
	"fmt"

	"k8s.io/apimachinery/pkg/util/version"
)

// IsAtLeastVersion returns true if the client is at least the specified version.
// For example, on Kubernetes v1.20.2, IsAtLeastVersion(18) == true, IsAtLeastVersion(21) == false
func IsAtLeastVersion(client Client, minorVersion uint) bool {
	clusterVersion, err := client.GetKubernetesVersion()
	if err != nil {
		return true
	}
	cv, err := version.ParseGeneric(fmt.Sprintf("v%s.%s.0", clusterVersion.Major, clusterVersion.Minor))
	if err != nil {
		return true
	}
	ev, err := version.ParseGeneric(fmt.Sprintf("v1.%d.0", minorVersion))
	if err != nil {
		return true
	}
	return cv.AtLeast(ev)
}

// IsLessThanVersion returns true if the client version is less than the specified version.
// For example, on Kubernetes v1.20.2, IsLessThanVersion(18) == false, IsLessThanVersion(21) == true
func IsLessThanVersion(client Client, minorVersion uint) bool {
	clusterVersion, err := client.GetKubernetesVersion()
	if err != nil {
		return true
	}
	cv, err := version.ParseGeneric(fmt.Sprintf("v%s.%s.0", clusterVersion.Major, clusterVersion.Minor))
	if err != nil {
		return true
	}
	ev, err := version.ParseGeneric(fmt.Sprintf("v1.%d.0", minorVersion))
	if err != nil {
		return true
	}
	return cv.LessThan(ev)
}
