package csmlog

import (
	"github.com/spf13/viper"
)

const (
	logDir     = "server.logs.logDir"
	logLevel   = "server.logs.logLevel"
	maxSize    = "server.logs.maxSize"
	maxBackups = "server.logs.maxBackups"
	maxAge     = "server.logs.maxAge"
	compress   = "server.logs.compress"
)

// loggerOptT collects all the global state of the logging setup.
type loggerOptT struct {
	// If non-empty, write log files in this directory
	logDir string

	// zap log level, valid options are
	// ("debug", "info", "warn", "error", "dpanic", "panic", and "fatal").
	level string

	// whether enable timetrack log, may affect performace if set to 'true'
	enableTimetrack bool

	// 文件大小限制,单位MB 默认值 500MB
	maxSize int

	// 最大保留日志文件数量  默认值 10
	maxBackups int

	// 日志文件保留天数 默认值 30
	maxAge int

	// 是否压缩处理日志,默认值 false
	compress bool
}

func NewLoggerOpt() loggerOptT {
	maxSizeValue := viper.GetInt(maxSize)
	if maxSizeValue == 0 {
		maxSizeValue = 500
	}
	maxBackupsValue := viper.GetInt(maxBackups)
	if maxBackupsValue == 0 {
		maxBackupsValue = 10
	}
	maxAgeValue := viper.GetInt(maxAge)
	if maxAgeValue == 0 {
		maxAgeValue = 30
	}
	compressValue := viper.GetBool(compress)
	lo := loggerOptT{
		logDir:     viper.GetString(logDir),
		level:      viper.GetString(logLevel),
		maxSize:    maxSizeValue,
		maxBackups: maxBackupsValue,
		maxAge:     maxAgeValue,
		compress:   compressValue,
	}
	return lo
}
