package csmlog

// <PERSON><PERSON><PERSON> creates an entry from the standard logger and adds a field to
// it. If you want multiple fields, use `WithFields`.
//
// Note that it doesn't log until you call Debug, Print, Info, Warn, Fatal
// or Panic on the Entry it returns.
func GlobalWithField(key string, value interface{}) *Logger {
	globalLogger = globalLogger.With<PERSON>ield(key, value)
	return globalLogger
}

// With<PERSON>ields creates an entry from the standard logger and adds multiple
// fields to it. This is simply a helper for `<PERSON><PERSON>ield`, invoking it
// once for each field.
//
// Note that it doesn't log until you call Debug, Print, Info, Warn, Fatal
// or Panic on the Entry it returns.
func GlobalWithFields(fields Fields) *Logger {
	globalLogger = globalLogger.WithFields(fields)
	return globalLogger
}

func WithFields(fields Fields) *Logger {
	return globalLogger.WithFields(fields)
}

func WithField(key string, value interface{}) *Logger {
	return globalLogger.WithField(key, value)
}

// Debug logs a message at level Debug on the standard logger.
func Debug(msg string) {
	globalLogger.Debug(msg)
}

// Debug<PERSON> logs a message at level Debug on the standard logger.
func Debugf(format string, args ...interface{}) {
	globalLogger.Debugf(format, args...)
}

// Info logs a message at level Info on the standard logger.
func Info(msg string) {
	globalLogger.Info(msg)
}

// Warn logs a message at level Warn on the standard logger.
func Warn(msg string) {
	globalLogger.Warn(msg)
}

// Error logs a message at level Error on the standard logger.
func Error(msg string) {
	globalLogger.Error(msg)
}

// Fatal logs a message at level Fatal on the standard logger then the process will exit with status set to 1.
func Fatal(msg string) {
	globalLogger.Fatal(msg)
}

// Infof logs a message at level Info on the standard logger.
func Infof(format string, args ...interface{}) {
	globalLogger.Infof(format, args...)
}

// Warnf logs a message at level Warn on the standard logger.
func Warnf(format string, args ...interface{}) {
	globalLogger.Warnf(format, args...)
}

// Errorf logs a message at level Error on the standard logger.
func Errorf(format string, args ...interface{}) {
	globalLogger.Errorf(format, args...)
}

// Fatalf logs a message at level Fatal on the standard logger then the process will exit with status set to 1.
func Fatalf(format string, args ...interface{}) {
	globalLogger.Fatalf(format, args...)
}

func FlushLogs() error {
	return globalLogger.FlushLogs()
}
