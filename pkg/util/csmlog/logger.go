package csmlog

import (
	"bytes"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"k8s.io/klog"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/config"
)

var (
	loggerOpt    loggerOptT
	nopLogger    *zap.Logger
	zapLogger    *zap.Logger
	utLogger     *zap.Logger
	globalLogger *Logger
	program      = filepath.Base(os.Args[0])
)

func init() {
	// build default logger to prevent nil pointer
	// actually it's print to nowhere
	nopLogger = zap.NewNop()
	// Default to ut log in unittest envrionment
	if strings.HasSuffix(program, ".test") {
		utLogger = zap.New(newUtCore()).WithOptions(zap.AddCallerSkip(2), zap.AddCaller())
		zapLogger = utLogger
		globalLogger = (*Logger)(utLogger)
	} else {
		zapLogger = nopLogger
		globalLogger = (*Logger)(zapLogger)
	}
}

// InitLogs need to be called explicit in the main application
func InitLogs() {
	if zapLogger != nopLogger {
		return
	}
	if viper.GetBool("debug") {
		loggerOpt.level = "debug"
	}
	level := zap.NewAtomicLevelAt(zap.InfoLevel)
	if err := level.UnmarshalText([]byte(loggerOpt.level)); err != nil {
		fmt.Printf("log level '%s' invalid", loggerOpt.level)
	}

	name := viper.GetString(config.AppNameKey)
	loggerOpt = NewLoggerOpt()

	core := newFileCore(level)
	zapLogger = zap.New(core).WithOptions(zap.AddCallerSkip(2), zap.AddCaller()).Named(name)
	globalLogger = (*Logger)(zapLogger)
}

// 专为单测设计
func newUtCore() zapcore.Core {
	if runtime.GOOS == "darwin" {
		// 本地测试用
		return zapcore.NewCore(zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig()), zapcore.AddSync(os.Stderr), zapcore.DebugLevel)
	}

	var (
		logdir   = "/tmp/csm-unit-test/"
		filename string
		newest   time.Time
	)
	files, _ := os.ReadDir(logdir)
	for _, f := range files {
		fs, err := os.Stat(logdir + f.Name())
		if err != nil {
			panic(err)
		}
		ft := fs.ModTime()
		if ft.After(newest) {
			filename = logdir + f.Name()
			newest = ft
		}
	}
	encoder := zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig())
	file, err := os.OpenFile(filename, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0755)
	if err != nil {
		panic(filename + " cannot be opened for write")
	}
	writer := zapcore.AddSync(file)
	klog.SetOutput(writer)
	return zapcore.NewCore(encoder, writer, zap.DebugLevel)
}

func newFileCore(level zap.AtomicLevel) zapcore.Core {
	infoenabler := zap.LevelEnablerFunc(func(lvl zapcore.Level) bool {
		return lvl >= level.Level()
	})

	infofilewriter := zapcore.AddSync(
		&lumberjack.Logger{
			Filename:   loggerOpt.logDir + "/csm.log", //日志文件存放目录，如果文件夹不存在会自动创建
			MaxSize:    loggerOpt.maxSize,             //文件大小限制,单位MB
			MaxBackups: loggerOpt.maxBackups,          //最大保留日志文件数量
			MaxAge:     loggerOpt.maxAge,              //日志文件保留天数
			Compress:   loggerOpt.compress,            //是否压缩处理
		},
	)

	// k8s client使用klog打印日志，统一打印到我们的rotate日志中
	klog.SetOutputBySeverity("INFO", infofilewriter)

	config := zap.NewProductionEncoderConfig()
	// 设置时间戳键的名称
	config.TimeKey = "timestamp"
	config.EncodeTime = zapcore.RFC3339TimeEncoder

	jsonEncoder := zapcore.NewJSONEncoder(config)
	if viper.GetBool("debug") {
		jsonEncoder = zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig())
	}
	return zapcore.NewTee(
		zapcore.NewCore(jsonEncoder, zapcore.NewMultiWriteSyncer(infofilewriter, zapcore.AddSync(os.Stdout)), infoenabler),
	)
}

// Fields is structured loggerOpt field
type Fields map[string]interface{}

// Logger xxx
type Logger zap.Logger

// NewLogger creates a new log.Logger
func NewLogger() *Logger {
	return (*Logger)(((*zap.Logger)(globalLogger)).WithOptions(zap.AddCallerSkip(-1)))
}

// Infof is equivalent to the global Infof function, guarded by the value of v.
// See the documentation of V for usage.
func (l *Logger) Infof(template string, args ...interface{}) {
	(*zap.Logger)(l).Sugar().Infof(template, args...)
}

// Info is equivalent to the global Info function, guarded by the value of v.
// See the documentation of V for usage.
func (l *Logger) Info(msg string) {
	(*zap.Logger)(l).Info(msg)
}

// Debugf is equivalent to the global Debugf function, guarded by the value of v.
// See the documentation of V for usage.
func (l *Logger) Debugf(template string, args ...interface{}) {
	(*zap.Logger)(l).Sugar().Debugf(template, args...)
}

// Debug is equivalent to the global Debug function, guarded by the value of v.
// See the documentation of V for usage.
func (l *Logger) Debug(msg string) {
	(*zap.Logger)(l).Debug(msg)
}

// Warn is equivalent to the global Warn function, guarded by the value of v.
// See the documentation of V for usage.
func (l *Logger) Warn(msg string) {
	(*zap.Logger)(l).Warn(msg)
}

func (l *Logger) Warnf(template string, args ...interface{}) {
	(*zap.Logger)(l).Sugar().Warnf(template, args...)
}

func (l *Logger) Error(msg string) {
	(*zap.Logger)(l).Error(msg)
}

func (l *Logger) Errorf(template string, args ...interface{}) {
	(*zap.Logger)(l).Sugar().Errorf(template, args...)
}

func (l *Logger) Fatal(msg string) {
	(*zap.Logger)(l).Fatal(msg)
}

func (l *Logger) Fatalf(template string, args ...interface{}) {
	(*zap.Logger)(l).Sugar().Fatalf(template, args...)
}

func (l *Logger) Panic(msg string) {
	(*zap.Logger)(l).Panic(msg)
}

func (l *Logger) Panicf(template string, args ...interface{}) {
	(*zap.Logger)(l).Sugar().Panicf(template, args...)
}

func (l *Logger) Named(s string) *Logger {
	return (*Logger)((*zap.Logger)(l).Named(s))
}

// Write implements io.Writer so we can use this logger in anywhere that needs a *log.Logger
func (l *Logger) Write(p []byte) (int, error) {
	p = bytes.TrimSpace(p)
	// log level hard-coded as DEBUG so it logs nothing on production
	l.Debug(string(p))
	return len(p), nil
}

func (l *Logger) WithField(key string, value interface{}) *Logger {
	return (*Logger)((*zap.Logger)(l).With(zap.Any(key, value)))
}

func (l *Logger) WithFields(fields Fields) *Logger {
	zapFields := make([]zap.Field, len(fields))
	i := 0
	for k, v := range fields {
		zapFields[i] = zap.Any(k, v)
		i++
	}
	return (*Logger)((*zap.Logger)(l).With(zapFields...))
}

func (l *Logger) TimeTrack(start time.Time, thumbnail string) {
	if loggerOpt.enableTimetrack {
		//结构化,时间戳为UTC ms时间戳
		endTime := time.Now().UTC()
		endTimestamp := endTime.UnixNano() / 1e6
		startTimestamp := start.UTC().UnixNano() / 1e6
		sub := endTime.Sub(start).Seconds() * 1e3
		l.WithFields(Fields{
			"timeTrack": thumbnail,
			"startT":    startTimestamp,
			"endT":      endTimestamp,
			"sub":       int(sub),
		}).Info(thumbnail)
	}
}

func (l *Logger) AddCallerSkip(n int) *Logger {
	return (*Logger)((*zap.Logger)(l).WithOptions(zap.AddCallerSkip(n)))
}

func (l *Logger) FlushLogs() error {
	return (*zap.Logger)(l).Sync()
}

// Print 实现gorm Logger接口，用于打印gorm日志
func (l *Logger) Print(values ...interface{}) {
	l.Infof("%+v", gorm.LogFormatter(values))
}

// Log 实现 go-kit Logger 接口，用于报警规则输出结构化日志
func (l *Logger) Log(kvs ...interface{}) error {
	zl := (*zap.Logger)(l)
	zl.Sugar().Infow("", kvs...)
	return zl.Sync()
}
