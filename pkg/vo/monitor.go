package vo

type MonitorInstances struct {
	Enabled   bool               `json:"enabled,omitempty"`
	Instances []*MonitorInstance `json:"instances"`
}

type MonitorInstance struct {
	Region              string `json:"region"`
	ID                  string `json:"id"`
	Name                string `json:"name"`
	IsInstallCPromAgent bool   `json:"isInstallCPromAgent"`
}

type MonitorInstanceDetail struct {
	CSMMonitor         *CSMMonitor `json:"csmMonitor"`
	Status             *Status     `json:"status"`
	Spec               *Spec       `json:"spec"`
	Metadata           *Metadata   `json:"metadata"`
	MonitorGrafanaId   string      `json:"monitorGrafanaId"`
	MonitorGrafanaName string      `json:"monitorGrafanaName"`
}

type Status struct {
	Phase   string `json:"phase"`
	Message string `json:"message"`
}

type Metadata struct {
	Labels            map[string]string `json:"labels"`
	Name              string            `json:"name"`
	CreationTimestamp string            `json:"creationTimestamp"`
}

type Spec struct {
	InstanceID      string          `json:"instanceID"`
	InstanceName    string          `json:"instanceName"`
	Region          string          `json:"region"`
	VmClusterConfig VmClusterConfig `json:"vmClusterConfig"`
}

type VmClusterConfig struct {
	RetentionPeriod string `json:"retentionPeriod"`
}

type MonitorDetailReason string

// 参考 https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master/pkg/bcesdk/cprom/apis/v1/monitor_instance_types.go#L23
const (
	// CProm 运行状态
	MonitorInstancePhasePending     MonitorDetailReason = "Pending"
	MonitorInstancePhaseCreating    MonitorDetailReason = "Creating"
	MonitorInstancePhaseTerminating MonitorDetailReason = "Terminating"
	MonitorInstancePhaseFailed      MonitorDetailReason = "Failed"
	MonitorInstancePhaseRunning     MonitorDetailReason = "Running"
	MonitorInstancePhaseUpgrading   MonitorDetailReason = "Upgrading"
	MonitorInstancePhaseUnknown     MonitorDetailReason = "Unknown"
	CPromAgentException             MonitorDetailReason = "CPromAgentException"
	CSMEnvoyJobDeletedException     MonitorDetailReason = "CSMEnvoyJobDeletedException"
	CPromInstanceDeletedException   MonitorDetailReason = "CPromInstanceDeletedException"
)

type CSMMonitor struct {
	// 是否开启监控
	Enabled bool `json:"enabled"`
	// 是否获取 CProm 监控详情
	MonitorDetail bool `json:"monitorDetail"`
	// 不获取 CProm 监控详情的原因
	MonitorReason MonitorDetailReason `json:"monitorReason"`
	// 失败原因详情
	Description string `json:"description"`
}

type CPromAgentCheckResult struct {
	IsExit bool `json:"isExit"`
}
