package vo

// TODO: 平移到 pkg/api/v1/cnap/cluster.go
const (
	QueryClusterName            = "clusterName"
	QueryClusterVpcName         = "vpcName"
	QueryClusterStatus          = "status"
	QueryClusterConnectionState = "connectionState"
	QueryClusterRegion          = "region"
	QueryClusterMasterMode      = "masterMode"
)

type ManagedClusterView struct {
	ClusterId       string `json:"clusterId"`
	ClusterName     string `json:"clusterName"`
	Status          string `json:"status"`
	Version         string `json:"version"`
	NetworkSegment  string `json:"networkSegment"`
	Region          string `json:"region"`
	AddedTime       string `json:"addedTime"`
	ConnectionState string `json:"connectionState"`
	IsPrimary       bool   `json:"isPrimary"`
	IsConfig        bool   `json:"isConfig"`
	Vpc
}

type CandidateClusterView struct {
	ClusterId      string `json:"clusterId"`
	ClusterName    string `json:"clusterName"`
	Status         string `json:"status"`
	Version        string `json:"version"`
	MasterMode     string `json:"masterMode"`
	NetworkSegment string `json:"networkSegment"`
	Region         string `json:"region"`
	Available      bool   `json:"available"`
	Vpc
}

type Vpc struct {
	VpcId   string `json:"vpcId"`
	VpcName string `json:"vpcName"`
}

type ClustersRequest struct {
	Clusters []ClustersRequestItem `json:"clusters"`
}

type ClustersRequestItem struct {
	ClusterId string `json:"clusterId"`
	Region    string `json:"region"`
}
