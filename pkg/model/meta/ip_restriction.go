package meta

import (
	"net"

	"github.com/pkg/errors"
)

// CreateIPRestrictionRequest 创建IP黑白名单请求
type CreateIPRestrictionRequest struct {
	Enabled     bool     `json:"enabled"`                      // 是否开启
	Name        string   `json:"name" valid:"required"`        // 名称
	Description string   `json:"description"`                  // 备注
	Type        string   `json:"type" valid:"required"`        // 类型：whitelist(白名单)、blacklist(黑名单)
	Scope       string   `json:"scope"`                        // 生效粒度，默认值为global（全局生效）
	IPAddresses []string `json:"ipAddresses" valid:"required"` // IP地址/地址段列表
}

// Validate 验证创建IP黑白名单请求参数
func (r *CreateIPRestrictionRequest) Validate() error {
	// 验证名称
	if r.Name == "" {
		return errors.New("name不能为空")
	}
	if len(r.Name) < 2 || len(r.Name) > 64 {
		return errors.New("name长度必须在2-64个字符之间")
	}

	// 验证类型
	if r.Type != "whitelist" && r.Type != "blacklist" {
		return errors.New("type只能是whitelist或blacklist")
	}

	// 验证描述长度
	if len(r.Description) > 200 {
		return errors.New("description长度不能超过200个字符")
	}

	// 验证IP地址列表
	if len(r.IPAddresses) == 0 {
		return errors.New("ipAddresses不能为空")
	}
	if len(r.IPAddresses) > 100 {
		return errors.New("ipAddresses数组长度不能超过100")
	}

	// 验证每个IP地址格式
	for _, ipAddr := range r.IPAddresses {
		if err := validateIPAddress(ipAddr); err != nil {
			return errors.Wrapf(err, "无效的IP地址：%s", ipAddr)
		}
	}

	// 设置默认值
	if r.Scope == "" {
		r.Scope = "global"
	}

	return nil
}

// validateIPAddress 验证IP地址或CIDR格式
func validateIPAddress(ipAddr string) error {
	// 尝试解析为IP地址
	if ip := net.ParseIP(ipAddr); ip != nil {
		return nil
	}

	// 尝试解析为CIDR
	if _, _, err := net.ParseCIDR(ipAddr); err == nil {
		return nil
	}

	return errors.New("无效的IP地址或CIDR格式")
}

// IPRestrictionTemplateData IP黑白名单模板数据
type IPRestrictionTemplateData struct {
	PluginName      string   `json:"pluginName"`      // 插件名称
	Namespace       string   `json:"namespace"`       // 命名空间
	RuleName        string   `json:"ruleName"`        // 规则名称
	RuleDescription string   `json:"ruleDescription"` // 规则描述
	Type            string   `json:"type"`            // 类型：whitelist、blacklist
	Scope           string   `json:"scope"`           // 生效粒度
	IPAddresses     []string `json:"ipAddresses"`     // IP地址列表
	IPSourceType    string   `json:"ipSourceType"`    // IP来源类型，默认为"origin-source"
	Status          int      `json:"status"`          // 拦截时返回的状态码
	Message         string   `json:"message"`         // 拦截时返回的消息
	PluginURL       string   `json:"pluginURL"`       // 插件镜像地址
	Enabled         bool     `json:"enabled"`         // 是否启用
}

// CreateIPRestrictionResponse 创建IP黑白名单响应
type CreateIPRestrictionResponse struct {
	ID          string   `json:"id"`          // IP黑白名单规则ID
	Enabled     bool     `json:"enabled"`     // 是否开启
	Name        string   `json:"name"`        // 名称
	Description string   `json:"description"` // 备注
	Type        string   `json:"type"`        // 类型
	Scope       string   `json:"scope"`       // 生效粒度
	IPAddresses []string `json:"ipAddresses"` // IP地址/地址段列表
	CreateTime  string   `json:"createTime"`  // 创建时间
}

// IPRestrictionInfo IP黑白名单详情信息
type IPRestrictionInfo struct {
	ID          string   `json:"id"`          // IP黑白名单规则ID
	Enabled     bool     `json:"enabled"`     // 是否开启
	Name        string   `json:"name"`        // 名称
	Description string   `json:"description"` // 备注
	Type        string   `json:"type"`        // 类型
	Scope       string   `json:"scope"`       // 生效粒度
	IPAddresses []string `json:"ipAddresses"` // IP地址/地址段列表
	CreateTime  string   `json:"createTime"`  // 创建时间
	UpdateTime  string   `json:"updateTime"`  // 更新时间
}

// UpdateIPRestrictionRequest 编辑IP黑白名单请求
type UpdateIPRestrictionRequest struct {
	Enabled     bool     `json:"enabled" valid:"required"`     // 是否开启
	Name        string   `json:"name" valid:"required"`        // 名称
	Description string   `json:"description"`                  // 备注
	Type        string   `json:"type" valid:"required"`        // 类型：whitelist(白名单)、blacklist(黑名单)
	Scope       string   `json:"scope"`                        // 生效粒度，默认值为global（全局生效）
	IPAddresses []string `json:"ipAddresses" valid:"required"` // IP地址/地址段列表
}

// Validate 验证编辑IP黑白名单请求参数
func (r *UpdateIPRestrictionRequest) Validate() error {
	// 验证类型
	if r.Type != "whitelist" && r.Type != "blacklist" {
		return errors.New("type只能是whitelist或blacklist")
	}

	// 验证描述长度
	if len(r.Description) > 200 {
		return errors.New("description长度不能超过200个字符")
	}

	// 验证IP地址列表
	if len(r.IPAddresses) == 0 {
		return errors.New("ipAddresses不能为空")
	}
	if len(r.IPAddresses) > 100 {
		return errors.New("ipAddresses数组长度不能超过100")
	}

	// 验证每个IP地址格式
	for _, ipAddr := range r.IPAddresses {
		if err := validateIPAddress(ipAddr); err != nil {
			return errors.Wrapf(err, "无效的IP地址：%s", ipAddr)
		}
	}

	// 设置默认值
	if r.Scope == "" {
		r.Scope = "global"
	}

	return nil
}

// IPRestrictionListResponse IP黑白名单列表响应
type IPRestrictionListResponse struct {
	OrderBy    string              `json:"orderBy"`    // 排序字段
	Order      string              `json:"order"`      // 排序方式
	PageNo     int                 `json:"pageNo"`     // 当前页码
	PageSize   int                 `json:"pageSize"`   // 每页条数
	TotalCount int                 `json:"totalCount"` // 总数据条数
	Result     []IPRestrictionItem `json:"result"`     // IP黑白名单列表数据
}

// IPRestrictionItem IP黑白名单列表项
type IPRestrictionItem struct {
	ID          string   `json:"id"`          // IP黑白名单规则ID
	Name        string   `json:"name"`        // 名称
	Enabled     bool     `json:"enabled"`     // 启用状态
	Type        string   `json:"type"`        // 类型
	Scope       string   `json:"scope"`       // 生效粒度
	IPAddresses []string `json:"ipAddresses"` // IP地址/地址段列表
}

// DeleteIPRestrictionResponse 删除IP黑白名单响应
type DeleteIPRestrictionResponse struct {
	Success bool   `json:"success"` // 请求是否成功
	Status  int    `json:"status"`  // 状态码
	Message string `json:"message"` // 删除结果消息
}
