package meta

import (
	"testing"
)

func TestValidateRewriteConfig(t *testing.T) {
	tests := []struct {
		name    string
		request *AIRouteRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "启用重写且路径有效",
			request: &AIRouteRequest{
				Rewrite: Rewrite{
					Enabled: true,
					Path:    "/api/v1/inference",
				},
			},
			wantErr: false,
		},
		{
			name: "未启用重写，路径为空",
			request: &AIRouteRequest{
				Rewrite: Rewrite{
					Enabled: false,
					Path:    "",
				},
			},
			wantErr: false,
		},
		{
			name: "启用重写但路径为空",
			request: &AIRouteRequest{
				Rewrite: Rewrite{
					Enabled: true,
					Path:    "",
				},
			},
			wantErr: true,
			errMsg:  "rewrite path is required when rewrite is enabled",
		},
		{
			name: "启用重写但路径格式错误",
			request: &AIRouteRequest{
				Rewrite: Rewrite{
					Enabled: true,
					Path:    "invalid-path",
				},
			},
			wantErr: true,
			errMsg:  "rewrite path must start with '/'",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.request.ValidateRewriteConfig()
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateRewriteConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil && err.Error() != tt.errMsg {
				t.Errorf("ValidateRewriteConfig() error = %v, wantErr %v", err.Error(), tt.errMsg)
			}
		})
	}
}
