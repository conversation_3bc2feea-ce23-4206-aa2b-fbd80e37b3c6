package meta

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCreateExtAuthRequest_Validate(t *testing.T) {
	tests := []struct {
		name    string
		request *CreateExtAuthRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid gateway scope request",
			request: &CreateExtAuthRequest{
				Enabled:       true,
				Name:          "test-gateway-auth",
				Description:   "Test gateway auth plugin",
				Scope:         "gateway",
				HTTPService:   "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service.default.svc.cluster.local\n  service_port: 8080\n  path_prefix: /validate\ntimeout: 2000",
				StatusOnError: 403,
			},
			wantErr: false,
		},
		{
			name: "valid route scope request",
			request: &CreateExtAuthRequest{
				Enabled:       true,
				Name:          "test-route-auth",
				Description:   "Test route auth plugin",
				Scope:         "route",
				MatchType:     "blacklist",
				RouteList:     []string{"api-route-v1", "user-route"},
				HTTPService:   "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service.default.svc.cluster.local\n  service_port: 8080\n  path_prefix: /validate\ntimeout: 2000",
				StatusOnError: 403,
			},
			wantErr: false,
		},
		{
			name: "empty name",
			request: &CreateExtAuthRequest{
				Name: "",
			},
			wantErr: true,
			errMsg:  "name不能为空",
		},
		{
			name: "name too short",
			request: &CreateExtAuthRequest{
				Name: "a",
			},
			wantErr: true,
			errMsg:  "name长度必须在2-64个字符之间",
		},
		{
			name: "name too long",
			request: &CreateExtAuthRequest{
				Name: "this-is-a-very-long-name-that-exceeds-the-maximum-allowed-length-of-64-characters",
			},
			wantErr: true,
			errMsg:  "name长度必须在2-64个字符之间",
		},
		{
			name: "invalid scope",
			request: &CreateExtAuthRequest{
				Name:  "test",
				Scope: "invalid",
			},
			wantErr: true,
			errMsg:  "scope只能是gateway或route",
		},
		{
			name: "route scope without match type",
			request: &CreateExtAuthRequest{
				Name:        "test",
				Scope:       "route",
				RouteList:   []string{"route1"},
				HTTPService: "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service\n  service_port: 8080\ntimeout: 2000",
			},
			wantErr: false, // 应该设置默认值
		},
		{
			name: "route scope without route list",
			request: &CreateExtAuthRequest{
				Name:      "test",
				Scope:     "route",
				MatchType: "blacklist",
				RouteList: []string{},
			},
			wantErr: true,
			errMsg:  "路由维度时routeList不能为空",
		},
		{
			name: "invalid status on error",
			request: &CreateExtAuthRequest{
				Name:          "test",
				Scope:         "gateway",
				HTTPService:   "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service\n  service_port: 8080\ntimeout: 2000",
				StatusOnError: 200, // Invalid status code for error
			},
			wantErr: true,
			errMsg:  "statusOnError必须在400-599范围内",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.request.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateHTTPServiceYAML(t *testing.T) {
	tests := []struct {
		name    string
		yaml    string
		wantErr bool
		errMsg  string
	}{
		{
			name:    "valid yaml",
			yaml:    "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service.default.svc.cluster.local\n  service_port: 8080\n  path_prefix: /validate\ntimeout: 2000",
			wantErr: false,
		},
		{
			name:    "empty yaml",
			yaml:    "",
			wantErr: true,
			errMsg:  "httpService YAML不能为空",
		},
		{
			name:    "missing endpoint_mode",
			yaml:    "endpoint:\n  service_name: auth-service\n  service_port: 8080\ntimeout: 2000",
			wantErr: true,
			errMsg:  "httpService YAML缺少必要字段: endpoint_mode",
		},
		{
			name:    "invalid endpoint_mode",
			yaml:    "endpoint_mode: invalid\nendpoint:\n  service_name: auth-service\n  service_port: 8080\ntimeout: 2000",
			wantErr: true,
			errMsg:  "httpService YAML中endpoint_mode必须为envoy",
		},
		{
			name:    "missing service_name",
			yaml:    "endpoint_mode: envoy\nendpoint:\n  service_port: 8080\ntimeout: 2000",
			wantErr: true,
			errMsg:  "httpService YAML缺少必要字段: service_name",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateHTTPServiceYAML(tt.yaml)
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestIsValidServiceName(t *testing.T) {
	tests := []struct {
		name        string
		serviceName string
		want        bool
	}{
		{
			name:        "valid simple name",
			serviceName: "auth-service",
			want:        true,
		},
		{
			name:        "valid FQDN",
			serviceName: "auth-service.default.svc.cluster.local",
			want:        true,
		},
		{
			name:        "empty name",
			serviceName: "",
			want:        false,
		},
		{
			name:        "name too long",
			serviceName: "this-is-a-very-long-service-name-that-exceeds-the-maximum-allowed-length-of-253-characters-which-is-the-limit-for-kubernetes-service-names-and-should-be-rejected-by-the-validation-function-because-it-is-way-too-long-to-be-a-valid-service-name-in-kubernetes-cluster",
			want:        false,
		},
		{
			name:        "invalid characters",
			serviceName: "auth_service@domain",
			want:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := isValidServiceName(tt.serviceName)
			assert.Equal(t, tt.want, got)
		})
	}
}
