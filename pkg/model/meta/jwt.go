package meta

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type JwtToken struct {
	JWK   map[string]interface{} `json:"jwk"`
	Token map[string]string      `json:"token"`
}

func (cert *JwtCert) TableName() string {
	return "t_service_jwt_cert"
}

type JwtCert struct {
	dbutil.BaseModel
	//nolint
	Region string `gorm:"column:region" json:"region" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	ClusterUUID string `gorm:"column:cluster_uuid" json:"clusterUUID" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	AccountId string `gorm:"column:account_id" json:"accountId" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	PrivateKeyPem string `gorm:"column:private_key_pem" json:"privateKeyPem" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	PublicKeyPem string `gorm:"column:public_key_pem" json:"publicKeyPem" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	Deleted *int `gorm:"column:deleted" json:"deleted" dbutil:"searchable:wildcard,orderable,updatable"`
}

func NewJwtCert(region, clusterUUID, accountId, privateKeyPem, publicKeyPem string) *JwtCert {
	return &JwtCert{
		Region:        region,
		ClusterUUID:   clusterUUID,
		AccountId:     accountId,
		PrivateKeyPem: privateKeyPem,
		PublicKeyPem:  publicKeyPem,
		Deleted:       csm.Int(0),
	}
}
