package meta

// CreateVpcEndpointRequest 创建VPC Endpoint请求
type CreateVpcEndpointRequest struct {
	Name        string `json:"name"`
	VpcId       string `json:"vpcId"`
	Protocol    string `json:"protocol"`
	BackendIp   string `json:"backendIp"`
	BackendPort int    `json:"backendPort"`
	Type        string `json:"type"`
	Description string `json:"description,omitempty"`
	ClientToken string `json:"-"`
}

// VpcEndpoint VPC Endpoint信息
type VpcEndpoint struct {
	VpcEndpointId string `json:"vpcEndpointId"`
	VpcId         string `json:"vpcId"`
	Protocol      string `json:"protocol"`
	BackendIp     string `json:"backendIp"`
	BackendPort   string `json:"backendPort"`
	EndpointIp    string `json:"endpointIp"`
	EndpointPort  string `json:"endpointPort"`
	Name          string `json:"name"`
	Description   string `json:"description"`
	Type          string `json:"type"`
	Status        string `json:"status"`
}

// CreateVpcEndpointResult 创建VPC Endpoint结果
type CreateVpcEndpointResult struct {
	VpcEndpoint
}

// ListVpcEndpointArgs 列出VPC Endpoints参数
type ListVpcEndpointArgs struct {
	VpcId     string `json:"vpcId,omitempty"`
	Name      string `json:"name,omitempty"`
	IpAddress string `json:"ipAddress,omitempty"`
	Status    string `json:"status,omitempty"`
	Marker    string `json:"marker,omitempty"`
	MaxKeys   int    `json:"maxKeys,omitempty"`
}

// ListVpcEndpointResult 列出VPC Endpoints结果
type ListVpcEndpointResult struct {
	Marker      string        `json:"marker"`
	IsTruncated bool          `json:"isTruncated"`
	NextMarker  string        `json:"nextMarker"`
	MaxKeys     int           `json:"maxKeys"`
	Endpoints   []VpcEndpoint `json:"endpoints"`
}
