package meta

import (
	"github.com/pkg/errors"
)

// CreateConsumerRequest 创建消费者请求
type CreateConsumerRequest struct {
	ConsumerName   string   `json:"consumerName"`
	Description    string   `json:"description"`
	AuthType       string   `json:"authType"`
	RouteNames     []string `json:"routeNames"`
	UnlimitedQuota bool     `json:"unlimitedQuota"`
	TotalQuota     *int     `json:"totalQuota"`
	SrcProduct     string   `json:"srcProduct"` // 新增：来源产品
}

// Validate 验证创建消费者请求参数
func (r *CreateConsumerRequest) Validate() error {
	if r.ConsumerName == "" {
		return errors.New("consumerName不能为空")
	}

	if len(r.ConsumerName) < 2 || len(r.ConsumerName) > 64 {
		return errors.New("consumerName长度必须在2-64个字符之间")
	}

	// 验证认证方式
	if r.AuthType == "" {
		return errors.New("authType不能为空")
	}

	// TODO: 支持更多认证方式
	if r.AuthType != "KeyAuth" && r.AuthType != "JWT" {
		return errors.New("authType目前仅支持JWT和KeyAuth")
	}

	// 验证描述长度
	if len(r.Description) > 255 {
		return errors.New("description长度不能超过255个字符")
	}

	// 新增：验证配额相关参数
	if !r.UnlimitedQuota {
		// 当不是无限配额时，totalQuota必须提供且大于等于0
		if r.TotalQuota == nil {
			return errors.New("当unlimitedQuota为false时，totalQuota不能为空")
		}
		if *r.TotalQuota < 0 {
			return errors.New("totalQuota必须大于等于0")
		}
	}

	return nil
}

// ConsumerTemplateData JWT认证消费者模板数据
type ConsumerTemplateData struct {
	Namespace      string
	ConsumerID     string
	ConsumerName   string
	Description    string
	Credential     string
	AuthType       string
	RouteNames     []string
	CreateTime     string
	UnlimitedQuota bool
	TotalQuota     *int // 新增：原始配额值
	// 插件相关字段
	PluginName string
	PluginURL  string
}

// CreateConsumerResult 创建消费者响应结果
type CreateConsumerResult struct {
	Success    bool   `json:"success"`
	Status     int    `json:"status"`
	ConsumerID string `json:"consumerId"`
	Credential string `json:"credential"`
}

// ConsumerRouteInfo 消费者关联的路由信息
type ConsumerRouteInfo struct {
	RouteName   string `json:"routeName"`
	CreateTime  string `json:"createTime"`
	AuthEnabled bool   `json:"authEnabled"`
}

// ConsumerAuthInfo 消费者认证信息
type ConsumerAuthInfo struct {
	Token string `json:"token"`
}

// ConsumerDetailInfo 消费者详情信息
type ConsumerDetailInfo struct {
	ConsumerID     string              `json:"consumerId"`
	ConsumerName   string              `json:"consumerName"`
	Description    string              `json:"description"`
	AuthType       string              `json:"authType"`
	AuthInfo       ConsumerAuthInfo    `json:"authInfo"`
	Routes         []ConsumerRouteInfo `json:"routes"`
	UnlimitedQuota bool                `json:"unlimitedQuota"` // 是否无限配额
	TotalQuota     int64               `json:"totalQuota"`     // 新增：原始配额值
	QuotaValue     int64               `json:"quotaValue"`     // 剩余配额
	SrcProduct     string              `json:"srcProduct"`     // 新增：来源产品
}

// ConsumerDetailResponse 查询消费者详情响应
type ConsumerDetailResponse struct {
	Success bool               `json:"success"`
	Result  ConsumerDetailInfo `json:"result"`
	Status  int                `json:"status"`
}

// UpdateConsumerRequest 编辑消费者请求
type UpdateConsumerRequest struct {
	Description    string   `json:"description"`
	RouteNames     []string `json:"routeNames"`
	UnlimitedQuota *bool    `json:"unlimitedQuota"`
	TotalQuota     *int     `json:"totalQuota"`
}

// Validate 验证编辑消费者请求参数
func (r *UpdateConsumerRequest) Validate() error {
	// 验证描述长度
	if len(r.Description) > 255 {
		return errors.New("description长度不能超过255个字符")
	}

	// 新增：验证配额相关参数
	if r.UnlimitedQuota != nil && !*r.UnlimitedQuota {
		// 当不是无限配额时，totalQuota必须提供且大于等于0
		if r.TotalQuota == nil {
			return errors.New("当unlimitedQuota为false时，totalQuota不能为空")
		}
		if *r.TotalQuota < 0 {
			return errors.New("totalQuota必须大于等于0")
		}
	}

	return nil
}

// UpdateConsumerResponse 编辑消费者响应
type UpdateConsumerResponse struct {
	Success bool        `json:"success"`
	Result  interface{} `json:"result"`
	Status  int         `json:"status"`
}

// DeleteConsumerResponse 删除消费者响应
type DeleteConsumerResponse struct {
	Success bool        `json:"success"`
	Result  interface{} `json:"result"`
	Status  int         `json:"status"`
}
