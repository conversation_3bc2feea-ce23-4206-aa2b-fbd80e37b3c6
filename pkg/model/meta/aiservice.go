package meta

import (
	"time"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// AIService AI网关服务模型
type AIService struct {
	dbutil.BaseModel

	InstanceID    string `gorm:"column:instance_id" json:"instance_id" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	ServiceName   string `gorm:"column:service_name" json:"service_name" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	ServiceSource string `gorm:"column:service_source" json:"service_source" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	ClusterID     string `gorm:"column:cluster_id" json:"cluster_id" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	Namespace     string `gorm:"column:namespace" json:"namespace" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	ServiceStatus string `gorm:"column:service_status" json:"service_status" dbutil:"searchable:wildcard,orderable,updatable"`
	AccountID     string `gorm:"column:account_id" json:"account_id" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	Region        string `gorm:"column:region" json:"region" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	ServicePort   string `gorm:"column:service_port" json:"service_port" dbutil:"searchable:wildcard,orderable,updatable"`

	Deleted *int `gorm:"column:deleted" json:"deleted" dbutil:"searchable:wildcard,orderable,updatable"`
}

// TableName 指定表名
func (m *AIService) TableName() string {
	return "t_ai_gateway_service"
}

// NewAIService 创建服务实例
func NewAIService(instanceID, serviceName, serviceSource, clusterID, namespace, accountID, region string) *AIService {
	deleted := 0
	// 设置 1971-01-01 08:00:01 作为默认删除时间
	defaultDeleteTime := time.Date(1971, 1, 1, 8, 0, 1, 0, time.Local)
	now := time.Now()

	return &AIService{
		BaseModel: dbutil.BaseModel{
			CreateTime: &now,
			UpdateTime: &now,
			DeleteTime: &defaultDeleteTime,
		},
		InstanceID:    instanceID,
		ServiceName:   serviceName,
		ServiceSource: serviceSource,
		ClusterID:     clusterID,
		Namespace:     namespace,
		ServiceStatus: "running",
		AccountID:     accountID,
		Region:        region,
		Deleted:       &deleted,
	}
}

// AddServiceRequest 添加服务请求
type AddServiceRequest struct {
	ClusterID     string   `json:"clusterId" valid:"required"`
	ServiceSource string   `json:"serviceSource" valid:"required"`
	Namespace     string   `json:"namespace" valid:"required"`
	ServiceList   []string `json:"serviceList" valid:"required"`
}

// AddServiceResponse 添加服务响应
type AddServiceResponse struct {
	Success bool `json:"success"`
	Status  int  `json:"status"`
	Result  struct {
		AddedCount int `json:"addedCount"`
	} `json:"result"`
}

// ServiceListItem 服务列表项
type ServiceListItem struct {
	ServiceName   string   `json:"serviceName"`
	ServiceSource string   `json:"serviceSource"`
	RouteCount    int      `json:"routeCount"`
	CreateTime    string   `json:"createTime"`
	ServiceStatus string   `json:"serviceStatus"`
	ClusterID     string   `json:"clusterId"`
	Namespace     string   `json:"namespace"`
	ServicePort   []string `json:"servicePort"`
}

// ServiceListPage 服务列表分页
type ServiceListPage struct {
	OrderBy    string            `json:"orderBy"`
	Order      string            `json:"order"`
	PageNo     int               `json:"pageNo"`
	PageSize   int               `json:"pageSize"`
	TotalCount int               `json:"totalCount"`
	Result     []ServiceListItem `json:"result"`
}

// ServiceListResponse 服务列表响应
type ServiceListResponse struct {
	Success bool            `json:"success"`
	Status  int             `json:"status"`
	Page    ServiceListPage `json:"page"`
}

// ServiceDetailResult 服务详情结果
type ServiceDetailResult struct {
	ClusterID     string   `json:"clusterId"`
	Namespace     string   `json:"namespace"`
	RouteCount    int      `json:"routeCount"`
	ServiceSource string   `json:"serviceSource"`
	ServicePort   []string `json:"servicePort"`
}

// ServiceDetailResponse 服务详情响应
type ServiceDetailResponse struct {
	Success bool                `json:"success"`
	Status  int                 `json:"status"`
	Result  ServiceDetailResult `json:"result"`
}
