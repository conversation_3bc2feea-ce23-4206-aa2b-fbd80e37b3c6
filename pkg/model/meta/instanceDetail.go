package meta

type InstanceDetail struct {
	BillingModel        string `json:"billingModel"`
	Type                string `json:"type" gorm:"column:instance_type" dbutil:"searchable:wildcard,orderable" valid:"required"`
	IstioVersion        string `json:"istioVersion" gorm:"column:istio_version" dbutil:"searchable:wildcard,orderable" valid:"required"`
	Status              string `json:"status"`
	ControlPanelAddress string `json:"controlPanelAddress"`
	BlbInfo             BlbInfo
	VpcInfo             VpcInfo
	ClusterInfo         ClusterInfo
	OverviewOfSidecar   OverviewOfSidecar
	DiscoverySelector   *DiscoverySelector   `json:"discoverySelector"`
	NetworkType         *InstanceNetworkType `json:"networkType"`
	// MultiProtocol 支持多协议
	MultiProtocol bool   `json:"multiProtocol"`
	APIServerEip  bool   `json:"apiServerEip"`
	ConfigCluster string `json:"configCluster"`
}

type InstanceNetworkType struct {
	VpcNetworkId   string `json:"vpcNetworkId"`
	VpcNetworkName string `json:"vpcNetworkName"`
	VpcNetworkCidr string `json:"vpcNetworkCidr"`
	SubnetId       string `json:"subnetId"`
	SubnetName     string `json:"subnetName"`
	SubnetCidr     string `json:"subnetCidr"`
}

type DiscoverySelector struct {
	Enabled     bool              `json:"enabled"`
	MatchLabels map[string]string `json:"matchLabels"`
}

type Monitor struct {
	Enabled   bool              `json:"enabled"`
	Instances []MonitorInstance `json:"instances"`
}

type MonitorInstance struct {
	Region string `json:"region"`
	ID     string `json:"id"`
	Name   string `json:"name"`
}

// VpcInfo vpc信息
type VpcInfo struct {
	VpcId   string `json:"vpcId"`
	VpcName string `json:"vpcName"`
	VpcCidr string `json:"vpcCidr"`
}

// ClusterInfo CCE集群信息
type ClusterInfo struct {
	ClusterId   string `json:"clusterId"`
	ClusterName string `json:"clusterName"`
}

// OverviewOfSidecar 服务网格Sidecar相关描述
type OverviewOfSidecar struct {
	InstanceId   string `json:"instanceId" gorm:"column:instance_uuid" dbutil:"searchable:wildcard,orderable" valid:"required"`
	InstanceName string `json:"instanceName" gorm:"column:instance_name" dbutil:"searchable:wildcard,orderable" valid:"required"`
	Num          int    `json:"num"`
}

// BlbInfo 关联BLB信息
type BlbInfo struct {
	BlbId     string `json:"blbId"`
	BlbName   string `json:"blbName"`
	BlbStatus string `json:"blbStatus"`
}

type BlbDetailResult struct {
	BlbId    string `json:"blbId"`
	Name     string `json:"name"`
	Address  string `json:"address"`
	PublicIp string `json:"publicIp"`
	Cidr     string `json:"cidr"`
	VpcId    string `json:"vpcId"`
	SubnetId string `json:"subnetId"`
}

// TraceInfo 链路追踪信息
type TraceInfo struct {
	TraceEnabled bool    `json:"traceEnabled"`
	SamplingRate float64 `json:"samplingRate"`
	Service      string  `json:"service"`
	Address      string  `json:"address"`
}
