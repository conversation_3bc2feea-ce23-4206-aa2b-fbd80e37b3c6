package meta

import (
	"strconv"
	"strings"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
)

// Page 分页参数
type Page struct {
	OrderBy     string
	Order       string
	PageNoStr   string
	PageSizeStr string
	PageNo      int64
	PageSize    int64
}

func GetPageParam(ctx context.CsmContext, orderParam map[string]string) *Page {
	// 排序初始化
	page := &Page{}
	page.OrderBy = ctx.QueryParam("orderBy")
	page.Order = ctx.QueryParam("order")
	page.PageNoStr = ctx.QueryParam("pageNo")
	page.PageSizeStr = ctx.QueryParam("pageSize")
	orderBy := "created_at"
	// 排序字段映射
	for front, backend := range orderParam {
		if page.OrderBy == front {
			orderBy = backend
			break
		}
	}
	page.OrderBy = orderBy

	if strings.ToUpper(page.Order) == "ASC" {
		page.Order = strings.ToUpper(page.Order)
	} else {
		page.Order = "DESC"
	}

	// 分页
	pageNo, err := strconv.ParseInt(page.PageNoStr, 10, 64)
	pageNo = util.MaxInt64(1, pageNo)

	pageSize, err := strconv.ParseInt(page.PageSizeStr, 10, 64)
	if err != nil {
		pageSize = 10
	}
	pageSize = util.MaxInt64(1, pageSize)
	pageSize = util.MinInt64(10000, pageSize)

	page.PageNo = pageNo
	page.PageSize = pageSize
	page.PageNoStr = strconv.FormatInt(pageNo, 10)
	page.PageSizeStr = strconv.FormatInt(pageSize, 10)
	return page
}

type ListFilter struct {
	KeywordType string
	Keyword     string
	Filters     map[string][]string
}

// CsmMeshRequestParams 请求参数结构体
type CsmMeshRequestParams struct {
	InstanceUUID string `param:"instanceUUID"`
	ServiceHost  string `param:"host"`
	// page params
	PageSize    int64  `query:"pageSize"`
	PageNo      int64  `query:"pageNo"`
	Keyword     string `query:"keyword"`
	KeywordType string `query:"keywordType"`
	Order       string `query:"order"`
	OrderBy     string `query:"orderBy"`
	// user domain id
	AccountID string
	// region
	Region string `query:"region"`
	// status
	Status string `query:"instanceStatus"`
	// protocol
	Protocol string `query:"protocol"`
	// srcProduct 源产品过滤条件
	SrcProduct string `query:"srcProduct"`
}

// NewCsmMeshRequestParams 初始化一个请求参数结构体
func NewCsmMeshRequestParams() *CsmMeshRequestParams {
	return &CsmMeshRequestParams{
		InstanceUUID: "",
		ServiceHost:  "",
		PageSize:     10,
		PageNo:       1,
		Keyword:      "",
		KeywordType:  "",
		Order:        "desc",
		OrderBy:      "create_time",
		AccountID:    "",
		Region:       "",
		Status:       "",
		Protocol:     "",
		SrcProduct:   "",
	}
}

type LaneServiceParams struct {
	CsmMeshRequestParams
	ClusterID     string `query:"clusterID"`
	Namespace     string `query:"namespace"`
	ClusterRegion string `query:"clusterRegion"`
}

func NewLaneServiceParams() *LaneServiceParams {
	return &LaneServiceParams{
		CsmMeshRequestParams: CsmMeshRequestParams{
			PageSize: 10,
			PageNo:   1,
			Order:    "desc",
			OrderBy:  "create_time",
		},
	}
}

type CrdParams struct {
	*CsmMeshRequestParams
	Keys      []CrdKey
	Namespace string
	Kind      string
	Name      string
	Labels    map[string]string
}

type CrdParam struct {
	InstanceUUID string
	Content      string
	CrdKey
	ClusterID string
	Region    string
}

type CrdKey struct {
	Namespace string
	Kind      string
	Name      string
}

type TokenParam struct {
	PublicKey  string
	PrivateKey string
	KID        string
	Issuer     string
	Subject    string
	AccountID  string
	Region     string
	ClusterID  string
}
