package meta

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type ServiceMeshRouteRule struct {
	dbutil.BaseModel
	//nolint
	Name string `gorm:"column:name" json:"name" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	Namespace string `gorm:"column:namespace" json:"namespace" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	MatchRule string `gorm:"column:match_rule" json:"matchRule" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	AccountID string `gorm:"column:account_id;default:0" json:"accountId" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	Methods string `gorm:"column:methods" json:"methods" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	DestinationService string `gorm:"column:destination_service" json:"destinationService" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	LoadBalanceAlgo string `gorm:"column:load_balance_algo" json:"loadBalanceAlgo" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	AuthEnabled int `gorm:"column:auth_enabled" json:"authEnabled" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	Deleted int `gorm:"column:deleted" json:"deleted" dbutil:"searchable:wildcard,orderable,updatable"`
}

func (routeRule *ServiceMeshRouteRule) TableName() string {
	return "t_service_mesh_route_rule"
}
