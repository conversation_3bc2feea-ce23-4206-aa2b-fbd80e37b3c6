package meta

import (
	"github.com/pkg/errors"
)

// CreateExtAuthRequest 创建外部认证请求
type CreateExtAuthRequest struct {
	Enabled       bool     `json:"enabled"`                      // 是否开启
	Name          string   `json:"name" valid:"required"`        // 插件名称
	Description   string   `json:"description"`                  // 备注
	Scope         string   `json:"scope"`                        // 生效粒度：gateway(网关维度)、route(路由维度)
	MatchType     string   `json:"matchType"`                    // 匹配类型：blacklist、whitelist（仅路由维度）
	RouteList     []string `json:"routeList"`                    // 路由名称列表（仅路由维度）
	HTTPService   string   `json:"httpService" valid:"required"` // HTTP服务配置（YAML字符串）
	StatusOnError int      `json:"statusOnError"`                // 认证失败时的HTTP状态码
}

// ExtAuthMatchRule 外部认证匹配规则
type ExtAuthMatchRule struct {
	Path string `json:"path"` // 匹配路径
	Type string `json:"type"` // 匹配类型：prefix、exact
}

// Validate 验证创建外部认证请求参数
func (r *CreateExtAuthRequest) Validate() error {
	// 验证插件名称
	if r.Name == "" {
		return errors.New("name不能为空")
	}
	if len(r.Name) < 2 || len(r.Name) > 64 {
		return errors.New("name长度必须在2-64个字符之间")
	}

	// 验证描述长度
	if len(r.Description) > 200 {
		return errors.New("description长度不能超过200个字符")
	}

	// 验证生效粒度
	if r.Scope == "" {
		r.Scope = "gateway" // 默认为网关维度
	}
	if r.Scope != "gateway" && r.Scope != "route" {
		return errors.New("scope只能是gateway或route")
	}

	// 路由维度需要验证额外参数
	if r.Scope == "route" {
		if r.MatchType == "" {
			r.MatchType = "blacklist" // 默认黑名单
		}
		if r.MatchType != "blacklist" && r.MatchType != "whitelist" {
			return errors.New("matchType只能是blacklist或whitelist")
		}
		if len(r.RouteList) == 0 {
			return errors.New("路由维度时routeList不能为空")
		}
		if len(r.RouteList) > 50 {
			return errors.New("routeList数组长度不能超过50")
		}
	}

	// 设置默认状态码
	if r.StatusOnError == 0 {
		r.StatusOnError = 403
	}
	if r.StatusOnError < 400 || r.StatusOnError > 599 {
		return errors.New("statusOnError必须在400-599范围内")
	}

	// 验证HTTP服务配置
	if r.HTTPService == "" {
		return errors.New("httpService不能为空")
	}

	return nil
}

// isValidServiceName 验证Kubernetes服务名格式
func isValidServiceName(name string) bool {
	// 简单验证：允许字母、数字、点、连字符
	if len(name) == 0 || len(name) > 253 {
		return false
	}

	// 基本字符验证
	for _, char := range name {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '.' || char == '-') {
			return false
		}
	}

	return true
}

// ExtAuthTemplateData 外部认证模板数据
type ExtAuthTemplateData struct {
	PluginName      string             `json:"pluginName"`      // 插件名称
	Namespace       string             `json:"namespace"`       // 命名空间
	RuleName        string             `json:"ruleName"`        // 规则名称
	RuleDescription string             `json:"ruleDescription"` // 规则描述
	Scope           string             `json:"scope"`           // 生效粒度
	MatchType       string             `json:"matchType"`       // 匹配类型（仅路由维度）
	RouteList       []string           `json:"routeList"`       // 路由名称列表（仅路由维度）
	MatchList       []ExtAuthMatchRule `json:"matchList"`       // 匹配规则列表（仅路由维度）
	HTTPService     string             `json:"httpService"`     // HTTP服务配置（YAML字符串）
	StatusOnError   int                `json:"statusOnError"`   // 认证失败状态码
	PluginURL       string             `json:"pluginURL"`       // 插件镜像地址
	Enabled         bool               `json:"enabled"`         // 是否启用
}

// CreateExtAuthResponse 创建外部认证响应
type CreateExtAuthResponse struct {
	ID            string   `json:"id"`            // 外部认证插件ID
	Enabled       bool     `json:"enabled"`       // 是否开启
	Name          string   `json:"name"`          // 插件名称
	Description   string   `json:"description"`   // 备注
	Scope         string   `json:"scope"`         // 生效粒度
	MatchType     string   `json:"matchType"`     // 匹配类型（仅路由维度）
	RouteList     []string `json:"routeList"`     // 路由名称列表（仅路由维度）
	HTTPService   string   `json:"httpService"`   // HTTP服务配置（YAML字符串）
	StatusOnError int      `json:"statusOnError"` // 认证失败状态码
	CreateTime    string   `json:"createTime"`    // 创建时间
}

// ExtAuthInfo 外部认证信息
type ExtAuthInfo struct {
	Enabled       bool               `json:"enabled"`       // 是否开启
	Name          string             `json:"name"`          // 插件名称
	Description   string             `json:"description"`   // 备注
	Scope         string             `json:"scope"`         // 生效粒度
	MatchType     string             `json:"matchType"`     // 匹配类型（仅路由维度）
	RouteList     []string           `json:"routeList"`     // 路由名称列表（仅路由维度）
	MatchList     []ExtAuthMatchRule `json:"matchList"`     // 匹配规则列表（仅路由维度）
	HTTPService   string             `json:"httpService"`   // HTTP服务配置（YAML字符串）
	StatusOnError int                `json:"statusOnError"` // 认证失败状态码
	CreateTime    string             `json:"createTime"`    // 创建时间
	UpdateTime    string             `json:"updateTime"`    // 更新时间
}

// UpdateExtAuthRequest 编辑外部认证请求
type UpdateExtAuthRequest struct {
	Enabled       bool     `json:"enabled" valid:"required"`     // 是否开启
	Description   string   `json:"description"`                  // 备注
	Scope         string   `json:"scope"`                        // 生效粒度
	MatchType     string   `json:"matchType"`                    // 匹配类型（仅路由维度）
	RouteList     []string `json:"routeList"`                    // 路由名称列表（仅路由维度）
	HTTPService   string   `json:"httpService" valid:"required"` // HTTP服务配置（YAML字符串）
	StatusOnError int      `json:"statusOnError"`                // 认证失败状态码
}

// ExtAuthItem 外部认证列表项
type ExtAuthItem struct {
	Name          string   `json:"name"`          // 插件名称
	Enabled       bool     `json:"enabled"`       // 启用状态
	Scope         string   `json:"scope"`         // 生效粒度
	MatchType     string   `json:"matchType"`     // 匹配类型（仅路由维度）
	RouteList     []string `json:"routeList"`     // 路由名称列表（仅路由维度）
	HTTPService   string   `json:"httpService"`   // HTTP服务配置（YAML字符串）
	StatusOnError int      `json:"statusOnError"` // 认证失败状态码
	CreateTime    string   `json:"createTime"`    // 创建时间
	UpdateTime    string   `json:"updateTime"`    // 更新时间
}

// PluginMarketItem 插件市场已安装插件项
type PluginMarketItem struct {
	PluginName          string                `json:"pluginName"`          // 插件名称
	RuleCount           PluginMarketRuleCount `json:"ruleCount"`           // 规则数量统计
	PluginType          string                `json:"pluginType"`          // 插件类型（auth）
	InstallStatus       string                `json:"installStatus"`       // 安装状态：installed/uninstalled
	RouteEnabledCount   int                   `json:"routeEnabledCount"`   // 路由维度启用规则数量
	GatewayEnabledCount int                   `json:"gatewayEnabledCount"` // 网关维度启用规则数量
}

// PluginMarketRuleCount 插件市场规则数量统计
type PluginMarketRuleCount struct {
	Total            int `json:"total"`            // 规则总数
	RouteRuleCount   int `json:"routeRuleCount"`   // 路由维度规则数量
	GatewayRuleCount int `json:"gatewayRuleCount"` // 网关维度规则数量
}

// PluginMarketListResponse 插件市场列表响应
type PluginMarketListResponse struct {
	OrderBy    string             `json:"orderBy"`    // 排序字段
	Order      string             `json:"order"`      // 排序方式
	PageNo     int                `json:"pageNo"`     // 页码
	PageSize   int                `json:"pageSize"`   // 每页数量
	TotalCount int                `json:"totalCount"` // 总数
	Result     []PluginMarketItem `json:"result"`     // 结果列表
}

// DeleteExtAuthResponse 删除外部认证响应
type DeleteExtAuthResponse struct {
	Success bool   `json:"success"` // 请求是否成功
	Status  int    `json:"status"`  // 状态码
	Message string `json:"message"` // 删除结果消息
}

// UninstallPluginRequest 卸载插件请求
type UninstallPluginRequest struct {
	PluginName string `json:"pluginName" valid:"required"` // 插件名称（如：ext-auth）
}

// UninstallPluginResponse 卸载插件响应
type UninstallPluginResponse struct {
	Success       bool   `json:"success"`       // 请求是否成功
	Status        int    `json:"status"`        // HTTP状态码
	Message       string `json:"message"`       // 响应消息
	PluginName    string `json:"pluginName"`    // 插件名称
	DeletedCount  int    `json:"deletedCount"`  // 删除的规则数量
	UninstallTime string `json:"uninstallTime"` // 卸载时间
}

// RouteItem 路由项
type RouteItem struct {
	RouteName  string `json:"routeName"`  // 路由名称
	HasExtAuth bool   `json:"hasExtAuth"` // 是否关联外部认证插件
}

// RouteListResponse 路由列表响应
type RouteListResponse struct {
	Result []RouteItem `json:"result"` // 结果列表
}

// GatewayStatus 网关状态信息
type GatewayStatus struct {
	Ready     int32 `json:"ready"`     // 当前就绪的副本数
	UpToDate  int32 `json:"upToDate"`  // 已更新到最新版本的副本数
	Available int32 `json:"available"` // 当前可用的副本数
	Replicas  int32 `json:"replicas"`  // 期望的副本数
}
