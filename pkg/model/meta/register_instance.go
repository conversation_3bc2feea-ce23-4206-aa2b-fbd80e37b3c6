package meta

import (
	"time"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type RegisterInstance struct {
	dbutil.BaseModel

	//nolint
	InstanceId string `gorm:"column:instance_id" json:"instanceId" dbutil:"orderable" valid:"required"`
	//nolint
	InstanceName string `gorm:"column:instance_name" json:"instanceName" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	Region string `gorm:"column:region" json:"region" dbutil:"orderable" valid:"required"`
	//nolint
	AccountId string `gorm:"column:account_id" json:"accountId" dbutil:"orderable" valid:"required"`
	//nolint
	Version string `gorm:"column:version" json:"version" dbutil:"orderable"`
	//nolint
	Args string `gorm:"column:args" json:"args" dbutil:"orderable,updatable"`
	//nolint
	VpcId string `gorm:"column:vpc_id" json:"vpcId" dbutil:"updatable" valid:"required"`
	//nolint
	SubnetId string `gorm:"column:subnet_id" json:"subnetId" dbutil:"updatable" valid:"required"`
	//nolint
	Status int `gorm:"column:status" json:"status" dbutil:"orderable,updatable"`
	//nolint
	ServerProtocol string `gorm:"column:server_protocol" json:"serverProtocol" dbutil:"orderable,updatable"`
	//nolint
	ServerPort string `gorm:"column:server_port" json:"serverPort" dbutil:"orderable,updatable"`
	//nolint
	LoadBalanceList string `gorm:"column:load_balance_list" json:"loadBalanceList" dbutil:"updatable"`
	//nolint
	MonitorEnabled int `gorm:"column:monitor_enabled" json:"monitorEnabled" dbutil:"orderable,updatable"`
	//nolint
	MonitorInstanceId string `gorm:"column:monitor_instance_id" json:"monitorInstanceId" dbutil:"orderable,updatable"`
	//nolint
	MonitorAgentId string `gorm:"column:monitor_agent_id" json:"monitorAgentId" dbutil:"orderable,updatable"`
	//nolint
	MonitorRegion string `gorm:"column:monitor_region" json:"monitorRegion" dbutil:"orderable,updatable"`
	//nolint
	ExtendData string `gorm:"column:extend_data" json:"extendData" dbutil:"updatable"`
	//nolint
	Deleted *int `gorm:"column:deleted" json:"deleted" dbutil:"searchable:wildcard,orderable"`
}

func (registerInstance *RegisterInstance) TableName() string {
	return "t_register_instance"
}

// RegisterInstanceStatus 0 - 初始化，1 - 创建中，2 - 运行中，3 - 调整中，4 - 释放中，5 - 运行异常，6 - 创建失败
type RegisterInstanceStatus int

const (
	InitStatus         RegisterInstanceStatus = 0
	CreatingStatus     RegisterInstanceStatus = 1
	RunningStatus      RegisterInstanceStatus = 2
	AdjustingStatus    RegisterInstanceStatus = 3
	ReleasingStatus    RegisterInstanceStatus = 4
	ErrorStatus        RegisterInstanceStatus = 5
	CreateFailedStatus RegisterInstanceStatus = 6
)

type QueryRegisterInstance struct {
	// page params
	PageSize    int64  `query:"pageSize"`
	PageNo      int64  `query:"pageNo"`
	Keyword     string `query:"keyword"`
	KeywordType string `query:"keywordType"`
	Order       string `query:"order"`
	OrderBy     string `query:"orderBy"`
	// user domain id
	AccountID string
	// region
	Region string `query:"region"`
	// status
	Status string `query:"instanceStatus"`
}

type PageResponse struct {
	Order      string      `json:"order,omitempty"`
	OrderBy    string      `json:"orderBy,omitempty"`
	PageNo     int64       `json:"pageNo"`
	PageSize   int64       `json:"pageSize"`
	Result     interface{} `json:"result"`
	TotalCount int64       `json:"totalCount"`
}

type RegisterInsListRep struct {
	Id             string    `json:"id"`
	Name           string    `json:"name"`
	Region         string    `json:"region"`
	Status         int       `json:"status"`
	ServerPort     string    `json:"serverPort"`
	ServerProtocol string    `json:"serverProtocol"`
	CreateTime     time.Time `json:"createTime"`
	UpdateTime     time.Time `json:"updateTime"`
}

type RegisterInsDetailRep struct {
	Id                string                 `json:"id"`
	Name              string                 `json:"name"`
	Region            string                 `json:"region"`
	Status            int                    `json:"status"`
	LoadBalanceList   []RegisterLoadBalancer `json:"loadBalanceList"`
	CreateTime        time.Time              `json:"createTime"`
	UpdateTime        time.Time              `json:"updateTime"`
	ServerPort        string                 `json:"serverPort"`
	ServerProtocol    string                 `json:"serverProtocol"`
	MonitorInstanceId string                 `json:"monitorInstanceId"`
}

type RegisterLoadBalancer struct {
	Type       string `json:"type"`
	VpcId      string `json:"vpcId"`
	VpcName    string `json:"vpcName"`
	SubnetId   string `json:"subnetId"`
	SubnetName string `json:"subnetName"`
	Ip         string `json:"ip"`
	Id         string `json:"id"`
	EsgId      string `json:"enterpriseSecurityGroupId"`
	EsgName    string `json:"enterpriseSecurityGroupName"`
}

type UpdateRegistryReq struct {
	Endpoints []RegisterLoadBalancer `json:"endpoints"`
}
