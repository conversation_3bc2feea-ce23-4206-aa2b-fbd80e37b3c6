package meta

import (
	"github.com/asaskevich/govalidator"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
)

type IngressDetail struct {
	IstioVersion     string `json:"istioVersion" gorm:"column:istio_version" dbutil:"searchable:wildcard,orderable" valid:"required"`
	Namespace        string `json:"namespace"`
	IngressId        string `json:"ingressId"`
	GatewayType      string `json:"gatewayType"`
	IngressStatus    string `json:"ingressStatus"`
	ClusterInfo      ClusterInfo
	BlbDetailResult  BlbDetailResult `json:"blbInfo"`
	BAendpointResult VpcEndpoint     `json:"baEndpoint"`
}

type AiGateway struct {
	Namespace               string      `json:"namespace"`
	IngressId               string      `json:"instanceId"`
	IngressStatus           string      `json:"ingressStatus"`
	InternalIP              string      `json:"internalIP"`
	ExternalIP              string      `json:"externalIP"`
	CreateTime              string      `json:"createTime"`
	Region                  string      `json:"region"`
	Replicas                int         `json:"replicas"`
	VpcCidr                 string      `json:"vpcCidr"`
	VpcId                   string      `json:"vpcId"`
	SubnetId                string      `json:"subnetId"`
	GatewayType             string      `json:"gatewayType"`
	PublicAccessible        bool        `json:"publicAccessible"`
	DeletionProtection      bool        `json:"deleteProtection"`
	Name                    string      `json:"name"`
	Description             string      `json:"description"`
	EnableIngress           bool        `json:"enableIngress"`
	EnableAllIngressClasses bool        `json:"enableAllIngressClasses"`
	EnableAllNamespaces     bool        `json:"enableAllNamespaces"`
	IngressClasses          []string    `json:"ingressClasses"`
	WatchNamespaces         []string    `json:"watchNamespaces"`
	BAendpointResult        VpcEndpoint `json:"baEndpoint"`
	AssociatedCluster       string      `json:"associatedCluster"`
	SrcProduct              string      `json:"srcProduct"`
}

type AiGatewayList struct {
	TotalCount int64       `json:"totalCount"`
	Result     []AiGateway `json:"result"`
}

type IngressGatewayParams struct {
	ClusterName        string                `json:"clusterName"`
	DeployMode         string                `json:"deployMode"`
	GatewayType        string                `json:"gatewayType"`
	IstioVersion       string                `json:"istioVersion"`
	IngressGatewayList []IngressGatewayParam `json:"ingressGatewayList"`
}

type IngressGatewayParam struct {
	Name      string `json:"name"`
	TypeLabel string `json:"typeLabel"`
	BindPort  string `json:"bindPort"`
	Replicas  int    `json:"replicas"`
	BindVpc   string `json:"bindVpc"`
}

// ToIngressModel 生成原生网关配置对象，用于数据库交互
func (ig *IngressGatewayParams) ToIngressModel() (*GatewayModel, error) {
	if _, validErr := govalidator.ValidateStruct(ig); validErr != nil {
		return nil, validErr
	}
	gatewayModel := &GatewayModel{
		InstanceUUID:      "",
		IstioVersion:      ig.IstioVersion,
		ClusterName:       ig.ClusterName,
		ClusterUUID:       "",
		GatewayUUID:       "",
		GatewayName:       "",
		Region:            "",
		AccountId:         "",
		Namespace:         "",
		ResourceQuota:     "''",
		VpcNetworkId:      "''",
		SubnetId:          "''",
		DeployMode:        ig.DeployMode,
		GatewayType:       ig.GatewayType,
		Replicas:          1,
		MonitorAgentID:    "",
		MonitorInstanceID: "",
		MonitorJobID:      "",
		MonitorRegion:     "",
		Deleted:           csm.Int(0),
	}

	return gatewayModel, nil
}
