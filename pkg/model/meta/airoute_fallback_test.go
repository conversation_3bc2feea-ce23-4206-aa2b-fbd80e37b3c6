package meta

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestFallbackConfig_ValidateFallbackConfig(t *testing.T) {
	tests := []struct {
		name        string
		request     *AIRouteRequest
		expectError bool
		errorMsg    string
	}{
		{
			name: "Fallback disabled - should pass validation",
			request: &AIRouteRequest{
				RouteName: "test-route",
				FallbackConfig: &FallbackConfig{
					Enabled: false,
				},
			},
			expectError: false,
		},
		{
			name: "Fallback config is nil - should pass validation",
			request: &AIRouteRequest{
				RouteName:      "test-route",
				FallbackConfig: nil,
			},
			expectError: false,
		},
		{
			name: "Valid Fallback config - should pass validation",
			request: &AIRouteRequest{
				RouteName: "test-route",
				FallbackConfig: &FallbackConfig{
					Enabled:     true,
					ServiceName: "fallback-service",
					Namespace:   "default",
					ServicePort: 8080,
				},
			},
			expectError: false,
		},
		{
			name: "Missing service name - should fail validation",
			request: &AIRouteRequest{
				RouteName: "test-route",
				FallbackConfig: &FallbackConfig{
					Enabled:     true,
					ServiceName: "",
					Namespace:   "default",
					ServicePort: 8080,
				},
			},
			expectError: true,
			errorMsg:    "fallback service name is required",
		},
		{
			name: "Missing namespace - should fail validation",
			request: &AIRouteRequest{
				RouteName: "test-route",
				FallbackConfig: &FallbackConfig{
					Enabled:     true,
					ServiceName: "fallback-service",
					Namespace:   "",
					ServicePort: 8080,
				},
			},
			expectError: true,
			errorMsg:    "fallback service namespace is required",
		},
		{
			name: "Invalid port (zero) - should fail validation",
			request: &AIRouteRequest{
				RouteName: "test-route",
				FallbackConfig: &FallbackConfig{
					Enabled:     true,
					ServiceName: "fallback-service",
					Namespace:   "default",
					ServicePort: 0,
				},
			},
			expectError: true,
			errorMsg:    "fallback service port must be between 1 and 65535",
		},
		{
			name: "Invalid port (too high) - should fail validation",
			request: &AIRouteRequest{
				RouteName: "test-route",
				FallbackConfig: &FallbackConfig{
					Enabled:     true,
					ServiceName: "fallback-service",
					Namespace:   "default",
					ServicePort: 70000,
				},
			},
			expectError: true,
			errorMsg:    "fallback service port must be between 1 and 65535",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.request.ValidateFallbackConfig()
			
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestFallbackConfig_JSONSerialization(t *testing.T) {
	// Test JSON marshaling and unmarshaling
	original := &AIRouteRequest{
		RouteName: "test-route",
		FallbackConfig: &FallbackConfig{
			Enabled:     true,
			ServiceName: "fallback-service",
			Namespace:   "default",
			ServicePort: 8080,
		},
	}

	// This test would require JSON marshaling/unmarshaling
	// For now, just test that the struct is properly defined
	assert.NotNil(t, original.FallbackConfig)
	assert.True(t, original.FallbackConfig.Enabled)
	assert.Equal(t, "fallback-service", original.FallbackConfig.ServiceName)
	assert.Equal(t, "default", original.FallbackConfig.Namespace)
	assert.Equal(t, 8080, original.FallbackConfig.ServicePort)
}
