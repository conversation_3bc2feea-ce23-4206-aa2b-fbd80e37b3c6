package meta

// SecurityGroupRule 表示安全组规则的配置
type SecurityGroupRule struct {
	Protocol  string `json:"protocol"`  // 协议类型，如 "tcp", "udp", "icmp", "全部协议"
	PortRange string `json:"portRange"` // 端口范围，如 "1-65535"
	Direction string `json:"direction"` // 方向，ingress(入)或egress(出)
}

// CreateSecurityGroupRuleArgs 用于创建安全组规则的参数
type CreateSecurityGroupRuleArgs struct {
	Name  string              `json:"name"`  // 安全组名称
	Desc  string              `json:"desc"`  // 安全组描述
	VpcId string              `json:"vpcId"` // VPC ID
	Rules []SecurityGroupRule `json:"rules"` // 安全组规则列表
}

// DeleteSecurityGroupRuleArgs 用于删除安全组的参数
type DeleteSecurityGroupRuleArgs struct {
	SecurityGroupId string `json:"securityGroupId"` // 安全组ID
}
