package meta

type RouteRuleParams struct {
	Name               string   `json:"name" valid:"required"`
	Namespace          string   `json:"namespace" valid:"required"`
	MatchRule          string   `json:"matchRule" valid:"required"`
	Methods            []string `json:"methods"`
	DestinationService string   `json:"destinationService" valid:"required"`
	LoadBalanceAlgo    string   `json:"loadBalanceAlgo"`
	AuthEnabled        int      `json:"authEnabled"`
}
