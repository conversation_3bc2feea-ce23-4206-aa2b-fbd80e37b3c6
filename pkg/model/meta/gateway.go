package meta

import (
	"time"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// DomainConf 添加域名配置结构体
type DomainConf struct {
	InstanceUUID string      `param:"instanceUUID"`
	Domains      []string    `json:"domains"`
	Port         *DomainPort `json:"port"`
	Cert         *DomainCert `json:"cert"`
	IsForceHTTPS bool        `json:"isForceHttps"`
}

func NewDomainConf() *DomainConf {
	return &DomainConf{
		InstanceUUID: "",
		Domains:      []string{},
		Port:         &DomainPort{},
		Cert:         &DomainCert{},
		IsForceHTTPS: false,
	}
}

// IngressParam remote集群监听ingress前后端交互结构
type IngressParam struct {
	Enabled     bool            `json:"enabled"`
	Status      string          `json:"status"`
	CLusterList []RemoteCluster `json:"clusterList"`
}

type RemoteCluster struct {
	Enabled     bool   `json:"enabled"`
	ClusterID   string `json:"clusterId"`
	ClusterName string `json:"clusterName"`
	Region      string `json:"region"`
}

// HpaConf HPA配置结构体
type HpaConf struct {
	InstanceUUID string `param:"instanceUUID"`
	GatewayUUID  string `param:"gatewayUUID"`
	Enabled      bool   `json:"enabled"`
	MinReplicas  int32  `json:"minReplicas"`
	MaxReplicas  int32  `json:"maxReplicas"`
	Namespace    string
}

func NewHpaConf() *HpaConf {
	return &HpaConf{
		InstanceUUID: "",
		Enabled:      false,
		MinReplicas:  1,
		MaxReplicas:  1,
	}
}

// LogConf BLS日志配置结构体
type LogConf struct {
	InstanceUUID string `param:"instanceUUID"`
	GatewayUUID  string `param:"gatewayUUID"`
	Enabled      bool   `json:"enabled"`
	Type         string `json:"type"`
	LogFile      string `json:"logFile"`
	Namespace    string
	AccountID    string
}

func NewLogConf() *LogConf {
	return &LogConf{
		InstanceUUID: "",
		Enabled:      false,
		Type:         "",
		LogFile:      "",
	}
}

// TLSAccConf TLS加速配置结构体
type TLSAccConf struct {
	InstanceUUID string `param:"instanceUUID"`
	Enabled      bool   `json:"enabled"`
}

func NewTLSAccConf() *TLSAccConf {
	return &TLSAccConf{
		InstanceUUID: "",
		Enabled:      false,
	}
}

// ResourceQuotaConf 网关实例配额结构体
type ResourceQuotaConf struct {
	InstanceUUID  string `param:"instanceUUID"`
	GatewayUUID   string `param:"gatewayUUID"`
	ResourceQuota string `json:"resourceQuota"`
}

func NewResourceQuotaConf() *ResourceQuotaConf {
	return &ResourceQuotaConf{
		InstanceUUID:  "",
		GatewayUUID:   "",
		ResourceQuota: "",
	}
}

// BlbConf Blb配置结构体
type BlbConf struct {
	BlbID   string
	EipConf *EipConf
}

type ElasticPublicNetworkType string

const (
	BINDEIP ElasticPublicNetworkType = "BIND"
	BUYEIP  ElasticPublicNetworkType = "BUY"
)

// EipConf EIP配置结构体
type EipConf struct {
	Enabled bool
	Type    ElasticPublicNetworkType
	ID      string
	IP      string
}

// GatewayModel 网关实例数据库存储结构体
type GatewayModel struct {
	dbutil.BaseModel

	//nolint
	InstanceUUID string `gorm:"column:instance_uuid" json:"instanceUUID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	IstioVersion string `gorm:"column:istio_version" json:"istioVersion" dbutil:"searchable:wildcard" valid:"required"`
	//nolint
	ClusterUUID string `gorm:"column:cluster_uuid" json:"clusterUUID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	ClusterName string `gorm:"column:cluster_name" json:"clusterName" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	GatewayUUID string `gorm:"column:gateway_uuid" json:"gatewayUUID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	GatewayName string `gorm:"column:gateway_name" json:"gatewayName" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	Region string `gorm:"column:region" json:"region" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	AccountId string `gorm:"column:account_id" json:"accountId" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	Namespace string `gorm:"column:namespace" json:"namespace" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	ResourceQuota string `gorm:"column:resource_quota" json:"resourceQuota" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	VpcNetworkId string `gorm:"column:vpc_network_id" json:"vpcNetworkId" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	SubnetId string `gorm:"column:subnet_id" json:"subnetId" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	DeployMode string `gorm:"type:enum('standalone', 'hosting');column:deploy_mode" json:"deployMode" dbutil:"searchable:wildcard" valid:"required"`
	//nolint
	GatewayType string `gorm:"type:enum('ingress', 'egress');column:gateway_type" json:"gatewayType" dbutil:"searchable:wildcard" valid:"required"`
	//nolint
	Replicas int16 `gorm:"column:replicas" json:"replicas" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	MonitorEnabled *bool `gorm:"column:monitor_enabled" json:"monitorEnabled" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	MonitorInstanceID string `gorm:"column:monitor_instance_id" json:"monitorInstanceId" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	MonitorAgentID string `gorm:"column:monitor_agent_id" json:"monitorAgentId" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	MonitorRegion string `gorm:"column:monitor_region" json:"monitorRegion" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	MonitorJobID string `gorm:"column:monitor_job_id" json:"monitorJobId" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	RemoteIngressEnabled *bool `gorm:"column:remote_ingress_enabled" json:"remoteIngressEnabled" dbutil:"searchable:wildcard,updatable"`
	//nolint
	Deleted *int `gorm:"column:deleted" json:"deleted" dbutil:"searchable:wildcard,orderable"`
}

func (gateway *GatewayModel) TableName() string {
	return "t_service_mesh_gateway"
}

// AIGateway AI网关实例模型
type AIGateway struct {
	ID         uint   `gorm:"primary_key"`
	InstanceID string `gorm:"column:instance_id;type:varchar(64);not null;unique_index"`
	AccountID  string `gorm:"column:account_id;type:varchar(64);not null"`
	ClusterID  string `gorm:"column:cluster_id;type:varchar(64);not null"`
	Namespace  string `gorm:"column:namespace;type:varchar(64);not null"`
	CreatedAt  time.Time
	UpdatedAt  time.Time
	DeletedAt  *time.Time `sql:"index"`
}

// TableName 设置表名
func (AIGateway) TableName() string {
	return "ai_gateway"
}
