package vpc

import (
	bccApi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/baidubce/bce-sdk-go/services/endpoint"
	"github.com/baidubce/bce-sdk-go/services/esg"
	"github.com/baidubce/bce-sdk-go/services/vpc"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	GetVPCDetail(ctx csmContext.CsmContext, vpcId, region string) (*vpc.GetVPCDetailResult, error)

	ListVPC(ctx csmContext.CsmContext, args *vpc.ListVPCArgs, region string) (*vpc.ListVPCResult, error)

	ListSubnet(ctx csmContext.CsmContext, args *vpc.ListSubnetArgs, region string) (*vpc.ListSubnetResult, error)

	ListSecurityGroup(ctx csmContext.CsmContext, args *bccApi.ListSecurityGroupArgs, region string) (*bccApi.ListSecurityGroupResult, error)

	CreateSecurityGroupRule(ctx csmContext.CsmContext, args *meta.CreateSecurityGroupRuleArgs, region string) (securityGroupId string, error error)

	DeleteSecurityGroupRule(ctx csmContext.CsmContext, args *meta.DeleteSecurityGroupRuleArgs, region string) error

	CreateEndpoint(ctx csmContext.CsmContext, args *endpoint.CreateEndpointArgs, region string) (*endpoint.CreateEndpointResult, error)

	DeleteEndpoint(ctx csmContext.CsmContext, endpointId, region string) error

	GetEndpointDetail(ctx csmContext.CsmContext, endpointId, region string) (*endpoint.Endpoint, error)

	ListEndpoints(ctx csmContext.CsmContext, args *endpoint.ListEndpointArgs, region string) (*endpoint.ListEndpointResult, error)

	ListEsg(ctx csmContext.CsmContext, args *esg.ListEsgArgs, region string) (*esg.ListEsgResult, error)

	// TODO sdk未更新创建vpc请求参数，sdk更新后删除以下接口
	CreateEndpointWithEip(ctx csmContext.CsmContext, args *meta.CreateEndpointArgs, region string) (*endpoint.CreateEndpointResult, error)

	ListEndpointsWithEip(ctx csmContext.CsmContext, args *endpoint.ListEndpointArgs, region string) (*meta.ListEndpointResult, error)
}
