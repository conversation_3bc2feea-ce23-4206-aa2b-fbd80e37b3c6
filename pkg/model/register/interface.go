package register

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type ServiceInterface interface {
	WithTx(tx *dbutil.DB) ServiceInterface
	NewRegisterInstance(ctx context.CsmContext, instances *meta.RegisterInstance) error
	UpdateRegisterInstance(ctx context.CsmContext, update *meta.RegisterInstance) (*meta.RegisterInstance, error)
	DeleteRegisterInstanceByInstanceId(ctx context.CsmContext, id string) error
	GetRegisterInstances(ctx context.CsmContext, query *meta.QueryRegisterInstance) (*dao.BasePageListResult, *[]meta.RegisterInstance, error)
	GetRegisterInstancesByInstanceId(ctx context.CsmContext, instanceId string) (*meta.RegisterInstance, error)
}
