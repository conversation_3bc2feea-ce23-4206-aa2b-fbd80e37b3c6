package dao

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"

	"reflect"
)

type RegisterInstanceDao struct {
	*dao.Dao
}

func NewRegisterInstanceDao(db *dbutil.DB) *RegisterInstanceDao {
	t := reflect.TypeOf(meta.RegisterInstance{})
	return &RegisterInstanceDao{
		Dao: dao.NewDao(t, db),
	}
}
