package jwtcert

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type ServiceInterface interface {
	WithTx(tx *dbutil.DB) ServiceInterface
	NewJwtCert(ctx context.CsmContext, jwtCert *meta.JwtCert) error
	GetJwtCertByClusterID(ctx context.CsmContext, clusterID string) (*meta.JwtCert, error)
	DeleteJwtCertByClusterID(ctx context.CsmContext, clusterID string) error
}
