package jwtcert

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/jwtcert/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Service struct {
	opt *Option
	dao model.JwtCertDaoInterface
}

// NewJwtCertService 创建JwtCertService
func NewJwtCertService(option *Option) *Service {
	return &Service{
		opt: option,
		dao: dao.NewJwtCertDao(option.DB),
	}
}

// WithTx 返回一个事务的ServiceInterface
func (s *Service) WithTx(tx *dbutil.DB) ServiceInterface {
	Opt := *s.opt
	Opt.DB = tx
	return NewJwtCertService(&Opt)
}

// NewJwtCert 新增jwt证书
func (s *Service) NewJwtCert(ctx csmContext.CsmContext, jwtCert *meta.JwtCert) error {
	return s.dao.Save(ctx, jwtCert)
}

// GetJwtCertByClusterID 根据集群id获取jwt证书
func (s *Service) GetJwtCertByClusterID(ctx csmContext.CsmContext, clusterUUID string) (*meta.JwtCert, error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, csmErr.NewUnauthorizedException("user is nil", err)
	}
	where := &meta.JwtCert{
		Deleted:     csm.Int(0),
		AccountId:   accountId,
		ClusterUUID: clusterUUID,
	}
	jwtCert, err := s.dao.LoadWithWhere(ctx, where)
	if err != nil {
		return nil, err
	}
	return jwtCert.(*meta.JwtCert), nil
}

// DeleteJwtCertByClusterID 根据集群id删除jwt证书
func (s *Service) DeleteJwtCertByClusterID(ctx csmContext.CsmContext, clusterUUID string) error {
	where := &meta.JwtCert{
		Deleted:     csm.Int(0),
		ClusterUUID: clusterUUID,
	}
	return s.dao.BatchDelete(ctx, where)
}
