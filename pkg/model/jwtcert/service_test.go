package jwtcert

import (
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	daoMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao/mocks"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	"os"
	"path/filepath"
	"testing"
)

var (
	mockDB, _ = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	User      = "User"
	iamUser   = &sdkIAM.User{
		ID:   "1",
		Name: "test-user",
		Domain: sdkIAM.UserDomain{
			ID:   "123",
			Name: "test-domain",
		},
	}
	testRegion = "bj"
)

func TestService_NewJwtCert(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-SaveJwtCert",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockDao := daoMock.NewMockBaseInterface(ctrl)

		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDao,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockDao.EXPECT().Save(mockCtx, gomock.Any()).Return(nil)
			testJwtCert := &meta.JwtCert{}
			err := service.NewJwtCert(mockCtx, testJwtCert)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestService_DeleteJwtCertByClusterID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-DeleteJwtCert",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockDao := daoMock.NewMockBaseInterface(ctrl)

		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDao,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockDao.EXPECT().BatchDelete(mockCtx, gomock.Any()).Return(nil)
			err := service.DeleteJwtCertByClusterID(mockCtx, "inst-1")
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestService_GetJwtCertByClusterID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-GetJwtCert",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockDao := daoMock.NewMockBaseInterface(ctrl)

		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDao,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set(User, iamUser)
			mockCtx.Set(region.ContextRegion, testRegion)

			expectRes := &meta.JwtCert{
				ClusterUUID: "inst-1",
			}
			mockDao.EXPECT().LoadWithWhere(mockCtx, gomock.Any()).Return(expectRes, nil)

			res, err := service.GetJwtCertByClusterID(mockCtx, "inst-1")
			assert.Nil(t, err)
			assert.Equal(t, *expectRes, *res)
		})
	}
}
