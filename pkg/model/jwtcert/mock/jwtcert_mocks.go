// Code generated by MockGen. DO NOT EDIT.
// Source: ./interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	jwtcert "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/jwtcert"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	dbutil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// DeleteJwtCertByClusterID mocks base method.
func (m *MockServiceInterface) DeleteJwtCertByClusterID(ctx context.CsmContext, clusterID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteJwtCertByClusterID", ctx, clusterID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteJwtCertByClusterID indicates an expected call of DeleteJwtCertByClusterID.
func (mr *MockServiceInterfaceMockRecorder) DeleteJwtCertByClusterID(ctx, clusterID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteJwtCertByClusterID", reflect.TypeOf((*MockServiceInterface)(nil).DeleteJwtCertByClusterID), ctx, clusterID)
}

// GetJwtCertByClusterID mocks base method.
func (m *MockServiceInterface) GetJwtCertByClusterID(ctx context.CsmContext, clusterID string) (*meta.JwtCert, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetJwtCertByClusterID", ctx, clusterID)
	ret0, _ := ret[0].(*meta.JwtCert)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetJwtCertByClusterID indicates an expected call of GetJwtCertByClusterID.
func (mr *MockServiceInterfaceMockRecorder) GetJwtCertByClusterID(ctx, clusterID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetJwtCertByClusterID", reflect.TypeOf((*MockServiceInterface)(nil).GetJwtCertByClusterID), ctx, clusterID)
}

// NewJwtCert mocks base method.
func (m *MockServiceInterface) NewJwtCert(ctx context.CsmContext, jwtCert *meta.JwtCert) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewJwtCert", ctx, jwtCert)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewJwtCert indicates an expected call of NewJwtCert.
func (mr *MockServiceInterfaceMockRecorder) NewJwtCert(ctx, jwtCert interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewJwtCert", reflect.TypeOf((*MockServiceInterface)(nil).NewJwtCert), ctx, jwtCert)
}

// WithTx mocks base method.
func (m *MockServiceInterface) WithTx(tx *dbutil.DB) jwtcert.ServiceInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(jwtcert.ServiceInterface)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockServiceInterfaceMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockServiceInterface)(nil).WithTx), tx)
}
