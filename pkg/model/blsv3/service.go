package blsv3

import (
	"fmt"
	"sync"

	bce_sdk "github.com/baidubce/bce-sdk-go/bce"
	bls_sdk "github.com/baidubce/bce-sdk-go/services/bls/api"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blsv3"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/blsv3/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

var TaskEndpoint = map[string]string{
	"bj":  "bls.bj.baidubce.com",
	"gz":  "bls.gz.baidubce.com",
	"bd":  "bls.bd.baidubce.com",
	"su":  "bls.su.baidubce.com",
	"yq":  "bls.yq.baidubce.com",
	"fwh": "bls.fwh.baidubce.com",
}

var LogEndpoint = map[string]string{
	"bj":  "bls-log.bj.baidubce.com",
	"gz":  "bls-log.gz.baidubce.com",
	"bd":  "bls-log.bd.baidubce.com",
	"su":  "bls-log.su.baidubce.com",
	"yq":  "bls-log.yq.baidubce.com",
	"fwh": "bls-log.fwh.baidubce.com",
}

const (
	IstioProxyContainerNameKey   = "io.kubernetes.container.name"
	IstioProxyContainerNameValue = "istio-proxy"
	BlsTaskDefaultTTL            = 3 // 收集创建任务前n天的日志
	Close                        = "Close"
	Open                         = "Open"
	Abnormal                     = "Abnormal"
	Running                      = "Running"
	Unsynchronized               = "Unsynchronized"
)

type Service struct {
	opt          *Option
	dao          model.BlsDaoInterface
	blsv3Service blsv3.ServiceInterface
	blsLog       *bce_sdk.BceClient
}

// NewBlsService 创建一个新的BlsService实例
//
// 参数：
// option: Option类型指针，用于设置BlsService的配置信息
//
// 返回值：
// 返回一个Service类型指针，表示BlsService实例
func NewBlsService(option *Option) *Service {
	res := &Service{
		opt:          option,
		dao:          dao.NewBlsDao(option.DB),
		blsv3Service: &blsv3.Client{},
	}
	return res
}

// WithTaskOperation 方法为Service类型的方法，用于根据指定的endpoint获取BlsV3Client
//
// 参数：
// ctx: context.CsmContext类型，上下文对象
// endpoint: string类型，任务端点
//
// 返回值：
// error类型，如果endpoint无效则返回错误信息，否则返回nil
func (s *Service) WithTaskOperation(ctx context.CsmContext, endpoint string) error {
	if _, ok := TaskEndpoint[endpoint]; !ok {
		return fmt.Errorf("invaild endpoint %s", endpoint)
	}
	return s.blsv3Service.GetBlsV3Client(ctx, TaskEndpoint[endpoint])
}

// WithLogOperation 为Service类型的方法，用于设置日志操作所需的客户端
// ctx：CsmContext类型的上下文对象
// endpoint：日志服务的Endpoint地址
// 返回值：error类型，如果endpoint无效或获取客户端失败，则返回错误信息
func (s *Service) WithLogOperation(ctx context.CsmContext, endpoint string) error {
	if _, ok := LogEndpoint[endpoint]; !ok {
		return fmt.Errorf("invaild endpoint %s", endpoint)
	}

	bceClient, err := bce.GetDefaultClient(ctx, LogEndpoint[endpoint])
	if err != nil {
		return err
	}
	s.blsLog = bceClient
	return nil
}

// WithTaskOperationWithAkSk 使用AK/SK方式获取Task服务的BLSv3客户端
//
// 参数：
//
//	ctx: CsmContext上下文对象
//	ak: 访问密钥AccessKey
//	sk: 密钥SecretKey
//	endpoint: Task服务的Endpoint地址
//
// 返回值：
//
//	如果endpoint无效，则返回错误；否则返回nil
func (s *Service) WithTaskOperationWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) error {
	if _, ok := TaskEndpoint[endpoint]; !ok {
		return fmt.Errorf("invaild endpoint %s", endpoint)
	}
	return s.blsv3Service.GetBlsV3ClientWithAkSk(ctx, ak, sk, TaskEndpoint[endpoint])
}

// WithLogOperationWithAkSk 函数用于设置使用AK/SK方式初始化日志服务客户端
// 参数：
//
//	ctx: 上下文对象
//	ak: 访问密钥AK
//	sk: 访问密钥SK
//	endpoint: 日志服务的地域终端节点
//
// 返回值：
//
//	如果初始化成功，则返回nil；否则返回错误信息
func (s *Service) WithLogOperationWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) error {
	if _, ok := LogEndpoint[endpoint]; !ok {
		return fmt.Errorf("invaild endpoint %s", endpoint)
	}

	bceClient, err := bce.GetClientWithAkSk(ctx, ak, sk, LogEndpoint[endpoint])
	if err != nil {
		return err
	}
	s.blsLog = bceClient
	return nil
}

// NewBlsTask 是Service类型的方法，用于创建一个新的BlsTask。
// 参数ctx是CsmContext类型，表示上下文。
// 参数bls是meta.Bls类型的指针，表示待创建的BlsTask。
// 函数返回值为error类型，表示创建过程中出现的错误，如果创建成功，返回nil。
func (s *Service) NewBlsTask(ctx context.CsmContext, bls *meta.Bls) error {
	if err := s.dao.Save(ctx, bls); err != nil {
		return err
	}
	return nil
}

// WithTx 返回一个带事务的Service
func (s *Service) WithTx(tx *dbutil.DB) ServiceInterface {
	Opt := *s.opt
	Opt.DB = tx
	return NewBlsService(&Opt)
}

// GetAllBlsTasksByClusterUUID 根据集群UUID获取所有未删除的BLS任务
//
// 参数：
//
//	ctx context.CsmContext：上下文对象
//	clusterUUID string：集群UUID
//
// 返回值：
//
//	*[]meta.Bls：BLS任务列表
//	error：错误信息
func (s *Service) GetAllBlsTasksByClusterUUID(ctx context.CsmContext, clusterUUID string) (*[]meta.Bls, error) {
	accountId, _ := iam.GetAccountId(ctx)

	where := &meta.Bls{
		ClusterUUID: clusterUUID,
		AccountID:   accountId,
		Deleted:     csm.Int(0),
	}
	not := &meta.Bls{}

	tasks, err := s.dao.ListAll(ctx, nil, where, not)
	if err != nil {
		return nil, err
	}
	if tasks == nil {
		return nil, nil
	}
	return tasks.(*[]meta.Bls), nil
}

// GetAllBlsTasksByInstanceUUID 根据实例UUID获取所有未删除的BLS任务
//
// 参数：
// ctx：上下文信息
// instanceUUID：实例UUID
//
// 返回值：
// *[]meta.Bls：BLS任务列表
// error：错误信息
func (s *Service) GetAllBlsTasksByInstanceUUID(ctx context.CsmContext, instanceUUID string) (*[]meta.Bls, error) {
	accountId, _ := iam.GetAccountId(ctx)

	where := &meta.Bls{
		InstanceUUID: instanceUUID,
		AccountID:    accountId,
		Deleted:      csm.Int(0),
	}
	not := &meta.Bls{}

	tasks, err := s.dao.ListAll(ctx, nil, where, not)
	if err != nil {
		return nil, err
	}
	if tasks == nil {
		return nil, nil
	}
	return tasks.(*[]meta.Bls), nil
}

// GetBlsTaskByTaskId 根据任务ID获取BLS任务信息
//
// 参数：
//
//	ctx: 上下文对象
//	taskId: 任务ID
//
// 返回值：
//
//	*meta.Bls: BLS任务信息
//	error: 错误信息
func (s *Service) GetBlsTaskByTaskId(ctx context.CsmContext, taskId string) (*meta.Bls, error) {
	accountId, _ := iam.GetAccountId(ctx)
	where := &meta.Bls{
		TaskID:    taskId,
		AccountID: accountId,
	}
	not := &meta.Bls{}
	task, err := s.dao.ListAll(ctx, nil, where, not)
	if err != nil {
		return nil, err
	}
	if task == nil {
		return nil, nil
	}
	return task.(*meta.Bls), nil
}

// DeleteBlsTaskByClusterUUID 根据集群UUID删除BLS任务
//
// 参数：
//
//	ctx context.CsmContext: 上下文信息
//	clusterUUID string: 集群UUID
//
// 返回值：
//
//	error: 错误信息，如果删除成功则返回nil
func (s *Service) DeleteBlsTaskByClusterUUID(ctx context.CsmContext, clusterUUID string) error {
	accountId, _ := iam.GetAccountId(ctx)
	where := &meta.Bls{
		ClusterUUID: clusterUUID,
		AccountID:   accountId,
	}
	return s.dao.BatchDelete(ctx, where)
}

// DeleteBlsTaskByTaskID 根据任务ID删除BLS任务
//
// 参数：
// ctx：上下文对象，用于传递请求信息
// taskId：要删除的BLS任务的ID
//
// 返回值：
// error：如果删除成功，返回nil；否则返回错误信息
func (s *Service) DeleteBlsTaskByTaskID(ctx context.CsmContext, taskId string) error {
	accountId, _ := iam.GetAccountId(ctx)
	where := &meta.Bls{
		TaskID:    taskId,
		AccountID: accountId,
	}
	return s.dao.BatchDelete(ctx, where)
}

// DeleteBlsTaskByInstanceUUID 删除指定实例UUID的BLS任务
// 参数：ctx - context.CsmContext类型，上下文信息
//
//	instanceUUID - string类型，实例UUID
//
// 返回值：error类型，如果删除成功则为nil，否则为错误信息
func (s *Service) DeleteBlsTaskByInstanceUUID(ctx context.CsmContext, instanceUUID string) error {
	accountId, _ := iam.GetAccountId(ctx)
	where := &meta.Bls{
		InstanceUUID: instanceUUID,
		AccountID:    accountId,
	}
	return s.dao.BatchDelete(ctx, where)
}

// GetBlsLogs 函数从BLS日志服务中获取日志存储列表
//
// 参数：
//
//	ctx: CsmContext上下文对象
//
// 返回值：
//
//	*bls_sdk.ListLogStoreResult: 返回一个包含日志存储信息的ListLogStoreResult指针
//	error: 返回错误，如果没有错误则为nil
func (s *Service) GetBlsLogs(ctx context.CsmContext) (*bls_sdk.ListLogStoreResult, error) {
	if s.blsLog == nil {
		return nil, fmt.Errorf("BlsLog is not initialized")
	}
	// res := bls_sdk.ListLogStoreResult{}
	args := &bls_sdk.QueryConditions{
		Order:    "desc",
		OrderBy:  "creationDateTime",
		PageNo:   1,
		PageSize: 100}
	ctx.CsmLogger().Infof("GetBlsLogs args: %v", args)
	res, err := bls_sdk.ListLogStore(s.blsLog, args)
	if err != nil {
		return nil, err
	}
	if res.TotalCount > 100 {
		args.PageSize = res.TotalCount
		res, err = bls_sdk.ListLogStore(s.blsLog, args)
		if err != nil {
			return nil, err
		}
	}

	return res, nil
}

// GetBlsTasksByTaskID 通过任务ID获取BLS任务详情（配置信息）
// 参数：
//   - ctx：Csm上下文对象
//   - taskId：BLS任务ID
//
// 返回值：
//   - *blsv3.BlsTaskDetailResponse：BLS任务详情响应对象指针
//   - error：错误信息，如果操作成功则为nil
func (s *Service) GetBlsTasksByTaskID(ctx context.CsmContext, taskId string) (
	*blsv3.BlsTaskDetailResponse, error) {
	if s.blsv3Service == nil {
		return nil, fmt.Errorf("Blsv3Service is not initialized")
	}
	res, err := s.blsv3Service.GetBlsTask(taskId)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// GetBlsTasksInstanceByTaskID 根据任务ID获取BLS任务实例的运行信息
// 和上面GetBlsTasksByTaskID的区别在于：GetBlsTasksByTaskID获取配置信息，本函数为运行信息
// 参数：
// ctx：CsmContext上下文
// taskId：BLS任务ID
//
// 返回值：
// *blsv3.TaskInstanceResponseParameters：BLS任务实例
// error：错误信息，如果获取成功则为nil
func (s *Service) GetBlsTasksInstanceByTaskID(ctx context.CsmContext, taskId string) (
	*blsv3.TaskInstanceResponseParameters, error) {
	if s.blsv3Service == nil {
		return nil, fmt.Errorf("Blsv3Service is not initialized")
	}
	res, err := s.blsv3Service.GetBlsTaskInstance(taskId)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// CloseBlsTaskByTaskID 关闭指定任务ID的BLS任务
//
// 参数：
//
//	ctx: CsmContext上下文对象
//	taskId: 要关闭的BLS任务ID
//
// 返回值：
//
//	如果成功关闭任务，则返回nil，否则返回错误信息
func (s *Service) CloseBlsTaskByTaskID(ctx context.CsmContext, taskId string) error {
	if s.blsv3Service == nil {
		return fmt.Errorf("Blsv3Service is not initialized")
	}
	return s.blsv3Service.PostBlsTaskAction(taskId, "delete")
}

// CloseBlsTaskByTaskIDs 根据任务ID列表，批量关闭BLS任务
// 参数：
//
//	ctx: 上下文对象
//	tasks: 待关闭的BLS任务ID列表
//
// 返回值：
//
//	error: 错误信息，如果操作成功则返回nil
func (s *Service) CloseBlsTaskByTaskIDs(ctx context.CsmContext, tasks blsv3.TaskIDs) error {
	if s.blsv3Service == nil {
		return fmt.Errorf("Blsv3Service is not initialized")
	}
	return s.blsv3Service.PostBlsTaskActionBatch(tasks, "delete")
}

// CreateTask 方法在Service结构体中定义，用于创建任务
//
// 参数：
//
//	ctx: 上下文对象，类型为context.CsmContext
//	logConf: 日志配置对象，类型为*blsv3.LogConf
//
// 返回值：
//
//	string: 创建的任务ID
//	error: 错误信息，如果创建任务成功则返回nil
func (s *Service) CreateTask(ctx context.CsmContext, logConf *blsv3.LogConf, accountId string) (string, error) {
	if s.blsv3Service == nil {
		return "", fmt.Errorf("Blsv3Service is not initialized")
	}
	body := GenerateTaskCreationRequestBody(logConf, accountId)
	taskID, err := s.blsv3Service.CreateTask(body)
	if err != nil {
		return "", err
	}

	return taskID.TaskID, nil
}

// BoundTaskWithInstances 将任务绑定到集群上所有节点上部署的daemonset传输实例上
// 参数：ctx context.CsmContext - 上下文对象
//
//	taskId string - 任务ID
//	clusterID string - 集群ID
//
// 返回值：error - 错误信息，如果成功则为nil
func (s *Service) BoundTaskWithInstances(ctx context.CsmContext, taskId, clusterID string) error {
	groupDetail, err := s.blsv3Service.GetGroupDetailByClusterUUID(clusterID)
	if err != nil {
		return err
	}
	WaitGroup := sync.WaitGroup{}
	instanceMutex := sync.Mutex{}
	instanceNames := blsv3.TaskInstanceNames{}
	// 遍历groupDetail.Instances，通过group ID获取每个node上部署的daemoset传输实例的ID
	for _, item := range groupDetail.Instances {
		groupId := item.GroupID
		WaitGroup.Add(1)
		go func(groupId string) {
			instance, err := s.blsv3Service.GetTaskInstancesByGroupID(groupId)
			ctx.CsmLogger().Infof("GetTaskInstancesByGroupID, instance:%s", instance)
			if err != nil {
				ctx.CsmLogger().Errorf("GetTaskInstancesByGroupID has an error:%s", err.Error())
				return
			} else if instance == nil {
				return
			}
			instanceMutex.Lock()
			for _, instanceInfo := range instance.Instances {
				instanceNames.InstanceIDs = append(instanceNames.InstanceIDs,
					blsv3.TaskInstanceName{InstanceID: instanceInfo.InstanceID})
			}
			instanceMutex.Unlock()
			WaitGroup.Done()
		}(groupId)
	}
	WaitGroup.Wait()
	if len(instanceNames.InstanceIDs) == 0 {
		ctx.CsmLogger().Info("BoundTaskWithInstances found no task instance")
		return nil
	}
	err = s.blsv3Service.AddTaskInstances(taskId, instanceNames)
	if err != nil {
		// ctx.CsmLogger().Errorf("AddTaskInstances has an error:%s", err.Error())
		return err
	}
	return nil
}

// GenerateTaskCreationRequestBody 根据给定的日志配置生成任务创建请求体
//
// 参数：
// logConf *blsv3.LogConf - 日志配置
//
// 返回值：
// blsv3.TaskCreationRequestBody - 任务创建请求体
func GenerateTaskCreationRequestBody(logConf *blsv3.LogConf, accountID string) blsv3.TaskCreationRequestBody {
	return blsv3.TaskCreationRequestBody{
		Name: "csm_task_" + logConf.InstanceUUID + "_" + logConf.ClusterID,
		Config: blsv3.Config{
			SrcConfig: blsv3.SrcConfig{
				SrcType:        "container",
				LogType:        "stdout",
				SrcDir:         "/var/log",
				MatchedPattern: "^.*$",
				IgnorePattern:  "",
				LabelWhite: []blsv3.Label{
					{
						Key:   IstioProxyContainerNameKey,
						Value: IstioProxyContainerNameValue,
					},
				},
				ProcessType:  "none",
				LogTime:      "system",
				UseMultiline: false,
				TTL:          BlsTaskDefaultTTL,
			},
			DestConfig: blsv3.DestConfig{
				DestType:  logConf.Type,
				LogStore:  logConf.LogStore,
				AccountID: accountID,
				RateLimit: 1,
			},
		},
	}
}
