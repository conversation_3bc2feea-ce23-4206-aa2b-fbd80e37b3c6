package aigateway

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type ServiceInterface interface {
	WithTx(tx *dbutil.DB) ServiceInterface
	NewAIGateway(ctx context.CsmContext, gateway **meta.AIGatewayInstanceModel) error
	UpdateAIGateway(ctx context.CsmContext, dstGateway, updateGateway **meta.AIGatewayInstanceModel) error
	DeleteAIGateway(ctx context.CsmContext, instanceUUID, gatewayUUID string) error
	GetAIGatewayInfo(ctx context.CsmContext, instanceUUID, gatewayUUID string) (**meta.AIGatewayInstanceModel, error)
	GetAIGatewayList(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*[]*meta.AIGatewayInstanceModel, error)
	GetAIGatewayListWithPagination(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*[]*meta.AIGatewayInstanceModel, int64, error)
}
