package aigateway

import (
	"sort"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/aigateway/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Service struct {
	opt *Option
	dao model.AIGatewayDaoInterface
}

func NewAIGatewayService(option *Option) *Service {
	return &Service{
		opt: option,
		dao: dao.NewAIGatewayDao(option.DB),
	}
}

// WithTx 网关实例 tx
func (s *Service) WithTx(tx *dbutil.DB) ServiceInterface {
	nOpt := *s.opt
	nOpt.DB = tx
	return NewAIGatewayService(&nOpt)
}

// NewGateway 创建网关实例
func (s *Service) NewAIGateway(ctx csmContext.CsmContext, gateway **meta.AIGatewayInstanceModel) error {
	return s.dao.Save(ctx, *gateway)
}

// UpdateGateway 更新网关实例
func (s *Service) UpdateAIGateway(ctx csmContext.CsmContext, dstGateway, updateGateway **meta.AIGatewayInstanceModel) error {
	return s.dao.Update(ctx, *dstGateway, *updateGateway)
}

// DeleteGateway 删除网关实例
func (s *Service) DeleteAIGateway(ctx csmContext.CsmContext, instanceUUID, gatewayUUID string) error {
	where := &meta.AIGatewayInstanceModel{
		InstanceUUID: instanceUUID,
		GatewayUUID:  gatewayUUID,
	}
	return s.dao.BatchDelete(ctx, where)
}

// GetGatewayInfo 根据instanceUUID和gatewayUUID获取网关信息
func (s *Service) GetAIGatewayInfo(ctx csmContext.CsmContext, instanceUUID, gatewayUUID string) (**meta.AIGatewayInstanceModel, error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, csmErr.NewUnauthorizedException("user is nil", err)
	}
	where := &meta.AIGatewayInstanceModel{
		Deleted:      csm.Int(0),
		AccountID:    accountId,
		InstanceUUID: instanceUUID,
		GatewayUUID:  gatewayUUID,
	}
	gatewayInfo, err := s.dao.LoadWithWhere(ctx, where)
	if err != nil {
		return nil, err
	}
	result := gatewayInfo.(*meta.AIGatewayInstanceModel)
	return &result, nil
}

// GetGatewayList 获取网关列表
func (s *Service) GetAIGatewayList(ctx csmContext.CsmContext, mrp *meta.CsmMeshRequestParams) (*[]*meta.AIGatewayInstanceModel, error) {
	search := &meta.AIGatewayInstanceModel{}
	if mrp.Keyword != "" {
		switch mrp.KeywordType {
		case GatewayName:
			search.GatewayName = mrp.Keyword
		case GatewayUUID:
			search.GatewayUUID = mrp.Keyword
		}
	}
	where := &meta.AIGatewayInstanceModel{
		Deleted:      csm.Int(0),
		AccountID:    mrp.AccountID,
		InstanceUUID: mrp.InstanceUUID,
	}

	// 添加区域过滤条件
	if mrp.Region != "" {
		where.Region = mrp.Region
	}

	// 添加srcProduct过滤条件
	if mrp.SrcProduct != "" {
		where.SrcProduct = mrp.SrcProduct
	}

	// 使用ListPage方法代替ListAll，以支持按创建时间倒序排序
	// 页码和大小设置为足够大，以获取全部数据
	_, gatewayList, err := s.dao.ListPage(ctx, search, where, "create_time", dbutil.DBOrderDESC, 1000, 1)
	if err != nil {
		return nil, err
	}

	result := gatewayList.(*[]meta.AIGatewayInstanceModel)
	// 转换结果格式
	var convertedList []*meta.AIGatewayInstanceModel
	for i := range *result {
		convertedList = append(convertedList, &(*result)[i])
	}
	return &convertedList, nil
}

// GetAIGatewayListWithPagination 获取分页的网关列表，支持真正的数据库分页
func (s *Service) GetAIGatewayListWithPagination(ctx csmContext.CsmContext, mrp *meta.CsmMeshRequestParams) (*[]*meta.AIGatewayInstanceModel, int64, error) {
	ctx.CsmLogger().Infof("GetAIGatewayListWithPagination 开始，参数: %+v", mrp)
	search := &meta.AIGatewayInstanceModel{}
	if mrp.Keyword != "" {
		switch mrp.KeywordType {
		case GatewayName:
			search.GatewayName = mrp.Keyword
		case GatewayUUID:
			search.GatewayUUID = mrp.Keyword
		}
	}
	where := &meta.AIGatewayInstanceModel{
		Deleted:   csm.Int(0),
		AccountID: mrp.AccountID,
	}

	// 获取所有符合条件的数据
	not := &meta.AIGatewayInstanceModel{}
	allGatewayList, err := s.dao.ListAll(ctx, search, where, not)
	if err != nil {
		ctx.CsmLogger().Errorf("数据库查询失败: %v", err)
		return nil, 0, err
	}

	allResult := allGatewayList.(*[]meta.AIGatewayInstanceModel)
	ctx.CsmLogger().Infof("数据库查询成功，返回 %d 条记录", len(*allResult))

	// 在应用层进行srcProduct过滤
	var filteredResult []meta.AIGatewayInstanceModel
	for _, gateway := range *allResult {
		if mrp.SrcProduct == "" {
			// 默认排除aibox产品
			if gateway.SrcProduct != constants.AIGatewayProductAibox {
				filteredResult = append(filteredResult, gateway)
			}
		} else {
			// 指定了srcProduct的情况
			if gateway.SrcProduct == mrp.SrcProduct {
				filteredResult = append(filteredResult, gateway)
			}
		}
	}

	ctx.CsmLogger().Infof("srcProduct过滤后剩余 %d 条记录", len(filteredResult))
	result := &filteredResult
	totalCount := int64(len(*result))

	// 应用排序
	s.sortGatewayList(result, mrp.OrderBy, mrp.Order)

	// 应用分页
	startIndex := (mrp.PageNo - 1) * mrp.PageSize
	endIndex := startIndex + mrp.PageSize

	// 如果没有数据或开始索引大于总数，返回空结果
	if totalCount == 0 || startIndex >= totalCount {
		return &[]*meta.AIGatewayInstanceModel{}, totalCount, nil
	}

	// 确保结束索引不越界
	if endIndex > totalCount {
		endIndex = totalCount
	}

	// 获取分页数据
	pagedResult := (*result)[startIndex:endIndex]

	// 转换结果格式
	var convertedList []*meta.AIGatewayInstanceModel
	for i := range pagedResult {
		convertedList = append(convertedList, &pagedResult[i])
	}
	return &convertedList, totalCount, nil
}

// sortGatewayList 对网关列表进行排序
func (s *Service) sortGatewayList(gateways *[]meta.AIGatewayInstanceModel, orderBy, order string) {
	if len(*gateways) == 0 {
		return
	}

	sort.Slice(*gateways, func(i, j int) bool {
		switch orderBy {
		case "gateway_name", "name":
			if order == "asc" {
				return (*gateways)[i].GatewayName < (*gateways)[j].GatewayName
			}
			return (*gateways)[i].GatewayName > (*gateways)[j].GatewayName
		case "create_time", "createTime":
			fallthrough
		default:
			// 默认按创建时间排序
			if (*gateways)[i].CreateTime == nil || (*gateways)[j].CreateTime == nil {
				return false
			}
			if order == "asc" {
				return (*gateways)[i].CreateTime.Before(*(*gateways)[j].CreateTime)
			}
			return (*gateways)[i].CreateTime.After(*(*gateways)[j].CreateTime)
		}
	})
}
