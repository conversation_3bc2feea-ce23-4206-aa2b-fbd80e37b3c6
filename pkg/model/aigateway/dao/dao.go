package dao

import (
	"reflect"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type AIGatewayDao struct {
	*dao.Dao
}

func NewAIGatewayDao(db *dbutil.DB) *AIGatewayDao {
	t := reflect.TypeOf(meta.AIGatewayInstanceModel{})
	return &AIGatewayDao{
		Dao: dao.NewDao(t, db),
	}
}
