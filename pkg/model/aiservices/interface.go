package aiservices

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// ServiceInterface 服务接口定义
type ServiceInterface interface {
	// WithTx 支持事务的服务
	WithTx(tx *dbutil.DB) ServiceInterface

	// CreateService 创建服务
	CreateService(ctx csmContext.CsmContext, service *meta.AIService) error

	// BatchCreateServices 批量创建服务
	BatchCreateServices(ctx csmContext.CsmContext, services []*meta.AIService) (int, error)

	// GetServicesByInstanceID 获取网关实例下的服务列表
	GetServicesByInstanceID(ctx csmContext.CsmContext, instanceID string) ([]*meta.AIService, error)

	// GetServiceByID 根据ID获取服务
	GetServiceByID(ctx csmContext.CsmContext, id int64) (*meta.AIService, error)

	// DeleteService 删除服务
	DeleteService(ctx csmContext.CsmContext, instanceID, serviceName, namespace string) error

	// GetServiceDetail 获取服务详情
	GetServiceDetail(ctx csmContext.CsmContext, instanceID, serviceName string) (*meta.AIService, error)

	// ListServices 查询服务列表（带分页）
	ListServices(ctx csmContext.CsmContext, instanceID, keyword string, pageNo, pageSize int, orderBy, order, clusterId string) (*meta.ServiceListPage, error)
}
