package dao

import (
	"reflect"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// AIServiceDao AI服务数据访问对象
type AIServiceDao struct {
	*dao.Dao
}

// NewAIServiceDao 创建服务DAO
func NewAIServiceDao(db *dbutil.DB) *AIServiceDao {
	t := reflect.TypeOf(meta.AIService{})
	return &AIServiceDao{
		Dao: dao.NewDao(t, db),
	}
}
