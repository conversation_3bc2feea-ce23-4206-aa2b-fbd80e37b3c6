package aiservices

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"

	daoMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao/mocks"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

func TestService_CreateService(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDB, _ := gorm.Open("sqlite3", ":memory:")
	mockDao := daoMock.NewMockBaseInterface(ctrl)

	service := &Service{
		opt: NewOption(mockDB),
		dao: mockDao,
	}

	t.Run("success", func(t *testing.T) {
		mockCtx := context.MockNewCsmContext()
		testService := &meta.AIService{
			InstanceID:    "gw-ist9vvin",
			ServiceName:   "mnist-inference",
			ServiceSource: "CCE",
			ClusterID:     "cce-qhjz40ds",
			Namespace:     "default",
			AccountID:     "test-account",
			Region:        "gz",
		}

		mockDao.EXPECT().Save(mockCtx, testService).Return(nil)

		err := service.CreateService(mockCtx, testService)
		assert.Nil(t, err)
	})
}

func TestService_BatchCreateServices(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDB, _ := gorm.Open("sqlite3", ":memory:")
	mockDao := daoMock.NewMockBaseInterface(ctrl)

	service := &Service{
		opt: NewOption(mockDB),
		dao: mockDao,
	}

	t.Run("success", func(t *testing.T) {
		mockCtx := context.MockNewCsmContext()
		testServices := []*meta.AIService{
			{
				InstanceID:    "gw-ist9vvin",
				ServiceName:   "mnist-inference",
				ServiceSource: "CCE",
				ClusterID:     "cce-qhjz40ds",
				Namespace:     "default",
				AccountID:     "test-account",
				Region:        "gz",
			},
			{
				InstanceID:    "gw-ist9vvin",
				ServiceName:   "text-embedding",
				ServiceSource: "CCE",
				ClusterID:     "cce-qhjz40ds",
				Namespace:     "default",
				AccountID:     "test-account",
				Region:        "gz",
			},
		}

		// Setup expectations
		for _, testService := range testServices {
			mockDao.EXPECT().Save(mockCtx, testService).Return(nil)
		}

		count, err := service.BatchCreateServices(mockCtx, testServices)
		assert.Nil(t, err)
		assert.Equal(t, 2, count)
	})
}
