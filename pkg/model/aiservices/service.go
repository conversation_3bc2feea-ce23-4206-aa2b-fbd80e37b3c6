package aiservices

import (
	"context"
	"encoding/json"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/aiservices/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// Service AI服务操作service
type Service struct {
	opt        *Option
	dao        model.AIServiceDaoInterface
	cceService cce.ClientInterface
}

// NewAIServiceService 创建服务实例
func NewAIServiceService(option *Option) *Service {
	return &Service{
		opt:        option,
		dao:        dao.NewAIServiceDao(option.DB),
		cceService: cce.NewClientService(),
	}
}

// WithTx 支持事务的服务
func (s *Service) WithTx(tx *dbutil.DB) ServiceInterface {
	opt := *s.opt
	opt.DB = tx
	return NewAIServiceService(&opt)
}

// CreateService 创建服务
func (s *Service) CreateService(ctx csmContext.CsmContext, service *meta.AIService) error {
	// 保存服务
	return s.dao.Save(ctx, service)
}

// BatchCreateServices 批量创建服务
func (s *Service) BatchCreateServices(ctx csmContext.CsmContext, services []*meta.AIService) (int, error) {
	// 批量创建服务
	addedCount := 0
	for _, service := range services {
		err := s.CreateService(ctx, service)
		if err != nil {
			ctx.CsmLogger().Errorf("Create service error: %v", err)
			continue
		}
		addedCount++
	}
	return addedCount, nil
}

// GetServicesByInstanceID 获取网关实例下的服务列表
func (s *Service) GetServicesByInstanceID(ctx csmContext.CsmContext, instanceID string) ([]*meta.AIService, error) {
	// 获取账户ID
	accountID, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, csmErr.NewUnauthorizedException("user is nil", err)
	}

	// 构造查询条件
	where := &meta.AIService{
		Deleted:    csm.Int(0),
		AccountID:  accountID,
		InstanceID: instanceID,
	}

	// 查询服务列表
	result, err := s.dao.ListAll(ctx, nil, where, nil)
	if err != nil {
		return nil, err
	}

	// 类型转换
	services := result.(*[]*meta.AIService)
	return *services, nil
}

// GetServiceByID 根据ID获取服务
func (s *Service) GetServiceByID(ctx csmContext.CsmContext, id int64) (*meta.AIService, error) {
	// 获取账户ID
	accountID, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, csmErr.NewUnauthorizedException("user is nil", err)
	}

	// 根据ID获取服务
	resultObj, err := s.dao.Load(ctx, id)
	if err != nil {
		return nil, err
	}

	// 类型转换
	service := resultObj.(*meta.AIService)

	// 检查所有权
	if service.AccountID != accountID {
		return nil, csmErr.NewUnauthorizedException("您无权访问此资源", nil)
	}

	return service, nil
}

// DeleteService 删除服务
func (s *Service) DeleteService(ctx csmContext.CsmContext, instanceID, serviceName, namespace string) error {
	// 获取账户ID
	accountID, err := iam.GetAccountId(ctx)
	if err != nil {
		return csmErr.NewUnauthorizedException("user is nil", err)
	}

	// 构造删除条件
	where := &meta.AIService{
		Deleted:     csm.Int(0),
		AccountID:   accountID,
		InstanceID:  instanceID,
		ServiceName: serviceName,
		Namespace:   namespace,
	}

	// 执行软删除
	return s.dao.BatchDelete(ctx, where)
}

// GetServiceDetail 获取服务详情
func (s *Service) GetServiceDetail(ctx csmContext.CsmContext, instanceID, serviceName string) (*meta.AIService, error) {
	// 获取账户ID
	accountID, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, csmErr.NewUnauthorizedException("user is nil", err)
	}

	// 构造查询条件
	where := &meta.AIService{
		Deleted:     csm.Int(0),
		AccountID:   accountID,
		InstanceID:  instanceID,
		ServiceName: serviceName,
	}

	// 查询服务
	result, err := s.dao.LoadWithWhere(ctx, where)
	if err != nil {
		return nil, err
	}

	return result.(*meta.AIService), nil
}

// ListServices 查询服务列表（带分页）
func (s *Service) ListServices(ctx csmContext.CsmContext, instanceID, keyword string,
	pageNo, pageSize int, orderBy, order, clusterId string) (*meta.ServiceListPage, error) {
	// 获取账户ID
	accountID, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, csmErr.NewUnauthorizedException("user is nil", err)
	}

	// 构造查询条件
	where := &meta.AIService{
		Deleted:    csm.Int(0),
		AccountID:  accountID,
		InstanceID: instanceID,
	}

	// 构造搜索条件
	var search *meta.AIService
	if keyword != "" {
		search = &meta.AIService{
			ServiceName: keyword, // The WithSearch method will handle wildcard matching based on the dbutil tag
		}
	}

	// 设置排序
	var dbOrder dbutil.DBOrder
	if order == "asc" {
		dbOrder = dbutil.DBOrderASC
	} else {
		dbOrder = dbutil.DBOrderDESC
	}

	// 设置默认排序字段
	if orderBy == "" {
		orderBy = "create_time"
	}

	// 设置默认分页参数
	if pageNo <= 0 {
		pageNo = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询总数
	totalCount, err := s.dao.Count(ctx, search, where)
	if err != nil {
		return nil, err
	}

	// 查询分页数据
	_, serviceList, err := s.dao.ListPage(ctx, search, where, orderBy, dbOrder, int64(pageSize), int64(pageNo))
	if err != nil {
		return nil, err
	}

	// 转换为服务列表
	services := serviceList.(*[]meta.AIService)

	// 构造返回结果
	result := &meta.ServiceListPage{
		OrderBy:    orderBy,
		Order:      order,
		PageNo:     pageNo,
		PageSize:   pageSize,
		TotalCount: int(totalCount),
		Result:     make([]meta.ServiceListItem, 0, len(*services)),
	}

	// 构造服务列表项
	// 注意：RouteCount 需要在实际项目中查询关联的路由数量
	region := ctx.Get(reg.ContextRegion).(string)

	// 获取目标网关的命名空间
	gatewayNamespace := "istio-system-" + instanceID

	// 创建集群客户端
	var userclusterId string
	if len(*services) > 0 {
		// 获取第一个service的clusterId
		firstService := (*services)[0]
		userclusterId = firstService.ClusterID
		ctx.CsmLogger().Infof("Using clusterId %s from first service for NewClient", clusterId)
	}

	client, err := s.cceService.NewClient(ctx, region, clusterId, meta.HostingMeshType)
	userClient, err := s.cceService.NewClient(ctx, region, userclusterId, meta.StandaloneMeshType)
	istioClient := client.Istio()

	// 查询VirtualService资源列表, 获取RouteCount
	vsList, err := istioClient.NetworkingV1alpha3().VirtualServices(gatewayNamespace).List(context.TODO(), metav1.ListOptions{})

	// 填充服务列表
	for _, service := range *services {
		// 格式化创建时间
		createTimeStr := ""
		if service.CreateTime != nil {
			createTimeStr = service.CreateTime.Format("2006-01-02 15:04:05")
		}

		// 解析端口信息字符串为数组
		var servicePorts []string
		if service.ServicePort != "" {
			err = json.Unmarshal([]byte(service.ServicePort), &servicePorts)
			if err != nil {
				ctx.CsmLogger().Errorf("Failed to unmarshal service port info: %v", err)
			}
		}

		if err != nil {
			ctx.CsmLogger().Errorf("failed to create cluster client, region=%s, clusterId=%s, err=%v", region, "", err)
			// 发生错误时继续处理，不中断流程
			// 更新服务项的路由数量
			serviceItem := meta.ServiceListItem{
				ServiceName:   service.ServiceName,
				ServiceSource: service.ServiceSource,
				RouteCount:    0,
				CreateTime:    createTimeStr,
				ServiceStatus: service.ServiceStatus, // 保持原状态，因为无法查询集群
				ClusterID:     service.ClusterID,
				Namespace:     service.Namespace,
				ServicePort:   servicePorts,
			}
			result.Result = append(result.Result, serviceItem)
			continue // 处理完当前服务，继续下一个服务
		} else {
			// 定义服务的完整主机名
			fullHostName := service.ServiceName + "." + service.Namespace + ".svc.cluster.local"
			routeCount := 0

			// 遍历所有VirtualService
			for _, vs := range vsList.Items {
				// 遍历VirtualService中的HTTP规则
				for _, httpRoute := range vs.Spec.Http {
					// 遍历HTTP规则中的路由目标
					for _, route := range httpRoute.Route {
						if route.Destination != nil && route.Destination.Host == fullHostName {
							// 找到了引用当前服务的路由规则
							routeCount++
							break // 一个HTTP规则中可能有多个目标，找到匹配的就跳出内层循环
						}
					}
				}
			}

			// 检查服务是否存在
			serviceStatus := service.ServiceStatus
			svc, svcErr := userClient.Kube().CoreV1().Services(service.Namespace).Get(context.TODO(), service.ServiceName, metav1.GetOptions{})
			if svcErr != nil || svc == nil {
				ctx.CsmLogger().Infof("Service %s in namespace %s not found in cluster, marking as deleted",
					service.ServiceName, service.Namespace)
				serviceStatus = "deleted"
			}

			// 更新服务项的路由数量
			serviceItem := meta.ServiceListItem{
				ServiceName:   service.ServiceName,
				ServiceSource: service.ServiceSource,
				RouteCount:    routeCount,
				CreateTime:    createTimeStr,
				ServiceStatus: serviceStatus,
				ClusterID:     service.ClusterID,
				Namespace:     service.Namespace,
				ServicePort:   servicePorts,
			}

			result.Result = append(result.Result, serviceItem)
			continue // 处理完当前服务，继续下一个服务
		}
	}

	return result, nil
}
