/*
Copyright 2021.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

// CCRPhase is a label for the condition of a CCR instance at current time
type CCRPhase string

const (
	// CCRPending means the CCR request has been accepted by server without processing.
	CCRPending CCRPhase = ""
	// CCRCreating means the CCR instance is in creating process
	CCRCreating CCRPhase = "Creating"
	// CCRCreated means the CCR is created
	CCRCreated CCRPhase = "Created"
	// CCRRunning means the CCR instance has been created an in running
	CCRRunning CCRPhase = "Running"
	// CCRFailed means the CCR instance is failed to be created.
	CCRFailed CCRPhase = "Failed"
	// CCRTerminating means that the CCR is in terminating, but the resource has not been released completely.
	CCRTerminating CCRPhase = "Terminating"

	CCRUpgrading     CCRPhase = "Upgrading"
	CCRUpgraded      CCRPhase = "Upgraded"
	CCRUpgradeFailed CCRPhase = "UpgradeFailed"

	CCRCalibrating CCRPhase = "Calibrating"

	CCRStopping       CCRPhase = "Stopping"
	CCRStopped        CCRPhase = "Stopped"
	CCRStoppingFailed CCRPhase = "StoppingFailed"

	CCRStarting       CCRPhase = "Starting"
	CCRStartingFailed CCRPhase = "StartingFailed"
)

// ComponentHealth the health level of the component
type ComponentHealth string

const (
	// ComponentRed means the component is unavailable
	ComponentRed ComponentHealth = "red"
	// ComponentYellow means the component is partly available
	ComponentYellow ComponentHealth = "yellow"
	// ComponentGreen means the component is available
	ComponentGreen ComponentHealth = "green"
)

// +kubebuilder:object:generate=true
type DatabaseInfo struct {
	Databases []string `json:"databases,omitempty"`
}
