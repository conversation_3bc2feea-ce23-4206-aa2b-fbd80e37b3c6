/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	"fmt"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"strconv"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// RegisterInstanceSpec defines the desired state of RegisterInstance
type RegisterInstanceSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	AccountID  string     `json:"accountID,omitempty"`
	UserID     string     `json:"userID,omitempty"`
	VpcID      string     `json:"vpcID,omitempty"`
	SubnetID   string     `json:"subnetID,omitempty"`
	EsgID      string     `json:"esgID,omitempty"`
	EsgName    string     `json:"esgName,omitempty"`
	ServerSpec ServerSpec `json:"serverSpec,omitempty"`
	Args       *Args      `json:"args,omitempty"`
	Release    string     `json:"release,omitempty"`
}

// RegisterInstanceStatus defines the observed state of RegisterInstance
type RegisterInstanceStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file
	Phase               CCRPhase     `json:"phase,omitempty"`
	Store               Store        `json:"store,omitempty"`
	ServerBLB           *ServerBLB   `json:"serverBLB,omitempty"`
	EndpointList        []*Endpoint  `json:"endpointList,omitempty"`
	Reason              string       `json:"reason,omitempty"`
	Message             string       `json:"message,omitempty"`
	LastProbeTime       *metav1.Time `json:"lastProbeTime,omitempty"`
	Token               string       `json:"token,omitempty"`
	ServerConfigVersion string       `json:"serverConfigVersion,omitempty"`
	LastPhaseTime       *metav1.Time `json:"lastPhaseTime,omitempty"`
	Release             string       `json:"release,omitempty"`
}

type Args struct {
	APIEurekaCustomDciClass                     string `json:"apiEurekaCustomDciClass,omitempty"`
	APIEurekaCustomDciName                      string `json:"apiEurekaCustomDciName,omitempty"`
	APIEurekaDeltaIntervalSeconds               *int32 `json:"apiEurekaDeltaIntervalSeconds,omitempty"`
	JobDeleteServiceUnreferencedEnable          *bool  `json:"jobDeleteServiceUnreferencedEnable,omitempty"`
	JobDeleteServiceUnreferencedIntervalMinutes *int32 `json:"jobDeleteServiceUnreferencedIntervalMinutes,omitempty"`
	JobDeleteInstanceUnhealthyEnable            *bool  `json:"jobDeleteInstanceUnhealthyEnable,omitempty"`
	JobDeleteInstanceUnhealthyIntervalMinutes   *int32 `json:"jobDeleteInstanceUnhealthyIntervalMinutes,omitempty"`
	NamespaceAutoCreate                         *bool  `json:"namespaceAutoCreate,omitempty"`

	MonitorEnable     *bool  `json:"monitorEnable,omitempty"`
	MonitorCpromID    string `json:"monitorCpromId,omitempty"`
	MonitorCpromToken string `json:"monitorCpromToken,omitempty"`
}

type ServerSpec struct {
	Replicas int32  `json:"replicas,omitempty"`
	CPU      string `json:"cpu,omitempty"`    // 100m
	Memory   string `json:"memory,omitempty"` // 1000Mi
}

type Store struct {
	MySQL StoreConfig `json:"mysql,omitempty"`
	Redis StoreConfig `json:"redis,omitempty"`
}

type StoreConfig struct {
	Address  string `json:"address,omitempty"`
	Port     int    `json:"port,omitempty"`
	User     string `json:"user"`
	Password string `json:"password,omitempty"`
	Database string `json:"database,omitempty"`
}

type ServerBLB struct {
	ID        string `json:"id"`
	IP        string `json:"ip"`
	Port      int    `json:"port"`
	BRegionIP string `json:"bRegionIP"`
}

type EndpointType string

const (
	EndpointTypePrivate = "private"
	EndpointTypePublic  = "public"
)

type Endpoint struct {
	Type EndpointType `json:"type"`
	ID   string       `json:"id"`
	IP   string       `json:"ip"`
}

//+kubebuilder:printcolumn:name="PHASE",type=string,JSONPath=`.status.phase`
//+kubebuilder:object:root=true
//+kubebuilder:subresource:status

// RegisterInstance is the Schema for the registerinstances API
type RegisterInstance struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   RegisterInstanceSpec   `json:"spec,omitempty"`
	Status RegisterInstanceStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// RegisterInstanceList contains a list of RegisterInstance
type RegisterInstanceList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []RegisterInstance `json:"items"`
}

func (cr *RegisterInstance) WriteArgs(args map[string]interface{}) (bool, error) {
	v1Args := cr.Spec.Args

	argsUpdated := false
	ifArgsNil := func() {
		if v1Args == nil {
			v1Args = &Args{}
		}
		argsUpdated = true
	}

	getInt32 := func(i interface{}) *int32 {
		if v, ok := i.(int32); ok {
			return &v
		}
		if v, ok := i.(string); ok {
			vInt64, err := strconv.ParseInt(v, 10, 32)
			if err != nil {
				return nil
			}
			vInt32 := int32(vInt64)
			return &vInt32
		}
		return nil
	}

	getBool := func(i interface{}) *bool {
		if v, ok := i.(bool); ok {
			return &v
		}
		if v, ok := i.(string); ok {
			vBool, err := strconv.ParseBool(v)
			if err != nil {
				return nil
			}
			return &vBool
		}
		return nil
	}

	if i, ok := args["api.eureka.custom.dci.class"]; ok {
		if v, ok := i.(string); ok {
			ifArgsNil()
			v1Args.APIEurekaCustomDciClass = v
		} else {
			return false, fmt.Errorf("invalid parameter %v type, need string", "api.eureka.custom.dci.class")
		}
	} else {
		v1Args.APIEurekaCustomDciClass = ""
	}
	if i, ok := args["api.eureka.custom.dci.name"]; ok {
		if v, ok := i.(string); ok {
			ifArgsNil()
			v1Args.APIEurekaCustomDciName = v
		} else {
			return false, fmt.Errorf("invalid parameter %v type, need string", "api.eureka.custom.dci.name")
		}
	} else {
		v1Args.APIEurekaCustomDciName = ""
	}
	if i, ok := args["api.eureka.deltaIntervalSeconds"]; ok {
		v := getInt32(i)
		if v == nil {
			return false, fmt.Errorf("invalid parameter %v type, need integer", "api.eureka.deltaIntervalSeconds")
		}
		ifArgsNil()
		v1Args.APIEurekaDeltaIntervalSeconds = v
	} else {
		v1Args.APIEurekaDeltaIntervalSeconds = nil
	}
	if i, ok := args["job.deleteServiceUnreferenced.enable"]; ok {
		v := getBool(i)
		if v == nil {
			return false, fmt.Errorf("invalid parameter %v type, need boolean", "job.deleteServiceUnreferenced.enable")
		}
		ifArgsNil()
		v1Args.JobDeleteServiceUnreferencedEnable = v
	} else {
		v1Args.JobDeleteServiceUnreferencedEnable = nil
	}
	if i, ok := args["job.deleteInstanceUnhealthy.enable"]; ok {
		v := getBool(i)
		if v == nil {
			return false, fmt.Errorf("invalid parameter %v type, need boolean", "job.deleteInstanceUnhealthy.enable")
		}
		ifArgsNil()
		v1Args.JobDeleteInstanceUnhealthyEnable = v
	} else {
		v1Args.JobDeleteInstanceUnhealthyEnable = nil
	}
	if i, ok := args["job.deleteServiceUnreferenced.intervalMinutes"]; ok {
		v := getInt32(i)
		if v == nil {
			return false, fmt.Errorf("invalid parameter %v type, need integer", "job.deleteServiceUnreferenced.intervalMinutes")
		}
		ifArgsNil()
		v1Args.JobDeleteServiceUnreferencedIntervalMinutes = v
	} else {
		v1Args.JobDeleteServiceUnreferencedIntervalMinutes = nil
	}
	if i, ok := args["job.deleteInstanceUnhealthy.intervalMinutes"]; ok {
		v := getInt32(i)
		if v == nil {
			return false, fmt.Errorf("invalid parameter %v type, need integer", "job.deleteInstanceUnhealthy.intervalMinutes")
		}
		ifArgsNil()
		v1Args.JobDeleteInstanceUnhealthyIntervalMinutes = v
	} else {
		v1Args.JobDeleteInstanceUnhealthyIntervalMinutes = nil
	}
	if i, ok := args["namespace.autoCreate"]; ok {
		v := getBool(i)
		if v == nil {
			return false, fmt.Errorf("invalid parameter %v type, need boolean", "namespace.autoCreate")
		}
		ifArgsNil()
		v1Args.NamespaceAutoCreate = v
	} else {
		v1Args.NamespaceAutoCreate = nil
	}
	if i, ok := args["monitor.enable"]; ok {
		v := getBool(i)
		if v == nil {
			return false, fmt.Errorf("invalid parameter %v type, need boolean", "monitor.enable")
		}
		ifArgsNil()
		v1Args.MonitorEnable = v
	} else {
		v1Args.MonitorEnable = nil
	}
	if i, ok := args["monitor.cprom.id"]; ok {
		if v, ok := i.(string); ok {
			ifArgsNil()
			v1Args.MonitorCpromID = v
		} else {
			return false, fmt.Errorf("invalid parameter %v type, need string", "monitor.cprom.id")
		}
	} else {
		v1Args.MonitorCpromID = ""
	}
	if i, ok := args["monitor.cprom.token"]; ok {
		if v, ok := i.(string); ok {
			ifArgsNil()
			v1Args.MonitorCpromToken = v
		} else {
			return false, fmt.Errorf("invalid parameter %v type, need string", "monitor.cprom.token")
		}
	} else {
		v1Args.MonitorCpromToken = ""
	}
	cr.Spec.Args = v1Args
	return argsUpdated, nil
}

func (cr *RegisterInstance) ReadArgs() map[string]interface{} {
	if cr.Spec.Args == nil {
		return nil
	}

	args := make(map[string]interface{})
	if cr.Spec.Args.APIEurekaCustomDciName != "" {
		args["api.eureka.custom.dci.name"] = cr.Spec.Args.APIEurekaCustomDciName
	}
	if cr.Spec.Args.APIEurekaCustomDciClass != "" {
		args["api.eureka.custom.dci.class"] = cr.Spec.Args.APIEurekaCustomDciClass
	}
	if cr.Spec.Args.APIEurekaDeltaIntervalSeconds != nil {
		args["api.eureka.deltaIntervalSeconds"] = *cr.Spec.Args.APIEurekaDeltaIntervalSeconds
	}
	if cr.Spec.Args.JobDeleteInstanceUnhealthyEnable != nil {
		args["job.deleteInstanceUnhealthy.enable"] = *cr.Spec.Args.JobDeleteInstanceUnhealthyEnable
	}
	if cr.Spec.Args.JobDeleteServiceUnreferencedEnable != nil {
		args["job.deleteServiceUnreferenced.enable"] = *cr.Spec.Args.JobDeleteServiceUnreferencedEnable
	}
	if cr.Spec.Args.JobDeleteServiceUnreferencedIntervalMinutes != nil {
		args["job.deleteServiceUnreferenced.intervalMinutes"] = *cr.Spec.Args.JobDeleteServiceUnreferencedIntervalMinutes
	}
	if cr.Spec.Args.JobDeleteInstanceUnhealthyIntervalMinutes != nil {
		args["job.deleteInstanceUnhealthy.intervalMinutes"] = *cr.Spec.Args.JobDeleteInstanceUnhealthyIntervalMinutes
	}
	if cr.Spec.Args.NamespaceAutoCreate != nil {
		args["namespace.autoCreate"] = *cr.Spec.Args.NamespaceAutoCreate
	}
	if cr.Spec.Args.MonitorEnable != nil {
		args["monitor.enable"] = *cr.Spec.Args.MonitorEnable
	}
	if cr.Spec.Args.MonitorCpromID != "" {
		args["monitor.cprom.id"] = cr.Spec.Args.MonitorCpromID
	}
	if cr.Spec.Args.MonitorCpromToken != "" {
		args["monitor.cprom.token"] = cr.Spec.Args.MonitorCpromToken
	}

	if len(args) == 0 {
		return nil
	}
	return args
}

func (s *RegisterInstanceStatus) GetServerPortProtocol() (port, protocol string) {
	if version.NewVersionService(nil).CompareTo(s.Release, "1.1.0") >= 0 {
		// release大于等于1.1.0
		return "8848,9848,8761", "nacos-http,nacos-grpc,eureka"
	} else {
		return "8761", "eureka"
	}
}
