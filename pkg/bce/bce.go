package bce

import (
	"github.com/baidubce/bce-sdk-go/auth"
	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam/sts_credential"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

const (
	LocalMode      = "local.dev"
	LocalAccessKey = "local.access_key"
	LocalSecretKey = "local.secret_key"

	HostAccessKey = "cloud.hosting.access_key"
	HostSecretKey = "cloud.hosting.secret_key"

	BceIamProfile  = "cloud.iamProfile"
	BceServiceRole = "bceServiceRole"
)

var localCache sts_credential.Cache

func init() {
	localCache = sts_credential.NewLocalCredentialCache()
}

func GetDefaultClient(ctx context.CsmContext, endpoint string) (bceClient *bce.BceClient, error error) {
	roleName := viper.GetString(BceServiceRole)
	clientProfile := viper.GetString(BceIamProfile)
	return GetClient(ctx, clientProfile, roleName, endpoint)
}

func GetClient(ctx context.CsmContext, clientProfile, roleName, endpoint string) (bceClient *bce.BceClient, error error) {
	if viper.GetBool(LocalMode) {
		ctx.CsmLogger().Infof("*** local develop mode with GetClient ***")
		ak := viper.GetString(LocalAccessKey)
		sk := viper.GetString(LocalSecretKey)
		return bce.NewBceClientWithAkSk(ak, sk, endpoint)
	}
	return GetClientWithSts(ctx, clientProfile, roleName, endpoint)
}

func GetHostingClient(ctx context.CsmContext, endpoint string) (bceClient *bce.BceClient, error error) {
	roleName := viper.GetString(BceServiceRole)
	clientProfile := viper.GetString(BceIamProfile)
	return GetHostClient(ctx, clientProfile, roleName, endpoint)
}

func GetHostClient(ctx context.CsmContext, clientProfile, roleName, endpoint string) (bceClient *bce.BceClient, error error) {
	ctx.CsmLogger().Infof("*** local develop mode with GetHostingClient ***")
	ak := viper.GetString(HostAccessKey)
	sk := viper.GetString(HostSecretKey)
	return bce.NewBceClientWithAkSk(ak, sk, endpoint)
}

func GetClientWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) (bceClient *bce.BceClient, err error) {
	return bce.NewBceClientWithAkSk(ak, sk, endpoint)
}

func GetClientWithSts(ctx context.CsmContext, clientProfile, roleName, endpoint string) (bceClient *bce.BceClient, err error) {
	credential, err := iam.GetAssumeRoleCredential(ctx, clientProfile, roleName, localCache)
	if err != nil {
		return nil, err
	}
	ak := credential.AccessKeyId
	sk := credential.AccessKeySecret
	sessionToken := credential.SessionToken

	client, err := bce.NewBceClientWithAkSk(ak, sk, endpoint)
	if err != nil {
		return nil, err
	}
	stsCredential, err := auth.NewSessionBceCredentials(ak, sk, sessionToken)
	if err != nil {
		return nil, err
	}
	client.Config.Credentials = stsCredential
	return client, nil
}
