package cert

// CertsRawDataResult 证书详情接口返回结构体
type CertsRawDataResult struct {
	CertID         string `json:"certId"`
	CertName       string `json:"certName"`
	CertServerData string `json:"certServerData"`
	CertPrivateKey string `json:"certPrivateKey"`
	CertLinkData   string `json:"certLinkData"`
	CertType       int    `json:"certType"`
}

// CertsInfo
type CertsInfo struct {
	CertID         string `json:"certId"`
	CertName       string `json:"certName"`
	CertCommonName string `json:"certCommonName"`
	CertStartTime  string `json:"certStartTime"`
	CertStopTime   string `json:"certStopTime"`
	CertCreateTime string `json:"certCreateTime"`
	CertUpdateTime string `json:"certUpdateTime"`
}

// CertsListResult
type CertsListResult struct {
	Certs []CertsInfo `json:"certs"`
}
