package cert

import (
	"fmt"

	bce_sdk "github.com/baidubce/bce-sdk-go/bce"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

const (
	URI_PREFIX            = bce_sdk.URI_PREFIX + "v1"
	LIST_RESOURCE_URL     = "/certificate"
	RAW_DATA_RESOURCE_URL = "/certificate/%s/rawData"
)

// Client of Cert service is a kind of BceClient, so derived from BceClient
type Client struct {
	*bce_sdk.BceClient
}

func GetCertificateRawDataUri(certID string) string {
	return fmt.Sprintf(URI_PREFIX+RAW_DATA_RESOURCE_URL, certID)
}

func GetCertificateListUri() string {
	return fmt.Sprintf(URI_PREFIX + LIST_RESOURCE_URL)
}

func GetClient(ctx context.CsmContext, iamProfile, roleName, endpoint string) (certClient *Client, err error) {
	bceClient, err := bce.GetClient(ctx, iamProfile, roleName, endpoint)
	if err != nil {
		return nil, err
	}
	return &Client{bceClient}, nil
}

func GetDefaultClient(ctx context.CsmContext, endpoint string) (certClient *Client, err error) {
	bceClient, err := bce.GetDefaultClient(ctx, endpoint)
	if err != nil {
		return nil, err
	}
	return &Client{bceClient}, nil
}
