package cert

import (
	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/http"
)

func (c *Client) GetCertsList() (*CertsListResult, error) {
	result := &CertsListResult{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.GET).
		WithURL(GetCertificateListUri()).
		WithResult(result).
		Do()

	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetCertsRawData(certID string) (*CertsRawDataResult, error) {
	result := &CertsRawDataResult{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.GET).
		WithURL(GetCertificateRawDataUri(certID)).
		WithResult(result).
		Do()

	if err != nil {
		return nil, err
	}

	return result, nil
}
