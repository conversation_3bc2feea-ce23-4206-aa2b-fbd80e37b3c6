package blsv3

import "time"

type TaskStatus struct {
	ID               string      `json:"id"`
	Name             string      `json:"name"`
	Type             string      `json:"type"`
	CreationDateTime time.Time   `json:"creationDateTime"`
	Status           string      `json:"status"`
	TaskVersion      string      `json:"taskVersion"`
	Tags             interface{} `json:"tags"`
}

type DestConfig struct {
	DestType  string `json:"destType"`
	LogStore  string `json:"logStore"`
	AccountID string `json:"accountID"`
	RateLimit int    `json:"rateLimit"`
}

type ProcessConfig struct {
	DataType         string `json:"dataType"`
	DiscardOnFailure bool   `json:"discardOnFailure"`
	KeepOriginal     bool   `json:"keepOriginal"`
	Keys             string `json:"keys"`
	Quote            string `json:"quote"`
	Regex            string `json:"regex"`
	SampleLog        string `json:"sampleLog"`
	Separator        string `json:"separator"`
}

type SrcConfig struct {
	DateFormat     string        `json:"dateFormat"`
	LogTime        string        `json:"logTime"`
	LogType        string        `json:"logType"`
	IgnorePattern  string        `json:"ignorePattern"`
	MatchedPattern string        `json:"matchedPattern"`
	MultilineRegex string        `json:"multilineRegex"`
	ProcessConfig  ProcessConfig `json:"processConfig"`
	ProcessType    string        `json:"processType"`
	RecursiveDir   bool          `json:"recursiveDir"`
	SrcDir         string        `json:"srcDir"`
	SrcType        string        `json:"srcType"`
	TimestampKey   string        `json:"timestampKey"`
	TTL            int           `json:"ttl"`
	UseMultiline   bool          `json:"useMultiline"`
	LabelWhite     []Label       `json:"labelWhite"`
}
type Label struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}
type Config struct {
	DestConfig DestConfig `json:"destConfig"`
	SrcConfig  SrcConfig  `json:"srcConfig"`
}

type BlsTaskDetail struct {
	Status TaskStatus `json:"status"`
	Config Config     `json:"config"`
}

type BlsTaskDetailResponse struct {
	Task BlsTaskDetail `json:"task"`
}

// TaskInstance 表示单个任务的详情
type TaskInstance struct {
	InstanceID              string    `json:"instanceID"`              // 实例id
	InstanceName            string    `json:"instanceName"`            // 实例名
	IP                      string    `json:"ip"`                      // 实例ip
	Status                  string    `json:"status"`                  // 实例状态
	BytesWritten            int64     `json:"bytesWritten"`            // 实例在该任务下输出的总字节数
	Errors                  string    `json:"errors"`                  // 错误提示
	CreationDateTime        time.Time `json:"creationDateTime"`        // 创建时间
	UpdatedTime             time.Time `json:"updatedTime"`             // 更新时间
	IsAvailableForNewConfig bool      `json:"isAvailableForNewConfig"` // 是否可更新配置
	Groups                  string    `json:"groups"`                  // 所属全部组名列表字符串，用逗号分隔
}

// TaskInstanceResponseParameters 表示整个响应参数的结构（如果需要的话）
type TaskInstanceResponseParameters struct {
	TotalSize int            `json:"totalSize"` // 查询到的任务总数
	Offset    int            `json:"offset"`    // 分页偏移量
	Size      int            `json:"size"`      // 页大小
	Instances []TaskInstance `json:"instances"` // 任务列表详情
}

type TaskCreationRequestBody struct {
	Name   string         `json:"name"`      // 任务名
	Config Config         `json:"config"`    // 任务配置
	Hosts  []TaskInstance `json:"instances"` // TODO: 待新版本API上线后改为Instances []Instance `json:"instances"`
	Tags   []Tag          `json:"tags"`
}
type Tag struct {
	TagKey   string `json:"tagKey"`   // 标签key
	TagValue string `json:"tagValue"` // 标签value
}

type TaskIDs struct {
	TaskIDs []TaskID `json:"tasks"`
}
type TaskID struct {
	TaskID string `json:"taskID"`
}

type LogConf struct {
	InstanceUUID string `param:"instanceUUID"` //参数传入
	ClusterID    string `param:"clusterId"`    //遍历获取
	Type         string `json:"type"`          //默认为BLS
	LogStore     string `json:"logFile"`       //通过参数传入，在post中name
}

type GroupsPage struct {
	TotalSize int                   `json:"totalSize"` // 总大小
	Offset    int                   `json:"offset"`    // 偏移量
	Size      int                   `json:"size"`      // 页面大小
	Instances []GroupCreationParams `json:"groups"`    // 实例列表
}

type GroupCreationParams struct {
	GroupID          string    `json:"groupID"`          // 实例组id
	Name             string    `json:"name"`             // 实例组名称
	CreationDateTime time.Time `json:"creationDateTime"` // 创建时间
	Description      string    `json:"description"`      // 描述
	InstanceCount    int       `json:"instanceCount"`    // 组内实例数量
}

type TaskInstancePage struct {
	TotalSize int                `json:"total_size"` // 总大小
	Offset    int                `json:"offset"`     // 偏移量
	Size      int                `json:"size"`       // 页面大小
	Instances []TaskInstanceInfo `json:"instances"`  // 实例列表
}

type TaskInstanceInfo struct {
	InstanceID      string    `json:"instanceID"`              // 实例id
	InstanceName    string    `json:"instanceName"`            // 实例名
	IPAddress       string    `json:"ip"`                      // ip地址
	InstanceState   string    `json:"status"`                  // 实例运行状态
	CreationTime    time.Time `json:"creationDateTime"`        // 创建时间
	UpdateTime      time.Time `json:"updatedTime"`             // 更新时间
	CanUpdateConfig bool      `json:"isAvailableForNewConfig"` // 是否可更新配置
	GroupName       string    `json:"groups"`                  // 组名（用逗号分隔）
	// Tags            []Tag     `json:"tags"`                    // 标签列表
}

type TaskInstanceNames struct {
	InstanceIDs []TaskInstanceName `json:"instances"`
}
type TaskInstanceName struct {
	InstanceID string `json:"instanceID"`
}
