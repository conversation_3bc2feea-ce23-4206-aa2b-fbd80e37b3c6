package blsv3

import (
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/baidubce/bce-sdk-go/auth"
	bce_sdk "github.com/baidubce/bce-sdk-go/bce"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	ctx "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	fakeClient *bce_sdk.BceClient
	mockCtx, _ = ctx.NewCsmContextMock()
)

// TestGetBlsGetTaskDetailURL 是一个测试函数，用于测试GetBlsGetTaskDetailURL函数的正确性
func TestGetBlsGetTaskDetailURL(t *testing.T) {
	result := GetBlsGetTaskDetailURL("test")
	fmt.Println(result)
	expect := "/v3/task/test"
	if expect != result {
		t.Error("TestGetBlsGetTaskDetailURL do not work properly")
	}
}

// TestGetBlsGetTaskInstanceURL 测试函数，用于测试GetBlsGetTaskInstanceURL函数
// t是testing.T类型参数，用于测试时记录错误信息
func TestGetBlsGetTaskInstanceURL(t *testing.T) {
	result := GetBlsGetTaskInstanceURL("taskID")
	expected := fmt.Sprintf(URIPrefix+GetTaskInstanceURL, "taskID")
	if result != expected {
		t.Errorf("expected: %v, but got: %v", expected, result)
	}
}

// TestGetBlsPostTaskActionURL 测试GetBlsPostTaskActionURL函数
func TestGetBlsPostTaskActionURL(t *testing.T) {
	type args struct {
		taskID string
		action string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test",
			args: args{
				taskID: "taskID",
				action: "action",
			},
			want: fmt.Sprintf(URIPrefix+PostTaskActionURL, "action"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetBlsPostTaskActionURL(tt.args.action); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetBlsPostTaskActionURL() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestGetBlsPostTaskActionPatchURL 用于测试函数 GetBlsPostTaskActionPatchURL 的功能
func TestGetBlsPostTaskActionPatchURL(t *testing.T) {
	// action := "test"
	result := GetBlsPostTaskActionPatchURL()
	expect := "/v3/task"
	if result != expect {
		t.Error("TestGetBlsPostTaskActionPatchURL failed")
	}
}

// TestGetBlsCreateTaskActionURL 用于测试GetBlsCreateTaskActionURL函数是否正确返回创建任务的API地址
func TestGetBlsCreateTaskActionURL(t *testing.T) {
	url := GetBlsCreateTaskActionURL()
	assert.Equal(t, "/v3/task", url)
}

// TestGetBlsV3Client 测试获取BLS V3客户端
func TestGetBlsV3Client(t *testing.T) {

	endpoint := "bls.baidubce.com"
	fakeClient = &bce_sdk.BceClient{
		Config: &bce_sdk.BceClientConfiguration{
			Endpoint: endpoint,
		},
		Signer: &auth.BceV1Signer{},
	}
	patches := gomonkey.ApplyFunc(bce.GetClientWithSts, func(_ context.CsmContext, _ string, _ string, _ string) (
		*bce_sdk.BceClient, error) {
		return fakeClient, nil
	})
	defer patches.Reset()

	var client Client
	err := client.GetBlsV3Client(mockCtx, endpoint)
	assert.Nil(t, err)
	assert.Equal(t, client.Config.Endpoint, endpoint)
	assert.Equal(t, client.BceClient, fakeClient)
}

// TestGetBlsV3ClientWithAkSk 测试使用AK/SK获取BLS V3客户端
func TestGetBlsV3ClientWithAkSk(t *testing.T) {

	endpoint := "bls.baidubce.com"
	fakeClient = &bce_sdk.BceClient{
		Config: &bce_sdk.BceClientConfiguration{
			Endpoint: endpoint,
		},
		Signer: &auth.BceV1Signer{},
	}
	patches := gomonkey.ApplyFunc(bce.GetClientWithAkSk, func(_ context.CsmContext, _ string, _ string, _ string) (
		*bce_sdk.BceClient, error) {
		return fakeClient, nil
	})
	defer patches.Reset()

	var client Client
	err := client.GetBlsV3ClientWithAkSk(mockCtx, "fakeak", "fakesk", endpoint)
	assert.Nil(t, err)
	assert.Equal(t, client.Config.Endpoint, endpoint)
	assert.Equal(t, client.BceClient, fakeClient)
}
