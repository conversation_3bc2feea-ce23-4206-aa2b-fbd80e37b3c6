package blsv3

import (
	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/http"
)

// GetBlsTask 是Client类型的方法，用于获取指定taskId的BLS任务详情
// 参数taskId为待获取详情的BLS任务ID
// 返回值为*BlsTaskDetailResponse类型指针和error类型，分别表示BLS任务详情和错误信息
func (c *Client) GetBlsTask(taskId string) (*BlsTaskDetailResponse, error) {
	res := &BlsTaskDetailResponse{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.GET).
		WithURL(GetBlsGetTaskDetailURL(taskId)).
		WithResult(res).
		Do()
	if err != nil {
		return nil, err
	}
	return res, nil
}

// GetBlsTaskInstance 是Client结构体的一个方法，用于获取指定taskId对应的BLS任务实例，与GetBlsTask不同的地方在于：
// GetBlsTask主要用于查看配置情况，而GetBlsTaskInstance主要用于查看运行情况
// 参数taskId：任务ID
// 返回值1：*TaskInstanceResponseParameters，表示BLS任务实例的响应参数
// 返回值2：error，表示获取BLS任务实例时出现的错误，如果没有错误则为nil
func (c *Client) GetBlsTaskInstance(taskId string) (*TaskInstanceResponseParameters, error) {
	res := &TaskInstanceResponseParameters{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.GET).
		WithURL(GetBlsGetTaskInstanceURL(taskId)).
		WithResult(res).
		Do()
	if err != nil {
		return nil, err
	}
	return res, nil
}

// PostBlsTaskAction 向指定的Block Live Service任务发送指定的动作
// 参数taskId: Block Live Service任务的ID
// 参数action: 需要执行的动作
// 返回值err: 如果请求成功，返回nil；否则返回错误信息
func (c *Client) PostBlsTaskAction(taskId, action string) error {
	err := bce.NewRequestBuilder(c).
		WithMethod(http.POST).
		WithURL(GetBlsPostTaskActionURL(taskId)).
		WithQueryParamFilter(action, " ").
		Do()
	return err
}

// CreateTask 方法在Client类型上创建并发送一个创建任务的请求，并返回新创建的任务的ID和可能发生的错误。
// 参数body是一个TaskCreationRequestBody类型的值，它包含了要创建的任务的信息。
// 返回的TaskID类型的指针指向新创建的任务的ID，error类型表示在创建任务过程中可能发生的错误。
func (c *Client) CreateTask(body TaskCreationRequestBody) (*TaskID, error) {
	res := &TaskID{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.POST).
		WithURL(GetBlsCreateTaskActionURL()).
		WithBody(body).
		WithResult(res).
		Do()
	if err != nil {
		return res, err
	}
	return res, nil
}

// PostBlsTaskActionBatch 是 Client 类型的一个方法，用于向 Baidu Block Storage 发送批量任务操作请求
//
// 参数：
// tasks: 需要执行操作的任务ID列表
// action: 执行的任务操作，如 "cancel" 表示取消任务
//
// 返回值：
// error: 如果请求成功，返回 nil；否则返回错误信息
func (c *Client) PostBlsTaskActionBatch(tasks TaskIDs, action string) error {
	err := bce.NewRequestBuilder(c).
		WithMethod(http.POST).
		WithURL(GetBlsPostTaskActionPatchURL()).
		WithBody(tasks).
		WithQueryParamFilter(action, " ").
		Do()
	return err
}

// GetGroupDetailByClusterUUID 根据集群UUID获取组详情
// 参数:
//
//	name string - 组名，用于查询
//
// 返回值:
//
//	*GroupsPage pointer to GroupsPage - 包含组详情的结构体指针
//	  error pointer to error - 错误信息，如果请求成功则为nil
func (c *Client) GetGroupDetailByClusterUUID(name string) (*GroupsPage, error) {
	url := GetGroupDetailByClusterUUIDURL
	res := &GroupsPage{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.GET).
		WithURL(url).
		WithQueryParam("groupNamePattern", name).
		WithResult(res).
		Do()
	if err != nil {
		return nil, err
	}
	return res, nil
}

// GetTaskInstancesByGroupID 根据组ID获取任务实例列表，返回值为*TaskInstancePage和error类型
func (c *Client) GetTaskInstancesByGroupID(groupId string) (*TaskInstancePage, error) {
	url := GetTaskInstanceByGroupIDURL(groupId)
	res := &TaskInstancePage{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.GET).
		WithURL(url).
		WithResult(res).
		Do()

	if err != nil {
		return nil, err
	}
	return res, nil
}

// AddTaskInstances 添加任务实例
//
// 参数:
//
//	TaskId string - 任务ID
//	taskInstances TaskInstanceNames - 任务实例名称列表
//
// 返回值:
//
//	error - 错误信息，无错误时为nil
func (c *Client) AddTaskInstances(TaskId string, taskInstances TaskInstanceNames) error {
	url := GetAddTaskInstancesURL(TaskId)
	err := bce.NewRequestBuilder(c).
		WithMethod(http.POST).
		WithURL(url).
		WithBody(taskInstances).
		Do()
	return err
}
