// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	blsv3 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blsv3"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// AddTaskInstances mocks base method.
func (m *MockServiceInterface) AddTaskInstances(TaskId string, taskInstances blsv3.TaskInstanceNames) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTaskInstances", TaskId, taskInstances)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddTaskInstances indicates an expected call of AddTaskInstances.
func (mr *MockServiceInterfaceMockRecorder) AddTaskInstances(TaskId, taskInstances interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTaskInstances", reflect.TypeOf((*MockServiceInterface)(nil).AddTaskInstances), TaskId, taskInstances)
}

// CreateTask mocks base method.
func (m *MockServiceInterface) CreateTask(body blsv3.TaskCreationRequestBody) (*blsv3.TaskID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTask", body)
	ret0, _ := ret[0].(*blsv3.TaskID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTask indicates an expected call of CreateTask.
func (mr *MockServiceInterfaceMockRecorder) CreateTask(body interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTask", reflect.TypeOf((*MockServiceInterface)(nil).CreateTask), body)
}

// GetBlsTask mocks base method.
func (m *MockServiceInterface) GetBlsTask(taskId string) (*blsv3.BlsTaskDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlsTask", taskId)
	ret0, _ := ret[0].(*blsv3.BlsTaskDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlsTask indicates an expected call of GetBlsTask.
func (mr *MockServiceInterfaceMockRecorder) GetBlsTask(taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlsTask", reflect.TypeOf((*MockServiceInterface)(nil).GetBlsTask), taskId)
}

// GetBlsTaskInstance mocks base method.
func (m *MockServiceInterface) GetBlsTaskInstance(taskId string) (*blsv3.TaskInstanceResponseParameters, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlsTaskInstance", taskId)
	ret0, _ := ret[0].(*blsv3.TaskInstanceResponseParameters)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlsTaskInstance indicates an expected call of GetBlsTaskInstance.
func (mr *MockServiceInterfaceMockRecorder) GetBlsTaskInstance(taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlsTaskInstance", reflect.TypeOf((*MockServiceInterface)(nil).GetBlsTaskInstance), taskId)
}

// GetBlsV3Client mocks base method.
func (m *MockServiceInterface) GetBlsV3Client(ctx context.CsmContext, endpoint string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlsV3Client", ctx, endpoint)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBlsV3Client indicates an expected call of GetBlsV3Client.
func (mr *MockServiceInterfaceMockRecorder) GetBlsV3Client(ctx, endpoint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlsV3Client", reflect.TypeOf((*MockServiceInterface)(nil).GetBlsV3Client), ctx, endpoint)
}

// GetBlsV3ClientWithAkSk mocks base method.
func (m *MockServiceInterface) GetBlsV3ClientWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlsV3ClientWithAkSk", ctx, ak, sk, endpoint)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBlsV3ClientWithAkSk indicates an expected call of GetBlsV3ClientWithAkSk.
func (mr *MockServiceInterfaceMockRecorder) GetBlsV3ClientWithAkSk(ctx, ak, sk, endpoint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlsV3ClientWithAkSk", reflect.TypeOf((*MockServiceInterface)(nil).GetBlsV3ClientWithAkSk), ctx, ak, sk, endpoint)
}

// GetGroupDetailByClusterUUID mocks base method.
func (m *MockServiceInterface) GetGroupDetailByClusterUUID(name string) (*blsv3.GroupsPage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupDetailByClusterUUID", name)
	ret0, _ := ret[0].(*blsv3.GroupsPage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupDetailByClusterUUID indicates an expected call of GetGroupDetailByClusterUUID.
func (mr *MockServiceInterfaceMockRecorder) GetGroupDetailByClusterUUID(name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupDetailByClusterUUID", reflect.TypeOf((*MockServiceInterface)(nil).GetGroupDetailByClusterUUID), name)
}

// GetTaskInstancesByGroupID mocks base method.
func (m *MockServiceInterface) GetTaskInstancesByGroupID(groupId string) (*blsv3.TaskInstancePage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskInstancesByGroupID", groupId)
	ret0, _ := ret[0].(*blsv3.TaskInstancePage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskInstancesByGroupID indicates an expected call of GetTaskInstancesByGroupID.
func (mr *MockServiceInterfaceMockRecorder) GetTaskInstancesByGroupID(groupId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskInstancesByGroupID", reflect.TypeOf((*MockServiceInterface)(nil).GetTaskInstancesByGroupID), groupId)
}

// PostBlsTaskAction mocks base method.
func (m *MockServiceInterface) PostBlsTaskAction(taskId, action string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PostBlsTaskAction", taskId, action)
	ret0, _ := ret[0].(error)
	return ret0
}

// PostBlsTaskAction indicates an expected call of PostBlsTaskAction.
func (mr *MockServiceInterfaceMockRecorder) PostBlsTaskAction(taskId, action interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostBlsTaskAction", reflect.TypeOf((*MockServiceInterface)(nil).PostBlsTaskAction), taskId, action)
}

// PostBlsTaskActionBatch mocks base method.
func (m *MockServiceInterface) PostBlsTaskActionBatch(tasks blsv3.TaskIDs, action string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PostBlsTaskActionBatch", tasks, action)
	ret0, _ := ret[0].(error)
	return ret0
}

// PostBlsTaskActionBatch indicates an expected call of PostBlsTaskActionBatch.
func (mr *MockServiceInterfaceMockRecorder) PostBlsTaskActionBatch(tasks, action interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostBlsTaskActionBatch", reflect.TypeOf((*MockServiceInterface)(nil).PostBlsTaskActionBatch), tasks, action)
}
