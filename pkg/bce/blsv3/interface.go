package blsv3

import "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"

type ServiceInterface interface {
	GetBlsV3Client(ctx context.CsmContext, endpoint string) error
	GetBlsV3ClientWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) error
	GetBlsTask(taskId string) (*BlsTaskDetailResponse, error)
	GetBlsTaskInstance(taskId string) (*TaskInstanceResponseParameters, error)
	PostBlsTaskAction(taskId, action string) error
	CreateTask(body TaskCreationRequestBody) (*TaskID, error)
	PostBlsTaskActionBatch(tasks TaskIDs, action string) error
	GetGroupDetailByClusterUUID(name string) (*GroupsPage, error)
	GetTaskInstancesByGroupID(groupId string) (*TaskInstancePage, error)
	AddTaskInstances(TaskId string, taskInstances TaskInstanceNames) error
}
