package blsv3

import (
	"fmt"

	bce_sdk "github.com/baidubce/bce-sdk-go/bce"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

const (
	URIPrefix                      = bce_sdk.URI_PREFIX + "v3"
	GetTaskDetailURL               = "/task/%s"
	GetTaskInstanceURL             = "/task/%s/instance"
	PostTaskActionURL              = "/task/%s"
	PostTaskActionPatchURL         = "/task"
	CreateTaskURL                  = "/task"
	TaskInstanceByGroupIDURL       = "/v3/instanceGroup/%s/instance"
	AddTaskInstancesUR             = "/v3/task/%s/instance"
	GetGroupDetailByClusterUUIDURL = "/v3/instanceGroup"
)

type Client struct {
	*bce_sdk.BceClient
}

// GetBlsGetTaskDetailURL 函数根据任务ID生成获取任务详情的URL
// 参数：
//
//	taskID string - 任务ID
//
// 返回值：
//
//	string - 获取任务详情的URL
func GetBlsGetTaskDetailURL(taskID string) string {
	return fmt.Sprintf(URIPrefix+GetTaskDetailURL, taskID)

}

// GetBlsGetTaskInstanceURL 根据任务ID获取任务实例URL
// 参数：
//
//	taskID string - 任务ID
//
// 返回值：
//
//	string - 任务实例URL
func GetBlsGetTaskInstanceURL(taskID string) string {
	return fmt.Sprintf(URIPrefix+GetTaskInstanceURL, taskID)
}

// GetBlsPostTaskActionURL 根据任务ID和动作生成BLS POST任务动作URL
//
// 参数：
// taskID: 任务ID
// action: 动作
//
// 返回值：
// 返回生成的BLS POST任务动作URL
func GetBlsPostTaskActionURL(action string) string {
	return fmt.Sprintf(URIPrefix+PostTaskActionURL, action)
}

// 用于批处理
// GetBlsPostTaskActionPatchURL 函数用于生成BLS PostTaskAction的Patch请求URL
// 参数action为string类型，表示要执行的操作
// 返回值为string类型，表示生成的Patch请求URL
func GetBlsPostTaskActionPatchURL() string {
	return fmt.Sprintf(URIPrefix + PostTaskActionPatchURL)
}

// GetBlsCreateTaskActionURL 函数返回一个字符串，该字符串为BLS创建任务操作的URL。
// 它使用URIPrefix和CreateTaskURL两个常量来格式化字符串。
func GetBlsCreateTaskActionURL() string {
	return fmt.Sprintf(URIPrefix + CreateTaskURL)
}

// GetBlsV3Client 函数从Client结构体中获取BlsV3客户端
//
// 参数：
// ctx: CsmContext上下文对象
// endpoint: BlsV3服务的访问地址
//
// 返回值：
// error: 如果获取BlsV3客户端失败，则返回错误信息；否则返回nil
func (c *Client) GetBlsV3Client(ctx context.CsmContext, endpoint string) error {
	bceClient, err := bce.GetDefaultClient(ctx, endpoint)
	if err != nil {
		return err
	}
	c.BceClient = bceClient
	return nil
}

// GetBlsV3ClientWithAkSk 函数通过Access Key ID、Access Key Secret和Endpoint生成BCE客户端实例，并赋值给Client结构体中的BceClient字段
// 参数：
//
//	ctx：CsmContext上下文对象
//	ak：Access Key ID
//	sk：Access Key Secret
//	endpoint：Endpoint地址
//
// 返回值：
//
//	error：如果生成BCE客户端实例失败，则返回错误信息；否则返回nil
func (c *Client) GetBlsV3ClientWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) error {
	bceClient, err := bce.GetClientWithAkSk(ctx, ak, sk, endpoint)
	if err != nil {
		return err
	}
	c.BceClient = bceClient
	return nil
}

// GetTaskInstanceByGroupIDURL 函数通过给定的groupId生成TaskInstanceByGroupIDURL字符串并返回
// 参数groupId为string类型，表示任务组ID
// 返回值类型为string，表示生成的TaskInstanceByGroupIDURL字符串
func GetTaskInstanceByGroupIDURL(groupId string) string {
	return fmt.Sprintf(TaskInstanceByGroupIDURL, groupId)
}

// GetAddTaskInstancesURL 函数用于生成添加任务实例的URL
// 参数TaskId为任务ID
// 返回值是字符串类型的URL
func GetAddTaskInstancesURL(TaskId string) string {
	return fmt.Sprintf(AddTaskInstancesUR, TaskId)
}
