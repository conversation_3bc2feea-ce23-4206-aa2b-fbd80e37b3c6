package bce

import (
	"encoding/json"
	"os"
	"path/filepath"
	"runtime"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/baidubce/bce-sdk-go/auth"
	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/util/log"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam/sts_credential"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	FAKE_CLIENT *bce.BceClient
)

// For security reason, ak/sk should not hard write here.
type Conf struct {
	AK       string
	SK       string
	Endpoint string
}

func init() {
	_, f, _, _ := runtime.Caller(0)
	conf := filepath.Join(filepath.Dir(f), "config.json")
	fp, err := os.Open(conf)
	if err != nil {
		log.Fatal("config json file of ak/sk not given:", conf)
		os.Exit(1)
	}
	decoder := json.NewDecoder(fp)
	confObj := &Conf{}
	err = decoder.Decode(confObj)
	if err != nil {
		log.Fatal("config json file not correct:", conf)
		os.Exit(1)
	}

	FAKE_CLIENT = &bce.BceClient{
		Config: &bce.BceClientConfiguration{
			Endpoint: "sample.baidu-int.com",
		},
		Signer: &auth.BceV1Signer{},
	}
	log.SetLogLevel(log.WARN)
}

func TestGetDefaultClient(t *testing.T) {
	type FakeResults struct {
		credential *sts_credential.StsCredential
	}

	type Args struct {
		endpoint      string
		localMode     bool
		ak            string
		sk            string
		roleName      string
		clientProfile string
	}

	testCases := []struct {
		name           string
		args           *Args
		expected       error
		expectedErrMsg string
		fr             *FakeResults
	}{
		{
			name: "local mode",
			args: &Args{
				endpoint:  "",
				localMode: true,
				ak:        "test-ak",
				sk:        "test-sk",
			},
			expected:       nil,
			expectedErrMsg: "",
			fr: &FakeResults{
				credential: &sts_credential.StsCredential{},
			},
		},
		{
			name: "sts mode",
			args: &Args{
				endpoint:      "test-endpoint",
				localMode:     false,
				ak:            "test-ak",
				sk:            "test-sk",
				roleName:      "test-role",
				clientProfile: "test-profile",
			},
			expected:       nil,
			expectedErrMsg: "",
			fr: &FakeResults{
				credential: &sts_credential.StsCredential{
					AccessKeyId:     "temp-ak",
					AccessKeySecret: "temp-sk",
					SessionToken:    "temp-st",
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx, _ := csmContext.NewCsmContextMock()

			viper.Set(LocalMode, tc.args.localMode)
			viper.Set(LocalAccessKey, tc.args.ak)
			viper.Set(LocalSecretKey, tc.args.sk)
			viper.Set(BceServiceRole, tc.args.roleName)
			viper.Set(BceIamProfile, tc.args.clientProfile)

			patches := gomonkey.ApplyFunc(iam.GetAssumeRoleCredential, func(_ csmContext.CsmContext, _ string, _ string, _ sts_credential.Cache) (
				*sts_credential.StsCredential, error) {
				return tc.fr.credential, nil
			})
			defer patches.Reset()

			client, err := GetDefaultClient(ctx, tc.args.endpoint)
			if err == nil {
				assert.Equal(t, client.GetBceClientConfig().Endpoint, tc.args.endpoint)
				if tc.args.localMode {
					assert.Equal(t, client.Config.Credentials.AccessKeyId, tc.args.ak)
				} else {
					assert.Equal(t, client.Config.Credentials.AccessKeyId, tc.fr.credential.AccessKeyId)
				}
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}
