// tag.go - the TAG APIs definition supported by the CSM service

package tag

import (
	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/http"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/validate"
)

// BindResource - bind tags to a resource
//
// PARAMS:
//   - args: parameters to bind resource
//
// RETURNS:
//   - error: nil if ok otherwise the specific error
func (c *Client) BindResource(args *BindResourceArgs) error {
	err := validate.Validator.Struct(args)
	if err != nil {
		return err
	}

	return bce.NewRequestBuilder(c).
		WithMethod(http.POST).
		WithURL(getBindResourceUri()).
		WithBody(args).
		Do()
}

// UnBindResource - unbind tags from a resource
//
// PARAMS:
//   - args: parameters to unbind resource
//
// RETURNS:
//   - error: nil if ok otherwise the specific error
func (c *Client) UnBindResource(args *BindResourceArgs) error {
	err := validate.Validator.Struct(args)
	if err != nil {
		return err
	}

	return bce.NewRequestBuilder(c).
		WithMethod(http.PUT).
		WithURL(getBindResourceUri()).
		WithBody(args).
		Do()
}
