// model.go - definitions of the request arguments and results data structure model

package tag

type ServiceType string

const (
	ServiceTypeBLB ServiceType = "BLB"
)

type BindResourceArgs struct {
	TagKey      string      `json:"tagKey" validate:"required"`
	TagValue    string      `json:"tagValue" validate:"required"`
	ServiceType ServiceType `json:"serviceType" validate:"required"`
	Resources   []Resource  `json:"resources" validate:"required"`
}

type Resource struct {
	ResourceId  string      `json:"resourceId" validate:"required"`
	ServiceType ServiceType `json:"serviceType" validate:"required"`
	Region      string      `json:"region" validate:"required"`
}
