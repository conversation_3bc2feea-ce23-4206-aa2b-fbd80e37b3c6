// client.go - define the client for Tag Management

// Package tag defines the TAG services of BCE. The supported APIs are all defined in sub-package
package tag

import (
	bce_sdk "github.com/baidubce/bce-sdk-go/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

const (
	DEFAULT_SERVICE_DOMAIN = "tag.baidubce.com"
	URI_PREFIX             = bce_sdk.URI_PREFIX + "v1"
	REQUEST_TAG_URL        = "/tag"

	RESOURCE_URL = "/resource"
)

// Client of TAG service is a kind of BceClient, so derived from BceClient
type Client struct {
	*bce_sdk.BceClient
}

func getBindResourceUri() string {
	return URI_PREFIX + RESOURCE_URL
}

func GetClient(ctx context.CsmContext, endpoint, iamProfile, roleName string) (tagClient *Client, err error) {
	bceClient, err := bce.GetClient(ctx, iamProfile, roleName, endpoint)
	if err != nil {
		return nil, err
	}
	return &Client{bceClient}, nil
}

func GetDefaultClient(ctx context.CsmContext, endpoint string) (tagClient *Client, err error) {
	bceClient, err := bce.GetDefaultClient(ctx, endpoint)
	if err != nil {
		return nil, err
	}
	return &Client{bceClient}, nil
}
