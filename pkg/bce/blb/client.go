package blb

import (
	"github.com/baidubce/bce-sdk-go/services/appblb"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

func NewClient(ctx context.CsmContext, endpoint, iamProfile, roleName string) (appBlbClient *appblb.Client, err error) {
	bceClient, err := bce.GetClient(ctx, iamProfile, roleName, endpoint)
	if err != nil {
		return nil, err
	}
	client := &appblb.Client{BceClient: bceClient}
	return client, nil
}
