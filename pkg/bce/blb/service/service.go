// service.go - the service APIs definition supported by the CSM service

package service

import (
	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/http"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/validate"
)

// CreateBlbService 创建服务发布点
func (c *Client) CreateBlbService(ctx context.CsmContext, args *CreateBlbServiceArgs) (*CreateBlbServiceResult, error) {
	result := &CreateBlbServiceResult{}

	err := validate.Validator.Struct(args)
	if err != nil {
		return result, err
	}

	request := bce.NewRequestBuilder(c).
		WithMethod(http.POST).
		WithURL(getBlbServiceURI()).
		WithQueryParamFilter("clientToken", args.ClientToken).
		WithBody(args).
		WithResult(result)

	err = request.Do()

	return result, err
}

// DeleteBlbService 删除服务发布点
func (c *Client) DeleteBlbService(ctx context.CsmContext, args *DeleteBlbServiceArgs) error {

	err := validate.Validator.Struct(args)
	if err != nil {
		return err
	}

	return bce.NewRequestBuilder(c).
		WithMethod(http.DELETE).
		WithURL(getBlbServiceDeleteURI(args.Service)).
		WithQueryParamFilter("clientToken", args.ClientToken).
		Do()
}

// GetBlbService 查询服务发布点详情
func (c *Client) GetBlbService(ctx context.CsmContext, args *GetBlbServiceArgs) (*GetBlbServiceResult, error) {
	result := &GetBlbServiceResult{}
	err := validate.Validator.Struct(args)
	if err != nil {
		return nil, err
	}

	request := bce.NewRequestBuilder(c).
		WithMethod(http.GET).
		WithURL(getBlbServiceDeleteURI(args.Service)).
		WithQueryParamFilter("clientToken", args.ClientToken).
		WithResult(result)

	err = request.Do()
	return result, err
}
