package service

import (
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/baidubce/bce-sdk-go/auth"
	bce_sdk "github.com/baidubce/bce-sdk-go/bce"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/util"
	ctx "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	fakeClient *bce_sdk.BceClient
	mockCtx, _ = ctx.NewCsmContextMock()
)

func init() {
	fakeClient = &bce_sdk.BceClient{
		Config: &bce_sdk.BceClientConfiguration{
			Endpoint: "sample.baidu-int.com",
		},
		Signer: &auth.BceV1Signer{},
	}
}

func TestGetBlbService(t *testing.T) {
	args := &GetBlbServiceArgs{
		Service:     "xxx",
		ClientToken: util.GetClientToken(),
	}
	patches := gomonkey.ApplyFunc(bce_sdk.NewRequestBuilder, func(_ bce_sdk.Client) *bce_sdk.RequestBuilder {
		return &bce_sdk.RequestBuilder{}
	})
	defer patches.Reset()

	var rb *bce_sdk.RequestBuilder
	patches.ApplyMethod(rb, "Do", func(_ *bce_sdk.RequestBuilder) error {
		return nil
	})

	c := &Client{fakeClient}
	_, err := c.GetBlbService(mockCtx, args)
	assert.Nil(t, err)
}
