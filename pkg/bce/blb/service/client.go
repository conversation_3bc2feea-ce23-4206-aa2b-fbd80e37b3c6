package service

import (
	bce_sdk "github.com/baidubce/bce-sdk-go/bce"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

const (
	blbServiceURI = "/v1/service"
)

func getBlbServiceURI() string {
	return blbServiceURI
}

func getBlbServiceDeleteURI(id string) string {
	return blbServiceURI + "/" + id
}

// Client of VPC Service is a kind of BceClient, so derived from BceClient
type Client struct {
	*bce_sdk.BceClient
}

func GetClient(ctx context.CsmContext, endpoint string) (client *Client, err error) {
	bceClient, err := bce.GetDefaultClient(ctx, endpoint)
	if err != nil {
		return nil, err
	}
	return &Client{bceClient}, nil
}

// GetClientWithAkSk 通过 ak、sk 初始化 client
func GetClientWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) (client *Client, err error) {
	bceClient, err := bce.GetClientWithAkSk(ctx, ak, sk, endpoint)
	if err != nil {
		return nil, err
	}
	return &Client{bceClient}, nil
}
