package service

type CreateBlbServiceArgs struct {
	// 幂等性Token，是一个长度不超过64位的ASCII字符串
	ClientToken string `json:"-"`
	// 服务发布点的名称，大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65
	Name string `json:"name" validate:"required"`
	// 服务发布点的描述，最大支持200字符
	Description string `json:"description"`
	// 对应服务名称,大小写字母、数字长度1-65
	ServiceName string `json:"serviceName" validate:"required"`
	// 绑定实例id，当前只支持绑定blb
	InstanceID string `json:"instanceId" validate:"required"`
	// 用户授权列表，默认拒绝所有
	AuthList []Auth `json:"authList"`
}

type CreateBlbServiceResult struct {
	// 服务发布点的域名，使用此域名绑定服务网卡
	Service string `json:"service"`
}

type Auth struct {
	// 用户id, 所有人使用"*"
	UID string `json:"uid"`
	// 鉴权方式，取值: allow/deny, 分别表示允许/拒绝
	Auth string `json:"auth"`
}

func NewDefaultAuth() []Auth {
	return []Auth{
		{
			UID:  "*",
			Auth: "allow",
		},
	}
}

type DeleteBlbServiceArgs struct {
	Service     string `json:"service" validate:"required"`
	ClientToken string `json:"-"`
}

type GetBlbServiceArgs struct {
	Service     string `json:"service" validate:"required"`
	ClientToken string `json:"-"`
}

// GetBlbServiceResult 服务发布点详情
type GetBlbServiceResult struct {
	// 服务发布点的 ID
	ServiceId string `json:"serviceId"`
	//  服务发布点的名称
	Name string `json:"name"`
	//  描述
	Description string `json:"description"`
	// 服务名称
	ServiceName string `json:"serviceName"`
	// 绑定服务类型，目前仅支持绑定BLB实例
	BindType string `json:"bindType"`
	// 绑定实例ID
	InstanceId string `json:"instanceId"`
	// 发布点状态，取值范围 inService/available/unavailable/dead/free，分别表示：服务中/可用/不可用/故障/未绑定
	Status string `json:"status"`
	// 服务发布点唯一对应域名
	Service string `json:"service"`
	// 创建时间
	CreateTime string `json:"createTime"`
	//  关联的服务网卡数量
	EndpointCount int `json:"endpointCount"`
}
