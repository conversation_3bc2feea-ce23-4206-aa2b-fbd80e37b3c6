package bls

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
)

func TestGenerateTaskCreationRequestBody(t *testing.T) {
	testLogConf := &meta.LogConf{
		Enabled:     true,
		Type:        "BLS",
		LogFile:     "a",
		Namespace:   "ns-1",
		GatewayUUID: "gw-1",
		AccountID:   "acc-1",
	}
	expectReuqestBody := TaskCreationRequestBody{
		Name: "cross_account_gw-1",
		Config: ConfigBls{
			SrcConfig: SrcConfig{
				SrcType:        "container",
				LogType:        "stdout",
				SrcDir:         "/var/log",
				MatchedPattern: "^.*$",
				IgnorePattern:  "",
				LabelWhite: []Label{
					{
						Key:   GatewayPodNameKey,
						Value: GatewayPodNameValue,
					},
					{
						Key:   GatewayPodNamespaceKey,
						Value: "ns-1",
					},
				},
				ProcessType:  "none",
				LogTime:      "system",
				UseMultiline: false,
				TTL:          BlsTaskDefaultTTL,
			},
			DestConfig: DestBlsConfig{
				DestType:  "BLS",
				LogStore:  "a",
				AccountID: "acc-1",
				RateLimit: 1,
			},
		},
	}
	assert.Equal(t, expectReuqestBody, GenerateTaskCreationRequestBody(testLogConf))
}
