// Package bls implements SDK like BLS APIs
package bls

import (
	"fmt"

	bce_sdk "github.com/baidubce/bce-sdk-go/bce"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

const (
	URIPrefix     = bce_sdk.URI_PREFIX // TODO:待新版本API上线 + "v3"
	CreateTaskURL = "task"
	UpdateTaskURL = "task/%s"
	DeleteTaskURL = "task/%s/stop"
	StartTaskURL  = "task/%s/start"
	PauseTaskURL  = "task/%s/pause"
	DetailTaskURL = "task/%s"
)

// Client of BLS Service is a kind of BceClient, so derived from BceClient
type Client struct {
	*bce_sdk.BceClient
}

func GetBlsCreateTaskURI() string {
	return fmt.Sprintf(URIPrefix + CreateTaskURL)
}

func GetBlsUpdateTaskURI(taskID string) string {
	return fmt.Sprintf(URIPrefix+UpdateTaskURL, taskID)
}

func GetBlsDeleteTaskURI(taskID string) string {
	return fmt.Sprintf(URIPrefix+DeleteTaskURL, taskID)
}

func GetBlsStartTaskURI(taskID string) string {
	return fmt.Sprintf(URIPrefix+StartTaskURL, taskID)
}

func GetBlsPauseTaskURI(taskID string) string {
	return fmt.Sprintf(URIPrefix+PauseTaskURL, taskID)
}

func GetBlsDetailTaskURI(taskID string) string {
	return fmt.Sprintf(URIPrefix+DetailTaskURL, taskID)
}

func (c *Client) GetClient(ctx context.CsmContext, endpoint string) error {
	bceClient, err := bce.GetDefaultClient(ctx, endpoint)
	if err != nil {
		return err
	}
	c.BceClient = bceClient
	return nil
}

// GetClientWithAkSk 通过 ak、sk 初始化 client
func (c *Client) GetClientWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) error {
	bceClient, err := bce.GetClientWithAkSk(ctx, ak, sk, endpoint)
	if err != nil {
		return err
	}
	c.BceClient = bceClient
	return nil
}
