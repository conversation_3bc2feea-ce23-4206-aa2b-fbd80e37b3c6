package bls

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
)

const (
	GatewayPodNameKey      = "io.kubernetes.pod.name"
	GatewayPodNameValue    = "istio-ingressgateway-.*"
	GatewayPodNamespaceKey = "io.kubernetes.pod.namespace"

	BlsTaskDefaultTTL = 3
)

type TaskCreationResponse struct {
	TaskID string `json:"taskID"`
}

type TaskDetailResponse struct {
	Task TaskDetail `json:"task"`
}

type TaskDetail struct {
	Status TaskStatus `json:"status"`
	Config ConfigBls  `json:"config"`
}

type TaskStatus struct {
	ID               string `json:"id"`
	Name             string `json:"name"`
	Type             string `json:"type"`
	CreationDateTime string `json:"creationDateTime"`
	Status           string `json:"status"`
	TaskVersion      string `json:"taskVersion"`
	Tags             []Tag  `json:"tags"`
}

type TaskCreationRequestBody struct {
	Name   string    `json:"name"`
	Config ConfigBls `json:"config"`
	Hosts  []Host    `json:"hosts"` // TODO: 待新版本API上线后改为Instances []Instance `json:"instances"`
	Tags   []Tag     `json:"tags"`
}

type ConfigBls struct {
	SrcConfig  SrcConfig     `json:"srcConfig"`
	DestConfig DestBlsConfig `json:"destConfig"`
}

type SrcConfig struct {
	SrcType        string        `json:"srcType"`
	LogType        string        `json:"logType"`
	SrcDir         string        `json:"srcDir"`
	MatchedPattern string        `json:"matchedPattern"`
	IgnorePattern  string        `json:"ignorePattern"`
	TimeFormat     string        `json:"timeFormat"`
	TTL            int           `json:"ttl"`
	UseMultiline   bool          `json:"useMultiline"`
	MultilineRegex string        `json:"multilineRegex"`
	RecursiveDir   bool          `json:"recursiveDir"`
	ProcessType    string        `json:"processType"`
	ProcessConfig  ProcessConfig `json:"processConfig"`
	LabelWhite     []Label       `json:"labelWhite"`
	LogTime        string        `json:"logTime"`
	TimestampKey   string        `json:"timestampKey"`
	DateFormat     string        `json:"dateFormat"`
	FilterExpr     string        `json:"filterExpr"`
}

type ProcessConfig struct {
	Regex            string `json:"regex"`
	Separator        string `json:"separator"`
	Quote            string `json:"quote"`
	SampleLog        string `json:"sampleLog"`
	Keys             string `json:"keys"`
	DataType         string `json:"dataType"`
	DiscardOnFailure bool   `json:"discardOnFailure"`
	KeepOriginal     bool   `json:"keepOriginal"`
}

// DestConfig BLS 目的端配置
type DestBlsConfig struct {
	DestType  string `json:"destType"`
	LogStore  string `json:"logStore"`
	AccountID string `json:"accountID"`
	RateLimit int    `json:"rateLimit"`
}

type Instance struct {
	InstanceID string `json:"instanceID"`
}

// TODO: 带新版本API上线后改为上面的Instance
type Host struct {
	HostID string `json:"hostId"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

type Label struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

func GenerateTaskCreationRequestBody(logConf *meta.LogConf) TaskCreationRequestBody {
	return TaskCreationRequestBody{
		Name: "cross_account_" + logConf.GatewayUUID,
		Config: ConfigBls{
			SrcConfig: SrcConfig{
				SrcType:        "container",
				LogType:        "stdout",
				SrcDir:         "/var/log",
				MatchedPattern: "^.*$",
				IgnorePattern:  "",
				LabelWhite: []Label{
					{
						Key:   GatewayPodNameKey,
						Value: GatewayPodNameValue,
					},
					{
						Key:   GatewayPodNamespaceKey,
						Value: logConf.Namespace,
					},
				},
				ProcessType:  "none",
				LogTime:      "system",
				UseMultiline: false,
				TTL:          BlsTaskDefaultTTL,
			},
			DestConfig: DestBlsConfig{
				DestType:  logConf.Type,
				LogStore:  logConf.LogFile,
				AccountID: logConf.AccountID,
				RateLimit: 1,
			},
		},
	}
}
