// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bls "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/bls"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// CreateHostingBlsTask mocks base method.
func (m *MockServiceInterface) CreateHostingBlsTask(logConf *meta.LogConf) (*bls.TaskCreationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateHostingBlsTask", logConf)
	ret0, _ := ret[0].(*bls.TaskCreationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateHostingBlsTask indicates an expected call of CreateHostingBlsTask.
func (mr *MockServiceInterfaceMockRecorder) CreateHostingBlsTask(logConf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHostingBlsTask", reflect.TypeOf((*MockServiceInterface)(nil).CreateHostingBlsTask), logConf)
}

// DeleteHostingBlsTask mocks base method.
func (m *MockServiceInterface) DeleteHostingBlsTask(taskID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteHostingBlsTask", taskID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteHostingBlsTask indicates an expected call of DeleteHostingBlsTask.
func (mr *MockServiceInterfaceMockRecorder) DeleteHostingBlsTask(taskID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteHostingBlsTask", reflect.TypeOf((*MockServiceInterface)(nil).DeleteHostingBlsTask), taskID)
}

// GetClient mocks base method.
func (m *MockServiceInterface) GetClient(ctx context.CsmContext, endpoint string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClient", ctx, endpoint)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetClient indicates an expected call of GetClient.
func (mr *MockServiceInterfaceMockRecorder) GetClient(ctx, endpoint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClient", reflect.TypeOf((*MockServiceInterface)(nil).GetClient), ctx, endpoint)
}

// GetClientWithAkSk mocks base method.
func (m *MockServiceInterface) GetClientWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientWithAkSk", ctx, ak, sk, endpoint)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetClientWithAkSk indicates an expected call of GetClientWithAkSk.
func (mr *MockServiceInterfaceMockRecorder) GetClientWithAkSk(ctx, ak, sk, endpoint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientWithAkSk", reflect.TypeOf((*MockServiceInterface)(nil).GetClientWithAkSk), ctx, ak, sk, endpoint)
}

// GetHostingBlsTaskDetail mocks base method.
func (m *MockServiceInterface) GetHostingBlsTaskDetail(taskID string) (*bls.TaskDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHostingBlsTaskDetail", taskID)
	ret0, _ := ret[0].(*bls.TaskDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHostingBlsTaskDetail indicates an expected call of GetHostingBlsTaskDetail.
func (mr *MockServiceInterfaceMockRecorder) GetHostingBlsTaskDetail(taskID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHostingBlsTaskDetail", reflect.TypeOf((*MockServiceInterface)(nil).GetHostingBlsTaskDetail), taskID)
}

// PauseHostingBlsTask mocks base method.
func (m *MockServiceInterface) PauseHostingBlsTask(taskID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PauseHostingBlsTask", taskID)
	ret0, _ := ret[0].(error)
	return ret0
}

// PauseHostingBlsTask indicates an expected call of PauseHostingBlsTask.
func (mr *MockServiceInterfaceMockRecorder) PauseHostingBlsTask(taskID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PauseHostingBlsTask", reflect.TypeOf((*MockServiceInterface)(nil).PauseHostingBlsTask), taskID)
}

// StartHostingBlsTask mocks base method.
func (m *MockServiceInterface) StartHostingBlsTask(taskID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartHostingBlsTask", taskID)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartHostingBlsTask indicates an expected call of StartHostingBlsTask.
func (mr *MockServiceInterfaceMockRecorder) StartHostingBlsTask(taskID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartHostingBlsTask", reflect.TypeOf((*MockServiceInterface)(nil).StartHostingBlsTask), taskID)
}

// UpdateHostingBlsTask mocks base method.
func (m *MockServiceInterface) UpdateHostingBlsTask(logConf *meta.LogConf, taskID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateHostingBlsTask", logConf, taskID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateHostingBlsTask indicates an expected call of UpdateHostingBlsTask.
func (mr *MockServiceInterfaceMockRecorder) UpdateHostingBlsTask(logConf, taskID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHostingBlsTask", reflect.TypeOf((*MockServiceInterface)(nil).UpdateHostingBlsTask), logConf, taskID)
}
