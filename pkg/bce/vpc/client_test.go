package vpc

import (
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/baidubce/bce-sdk-go/auth"
	bce_sdk "github.com/baidubce/bce-sdk-go/bce"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	ctx "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	fakeClient *bce_sdk.BceClient
	mockCtx, _ = ctx.NewCsmContextMock()
)

func init() {
	fakeClient = &bce_sdk.BceClient{
		Config: &bce_sdk.BceClientConfiguration{
			Endpoint: "sample.baidu-int.com",
		},
		Signer: &auth.BceV1Signer{},
	}
}

func TestGetClient(t *testing.T) {
	patches := gomonkey.ApplyFunc(bce.GetClient, func(_ context.CsmContext, _ string, _ string, _ string) (
		*bce_sdk.BceClient, error) {
		return fakeClient, nil
	})
	defer patches.Reset()

	client, err := GetClient(mockCtx, "test-endpoint")
	assert.Nil(t, err)
	assert.Equal(t, client.Config.Endpoint, "sample.baidu-int.com")
}
