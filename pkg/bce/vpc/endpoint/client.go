package endpoint

import (
	bceEndpoint "github.com/baidubce/bce-sdk-go/services/endpoint"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// GetClient TODO: 以后优化成单例
func GetClient(ctx context.CsmContext, endpoint string) (endpointClient *bceEndpoint.Client, err error) {
	bceClient, err := bce.GetDefaultClient(ctx, endpoint)
	if err != nil {
		return nil, err
	}
	client := &bceEndpoint.Client{BceClient: bceClient}
	return client, nil
}

func GetBceHostClient(ctx context.CsmContext, endpoint string) (endpointClient *bceEndpoint.Client, err error) {
	bceClient, err := bce.GetHostingClient(ctx, endpoint)
	if err != nil {
		return nil, err
	}
	client := &bceEndpoint.Client{BceClient: bceClient}
	return client, nil
}
