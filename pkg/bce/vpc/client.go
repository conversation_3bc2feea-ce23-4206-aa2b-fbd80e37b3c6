package vpc

import (
	"github.com/baidubce/bce-sdk-go/services/vpc"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// GetClient TODO: 以后优化成单例
func GetClient(ctx context.CsmContext, endpoint string) (vpcClient *vpc.Client, err error) {
	bceClient, err := bce.GetDefaultClient(ctx, endpoint)
	if err != nil {
		return nil, err
	}
	client := &vpc.Client{BceClient: bceClient}
	return client, nil
}
