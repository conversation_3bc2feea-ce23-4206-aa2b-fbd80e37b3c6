package esg

import (
	"github.com/baidubce/bce-sdk-go/services/esg"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// GetClient TODO: 以后优化成单例
func GetClient(ctx context.CsmContext, endpoint string) (endpointClient *esg.Client, err error) {
	bceClient, err := bce.GetDefaultClient(ctx, endpoint)
	if err != nil {
		return nil, err
	}
	client := &esg.Client{BceClient: bceClient}
	return client, nil
}
