package cce

// 目前cce提供的addon openAPI不支持使用SDK也没有标准的数据结构，需要自定义

type AddonResponse struct {
	RequestID string      `json:"requestID"`
	Items     []AddonInfo `json:"items,omitempty"` // omitempty 表示如果为空则不包含在JSON中
	Code      string      `json:"code,omitempty"`
	Message   string      `json:"message,omitempty"`
}

type AddonInfo struct {
	Meta           Meta            `json:"meta"`
	Instance       AddonInstance   `json:"instance,omitempty"` // 支持空值
	MultiInstances []AddonInstance `json:"multiInstances,omitempty"`
}

type Meta struct {
	Name              string      `json:"name"`
	Type              string      `json:"type"` // CloudNativeAI、Networking等
	LatestVersion     string      `json:"latestVersion"`
	ShortIntroduction string      `json:"shortIntroduction"`
	DefaultParams     string      `json:"defaultParams"`
	InstallInfo       InstallInfo `json:"installInfo"`
}

type AddonInstance struct {
	Name             string              `json:"name"`
	InstalledVersion string              `json:"installedVersion"`
	Params           string              `json:"params"`
	Status           AddonInstanceStatus `json:"status"`
	UninstallInfo    UninstallInfo       `json:"uninstallInfo"`
	UpgradeInfo      UpgradeInfo         `json:"upgradeInfo"`
	UpdateInfo       UpdateInfo          `json:"updateInfo"`
}

type AddonInstanceStatus struct {
	Phase   string `json:"phase"`
	Code    string `json:"code"`
	Message string `json:"message"`
	TraceID string `json:"traceID"`
}

type InstallInfo struct {
	AllowInstall bool   `json:"allowInstall"`
	Message      string `json:"message"`
}

type UninstallInfo struct {
	Message string `json:"message"`
}

type UpgradeInfo struct {
	AllowUpgrade bool   `json:"allowUpgrade"`
	NextVersion  string `json:"nextVersion"`
	Message      string `json:"message"`
}

type UpdateInfo struct {
	AllowUpdate bool   `json:"allowUpdate"`
	Message     string `json:"message"`
}
