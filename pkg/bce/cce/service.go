package cce

import (
	"fmt"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/http"
	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
)

func (c *Client) GetAdminKubeConfig(args *cce_v2.GetKubeConfigArgs) (*cce_v2.GetKubeConfigResponse, error) {
	if args == nil {
		return nil, fmt.Errorf("args is nil")
	}
	if err := cce_v2.CheckKubeConfigType(string(args.KubeConfigType)); err != nil {
		return nil, err
	}

	result := &cce_v2.GetKubeConfigResponse{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.GET).
		WithURL(getAdminKubeConfigURI(args.ClusterID, args.KubeConfigType)).
		WithResult(result).
		Do()

	return result, err
}

// GetClusterAddonByClusterId 通过集群ID获取集群插件信息
//
// 参数：
//
//	c *Client: 客户端实例指针
//	clusterID string: 集群ID
//	addons string: 插件名称，可选参数
//
// 返回值：
//
//	*AddonResponse: 插件信息结构体指针
//	error: 错误信息
func (c *Client) GetClusterAddonByClusterId(clusterID, addons string) (*AddonResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is nil")
	}

	result := &AddonResponse{}
	reqBuilder := bce.NewRequestBuilder(c).
		WithMethod(http.GET).
		WithURL(GetAddonURL(clusterID)).
		WithResult(result)

	// 根据addons的值添加查询参数
	if addons != "" {
		reqBuilder = reqBuilder.WithQueryParamFilter("addons", addons)
	}

	// 执行请求并处理错误
	err := reqBuilder.Do()
	if err != nil {
		return nil, err
	}
	return result, nil
}
