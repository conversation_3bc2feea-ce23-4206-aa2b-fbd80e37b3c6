package cce

import (
	"fmt"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/baidubce/bce-sdk-go/auth"
	bce_sdk "github.com/baidubce/bce-sdk-go/bce"
	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	ctx "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	clusterID  = "test"
	fakeClient *bce_sdk.BceClient
	mockCtx, _ = ctx.NewCsmContextMock()
)

func init() {
	fakeClient = &bce_sdk.BceClient{
		Config: &bce_sdk.BceClientConfiguration{
			Endpoint: "sample.baidu-int.com",
		},
		Signer: &auth.BceV1Signer{},
	}
}

func TestGetClient(t *testing.T) {
	patches := gomonkey.ApplyFunc(bce.GetClient, func(_ context.CsmContext, _ string, _ string, _ string) (
		*bce_sdk.BceClient, error) {
		return fakeClient, nil
	})
	defer patches.Reset()

	client, err := GetClient(mockCtx, "test-endpoint")
	assert.Nil(t, err)
	assert.Equal(t, client.Config.Endpoint, "sample.baidu-int.com")
}

func TestGetClientWithAkSk(t *testing.T) {
	patches := gomonkey.ApplyFunc(bce.GetClientWithAkSk, func(_ context.CsmContext, _ string, _ string, _ string) (
		*bce_sdk.BceClient, error) {
		return fakeClient, nil
	})
	defer patches.Reset()

	client, err := GetClientWithAkSk(mockCtx, "", "", "test-endpoint")
	assert.Nil(t, err)
	assert.Equal(t, client.Config.Endpoint, "sample.baidu-int.com")
}

func TestGetAdminKubeConfig(t *testing.T) {
	args := &cce_v2.GetKubeConfigArgs{
		ClusterID:      clusterID,
		KubeConfigType: cce_v2.KubeConfigTypeInternal,
	}
	patches := gomonkey.ApplyFunc(bce_sdk.NewRequestBuilder, func(_ bce_sdk.Client) *bce_sdk.RequestBuilder {
		return &bce_sdk.RequestBuilder{}
	})
	defer patches.Reset()

	var rb *bce_sdk.RequestBuilder
	patches.ApplyMethod(rb, "Do", func(_ *bce_sdk.RequestBuilder) error {
		return nil
	})

	client := &Client{
		BceClient: fakeClient,
	}
	_, err := client.GetAdminKubeConfig(args)
	assert.Nil(t, err)
}

func TestGetSugarClient(t *testing.T) {
	patches := gomonkey.ApplyFunc(bce.GetClientWithSts, func(_ context.CsmContext, _ string, _ string, _ string) (
		*bce_sdk.BceClient, error) {
		return fakeClient, nil
	})
	defer patches.Reset()
	client, err := GetSugarClient(mockCtx, "", "", "test-endpoint")
	assert.Nil(t, err)
	assert.Equal(t, client.Config.Endpoint, "sample.baidu-int.com")
}

// TestGetAddonURL 是一个测试函数，用于测试GetAddonURL函数
// 参数t是testing.T类型，用于记录测试结果
func TestGetAddonURL(t *testing.T) {
	clusterID := "cce-xxxxxxxxx"
	url := GetAddonURL(clusterID)
	assert.Equal(t, fmt.Sprintf("%sv2/cluster/%s/addon", URIPrefix, clusterID), url)
}

func TestGetClusterAddonByClusterId(t *testing.T) {
	// 准备测试参数
	clusterID := "test-cluster-id"
	addons := "test-addons"

	// 使用gomonkey模拟bce.NewRequestBuilder
	patches := gomonkey.ApplyFunc(bce_sdk.NewRequestBuilder, func(_ interface{}) *bce_sdk.RequestBuilder {
		return &bce_sdk.RequestBuilder{}
	})
	defer patches.Reset()

	// 模拟RequestBuilder的Do方法总是返回nil错误
	var rb *bce_sdk.RequestBuilder
	patches.ApplyMethod(rb, "Do", func(_ *bce_sdk.RequestBuilder) error {
		return nil
	})

	// 创建测试的Client实例
	client := &Client{
		BceClient: fakeClient,
	}

	// 执行测试目标函数
	_, err := client.GetClusterAddonByClusterId(clusterID, addons)

	// 验证返回的错误是否为nil（预期没有错误发生）
	assert.Nil(t, err)

	// 执行测试目标函数，此时clusterID为空字符串
	result, err := client.GetClusterAddonByClusterId("", "test-addons")

	// 验证函数是否返回了错误
	assert.NotNil(t, err)
	// 也可以使用更具体的断言来检查错误的类型或内容
	assert.EqualError(t, err, "clusterID is nil")

	// 验证函数是否如预期那样没有返回结果
	assert.Nil(t, result)
}
