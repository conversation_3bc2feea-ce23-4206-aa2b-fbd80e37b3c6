// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	gomock "github.com/golang/mock/gomock"
	cce "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/cce"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// GetAdminKubeConfig mocks base method.
func (m *MockServiceInterface) GetAdminKubeConfig(args *v2.GetKubeConfigArgs) (*v2.GetKubeConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAdminKubeConfig", args)
	ret0, _ := ret[0].(*v2.GetKubeConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAdminKubeConfig indicates an expected call of GetAdminKubeConfig.
func (mr *MockServiceInterfaceMockRecorder) GetAdminKubeConfig(args interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdminKubeConfig", reflect.TypeOf((*MockServiceInterface)(nil).GetAdminKubeConfig), args)
}

// GetClusterAddonByClusterId mocks base method.
func (m *MockServiceInterface) GetClusterAddonByClusterId(clusterID, addons string) (*cce.AddonResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterAddonByClusterId", clusterID, addons)
	ret0, _ := ret[0].(*cce.AddonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterAddonByClusterId indicates an expected call of GetClusterAddonByClusterId.
func (mr *MockServiceInterfaceMockRecorder) GetClusterAddonByClusterId(clusterID, addons interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterAddonByClusterId", reflect.TypeOf((*MockServiceInterface)(nil).GetClusterAddonByClusterId), clusterID, addons)
}
