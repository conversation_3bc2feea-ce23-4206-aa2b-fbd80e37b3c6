package cce

import (
	"fmt"

	bce_sdk "github.com/baidubce/bce-sdk-go/bce"
	cce_v1 "github.com/baidubce/bce-sdk-go/services/cce"
	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

const (
	REQUEST_ADMIN_KUBECONFIG = "/kubeconfig/%s/admin/%s"

	BceIamProfile  = "cloud.iamProfile"
	BceServiceRole = "bceServiceRole"
)
const (
	URIPrefix      = bce_sdk.URI_PREFIX
	GetCCEAddonURL = "v2/cluster/%s/addon"
)

type Client struct {
	*bce_sdk.BceClient
	CceClientV2 *cce_v2.Client
	CceClientV1 *cce_v1.Client
}

func getAdminKubeConfigURI(clusterID string, kubeConfigType cce_v2.KubeConfigType) string {
	return cce_v2.URI_PREFIX + fmt.Sprintf(REQUEST_ADMIN_KUBECONFIG, clusterID, kubeConfigType)
}

// GetClientWithAkSk 通过 ak、sk 初始化 client
func GetClientWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) (*Client, error) {
	bceClient, err := bce.GetClientWithAkSk(ctx, ak, sk, endpoint)
	if err != nil {
		return nil, err
	}
	client := &Client{
		BceClient: bceClient,
		CceClientV2: &cce_v2.Client{
			BceClient: bceClient,
		},
		CceClientV1: &cce_v1.Client{
			BceClient: bceClient,
		},
	}
	return client, nil
}

func GetClient(ctx context.CsmContext, endpoint string) (*Client, error) {
	bceClient, err := bce.GetDefaultClient(ctx, endpoint)
	if err != nil {
		return nil, err
	}

	client := &Client{
		BceClient: bceClient,
		CceClientV2: &cce_v2.Client{
			BceClient: bceClient,
		},
		CceClientV1: &cce_v1.Client{
			BceClient: bceClient,
		},
	}
	return client, nil
}

func GetSugarClient(ctx context.CsmContext, roleName, clientProfile, endpoint string) (*Client, error) {
	bceClient, err := bce.GetClientWithSts(ctx, clientProfile, roleName, endpoint)
	if err != nil {
		return nil, err
	}

	client := &Client{
		BceClient: bceClient,
		CceClientV2: &cce_v2.Client{
			BceClient: bceClient,
		},
		CceClientV1: &cce_v1.Client{
			BceClient: bceClient,
		},
	}
	return client, nil
}
func GetAddonURL(clusterID string) string {
	return fmt.Sprintf(URIPrefix+GetCCEAddonURL, clusterID)
}
