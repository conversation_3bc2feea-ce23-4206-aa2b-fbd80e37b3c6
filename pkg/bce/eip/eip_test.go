package eip

import (
	"encoding/json"
	"os"
	"path/filepath"
	"runtime"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/baidubce/bce-sdk-go/auth"
	bce_sdk "github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/util/log"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	FAKE_CLIENT *bce_sdk.BceClient
	ctx         context.CsmContext
)

// For security reason, ak/sk should not hard write here.
type Conf struct {
	AK       string
	SK       string
	Endpoint string
}

func init() {
	ctx, _ = context.NewCsmContextMock()

	_, f, _, _ := runtime.Caller(0)
	conf := filepath.Join(filepath.Dir(f), "../config.json")
	fp, err := os.Open(conf)
	if err != nil {
		log.Fatal("config json file of ak/sk not given:", conf)
		os.Exit(1)
	}
	decoder := json.NewDecoder(fp)
	confObj := &Conf{}
	err = decoder.Decode(confObj)
	if err != nil {
		log.Fatal("config json file not correct:", conf)
		os.Exit(1)
	}

	FAKE_CLIENT = &bce_sdk.BceClient{
		Config: &bce_sdk.BceClientConfiguration{
			Endpoint: "sample.baidu-int.com",
		},
		Signer: &auth.BceV1Signer{},
	}
	log.SetLogLevel(log.WARN)
}

func TestClient_GetDefaultClient(t *testing.T) {
	viper.Set(bce.LocalMode, true)
	viper.Set(bce.LocalAccessKey, "test-ak")
	viper.Set(bce.LocalSecretKey, "test-sk")
	viper.Set(bce.BceServiceRole, "test-roleName")
	viper.Set(bce.BceIamProfile, "test-iam-profile")

	client, err := GetDefaultClient(ctx, "test-endpoint")
	assert.Nil(t, err)
	assert.Equal(t, client.Config.Credentials.AccessKeyId, "test-ak")
}

func TestClient_GetClient(t *testing.T) {
	patches := gomonkey.ApplyFunc(bce.GetClient, func(_ context.CsmContext, _ string, _ string, _ string) (
		*bce_sdk.BceClient, error) {
		return FAKE_CLIENT, nil
	})
	defer patches.Reset()

	client, err := GetClient(ctx, "test-endpoint", "test-iam-profile", "test-role-name")
	assert.Nil(t, err)
	assert.Equal(t, client.Config.Endpoint, "sample.baidu-int.com")
}
