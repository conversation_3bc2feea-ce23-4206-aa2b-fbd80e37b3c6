package eip

import (
	"github.com/baidubce/bce-sdk-go/services/eip"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// TODO: 以后优化成单例
func GetClient(ctx context.CsmContext, endpoint, iamProfile, roleName string) (eipClient *eip.Client, err error) {
	bceClient, err := bce.GetClient(ctx, iamProfile, roleName, endpoint)
	if err != nil {
		return nil, err
	}
	return &eip.Client{BceClient: bceClient}, nil
}

func GetDefaultClient(ctx context.CsmContext, endpoint string) (eipClient *eip.Client, err error) {
	bceClient, err := bce.GetDefaultClient(ctx, endpoint)
	if err != nil {
		return nil, err
	}
	return &eip.Client{BceClient: bceClient}, nil
}
