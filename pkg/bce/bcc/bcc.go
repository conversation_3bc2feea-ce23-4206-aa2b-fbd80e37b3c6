package bcc

import (
	"github.com/baidubce/bce-sdk-go/services/bcc"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// GetClient TODO: 以后优化成单例
func GetClient(ctx context.CsmContext, endpoint string) (bccClient *bcc.Client, err error) {
	bceClient, err := bce.GetDefaultClient(ctx, endpoint)
	if err != nil {
		return nil, err
	}
	client := &bcc.Client{BceClient: bceClient}
	return client, nil
}
