package dao

import (
	"reflect"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type ResourceModel struct {
	dbutil.BaseModel
	ResourceID *string `json:"resourceID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	Owner      string  `json:"-" valid:"required"`
}

type BasePageListResult struct {
	PageSize   int64 `json:"pageSize,omitempty"`
	PageNo     int64 `json:"pageNo,omitempty"`
	TotalCount int64 `json:"totalCount"`
}

var (
	baseModelType     = reflect.TypeOf(dbutil.BaseModel{})
	resourceModelType = reflect.TypeOf(ResourceModel{})
)
