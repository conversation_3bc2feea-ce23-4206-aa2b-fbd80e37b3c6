package dao

import (
	"database/sql/driver"
	"fmt"
	"reflect"
	"testing"

	"github.com/erikstmartin/go-testdb"
	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type daoTest struct {
	dbutil.BaseModel
	A *string `dbutil:"searchable,updatable,orderable" valid:"optional,matches(^[A-Za-z0-9]+$)"`
	B *string `dbutil:"searchable,updatable,orderable"`
	C *string `dbutil:"searchable,orderable"`
}

func newDaoTest(id *int64, a *string, b *string, c *string) *daoTest {
	return &daoTest{
		BaseModel: dbutil.BaseModel{
			ID: id,
		},
		A: a,
		B: b,
		C: c,
	}
}

type daoNilTest struct {
	dbutil.BaseModel
	A *bool   `sql:"default:'false'"`
	B *int    `sql:"default:'0'"`
	C *string `sql:"default:''"`
}

type daoTest2 struct {
	dbutil.BaseModel
	Label string `json:"label"`
	Age   int    `json:"age"`
}

func TestDao_Load(t *testing.T) {
	db, _ := gorm.Open("testdb", "")
	testdb.EnableTimeParsing(true)
	utilDb := dbutil.NewDB(db)
	ctx := context.NewCsmContext(nil)
	type fields struct {
		Instance  interface{}
		modelType reflect.Type
		db        *dbutil.DB
	}
	type args struct {
		id  int64
		ctx context.CsmContext
	}
	tests := []struct {
		name      string
		fields    fields
		args      args
		result    driver.Rows
		wantQuery string
		wantArgs  []driver.Value
		wantLoad  *daoTest
		wantErr   bool
	}{
		{
			name: "load data",
			fields: fields{
				Instance: &daoTest{
					BaseModel: dbutil.BaseModel{},
				},
				modelType: reflect.TypeOf(daoTest{}),
				db:        utilDb,
			},
			args: args{
				id:  1,
				ctx: ctx,
			},
			wantLoad: newDaoTest(csm.Int64(1), csm.String("a"), csm.String("b"), csm.String("c")),
			wantArgs: []driver.Value{
				int64(1),
			},
			wantQuery: `SELECT * FROM "dao_tests"  WHERE (id = ?)`,
			result:    testdb.RowsFromCSVString([]string{"id", "a", "b", "c"}, "1, a, b ,c"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdb.SetQueryWithArgsFunc(func(query string, args []driver.Value) (driver.Rows, error) {
				if !reflect.DeepEqual(args, tt.wantArgs) {
					t.Errorf("Dao.Load() Args = %v, want %v", args, tt.wantArgs)
				}
				if query != tt.wantQuery {
					t.Errorf("Dao.Load() Query = %v, want %v", query, tt.wantQuery)
				}
				return tt.result, nil
			})
			b := &Dao{
				modelType: tt.fields.modelType,
				db:        tt.fields.db,
			}
			instance, err := b.Load(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("Dao.Load() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !reflect.DeepEqual(instance, tt.wantLoad) {
				t.Errorf("Dao.Load() Result = %v, want %v", instance, tt.wantLoad)
			}
		})
	}
}

func TestDao_Update(t *testing.T) {
	db, _ := gorm.Open("testdb", "")
	mDb := dbutil.NewDB(db)
	ctx := context.NewCsmContext(nil)
	type fields struct {
		modelType reflect.Type
		db        *dbutil.DB
	}
	type args struct {
		instance interface{}
		ctx      context.CsmContext
		update   interface{}
	}
	tests := []struct {
		name          string
		fields        fields
		args          args
		wantErr       bool
		skipArgsEqual []int
		result        driver.Result
		wantExec      string
		wantArgs      []driver.Value
	}{
		{
			name: "update data",
			fields: fields{
				modelType: reflect.TypeOf(daoTest{}),
				db:        mDb,
			},
			args: args{
				instance: &daoTest{
					BaseModel: dbutil.BaseModel{
						ID: csm.Int64(1),
					},
					A: csm.String("a"),
					B: csm.String("b"),
					C: csm.String("c"),
				},
				ctx:    ctx,
				update: *newDaoTest(nil, csm.String("foo"), csm.String(""), nil),
			},
			skipArgsEqual: []int{2},
			wantArgs: []driver.Value{
				"foo",
				"",
				nil,
			},
			wantExec: `UPDATE "dao_tests" SET "a" = ?, "b" = ?  WHERE "dao_tests"."id" = ?`,
			result:   testdb.NewResult(1, nil, 1, nil),
		},
		{
			name: "should not update ignored data",
			fields: fields{
				modelType: reflect.TypeOf(daoTest{}),
				db:        mDb,
			},
			args: args{
				instance: &daoTest{
					BaseModel: dbutil.BaseModel{
						ID: csm.Int64(1),
					},
					A: csm.String("a"),
					B: csm.String("b"),
					C: csm.String("c"),
				},
				ctx:    ctx,
				update: *newDaoTest(nil, csm.String("foo"), nil, csm.String("cc")),
			},
			skipArgsEqual: []int{1},
			wantArgs: []driver.Value{
				"foo",
				nil,
			},
			wantExec: `UPDATE "dao_tests" SET "a" = ?  WHERE "dao_tests"."id" = ?`,
			result:   testdb.NewResult(1, nil, 1, nil),
		},
		{
			name: "update on nil obj",
			fields: fields{
				modelType: reflect.TypeOf(daoTest{}),
				db:        mDb,
			},
			args: args{
				instance: nil,
				ctx:      ctx,
				update:   *newDaoTest(nil, csm.String("foo"), csm.String(""), nil),
			},
			wantErr: true,
		},
		{
			name: "update on nil id",
			fields: fields{
				modelType: reflect.TypeOf(daoTest{}),
				db:        mDb,
			},
			args: args{
				instance: &daoTest{
					BaseModel: dbutil.BaseModel{},
				},
				ctx:    ctx,
				update: *newDaoTest(nil, csm.String("foo"), csm.String(""), nil),
			},
			wantErr: true,
		},
		{
			name: "update with invalid struct",
			fields: fields{
				modelType: reflect.TypeOf(daoTest{}),
				db:        mDb,
			},
			args: args{
				instance: &daoTest{
					BaseModel: dbutil.BaseModel{
						ID: csm.Int64(1),
					},
					A: csm.String("ab"),
					B: csm.String("b"),
					C: csm.String("c"),
				},
				ctx:    ctx,
				update: *newDaoTest(nil, csm.String("foo."), csm.String(""), nil),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdb.SetExecWithArgsFunc(func(query string, args []driver.Value) (driver.Result, error) {
				for _, v := range tt.skipArgsEqual {
					args[v] = nil
				}
				if !reflect.DeepEqual(args, tt.wantArgs) {
					t.Errorf("Dao.Update() Args = %v, want %v", args, tt.wantArgs)
				}
				if query != tt.wantExec {
					t.Errorf("Dao.Update() Query = %v, want %v", query, tt.wantExec)
				}
				return tt.result, nil
			})

			b := &Dao{
				modelType: tt.fields.modelType,
				db:        tt.fields.db,
			}
			if err := b.Update(tt.args.ctx, tt.args.instance, tt.args.update); (err != nil) != tt.wantErr {
				t.Errorf("Dao.Update() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDao_Save(t *testing.T) {
	db, _ := gorm.Open("testdb", "")
	mDb := dbutil.NewDB(db)
	ctx := context.NewCsmContext(nil)
	type fields struct {
		modelType reflect.Type
		db        *dbutil.DB
		model     interface{}
		typeName  string
	}
	type args struct {
		instance interface{}
		ctx      context.CsmContext
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
		args    args

		skipArgsEqual []int
		result        driver.Result
		wantExec      string
		wantSelect    string
		selectResult  driver.Rows
		wantArgs      []driver.Value
	}{
		{
			name: "new data skip nil field",
			fields: fields{
				model:     newInstance(reflect.TypeOf(daoNilTest{})),
				modelType: reflect.TypeOf(daoNilTest{}),
				db:        mDb,
			},
			args: args{
				instance: &daoNilTest{
					A: csm.Bool(true),
				},
				ctx: ctx,
			},
			skipArgsEqual: []int{0},
			wantArgs: []driver.Value{
				nil,
				true,
			},
			wantExec:     `INSERT INTO "dao_nil_tests" ("delete_time","a") VALUES (?,?)`,
			wantSelect:   `SELECT "b", "c" FROM "dao_nil_tests"  WHERE (id = ?)`,
			selectResult: testdb.RowsFromCSVString([]string{"b", "c"}, "3,foo"),
			result:       testdb.NewResult(1, nil, 1, nil),
		},
		{
			name: "new data",
			fields: fields{
				model:     newInstance(reflect.TypeOf(daoTest{})),
				modelType: reflect.TypeOf(daoTest{}),
				db:        mDb,
			},
			args: args{
				instance: &daoTest{
					BaseModel: dbutil.BaseModel{},
					A:         csm.String("a"),
					B:         csm.String("b"),
					C:         csm.String("c"),
				},
				ctx: ctx,
			},
			skipArgsEqual: []int{0},
			wantArgs: []driver.Value{
				nil,
				"a",
				"b",
				"c",
			},
			wantExec: `INSERT INTO "dao_tests" ("delete_time","a","b","c") VALUES (?,?,?,?)`,
			result:   testdb.NewResult(1, nil, 1, nil),
		},
		{
			name: "update data",
			fields: fields{
				model:     newInstance(reflect.TypeOf(daoTest{})),
				modelType: reflect.TypeOf(daoTest{}),
				db:        mDb,
			},
			args: args{
				ctx: ctx,
				instance: &daoTest{
					BaseModel: dbutil.BaseModel{
						ID: csm.Int64(1),
					},
					A: csm.String("a"),
					B: csm.String("b"),
					C: csm.String("c"),
				},
			},
			skipArgsEqual: []int{0, 1},
			wantArgs: []driver.Value{
				nil,
				nil,
				nil,
				"a",
				"b",
				"c",
				int64(1),
			},
			wantExec: `UPDATE "dao_tests" SET "update_time" = ?, "create_time" = ?, "delete_time" = ?, "a" = ?, "b" = ?, "c" = ?  WHERE "dao_tests"."id" = ?`,
			result:   testdb.NewResult(1, nil, 1, nil),
		},
		{
			name: "new data with invalid struct",
			fields: fields{
				model:     newInstance(reflect.TypeOf(daoTest{})),
				modelType: reflect.TypeOf(daoTest{}),
				db:        mDb,
			},
			args: args{
				instance: &daoTest{
					BaseModel: dbutil.BaseModel{},
					A:         csm.String("a.s"),
					B:         csm.String("b"),
					C:         csm.String("c"),
				},
				ctx: ctx,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdb.SetExecWithArgsFunc(func(query string, args []driver.Value) (driver.Result, error) {
				for _, v := range tt.skipArgsEqual {
					args[v] = nil
				}
				if !reflect.DeepEqual(args, tt.wantArgs) {
					t.Errorf("Dao.Save() Args = %v, want %v", args, tt.wantArgs)
				}
				if query != tt.wantExec {
					t.Errorf("Dao.Save() Query = %v, want %v", query, tt.wantExec)
				}
				return tt.result, nil
			})
			if len(tt.wantSelect) > 0 {
				testdb.SetQueryWithArgsFunc(func(query string, args []driver.Value) (result driver.Rows, err error) {
					if query == tt.wantSelect {
						return tt.selectResult, nil
					}
					return nil, fmt.Errorf("not implement query for %s, want: %s", query, tt.wantSelect)
				})
			}

			b := &Dao{
				modelType: tt.fields.modelType,
				db:        tt.fields.db,
				typeName:  tt.fields.typeName,
			}
			if err := b.Save(tt.args.ctx, tt.args.instance); (err != nil) != tt.wantErr {
				t.Errorf("Dao.Save() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDao_Delete(t *testing.T) {
	db, _ := gorm.Open("testdb", "")
	testdb.EnableTimeParsing(true)
	ctx := context.NewCsmContext(nil)
	utilDb := dbutil.NewDB(db)

	type fields struct {
		db       *dbutil.DB
		ctx      context.CsmContext
		instance interface{}
	}

	tests := []struct {
		name          string
		input         fields
		wantDeleteSQL string
		modelType     reflect.Type
		result        driver.Result
	}{
		{
			name: "Delete Test",
			input: fields{
				db:  utilDb,
				ctx: ctx,
				instance: &daoTest2{
					BaseModel: dbutil.BaseModel{
						ID: csm.Int64(111),
					},
					Label: "abc",
				},
			},

			wantDeleteSQL: `UPDATE "dao_test2" SET "delete_time" = ?  WHERE "dao_test2"."id" = ?`,
			modelType:     reflect.TypeOf(daoTest2{}),
			result:        testdb.NewResult(111, nil, 1, nil),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdb.SetExecWithArgsFunc(func(query string, args []driver.Value) (driver.Result, error) {
				if query != tt.wantDeleteSQL {
					t.Errorf("Dao.Delete() SQL = %v, want %v", query, tt.wantDeleteSQL)
				}
				return tt.result, nil
			})
			b := NewDao(tt.modelType, tt.input.db)
			err := b.Delete(tt.input.ctx, tt.input.instance)
			if err != nil {
				t.Errorf("Dao.Delete() error: %v", err)
				return
			}
		})
	}
}

func TestDao_BatchDelete(t *testing.T) {
	db, _ := gorm.Open("testdb", "")
	testdb.EnableTimeParsing(true)
	ctx := context.NewCsmContext(nil)
	utilDb := dbutil.NewDB(db)

	type fields struct {
		db       *dbutil.DB
		ctx      context.CsmContext
		instance interface{}
	}

	tests := []struct {
		name          string
		input         fields
		wantDeleteSQL string
		modelType     reflect.Type
		result        driver.Result
	}{
		{
			name: "Batch Delete Test",
			input: fields{
				db:  utilDb,
				ctx: ctx,
				instance: &daoTest2{
					// BaseModel: BaseModel{
					// 	ID: bap.Int64(111),
					// },
					Label: "abc",
					Age:   20,
				},
			},
			wantDeleteSQL: `UPDATE "dao_test2" SET "delete_time" = ?  WHERE ("dao_test2"."label" = ?) AND ("dao_test2"."age" = ?)`,
			modelType:     reflect.TypeOf(daoTest2{}),
			result:        testdb.NewResult(111, nil, 10, nil),
		},
		{
			name: "Batch Delete Test",
			input: fields{
				db:  utilDb,
				ctx: ctx,
				instance: &daoTest2{
					BaseModel: dbutil.BaseModel{
						ID: csm.Int64(111),
					},
					Label: "abc",
				},
			},
			wantDeleteSQL: `UPDATE "dao_test2" SET "delete_time" = ?  WHERE ("dao_test2"."id" = ?) AND ("dao_test2"."label" = ?)`,
			modelType:     reflect.TypeOf(daoTest2{}),
			result:        testdb.NewResult(111, nil, 1, nil),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdb.SetExecWithArgsFunc(func(query string, args []driver.Value) (driver.Result, error) {
				if query != tt.wantDeleteSQL {
					t.Errorf("Dao.BatchDelete() SQL = %v, want %v", query, tt.wantDeleteSQL)
				}
				return tt.result, nil
			})
			b := NewDao(tt.modelType, tt.input.db)
			err := b.BatchDelete(tt.input.ctx, tt.input.instance)
			if err != nil {
				t.Errorf("Dao.BatchDelete() error: %v", err)
				return
			}
		})
	}
}

func TestDao_List(t *testing.T) {
	db, _ := gorm.Open("testdb", "")
	testdb.EnableTimeParsing(true)
	ctx := context.NewCsmContext(nil)
	utilDb := dbutil.NewDB(db)

	type fields struct {
		modelType reflect.Type
		search    interface{}
		where     interface{}
		not       interface{}
	}
	tests := []struct {
		name           string
		ctx            context.CsmContext
		db             *dbutil.DB
		input          fields
		wantListQuery  string
		wantArgs       []driver.Value
		wantListResult driver.Rows
		wantListCount  int
	}{
		{
			name: "search with specified field",
			ctx:  ctx,
			db:   utilDb,
			input: fields{
				modelType: reflect.TypeOf(daoTest{}),
				search: daoTest{
					A: csm.String("my-field-a"),
					B: csm.String("my-field-b"),
				},
				where: map[string]interface{}{
					"c": csm.String("my-field-c"),
				},
				not: daoTest{},
			},
			wantListQuery: `SELECT * FROM "dao_tests"  WHERE (a = ?) AND (b = ?) AND ("dao_tests"."c" = ?) AND (deleted = 0) LIMIT 1000 OFFSET 0`,
			wantArgs:      []driver.Value{"my-field-a", "my-field-b", "my-field-c"},
			wantListResult: testdb.RowsFromCSVString([]string{"id", "created_at", "a", "b", "c"},
				"1,2019-01-16T16:00:00+08:00,my-field-a,my-field-b,my-field-c"),
			wantListCount: 1,
		},

		{
			name: "search with pointer",
			ctx:  ctx,
			db:   utilDb,
			input: fields{
				modelType: reflect.TypeOf(daoTest{}),
				search: &daoTest{
					A: csm.String("my-field-a"),
					B: csm.String("my-field-b"),
				},
				where: map[string]interface{}{
					"c": csm.String("my-field-c"),
				},
				not: daoTest{},
			},
			wantListQuery: `SELECT * FROM "dao_tests"  WHERE (a = ?) AND (b = ?) AND ("dao_tests"."c" = ?) AND (deleted = 0) LIMIT 1000 OFFSET 0`,
			wantArgs:      []driver.Value{"my-field-a", "my-field-b", "my-field-c"},
			wantListResult: testdb.RowsFromCSVString([]string{"id", "created_at", "a", "b", "c"},
				"1,2019-01-16T16:00:00+08:00,my-field-a,my-field-b,my-field-c"),
			wantListCount: 1,
		},

		{
			name: "filter with not",
			ctx:  ctx,
			db:   utilDb,
			input: fields{
				modelType: reflect.TypeOf(daoTest{}),
				search: &daoTest{
					A: csm.String("my-field-a"),
				},
				where: map[string]interface{}{
					"c": csm.String("my-field-c"),
				},
				not: map[string]interface{}{
					"b": csm.String("my-field-b"),
				},
			},
			wantListQuery:  `SELECT * FROM "dao_tests"  WHERE (a = ?) AND ("dao_tests"."c" = ?) AND (deleted = 0) AND ("dao_tests"."b" <> ?) LIMIT 1000 OFFSET 0`,
			wantArgs:       []driver.Value{"my-field-a", "my-field-c", "my-field-b"},
			wantListResult: testdb.RowsFromCSVString([]string{"id", "created_at", "a", "b", "c"}, ""),
			wantListCount:  0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdb.SetQueryWithArgsFunc(func(query string, args []driver.Value) (result driver.Rows, err error) {
				if query != tt.wantListQuery {
					t.Errorf("List() SQL = %v, want %v", query, tt.wantListQuery)
				}
				if !reflect.DeepEqual(args, tt.wantArgs) {
					t.Errorf("List() Args = %v, want %v", args, tt.wantArgs)
				}
				return tt.wantListResult, nil
			})
			dao := NewDao(tt.input.modelType, tt.db)
			result, err := dao.List(tt.ctx, tt.input.search, tt.input.where, tt.input.not, 0, 1000)
			listRet := result.(*[]daoTest)
			if err != nil {
				t.Errorf("List() error: %v", err)
				return
			}
			if len(*listRet) != tt.wantListCount {
				t.Errorf("List Result len(): %d  want: %d", len(*listRet), tt.wantListCount)
			}
		})
	}
}
