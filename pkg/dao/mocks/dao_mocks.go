// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	dao "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	dbutil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// MockBaseInterface is a mock of BaseInterface interface.
type MockBaseInterface struct {
	ctrl     *gomock.Controller
	recorder *MockBaseInterfaceMockRecorder
}

// MockBaseInterfaceMockRecorder is the mock recorder for MockBaseInterface.
type MockBaseInterfaceMockRecorder struct {
	mock *MockBaseInterface
}

// NewMockBaseInterface creates a new mock instance.
func NewMockBaseInterface(ctrl *gomock.Controller) *MockBaseInterface {
	mock := &MockBaseInterface{ctrl: ctrl}
	mock.recorder = &MockBaseInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBaseInterface) EXPECT() *MockBaseInterfaceMockRecorder {
	return m.recorder
}

// BatchDelete mock base method.
func (m *MockBaseInterface) BatchDelete(ctx context.CsmContext, where interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelete", ctx, where)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelete indicates an expected call of BatchDelete.
func (mr *MockBaseInterfaceMockRecorder) BatchDelete(ctx, where interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelete", reflect.TypeOf((*MockBaseInterface)(nil).BatchDelete), ctx, where)
}

// Count mock base method.
func (m *MockBaseInterface) Count(ctx context.CsmContext, search, where interface{}) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Count", ctx, search, where)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockBaseInterfaceMockRecorder) Count(ctx, search, where interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockBaseInterface)(nil).Count), ctx, search, where)
}

// CountGroupBy mock base method.
func (m *MockBaseInterface) CountGroupBy(ctx context.CsmContext, where interface{}, groupBy string) (map[string]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountGroupBy", ctx, where, groupBy)
	ret0, _ := ret[0].(map[string]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountGroupBy indicates an expected call of CountGroupBy.
func (mr *MockBaseInterfaceMockRecorder) CountGroupBy(ctx, where, groupBy interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountGroupBy", reflect.TypeOf((*MockBaseInterface)(nil).CountGroupBy), ctx, where, groupBy)
}

// Delete mock base method.
func (m *MockBaseInterface) Delete(ctx context.CsmContext, instance interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, instance)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockBaseInterfaceMockRecorder) Delete(ctx, instance interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockBaseInterface)(nil).Delete), ctx, instance)
}

// ForUpdate mock base method.
func (m *MockBaseInterface) ForUpdate() dao.BaseInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ForUpdate")
	ret0, _ := ret[0].(dao.BaseInterface)
	return ret0
}

// ForUpdate indicates an expected call of ForUpdate.
func (mr *MockBaseInterfaceMockRecorder) ForUpdate() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForUpdate", reflect.TypeOf((*MockBaseInterface)(nil).ForUpdate))
}

// In mock base method.
func (m *MockBaseInterface) In(ctx context.CsmContext, field string, in []interface{}) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "In", ctx, field, in)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// In indicates an expected call of In.
func (mr *MockBaseInterfaceMockRecorder) In(ctx, field, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "In", reflect.TypeOf((*MockBaseInterface)(nil).In), ctx, field, in)
}

// InWithWhere mock base method.
func (m *MockBaseInterface) InWithWhere(ctx context.CsmContext, field string, in []interface{}, where interface{}) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InWithWhere", ctx, field, in, where)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InWithWhere indicates an expected call of InWithWhere.
func (mr *MockBaseInterfaceMockRecorder) InWithWhere(ctx, field, in, where interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InWithWhere", reflect.TypeOf((*MockBaseInterface)(nil).InWithWhere), ctx, field, in, where)
}

// LessWithWhere mock base method.
func (m *MockBaseInterface) LessWithWhere(ctx context.CsmContext, field string, less, where interface{}, orderBy string, order dbutil.DBOrder, limit int64) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LessWithWhere", ctx, field, less, where, orderBy, order, limit)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LessWithWhere indicates an expected call of LessWithWhere.
func (mr *MockBaseInterfaceMockRecorder) LessWithWhere(ctx, field, less, where, orderBy, order, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LessWithWhere", reflect.TypeOf((*MockBaseInterface)(nil).LessWithWhere), ctx, field, less, where, orderBy, order, limit)
}

// List mock base method.
func (m *MockBaseInterface) List(ctx context.CsmContext, search, where, not interface{}, offset, limit int64) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, search, where, not, offset, limit)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockBaseInterfaceMockRecorder) List(ctx, search, where, not, offset, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockBaseInterface)(nil).List), ctx, search, where, not, offset, limit)
}

// ListAll mock base method.
func (m *MockBaseInterface) ListAll(ctx context.CsmContext, search, where, not interface{}) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAll", ctx, search, where, not)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAll indicates an expected call of ListAll.
func (mr *MockBaseInterfaceMockRecorder) ListAll(ctx, search, where, not interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAll", reflect.TypeOf((*MockBaseInterface)(nil).ListAll), ctx, search, where, not)
}

// ListPage mock base method.
func (m *MockBaseInterface) ListPage(ctx context.CsmContext, search, where interface{}, orderBy string, order dbutil.DBOrder, pageSize, pageNo int64) (*dao.BasePageListResult, interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPage", ctx, search, where, orderBy, order, pageSize, pageNo)
	ret0, _ := ret[0].(*dao.BasePageListResult)
	ret1, _ := ret[1].(interface{})
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListPage indicates an expected call of ListPage.
func (mr *MockBaseInterfaceMockRecorder) ListPage(ctx, search, where, orderBy, order, pageSize, pageNo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPage", reflect.TypeOf((*MockBaseInterface)(nil).ListPage), ctx, search, where, orderBy, order, pageSize, pageNo)
}

// Load mock base method.
func (m *MockBaseInterface) Load(ctx context.CsmContext, id int64) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Load", ctx, id)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Load indicates an expected call of Load.
func (mr *MockBaseInterfaceMockRecorder) Load(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Load", reflect.TypeOf((*MockBaseInterface)(nil).Load), ctx, id)
}

// LoadWithWhere mock base method.
func (m *MockBaseInterface) LoadWithWhere(ctx context.CsmContext, where interface{}) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoadWithWhere", ctx, where)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadWithWhere indicates an expected call of LoadWithWhere.
func (mr *MockBaseInterfaceMockRecorder) LoadWithWhere(ctx, where interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadWithWhere", reflect.TypeOf((*MockBaseInterface)(nil).LoadWithWhere), ctx, where)
}

// Save mock base method.
func (m *MockBaseInterface) Save(ctx context.CsmContext, instance interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, instance)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockBaseInterfaceMockRecorder) Save(ctx, instance interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockBaseInterface)(nil).Save), ctx, instance)
}

// Update mock base method.
func (m *MockBaseInterface) Update(ctx context.CsmContext, dst, update interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, dst, update)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockBaseInterfaceMockRecorder) Update(ctx, dst, update interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockBaseInterface)(nil).Update), ctx, dst, update)
}
