package dao

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type BaseInterface interface {
	ForUpdate() BaseInterface
	Load(ctx context.CsmContext, id int64) (interface{}, error)
	LoadWithWhere(ctx context.CsmContext, where interface{}) (interface{}, error)
	Update(ctx context.CsmContext, dst interface{}, update interface{}) error
	Save(ctx context.CsmContext, instance interface{}) error
	Delete(ctx context.CsmContext, instance interface{}) error
	BatchDelete(ctx context.CsmContext, where interface{}) error
	List(ctx context.CsmContext, search interface{}, where interface{}, not interface{},
		offset int64, limit int64) (interface{}, error)
	Count(ctx context.CsmContext, search interface{}, where interface{}) (int64, error)
	CountGroupBy(ctx context.CsmContext, where interface{}, groupBy string) (map[string]int64, error)
	ListAll(ctx context.CsmContext, search interface{}, where interface{}, not interface{}) (interface{}, error)
	ListPage(ctx context.CsmContext, search interface{}, where interface{},
		orderBy string, order dbutil.DBOrder,
		pageSize, pageNo int64) (*BasePageListResult, interface{}, error)
	In(ctx context.CsmContext, field string, in []interface{}) (interface{}, error)
	InWithWhere(ctx context.CsmContext, field string, in []interface{}, where interface{}) (interface{}, error)
	LessWithWhere(ctx context.CsmContext, field string, less interface{}, where interface{}, orderBy string, order dbutil.DBOrder, limit int64) (interface{}, error)
}
