package dao

import (
	"errors"
	"reflect"

	"github.com/asaskevich/govalidator"
	"github.com/jinzhu/gorm"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/ptrutil"
)

var (
	assertResourceModelFail = errors.New("cannot assert ResourceModel")
)

const ResourceModelTag = "ResourceModel"

// ResourceDao modelType 是 meta 下的结构体类型，必须包含 dbutil.ResourceModel
type ResourceDao struct {
	ResourcePrefix string
	Dao
}

func NewResourceDao(t reflect.Type, resourcePrefix string, db *dbutil.DB) *ResourceDao {
	b := NewDao(t, db)
	return &ResourceDao{
		ResourcePrefix: resourcePrefix,
		Dao:            *b,
	}
}

// getResourceModel通过反射将instance包含的ResourceModel返回
func (b *ResourceDao) getResourceModel(instance interface{}) (model *ResourceModel, err error) {
	if instance == nil {
		err = csmErr.NewMissingParametersException("", nilInstanceError)
		return
	}
	fp, ok := ptrutil.GetFieldPtrByName(instance, ResourceModelTag, resourceModelType)
	if !ok {
		err = csmErr.NewInvalidParameterValueException("", assertResourceModelFail)
		return
	}
	model, _ = fp.(*ResourceModel)
	return
}

// SaveResource 将instance写入数据库，新增一行或更新内容
// 返回经过封装的HTTP Error
func (b *ResourceDao) SaveResource(ctx context.CsmContext, instance interface{}) error {
	base, err := b.getBaseModel(instance)
	if err != nil {
		return csmErr.NewInvalidParameterValueException("", err)
	}
	// 有ID的被认为是老数据
	if base.ID != nil {
		return csmErr.NewResourceConflictException("")
	}
	ctx.CsmLogger().Infof("SaveResource create %s", b.typeName)
	// 验证参数
	_, err = govalidator.ValidateStruct(instance)
	if err != nil {
		ctx.CsmLogger().Errorf(err.Error())
		return csmErr.NewInvalidParameterValueException("", err)
	}
	if err = b.db.Save(instance).Error; err != nil {
		return csmErr.ConvertMysqlErr(err)
	}
	return nil
}

// LoadResource 根据 ResourceID 和 Owner 从数据库读取数据并存入instance
// 返回经过封装的 HTTP Error
func (b *ResourceDao) LoadResource(ctx context.CsmContext, resourceID string, owner string) (interface{}, error) {
	ctx.CsmLogger().Infof("LoadResource %s %s belonged to %s", b.typeName, resourceID, owner)
	find := newInstance(b.modelType)
	fp, _ := ptrutil.GetFieldPtrByName(find, ResourceModelTag, resourceModelType)
	resourceModel := fp.(*ResourceModel)
	resourceModel.ResourceID = &resourceID
	resourceModel.Owner = owner

	out := newInstance(b.modelType)
	if err := b.db.Find(out, find).Error; err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, csmErr.NewResourceNotFoundException("Resource not found", err)
		}
		return nil, csmErr.NewDBOperationException(err)
	}
	return out, nil
}
