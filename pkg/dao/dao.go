package dao

import (
	"errors"
	"fmt"
	"reflect"
	"time"

	"github.com/asaskevich/govalidator"
	"github.com/imdario/mergo"
	"github.com/jinzhu/gorm"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/ptrutil"
)

var (
	nilInstanceError    = errors.New("Instance is nil")
	nilInstanceIDError  = errors.New("Instance ID is nil")
	assertBaseModelFail = errors.New("assert BaseModel type failed")
	assertInstanceFail  = errors.New("assert fail, instance type != modelType")
)

const BaseModelField = "BaseModel"

// Dao modelType 是meta下的结构体类型，必须包含dbutil.BaseModel
type Dao struct {
	modelType reflect.Type // modelType是构造对象的类型
	db        *dbutil.DB   // db数据库连接
	// ctx是请求上下文，用来打印日志
	typeName string
}

// NewDao 初始化通用Dao
func NewDao(t reflect.Type, db *dbutil.DB) *Dao {
	b := &Dao{}
	if t.Kind() != reflect.Struct {
		panic("must set with struct type, not pointer")
	}
	b.modelType = t
	b.db = db
	b.typeName = t.Name()
	return b
}

// ForUpdate 对读取的内容加行锁，防止其他事务修改
// 不影响原本的db对象
func (b *Dao) ForUpdate() BaseInterface {
	n := &Dao{
		modelType: b.modelType,
		typeName:  b.typeName,
		db: &dbutil.DB{
			DB: b.db.Set("gorm:query_option", "FOR UPDATE"),
		},
	}
	return n
}

// getBaseModel 通过反射将instance包含的BaseModel返回
func (b *Dao) getBaseModel(instance interface{}) (model *dbutil.BaseModel, err error) {
	if instance == nil {
		err = csmErr.NewMissingParametersException("", nilInstanceError)
		return
	}
	fp, ok := ptrutil.GetFieldPtrByName(instance, BaseModelField, baseModelType)
	if !ok {
		err = csmErr.NewInvalidParameterValueException("", assertBaseModelFail)
		return
	}
	model, _ = fp.(*dbutil.BaseModel)
	return
}

func (b *Dao) getInstanceID(instance interface{}) (*int64, error) {
	base, err := b.getBaseModel(instance)
	if err != nil {
		return nil, err
	}
	if base.ID == nil {
		return nil, csmErr.NewMissingParametersException("", nilInstanceIDError)
	}
	return base.ID, nil
}

// Load 返回经过封装的HTTP Error
func (b *Dao) Load(ctx context.CsmContext, id int64) (interface{}, error) {
	ctx.CsmLogger().Infof("Dao load %s:%d", b.typeName, id)
	// 使用全新的实例Load数据库数据
	nIns := newInstance(b.modelType)
	if err := b.db.Where("id = ?", id).Find(nIns).Error; err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, csmErr.NewResourceNotFoundException("record not found", err)
		}
		return nil, csmErr.NewDBOperationException(err)
	}
	return nIns, nil
}

// LoadWithWhere 通过条件查询
func (b *Dao) LoadWithWhere(ctx context.CsmContext, where interface{}) (interface{}, error) {
	ctx.CsmLogger().Infof("Dao load %s:%v", b.typeName, where)
	nIns := newInstance(b.modelType)
	if err := b.db.Where(where).Find(nIns).Error; err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, csmErr.NewResourceNotFoundException("record not found", err)
		}
		return nil, csmErr.NewDBOperationException(err)
	}
	return nIns, nil
}

// Last 返回经过封装的HTTP Error
func (b *Dao) Last(ctx context.CsmContext) (interface{}, error) {
	ctx.CsmLogger().Infof("Dao Last %s", b.typeName)
	// 使用全新的实例Load数据库数据
	nIns := newInstance(b.modelType)
	if err := b.db.Last(nIns).Error; err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, csmErr.NewResourceNotFoundException("record not found", err)
		}
		return nil, csmErr.NewDBOperationException(err)
	}
	return nIns, nil
}

// Update 返回经过封装的HTTP Error
func (b *Dao) Update(ctx context.CsmContext, dst interface{}, update interface{}) error {
	id, err := b.getInstanceID(dst)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("update %s:%d", b.typeName, *id)

	// merge 对象用于validate
	m := newInstance(b.modelType)

	if err := mergo.Merge(m, dst, mergo.WithOverride); err != nil {
		return csmErr.NewServiceException("", err)
	}

	if err := mergo.Merge(m, update, mergo.WithOverride); err != nil {
		return csmErr.NewServiceException("", err)
	}

	_, err = govalidator.ValidateStruct(m)
	if err != nil {
		ctx.CsmLogger().Errorf("Error Validating struct: %T, error:%+v", m, err)
		return csmErr.NewInvalidParameterValueException("", err)
	}

	if err = b.db.WithModel(dst).WithUpdate(update).Error; err != nil {
		return csmErr.NewDBOperationException(err)
	}
	return nil
}

// Save 将instance写入数据库,新增一行或更新内容
// 用于返回经封装的mysql error, 如果需要原始错误，使用方自己解析
func (b *Dao) Save(ctx context.CsmContext, instance interface{}) error {
	base, err := b.getBaseModel(instance)
	if err != nil {
		return csmErr.NewInvalidParameterValueException("", err)
	}
	// 有ID的被认为是老数据
	if base.ID != nil {
		ctx.CsmLogger().Infof("update %s:%d changes", b.typeName, base.ID)
	} else {
		// 设置DeletedAt默认值
		//base.DeletedAt = time.Time{}
		ctx.CsmLogger().Infof("create %s:%d", b.typeName, base.ID)
	}
	_, err = govalidator.ValidateStruct(instance)
	if err != nil {
		ctx.CsmLogger().Errorf("Error Validating struct: %T, error:%+v", instance, err)
		return csmErr.NewInvalidParameterValueException("", err)
	}
	if err = b.db.Save(instance).Error; err != nil {
		return csmErr.NewDBOperationException(err)
	}
	return nil
}

// Delete 根据ID从数据库删除instance
// 返回经过封装的HTTP Error
func (b *Dao) Delete(ctx context.CsmContext, instance interface{}) error {
	id, err := b.getInstanceID(instance)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("%s:%d deleted", b.typeName, *id)
	// 设置DeletedAt为删除, 更新到数据库
	if err = b.db.WithModel(instance).Update("delete_time", time.Now()).Update("deleted", 1).Error; err != nil {
		return csmErr.NewDBOperationException(err)
	}
	return nil
}

// BatchDelete 批量删除方法，删除满足条件的所有字段
// 返回经过封装的HTTP Error
func (b *Dao) BatchDelete(ctx context.CsmContext, where interface{}) error {
	ctx.CsmLogger().Infof("%s:%v batch deleted", b.typeName, where)
	model := newInstance(b.modelType)
	if err := b.db.WithModel(model).Where(where).Update("delete_time", time.Now()).Update("deleted", 1).Error; err != nil {
		return csmErr.NewDBOperationException(err)
	}
	return nil

}

// List 函数用于根据条件查询数据
// t参数: 检索的结构体的类型
// search参数: 基于dbutil tag实现的结构体
// where参数: 不受dbutil searchable tag限制的的查询数据
// 返回值: interface{}类型是 *[]t, t为传入的类型
// 返回经过封装的HTTP Error
func (b *Dao) List(ctx context.CsmContext, search interface{}, where interface{}, not interface{},
	offset int64, limit int64) (interface{}, error) {
	ctx.CsmLogger().Infof("list %s", b.typeName)
	out := newInstanceList(b.modelType)
	if limit == 0 {
		return out, nil
	}
	if err := b.db.WithSearch(search).Where(where).Where("deleted = 0").Not(not).Offset(offset).Limit(limit).Find(out).Error; err != nil {
		return nil, csmErr.NewDBOperationException(err)
	}
	return out, nil
}

// Count 返回经过封装的HTTP Error
func (b *Dao) Count(ctx context.CsmContext, search interface{}, where interface{}) (int64, error) {

	ctx.CsmLogger().Infof("count %s ", b.typeName)

	find := newInstance(b.modelType)

	var totalCount int64
	if err := b.db.WithModel(find).WithSearch(search).
		Where(where).Where("deleted = 0").Count(&totalCount).
		Error; err != nil {
		return -1, csmErr.NewDBOperationException(err)
	}
	return totalCount, nil
}

func (b *Dao) CountGroupBy(ctx context.CsmContext, where interface{}, groupBy string) (map[string]int64, error) {
	ctx.CsmLogger().Infof("count %s group by %s", b.typeName, groupBy)

	find := newInstance(b.modelType)
	type Result struct {
		GroupBy string
		Count   int64
	}
	out := make([]Result, 0)
	selectedFields := fmt.Sprintf("%s as group_by, count(*) as count", groupBy)

	if err := b.db.WithModel(find).Select(selectedFields).Where(where).Group(groupBy).Scan(&out).Error; err != nil {
		return nil, csmErr.NewDBOperationException(err)
	}

	results := make(map[string]int64)
	for _, r := range out {
		results[r.GroupBy] = r.Count
	}
	return results, nil
}

func (b *Dao) ListAll(ctx context.CsmContext, search interface{}, where interface{}, not interface{}) (interface{}, error) {
	count, err := b.Count(ctx, search, where)
	if err != nil {
		return nil, err
	}

	return b.List(ctx, search, where, not, 0, count)
}

// ListPage 查询数据库列表并提供分页格式返回
// 返回经过封装的HTTP Error
func (b *Dao) ListPage(ctx context.CsmContext, search interface{}, where interface{},
	orderBy string, order dbutil.DBOrder,
	pageSize, pageNo int64) (*BasePageListResult, interface{}, error) {

	ctx.CsmLogger().Infof(
		"list page %s params: %v, orderBy: %s, order: %s pageSize: %d, pageNo: %d",
		b.typeName, search, orderBy, order, pageSize, pageNo)

	if len(orderBy) == 0 {
		orderBy = "Create_at"
	}
	out := newInstanceList(b.modelType)
	offset := pageSize * (pageNo - 1)
	limit := pageSize
	if err := b.db.WithSearch(search).WithOrder(b.modelType, orderBy, order).WithOrder(b.modelType, "ID", dbutil.DBOrderDESC).Where(where).Offset(offset).Limit(limit).Find(out).Error; err != nil {
		return nil, nil, csmErr.NewDBOperationException(err)
	}
	cnt, err := b.Count(ctx, search, where)
	if err != nil {
		return nil, nil, err
	}

	return &BasePageListResult{
		PageSize:   pageSize,
		PageNo:     pageNo,
		TotalCount: cnt,
	}, out, nil
}

// ListPageWithRaw 查询数据库列表并提供分页格式返回
// 返回经过封装的HTTP Error
func (b *Dao) ListPageWithRaw(ctx context.CsmContext, rawSql, countSql string,
	pageSize, pageNo int64, values ...interface{}) (*BasePageListResult, interface{}, error) {

	ctx.CsmLogger().Infof("execute rawSql: %s , countSql: %s , values: %v , pageSize: %d , pageNo: %d",
		rawSql, countSql, values, pageSize, pageNo)

	out := newInstanceList(b.modelType)
	values = append(values, pageSize)
	values = append(values, (pageNo-1)*pageSize)
	if err := b.db.Raw(rawSql, values[0], values[1], pageSize, pageNo).Unscoped().Scan(out).Error; err != nil {
		return nil, nil, csmErr.NewDBOperationException(err)
	}

	totalCount := 0

	return &BasePageListResult{
		PageSize:   pageSize,
		PageNo:     pageNo,
		TotalCount: int64(totalCount),
	}, out, nil
}

// In 返回经过封装的HTTP Error
func (b *Dao) In(ctx context.CsmContext, field string, in []interface{}) (interface{}, error) {
	ctx.CsmLogger().Infof(
		"list in %s ", b.typeName)
	out := newInstanceList(b.modelType)
	inString := fmt.Sprintf("%s IN (?)", field)
	if err := b.db.Where(inString, in).Find(out).Error; err != nil {
		return nil, csmErr.NewDBOperationException(err)
	}
	return out, nil
}

// InWithWhere 返回经过封装的HTTP Error
func (b *Dao) InWithWhere(ctx context.CsmContext, field string, in []interface{}, where interface{}) (interface{}, error) {
	ctx.CsmLogger().Infof(
		"list in %s ", b.typeName)
	out := newInstanceList(b.modelType)
	inString := fmt.Sprintf("%s IN (?)", field)
	if err := b.db.Where(where).Where(inString, in).Find(out).Error; err != nil {
		return nil, csmErr.NewDBOperationException(err)
	}
	return out, nil
}

// LessWithWhere 返回经过封装的HTTP Error
func (b *Dao) LessWithWhere(ctx context.CsmContext, field string, less interface{}, where interface{}, orderBy string, order dbutil.DBOrder, limit int64) (interface{}, error) {
	ctx.CsmLogger().Infof(
		"less in %s ", b.typeName)
	out := newInstanceList(b.modelType)
	lessString := fmt.Sprintf("%s < (?)", field)
	if err := b.db.WithOrder(b.modelType, orderBy, order).WithOrder(b.modelType, "ID", dbutil.DBOrderDESC).Where(where).Where(lessString, less).Limit(limit).Find(out).Error; err != nil {
		return nil, csmErr.NewDBOperationException(err)
	}
	return out, nil
}
