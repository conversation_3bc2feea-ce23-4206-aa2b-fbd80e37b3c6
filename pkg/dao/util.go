package dao

import (
	"reflect"
	"regexp"

	"github.com/VividCortex/mysqlerr"
	"github.com/go-sql-driver/mysql"
)

var (
	dupResourceIDRegexp = regexp.MustCompile(`^Duplicate entry '([\w-]+)'`)
)

func newInstance(t reflect.Type) interface{} {
	return reflect.New(t).Interface()
}

func newInstanceList(t reflect.Type) interface{} {
	slice := reflect.MakeSlice(reflect.SliceOf(t), 0, 0)
	return reflect.New(slice.Type()).Interface()
}

func isDuplicateEntry(err error) bool {
	driverErr, ok := err.(*mysql.MySQLError)
	if !ok {
		return false
	}

	return driverErr.Number == mysqlerr.ER_DUP_ENTRY
}
