package registercentersdk

import (
	"io"
	"net/http"
	"os"
	"strings"
)

var canonicalHeaders = []string{
	"host",
	"content-length",
	"content-type",
	"content-md5",
}

// Request is http request, but has some custom functions.
type Request http.Request

func NewRequest(method, url string, body io.Reader) (*Request, error) {
	method = strings.ToUpper(method)

	rawRequest, err := http.NewRequest(method, url, body)

	if file, ok := body.(*os.File); ok {
		fileInfo, err := file.Stat()

		if err != nil {
			return nil, err
		}

		rawRequest.ContentLength = fileInfo.Size()
	}

	req := (*Request)(rawRequest)
	return req, err
}

// AddHeaders Add headers to http request
func (req *Request) AddHeaders(headerMap map[string]string) {
	for key, value := range headerMap {
		req.addHeader(key, value)
	}
}

func (req *Request) addHeader(key, value string) {
	req.Header.Add(key, value)
}

// SetHeaders Set headers to http request
func (req *Request) SetHeaders(headerMap map[string]string) {
	for key, value := range headerMap {
		req.setHeader(key, value)
	}
}

func (req *Request) setHeader(key, value string) {
	req.Header.Set(key, value)
}

func (req *Request) clearHeaders() {
	for key := range req.Header {
		delete(req.Header, key)
	}
}

func (req *Request) Raw() *http.Request {
	return (*http.Request)(req)
}
