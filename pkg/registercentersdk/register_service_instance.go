package registercentersdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/jsonpb"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"strconv"
)

func (c *Client) CreateRegisterServiceInstance(ctx csmContext.CsmContext, req *CreateServiceInstanceRequest,
	option *AuthOption) (*apiservice.BatchWriteResponse, error) {

	url := fmt.Sprintf("/naming/v1/instances")

	// request body
	serviceInsListRequest := make([]*CreateServiceInstanceRequest, 0)
	serviceInsListRequest = append(serviceInsListRequest, req)
	requestBody, err := json.Marshal(serviceInsListRequest)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}
	ctx.CsmLogger().Infof("create service instance request body: %s", string(requestBody))

	request, err := NewRequest("POST", c.GetURL(url, option.Endpoint, nil), bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, request, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	ctx.CsmLogger().Infof("create service instance response body: %s", string(bodyContent))

	response := &apiservice.BatchWriteResponse{}
	if err = jsonpb.UnmarshalString(string(bodyContent), response); err != nil {
		return nil, err
	}

	return response, nil
}

func (c *Client) UpdateRegisterServiceInstance(ctx csmContext.CsmContext, req *CreateServiceInstanceRequest,
	option *AuthOption) (*apiservice.BatchWriteResponse, error) {

	url := fmt.Sprintf("/naming/v1/instances")

	// request body
	serviceInsListRequest := make([]*CreateServiceInstanceRequest, 0)
	serviceInsListRequest = append(serviceInsListRequest, req)
	requestBody, err := json.Marshal(serviceInsListRequest)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}
	ctx.CsmLogger().Infof("update service instance request body: %s", string(requestBody))

	request, err := NewRequest("PUT", c.GetURL(url, option.Endpoint, nil), bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, request, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	ctx.CsmLogger().Infof("update service instance response body: %s", string(bodyContent))

	response := &apiservice.BatchWriteResponse{}
	if err = jsonpb.UnmarshalString(string(bodyContent), response); err != nil {
		return nil, err
	}

	return response, nil
}

func (c *Client) DeleteRegisterServiceInstance(ctx csmContext.CsmContext, req *DeleteServiceInstanceRequest,
	option *AuthOption) (*apiservice.BatchWriteResponse, error) {

	url := fmt.Sprintf("/naming/v1/instances/delete")

	// request body
	serviceInsListRequest := make([]*DeleteServiceInstanceRequest, 0)
	serviceInsListRequest = append(serviceInsListRequest, req)
	requestBody, err := json.Marshal(serviceInsListRequest)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}

	request, err := NewRequest("POST", c.GetURL(url, option.Endpoint, nil), bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, request, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	ctx.CsmLogger().Infof("delete service instance response body: %s", string(bodyContent))

	response := &apiservice.BatchWriteResponse{}
	if err = jsonpb.UnmarshalString(string(bodyContent), response); err != nil {
		return nil, err
	}

	return response, nil
}

func (c *Client) GetServiceInstanceList(ctx csmContext.CsmContext, req *ServiceInstanceListRequest,
	option *AuthOption) (*ServiceInstanceListResponse, error) {

	// querystring
	params := map[string]string{
		"service":             req.Service,
		"offset":              strconv.Itoa(req.Offset),
		"limit":               strconv.Itoa(req.Limit),
		"show_last_heartbeat": "true",
	}

	if req.Id != "" {
		params["id"] = req.Id
	}

	if req.Host != "" {
		params["host"] = req.Host
	}

	if req.Healthy != "" {
		params["healthy"] = req.Healthy
	}

	if req.Isolate != "" {
		params["isolate"] = req.Isolate
	}

	if req.Namespace != "" {
		params["namespace"] = req.Namespace
	}

	url := fmt.Sprintf("/naming/v1/instances")
	request, err := NewRequest("GET", c.GetURL(url, option.Endpoint, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, request, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ServiceInstanceListResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) BatchDeleteServiceInstance(ctx csmContext.CsmContext, req []*BatchRequest,
	option *AuthOption) (*apiservice.BatchWriteResponse, error) {

	url := fmt.Sprintf("/naming/v1/instances/delete/host")

	// request body
	requestBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}
	ctx.CsmLogger().Infof("batch delete service instance request body: %s", string(requestBody))

	request, err := NewRequest("POST", c.GetURL(url, option.Endpoint, nil), bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, request, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	ctx.CsmLogger().Infof("batch delete service instance response body: %s", string(bodyContent))

	response := &apiservice.BatchWriteResponse{}
	if err = jsonpb.UnmarshalString(string(bodyContent), response); err != nil {
		return nil, err
	}

	return response, nil
}

func (c *Client) BatchIsolateServiceInstance(ctx csmContext.CsmContext, req []*BatchRequest,
	option *AuthOption) (*apiservice.BatchWriteResponse, error) {

	url := fmt.Sprintf("/naming/v1/instances/isolate/host")

	// request body
	requestBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}
	ctx.CsmLogger().Infof("batch isolate service instance request body: %s", string(requestBody))

	request, err := NewRequest("PUT", c.GetURL(url, option.Endpoint, nil), bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, request, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	ctx.CsmLogger().Infof("batch isolate instance response body: %s", string(bodyContent))

	response := &apiservice.BatchWriteResponse{}
	if err = jsonpb.UnmarshalString(string(bodyContent), response); err != nil {
		return nil, err
	}

	return response, nil
}

func (c *Client) CreateRegisterNamespaces(ctx csmContext.CsmContext, req []CreateNamespaceRequest,
	option *AuthOption) (*apiservice.BatchWriteResponse, error) {

	url := fmt.Sprintf("/naming/v1/namespaces")

	// request body
	requestBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}
	ctx.CsmLogger().Infof("create namespaces request body: %s", string(requestBody))

	request, err := NewRequest("POST", c.GetURL(url, option.Endpoint, nil), bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, request, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	ctx.CsmLogger().Infof("create service instance response body: %s", string(bodyContent))

	response := &apiservice.BatchWriteResponse{}
	if err = jsonpb.UnmarshalString(string(bodyContent), response); err != nil {
		return nil, err
	}

	return response, nil
}

func (c *Client) UpdateRegisterNamespaces(ctx csmContext.CsmContext, req []CreateNamespaceRequest,
	option *AuthOption) (*apiservice.BatchWriteResponse, error) {

	url := fmt.Sprintf("/naming/v1/namespaces")

	// request body
	requestBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}
	ctx.CsmLogger().Infof("create namespaces request body: %s", string(requestBody))

	request, err := NewRequest("PUT", c.GetURL(url, option.Endpoint, nil), bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, request, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	ctx.CsmLogger().Infof("create service instance response body: %s", string(bodyContent))

	response := &apiservice.BatchWriteResponse{}
	if err = jsonpb.UnmarshalString(string(bodyContent), response); err != nil {
		return nil, err
	}

	return response, nil
}

func (c *Client) DeleteRegisterNamespaces(ctx csmContext.CsmContext, req []CreateNamespaceRequest,
	option *AuthOption) (*apiservice.BatchWriteResponse, error) {

	url := fmt.Sprintf("/naming/v1/namespaces/delete")

	// request body
	requestBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}
	ctx.CsmLogger().Infof("delete namespaces request body: %s", string(requestBody))

	request, err := NewRequest("POST", c.GetURL(url, option.Endpoint, nil), bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, request, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	ctx.CsmLogger().Infof("delete namespace response body: %s", string(bodyContent))

	response := &apiservice.BatchWriteResponse{}
	if err = jsonpb.UnmarshalString(string(bodyContent), response); err != nil {
		return nil, err
	}

	return response, nil
}
