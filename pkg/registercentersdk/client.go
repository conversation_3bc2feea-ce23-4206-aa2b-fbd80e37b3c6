package registercentersdk

import (
	"bytes"
	"fmt"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"io/ioutil"
	"net/http"
	"strings"
)

// Client 实现 ccev2.Interface
type Client struct {
	httpClient *http.Client
	Config     *Config
}

type Config struct {
	Region   string
	Endpoint string
}

// NewClient client of register center
func NewClient() *Client {
	return &Client{
		httpClient: &http.Client{},
		Config: &Config{
			Region:   "bj",
			Endpoint: "https://register.bce.baidu.com/v1/",
		},
	}
}

// GetURL generates the full URL of http request for Baidu Cloud API.
func (c *Client) GetURL(uriPath string, host string, params map[string]string) string {
	if strings.Index(uriPath, "/") == 0 {
		uriPath = uriPath[1:]
	}

	return util.GetURL("http", host, uriPath, params)
}

func (c *Client) SendRequest(ctx csmContext.CsmContext, req *Request, option *AuthOption) (registerResponse *Response, err error) {
	if req == nil {
		return nil, fmt.Errorf("request is nil")
	}

	if option == nil {
		return nil, fmt.Errorf("register center token is nil")
	}
	// set header
	req.Header.Add("X-Polaris-Token", option.Token)
	req.Header.Add("Content-Type", "application/json")

	// request body
	var buf []byte
	if req.Body != nil {
		buf, _ = ioutil.ReadAll(req.Body)
	}

	req.Body = ioutil.NopCloser(bytes.NewBuffer(buf))

	rawRequest := req.Raw()

	// invoke http request
	// todo err and retry
	resp, httpError := c.httpClient.Do(rawRequest)
	response := NewResponse(resp)
	ctx.CsmLogger().Infof("%s %s", req.Method, req.URL.String())

	if httpError != nil {
		return response, httpError
	}

	return response, nil
}
