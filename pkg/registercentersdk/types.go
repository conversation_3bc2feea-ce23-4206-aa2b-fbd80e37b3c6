package registercentersdk

import (
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type Interface interface {
	// GetRegisterCenterServiceList 获取 register center service list GET /naming/v1/services
	GetRegisterCenterServiceList(ctx csmContext.CsmContext, req *RegisterCenterServiceListRequest, option *AuthOption) (*RegisterCenterServiceListResponse, error)
	// CreateRegisterServiceInstance 创建 register service instance POST /naming/v1/instances
	CreateRegisterServiceInstance(ctx csmContext.CsmContext, req *CreateServiceInstanceRequest, option *AuthOption) error
	// UpdateRegisterServiceInstance 更新 register service instance PUT /naming/v1/instances
	UpdateRegisterServiceInstance(ctx csmContext.CsmContext, req *CreateServiceInstanceRequest, option *AuthOption) error
	// DeleteRegisterServiceInstance 删除 register service instance DELETE /naming/v1/instances
	DeleteRegisterServiceInstance(ctx csmContext.CsmContext, req *DeleteServiceInstanceRequest, option *AuthOption) error
	// GetServiceInstanceList 获取 service instance list GET /naming/v1/instances
	GetServiceInstanceList(ctx csmContext.CsmContext, req *ServiceInstanceListRequest, option *AuthOption) (*ServiceInstanceListResponse, error)
	// BatchDeleteServiceInstance 批量删除 service instance POST /naming/v1/instances/delete/host
	BatchDeleteServiceInstance(ctx csmContext.CsmContext, req []*BatchRequest, option *AuthOption) error
	// BatchIsolateServiceInstance 批量隔离 service instance /naming/v1/instances/isolate/host
	BatchIsolateServiceInstance(ctx csmContext.CsmContext, req []*BatchRequest, option *AuthOption) error
}

type BatchOptRequest struct {
	ServiceInstanceList []*BatchRequest `json:"serviceInstanceList"`
}

type BatchRequest struct {
	Host      string `json:"host"`
	Namespace string `json:"namespace"`
	Service   string `json:"service"`
	Id        string `json:"id"`
	Isolate   *bool  `json:"isolate,omitempty"`
}

type ServiceInstanceListRequest struct {
	Service   string `json:"service"`
	Offset    int    `json:"offset"`
	Limit     int    `json:"limit"`
	Id        string `json:"id"`
	Host      string `json:"host"`
	Healthy   string `json:"healthy"`
	Isolate   string `json:"isolate"`
	Namespace string `json:"namespace"`
}

type ServiceInstanceListResponse struct {
	Amount    int                            `json:"amount"`
	Code      int                            `json:"code"`
	Info      string                         `json:"info"`
	Instances []RegisterServiceInstanceModel `json:"instances"`
	Size      int                            `json:"size"`
}

type RegisterServiceInstanceModel struct {
	Id                string            `json:"id"`
	Service           string            `json:"service"`
	Namespace         string            `json:"namespace"`
	Host              string            `json:"host"`
	Port              int               `json:"port"`
	Healthy           bool              `json:"healthy"`
	Isolate           bool              `json:"isolate"`
	Location          InstanceLocation  `json:"location"`
	Metadata          map[string]string `json:"metadata"`
	EnableHealthCheck bool              `json:"enableHealthCheck"`
	HealthCheck       HealthCheck       `json:"healthCheck"`
	Priority          int               `json:"priority"`
	Protocol          string            `json:"protocol"`
	Revision          string            `json:"revision"`
	Version           string            `json:"version"`
	Weight            int               `json:"weight"`
	Ctime             string            `json:"ctime"`
	Mtime             string            `json:"mtime"`
}

type Heartbeat struct {
	Ttl int `json:"ttl"`
}

type HealthCheck struct {
	Type      string    `json:"type"`
	Heartbeat Heartbeat `json:"heartbeat"`
}

type DeleteServiceInstanceRequest struct {
	Id string `json:"id"`
}

type InstanceLocation struct {
	Region string `json:"region"`
	Zone   string `json:"zone"`
	Campus string `json:"campus"`
}

type CreateServiceInstanceRequest struct {
	Id                string            `json:"id"`
	Service           string            `json:"service"`
	Namespace         string            `json:"namespace"`
	Host              string            `json:"host"`
	Port              int               `json:"port"`
	Location          InstanceLocation  `json:"location"`
	Metadata          map[string]string `json:"metadata"`
	EnableHealthCheck bool              `json:"enableHealthCheck"`
	HealthCheck       *HealthCheck      `json:"healthCheck,omitempty"`
	Isolate           bool              `json:"isolate"`
	Priority          int               `json:"priority"`
	Protocol          string            `json:"protocol"`
	Version           string            `json:"version"`
	Weight            int               `json:"weight"`
	Healthy           *bool             `json:"healthy,omitempty"`
}

type CreateNamespaceRequest struct {
	Name    string `json:"name"`
	Comment string `json:"comment"`
}

type AuthOption struct {
	Token    string `json:"token"`
	Endpoint string `json:"endpoint"`
}

type RegisterCenterServiceListRequest struct {
	Name      string `json:"name"`
	Offset    int    `json:"offset"`
	Limit     int    `json:"limit"`
	Namespace string `json:"namespace"`
}

type RegisterCenterServiceListResponse struct {
	Amount   int                          `json:"amount"`
	Code     int                          `json:"code"`
	Info     string                       `json:"info"`
	Services []RegisterCenterServiceModel `json:"services"`
	Size     int                          `json:"size"`
}

type RegisterCenterNamespaceListRequest struct {
	Name   string `json:"name"`
	Offset int    `json:"offset"`
	Limit  int    `json:"limit"`
}

type RegisterCenterNamespaceListResponse struct {
	Amount   int                            `json:"amount"`
	Code     int                            `json:"code"`
	Info     string                         `json:"info"`
	Services []RegisterCenterNamespaceModel `json:"namespaces"`
	Size     int                            `json:"size"`
}

type RegisterCenterServiceModel struct {
	Id                   string            `json:"id"`
	Name                 string            `json:"name"`
	Namespace            string            `json:"namespace"`
	Business             string            `json:"business"`
	Department           string            `json:"department"`
	Comment              string            `json:"comment"`
	Metadata             map[string]string `json:"metadata"`
	Ports                string            `json:"ports"`
	ExportTo             []string          `json:"export_to"`
	TotalInstanceCount   int               `json:"total_instance_count"`
	HealthyInstanceCount int               `json:"healthy_instance_count"`
	Ctime                string            `json:"ctime"`
	Mtime                string            `json:"mtime"`
}

type RegisterCenterNamespaceModel struct {
	Name                     string `json:"name"`
	Comment                  string `json:"comment"`
	Ctime                    string `json:"ctime"`
	Mtime                    string `json:"mtime"`
	TotalServiceCount        int    `json:"total_service_count"`
	TotalHealthInstanceCount int    `json:"total_health_instance_count"`
	TotalInstanceCount       int    `json:"total_instance_count"`
}
