package registercentersdk

import (
	"fmt"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io/ioutil"
	"net/http"
	"strings"
)

// Response holds an instance of type `http response`, and has some custom data and functions.
type Response struct {
	BodyContent []byte
	*http.Response
}

func NewResponse(res *http.Response) *Response {
	return &Response{Response: res}
}

// GetBodyContent gets body from http response.
func (res *Response) GetBodyContent() ([]byte, error) {
	if res.BodyContent == nil {
		defer func() {
			if res.Response != nil {
				res.Body.Close()
			}
		}()

		body, err := ioutil.ReadAll(res.Body)

		if err != nil {
			return nil, err
		}

		res.BodyContent = body
	}

	return res.BodyContent, nil
}

func NewPolarisError(i interface{}) error {
	e := &echo.HTTPError{}
	switch response := i.(type) {
	case *apiservice.BatchQueryResponse:
		e.Code = int(response.GetCode().GetValue() / 1000)
		if e.Code == http.StatusOK {
			return nil
		}
		e.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(response.GetCode().GetValue())]),
			Message: response.GetInfo().GetValue(),
		}
	case *ServiceInstanceListResponse:
		e.Code = int(response.Code / 1000)
		if e.Code == http.StatusOK {
			return nil
		}
		e.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(response.Code)]),
			Message: response.Info,
		}
	case *apiservice.BatchWriteResponse:
		e.Code = int(response.GetCode().GetValue() / 1000)
		if e.Code == http.StatusOK {
			return nil
		}
		msg := make([]string, 0)
		for _, subResponse := range response.Responses {
			resourceName := ""
			if subResponse.Namespace != nil {
				resourceName = subResponse.GetNamespace().GetName().GetValue()
			} else if subResponse.Service != nil {
				resourceName = subResponse.GetService().GetName().GetValue()
			}
			if resourceName == "" {
				msg = []string{response.GetInfo().GetValue()}
				break
			}
			msg = append(msg, fmt.Sprintf("%s:%s", resourceName, subResponse.GetInfo().GetValue()))
		}
		e.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(response.GetCode().GetValue())]),
			Message: strings.Join(msg, ";"),
		}
	default:
		e = csmErr.NewUnknownError()
	}
	return e
}
