package registercentersdk

import (
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/jsonpb"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"strconv"
)

func (c *Client) GetRegisterCenterServiceList(ctx csmContext.CsmContext,
	req *RegisterCenterServiceListRequest, option *AuthOption) (*RegisterCenterServiceListResponse, error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, err
	}
	if accountId == "" {
		return nil, fmt.Errorf("empty accountId")
	}

	// querystring
	params := map[string]string{
		"offset": strconv.Itoa(req.Offset),
		"limit":  strconv.Itoa(req.Limit),
		"owner":  accountId,
	}

	if req.Name != "" {
		params["name"] = req.Name
	}
	if req.Namespace != "" {
		params["namespace"] = req.Namespace
	}

	url := fmt.Sprintf("/naming/v1/services")
	request, err := NewRequest("GET", c.GetURL(url, option.Endpoint, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, request, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r RegisterCenterServiceListResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) GetRegisterCenterNamespaceList(ctx csmContext.CsmContext,
	req *RegisterCenterNamespaceListRequest, option *AuthOption) (*apiservice.BatchQueryResponse, error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, err
	}
	if accountId == "" {
		return nil, fmt.Errorf("empty accountId")
	}

	// querystring
	params := map[string]string{
		"offset": strconv.Itoa(req.Offset),
		"limit":  strconv.Itoa(req.Limit),
		"owner":  accountId,
	}

	if req.Name != "" {
		params["name"] = req.Name
	}

	url := fmt.Sprintf("/naming/v1/namespaces")
	request, err := NewRequest("GET", c.GetURL(url, option.Endpoint, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, request, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	response := &apiservice.BatchQueryResponse{}
	if err = jsonpb.UnmarshalString(string(bodyContent), response); err != nil {
		return nil, err
	}

	return response, nil
}
