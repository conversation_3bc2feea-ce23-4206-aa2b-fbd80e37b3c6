package aiingress

import (
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// TestGetIngressInstancesWithPagination_RegionGrouping 测试按区域分组功能
func TestGetIngressInstancesWithPagination_RegionGrouping(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建模拟的上下文
	mockCtx := context.MockNewCsmContext()

	// 创建测试用的网关实例数据
	now := time.Now()
	testGateways := []*meta.AIGatewayInstanceModel{
		{
			BaseModel: dbutil.BaseModel{
				CreateTime: &now,
			},
			GatewayUUID:   "gateway-1",
			GatewayName:   "test-gateway-1",
			Region:        "bj",
			Namespace:     "istio-system-1",
			AccessAddress: "***********,",
			SrcProduct:    "test",
		},
		{
			BaseModel: dbutil.BaseModel{
				CreateTime: &now,
			},
			GatewayUUID:   "gateway-2",
			GatewayName:   "test-gateway-2",
			Region:        "gz",
			Namespace:     "istio-system-2",
			AccessAddress: "***********,",
			SrcProduct:    "test",
		},
		{
			BaseModel: dbutil.BaseModel{
				CreateTime: &now,
			},
			GatewayUUID:   "gateway-3",
			GatewayName:   "test-gateway-3",
			Region:        "bj",
			Namespace:     "istio-system-3",
			AccessAddress: "***********,",
			SrcProduct:    "test",
		},
	}

	service := &Service{}

	t.Run("test_buildAiGatewayFromModel", func(t *testing.T) {
		gateway := testGateways[0]
		status := constants.AIGatewayStatusRunning

		result := service.buildAiGatewayFromModel(mockCtx, gateway, status)

		assert.Equal(t, gateway.GatewayUUID, result.IngressId)
		assert.Equal(t, gateway.GatewayName, result.Name)
		assert.Equal(t, gateway.Region, result.Region)
		assert.Equal(t, gateway.Namespace, result.Namespace)
		assert.Equal(t, status, result.IngressStatus)
		assert.Equal(t, "***********", result.InternalIP)
	})

	t.Run("test_shouldIncludeGateway", func(t *testing.T) {
		gateway := meta.AiGateway{
			IngressStatus: constants.AIGatewayStatusRunning,
		}

		// 测试无状态筛选
		assert.True(t, service.shouldIncludeGateway(gateway, ""))

		// 测试匹配状态筛选
		assert.True(t, service.shouldIncludeGateway(gateway, constants.AIGatewayStatusRunning))

		// 测试不匹配状态筛选
		assert.False(t, service.shouldIncludeGateway(gateway, constants.AIGatewayStatusCreating))
	})
}

// TestRegionGroupingLogic 测试区域分组逻辑
func TestRegionGroupingLogic(t *testing.T) {
	// 模拟区域分组逻辑
	gatewayModels := []*meta.AIGatewayInstanceModel{
		{GatewayUUID: "gw1", Region: "bj"},
		{GatewayUUID: "gw2", Region: "gz"},
		{GatewayUUID: "gw3", Region: "bj"},
		{GatewayUUID: "gw4", Region: "sh"},
		{GatewayUUID: "gw5", Region: ""}, // 空区域，应该被跳过
	}

	regionGroups := make(map[string][]*meta.AIGatewayInstanceModel)
	for _, gateway := range gatewayModels {
		region := gateway.Region
		if region == "" {
			continue // 跳过空区域
		}
		regionGroups[region] = append(regionGroups[region], gateway)
	}

	// 验证分组结果
	assert.Equal(t, 3, len(regionGroups), "应该有3个区域")
	assert.Equal(t, 2, len(regionGroups["bj"]), "bj区域应该有2个网关")
	assert.Equal(t, 1, len(regionGroups["gz"]), "gz区域应该有1个网关")
	assert.Equal(t, 1, len(regionGroups["sh"]), "sh区域应该有1个网关")

	// 验证空区域被正确跳过
	_, exists := regionGroups[""]
	assert.False(t, exists, "空区域不应该存在于分组中")
}
