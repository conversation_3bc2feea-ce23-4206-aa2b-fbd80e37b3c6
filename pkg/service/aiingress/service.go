package aiingress

import (
	"bytes"
	"context"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"math/big"
	"net/http"
	"os"
	"os/exec"
	"path"
	"reflect"
	"strconv"
	"strings"
	"text/template"
	"time"

	eipSDK "github.com/baidubce/bce-sdk-go/services/eip"
	"github.com/baidubce/bce-sdk-go/services/endpoint"
	blbService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blb/service"
	modelEIP "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/eip"
	modelVPC "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/vpc"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/blb"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/vpc"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/aiservices"

	"github.com/spf13/viper"
	"k8s.io/client-go/tools/clientcmd"

	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/aigateway"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"

	"github.com/baidubce/bce-sdk-go/services/appblb"
	"github.com/golang-jwt/jwt"
	"gopkg.in/yaml.v3"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model"
	modelBlb "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/blb"
	certModel "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cert"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/gateway"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/jwtcert"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/monitor"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/deploy"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/discoveryselector"
	monitorService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/monitor"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/multiprotocol"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/namespace"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/vpcendpoint"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil/rollback"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/object"
	extensionsV1alpha1 "istio.io/client-go/pkg/apis/extensions/v1alpha1"
	"istio.io/client-go/pkg/apis/networking/v1alpha3"
	"istio.io/client-go/pkg/apis/security/v1beta1"
	"istio.io/client-go/pkg/apis/telemetry/v1alpha1"
	istioClient "istio.io/client-go/pkg/clientset/versioned"
	v1 "k8s.io/api/core/v1"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	k8sMeta "k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	k8sYaml "k8s.io/apimachinery/pkg/runtime/serializer/yaml"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/dynamic"
)

const (
	crdFieldCreationTimestamp = "CreationTimestamp"
	crdManagedFields          = "ManagedFields"
	crdManagedFieldTime       = "Time"
	notFoundStatusCode        = 800
	isExistStatusCode         = 900
)

type Service struct {
	opt                  *Option
	dao                  model.GatewayDaoInterface
	vpcService           vpc.ServiceInterface
	modelVpc             modelVPC.ServiceInterface
	eipServer            modelEIP.ServiceInterface
	blbService           blb.ServiceInterface
	modelBlb             modelBlb.ServiceInterface
	baendpoint           vpcendpoint.ServiceInterface
	instancesModel       instances.ServiceInterface
	gatewayModel         gateway.ServiceInterface
	aigatewayModel       aigateway.ServiceInterface
	aiServiceService     aiservices.ServiceInterface
	jwtCertModel         jwtcert.ServiceInterface
	clusterModel         cluster.ServiceInterface
	discoverySelector    discoveryselector.ServiceInterface
	cceService           cce.ClientInterface
	monitorModel         monitor.ServiceInterface
	monitorService       monitorService.ServiceInterface
	certModel            certModel.ServiceInterface
	versionService       version.ServiceInterface
	MultiProtocolService multiprotocol.ServiceInterface
	namespaceService     namespace.ServiceInterface
}

func NewAiIngressService(opt *Option) *Service {
	gormDB := opt.DB.DB
	return &Service{
		opt:                  opt,
		modelVpc:             modelVPC.NewService(modelVPC.NewOption()),
		vpcService:           vpc.NewVPCService(),
		eipServer:            modelEIP.NewService(modelEIP.NewOption()),
		modelBlb:             modelBlb.NewService(modelBlb.NewOption()),
		blbService:           blb.NewBlbService(blb.NewOption(gormDB)),
		baendpoint:           vpcendpoint.NewService(vpcendpoint.NewOption()),
		instancesModel:       instances.NewInstancesService(instances.NewOption(gormDB)),
		gatewayModel:         gateway.NewGatewayService(gateway.NewOption(gormDB)),
		aigatewayModel:       aigateway.NewAIGatewayService(aigateway.NewOption(gormDB)),
		jwtCertModel:         jwtcert.NewJwtCertService(jwtcert.NewOption(gormDB)),
		clusterModel:         cluster.NewClusterService(cluster.NewOption(gormDB)),
		discoverySelector:    discoveryselector.NewDiscoverySelectorService(discoveryselector.NewOption(gormDB)),
		cceService:           cce.NewClientService(),
		monitorModel:         monitor.NewService(monitor.NewOption()),
		certModel:            certModel.NewCertService(certModel.NewOption(gormDB)),
		versionService:       version.NewVersionService(version.NewOption()),
		MultiProtocolService: multiprotocol.NewService(),
		monitorService:       monitorService.NewMonitorService(monitorService.NewOption(gormDB)),
		namespaceService:     namespace.NewNamespaceService(namespace.NewOption(gormDB)),
		aiServiceService:     aiservices.NewAIServiceService(aiservices.NewOption(gormDB)),
	}
}

func (s *Service) DeleteHigress(ctx csmContext.CsmContext, clusterId string) (err error) {
	var gatewayInfo meta.GatewayModel

	gatewayInfo, err = s.deleteGateway(ctx, clusterId)
	if err != nil {
		return err
	}
	go func() {
		// 在百舸CCE集群中卸载istio
		ctx.CsmLogger().Infof("uninstall istio cluster on ==>cce<==")

		// 异步卸载，不阻塞主 goroutine
		err = s.UnInstallHigress(ctx, &gatewayInfo)
		if err != nil {
			ctx.CsmLogger().Errorf("uninstall istio failed, err: %v", err)

		}
	}()
	return err
}

func (s *Service) deleteGateway(ctx csmContext.CsmContext, clusterId string) (gatewayInfo meta.GatewayModel, err error) {
	// 开启事务，并在发生异常时回滚事务
	tx := s.opt.DB.Begin()
	defer func() {
		rollbackErr := rollback.Rollback(ctx, tx, err, recover())
		if rollbackErr != nil {
			err = rollbackErr
		}
	}()
	gwList, err := s.gatewayModel.GetGatewayInfoListByClusterID(ctx, clusterId)
	if err == nil && len(*gwList) == 0 {
		return gatewayInfo, csmErr.CrdIsNotExistError(notFoundStatusCode, "gateway not found")
	}
	gatewayInfo = (*gwList)[0]
	gatewayService := s.gatewayModel.WithTx(dbutil.NewDB(tx))
	err = gatewayService.DeleteGateway(ctx, gatewayInfo.InstanceUUID, gatewayInfo.GatewayUUID)
	tx.Commit()
	return gatewayInfo, err
}

func (s *Service) UnInstallHigress(ctx csmContext.CsmContext, gatewayInfo *meta.GatewayModel) error {
	// 获取网格卸载参数
	region := gatewayInfo.Region
	clusterID := gatewayInfo.ClusterUUID
	meshType := gatewayInfo.DeployMode
	higressNamespace := gatewayInfo.Namespace
	higressVersion := gatewayInfo.IstioVersion
	higressUUID := gatewayInfo.InstanceUUID
	// 获取 k8s client
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterID, meta.MeshType(meshType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}

	// deploy params
	deployParams := deploy.NewHigressParams(
		higressVersion,
		higressNamespace,
		region,
		higressUUID,
		clusterID)

	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("get pwd error %v", err)
		return err
	}
	deployParams.ConfigPath = path.Join(pwd, constants.Templates)
	deployParams.InstanceRegion = region
	deploy := deploy.NewDeploy(deployParams, k8sClient)
	deploy.PrimaryClient = k8sClient

	go func() {
		err = deploy.UnDeployHigress(ctx)
		if err != nil {
			ctx.CsmLogger().Errorf("Undeploy istio error %v", err)
		}
	}()

	return err
}

func (s *Service) CreateHigress(ctx csmContext.CsmContext, ingress *meta.GatewayModel, installParams *meta.IngressGatewayParams) (err error) {
	clusterId := ingress.ClusterUUID
	meshType := ingress.DeployMode
	higressNamespace := ingress.Namespace
	region := ingress.Region
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterId, meta.MeshType(meshType))

	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}
	// 构建deploy对象
	deployParams := deploy.NewHigressParams(
		ingress.IstioVersion,
		higressNamespace,
		region,
		ingress.InstanceUUID,
		clusterId)

	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("get pwd error %v", err)
		return err
	}
	deployParams.ConfigPath = path.Join(pwd, constants.Templates)
	deployParams.InstanceRegion = region
	newDeploy := deploy.NewDeploy(deployParams, k8sClient)
	newDeploy.PrimaryClient = k8sClient
	// 安装列表参数
	newDeploy.IngressGateways = make([]deploy.IngressGateway, len(installParams.IngressGatewayList))
	for i, igParam := range installParams.IngressGatewayList {
		newDeploy.IngressGateways[i] = deploy.IngressGateway{
			Name:      igParam.Name,
			TypeLabel: igParam.TypeLabel,
			BindPort:  igParam.BindPort,
			Replicas:  igParam.Replicas,
			BindVpc:   igParam.BindVpc,
		}
	}
	// 创建命名空间
	ns := &v1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: higressNamespace,
		},
	}
	_, err = k8sClient.Kube().CoreV1().Namespaces().Create(context.TODO(), ns, metav1.CreateOptions{})
	if kubeErrors.IsAlreadyExists(err) {
		_, err = k8sClient.Kube().CoreV1().Namespaces().Get(context.TODO(), higressNamespace, metav1.GetOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("get %s namespace err %v", higressNamespace, err)
			return err
		}
	}
	if err != nil {
		ctx.CsmLogger().Errorf("create or update %s namespace err %v", higressNamespace, err)
		return err
	}
	ctx.CsmLogger().Infof("create or update %s namespace successful", higressNamespace)

	//err = s.NewGateway(ctx, ingress)
	if err != nil {
		ctx.CsmLogger().Errorf("new gateway failed, err: %v", err)
		return err
	}
	go func() {
		ctx.CsmLogger().Infof("install higress for cluster on ==>cce<==")
		err = newDeploy.DeployHigress(ctx, k8sClient)
		if err != nil {
			ctx.CsmLogger().Errorf("install istio failed, err: %v", err)
		}
	}()
	return err
}

func (s *Service) NewGateway(ctx csmContext.CsmContext, ingress *meta.AIGatewayInstanceModel) (err error) {
	// 开启事务，并在发生异常时回滚事务
	tx := s.opt.DB.Begin()
	defer func() {
		rollbackErr := rollback.Rollback(ctx, tx, err, recover())
		if rollbackErr != nil {
			err = rollbackErr
		}
	}()
	// 在数据库中创建新的网关记录
	gatewayService := s.aigatewayModel.WithTx(dbutil.NewDB(tx))
	if err = gatewayService.NewAIGateway(ctx, &ingress); err != nil {
		return err
	}
	tx.Commit()
	return err
}

// CreateIngress 函数用于在指定的集群中创建Ingress实例
//
// 参数：
//
//	ctx: 上下文信息，类型为csmContext.CsmContext
//	ingress: 要创建的Ingress实例信息，类型为*meta.GatewayModel
//
// 返回值：
//
//	返回error类型，如果操作成功，则返回nil；如果操作失败，则返回相应的错误信息
//
// 说明：
//
//	首先开启一个数据库事务，然后检查是否已经存在已创建的网关实例，如果已存在，则终止操作并返回错误信息。
//	如果不存在，则在数据库中创建新的网关记录，并提交事务。最后启动一个异步的goroutine来在百舸CCE集群中安装istio。
func (s *Service) CreateIngress(ctx csmContext.CsmContext, ingress *meta.GatewayModel, installParams *meta.IngressGatewayParams) (err error) {
	// 安装istio所需参数获取
	clusterId := ingress.ClusterUUID
	istioVersion := ingress.IstioVersion
	meshType := ingress.DeployMode
	// 创建k8s客户端，独立网格类型
	region := ctx.Get(reg.ContextRegion).(string)
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterId, meta.MeshType(meshType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}
	// 根据集群版本，适配不同版本的istio
	clusterVersion, err := k8sClient.GetKubernetesVersion()
	csmlog.Infof("cluster version: %v", clusterVersion)
	if err != nil {
		ctx.CsmLogger().Errorf("GetKubernetesVersion error %v", err)
		return err
	}
	// 维护一个map，后续兼容不同版本直接修改 map映射关系
	var k8sToIstioMap = map[string]string{
		constants.K8sVersion20: constants.IstioVersion13,
		constants.K8sVersion22: constants.IstioVersion13,
		constants.K8sVersion24: constants.IstioVersion14,
		constants.K8sVersion26: constants.IstioVersion20,
		constants.K8sVersion28: constants.IstioVersion22,
		constants.K8sVersion30: constants.IstioVersion22,
	}

	istioVersion = getIstioVersion(clusterVersion.String(), k8sToIstioMap)

	ingress.IstioVersion = istioVersion
	// 开启事务，并在发生异常时回滚事务
	tx := s.opt.DB.Begin()
	defer func() {
		rollbackErr := rollback.Rollback(ctx, tx, err, recover())
		if rollbackErr != nil {
			err = rollbackErr
		}
	}()
	// 校验是否存在已创建的网关实例，一个集群实例仅允许创建一个网关
	gwList, err := s.gatewayModel.GetGatewayInfoListByClusterID(ctx, ingress.ClusterUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to check whether a gateway is already exist %v", err)
		return err
	} else if len(*gwList) > 0 {
		ctx.CsmLogger().Warn("fail to create gateway because a gateway instance is already exist")
		errorMessage := "fail to create gateway because a gateway instance is already exist"
		return csmErr.GatewayIsExistError(isExistStatusCode, errorMessage)
	}
	// 在数据库中创建新的网关记录
	gatewayService := s.gatewayModel.WithTx(dbutil.NewDB(tx))
	if err = gatewayService.NewGateway(ctx, ingress); err != nil {
		return err
	}
	tx.Commit()
	go func() {
		// 在百舸CCE集群中安装 istio
		ctx.CsmLogger().Infof("install istio cluster on ==>cce<==")
		// 异步安装 istio，不阻塞主 goroutine
		err = s.InstallIstio(ctx, ingress, installParams, k8sClient)
		if err != nil {
			ctx.CsmLogger().Errorf("install istio failed, err: %v", err)
		}
	}()
	return err
}

// InstallIstio 在CCE集群上安装Istio
//
// 参数：
//
//	ctx: 上下文信息，包含区域、账户ID等信息
//	ingress: Ingress模型指针，包含集群UUID、命名空间、集群名称、Istio版本等信息
//
// 返回值：
//
//	如果安装成功，则返回nil；如果安装失败，则返回相应的错误信息
func (s *Service) InstallIstio(ctx csmContext.CsmContext, ingress *meta.GatewayModel,
	installParams *meta.IngressGatewayParams, k8sClient kube.Client) error {
	// 安装istio所需参数获取
	ingressInstanceUUID := ingress.InstanceUUID
	ingressNamespace := ingress.Namespace
	clusterName := ingress.ClusterName
	clusterId := ingress.ClusterUUID
	istioVersion := ingress.IstioVersion
	//accountId := ingress.AccountId
	region := ctx.Get(reg.ContextRegion).(string)

	// 构建deploy对象
	deployParams := deploy.NewParams(
		istioVersion,
		ingressNamespace,
		region,
		ingressInstanceUUID,
		clusterName,
		clusterId)

	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("get pwd error %v", err)
		return err
	}
	deployParams.ConfigPath = path.Join(pwd, constants.Templates)
	deployParams.InstanceRegion = region
	newDeploy := deploy.NewDeploy(deployParams, k8sClient)
	newDeploy.PrimaryClient = k8sClient
	// 安装列表参数
	newDeploy.IngressGateways = make([]deploy.IngressGateway, len(installParams.IngressGatewayList))
	for i, igParam := range installParams.IngressGatewayList {
		newDeploy.IngressGateways[i] = deploy.IngressGateway{
			Name:      igParam.Name,
			TypeLabel: igParam.TypeLabel,
			BindPort:  igParam.BindPort,
			Replicas:  igParam.Replicas,
			BindVpc:   igParam.BindVpc,
		}
	}
	// 安装前先卸载 istio
	err = newDeploy.UnDeployIstioForBG(ctx)
	err = newDeploy.AddNamespace(ctx, s.getNamespaceLabels(region, ingressInstanceUUID, nil))
	if err != nil {
		ctx.CsmLogger().Errorf("Undeploy istio error %v", err)
		return err
	}
	ctx.CsmLogger().Infof("DeployIstio on %s", "CCE")
	err = newDeploy.DeployIstioForBG(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("DeployIstio error %v", err)
		return err
	}
	return err
}

// getIstioVersion 函数功能：获取与集群版本对应的Istio版本，如果不存在则返回默认版本（constants.IstioVersion14）
// 参数：
//
//	clusterVersion (string) - 集群版本号，格式为"vX.Y.Z"
//	k8sToIstioMap (map[string]string) - Kubernetes版本和Istio版本之间的映射关系，key为Kubernetes版本号，value为Istio版本号
//
// 返回值（string）：返回与集群版本对应的Istio版本，如果不存在则返回默认版本（constants.IstioVersion14）
func getIstioVersion(clusterVersion string, k8sToIstioMap map[string]string) string {
	for k8sVersion, istioVersion := range k8sToIstioMap {
		if strings.Contains(clusterVersion, k8sVersion) {
			return istioVersion
		}
	}
	return constants.IstioVersion14
}

// getNamespaceLabels 根据区域、网格实例ID和标签生成新的标签映射
//
// 参数：
//
//	region: 字符串类型，表示区域
//	meshInstanceId: 字符串类型，表示网格实例ID
//	labels: 字符串到字符串的映射，表示额外的标签
//
// 返回值：
//
//	返回一个新的字符串到字符串的映射，包含了区域、网格实例ID和所有传入的额外标签
func (s *Service) getNamespaceLabels(region, meshInstanceId string, labels map[string]string) map[string]string {
	tmpLabels := make(map[string]string)
	tmpLabels[constants.TopologyIstioIoNetWork] = region
	tmpLabels[constants.MeshInstanceId] = meshInstanceId
	for k, v := range labels {
		tmpLabels[k] = v
	}
	return tmpLabels
}

// DeleteIngress 从CCE集群中删除Ingress实例
//
// 参数：
//
//	ctx: 上下文信息，类型为csmContext.CsmContext
//	clusterId: 要删除的Ingress实例所在的集群ID，类型为string
//
// 返回值：
//
//	如果删除成功，则返回nil；如果删除失败，则返回相应的错误信息
func (s *Service) DeleteIngress(ctx csmContext.CsmContext, clusterId string) (err error) {
	// 开启事务，并在发生异常时回滚事务
	tx := s.opt.DB.Begin()
	defer func() {
		rollbackErr := rollback.Rollback(ctx, tx, err, recover())
		if rollbackErr != nil {
			err = rollbackErr
		}
	}()
	gwList, err := s.gatewayModel.GetGatewayInfoListByClusterID(ctx, clusterId)
	if err == nil && len(*gwList) == 0 {
		return csmErr.CrdIsNotExistError(notFoundStatusCode, "gateway not found")
	}
	gatewayInfo := (*gwList)[0]
	gatewayService := s.gatewayModel.WithTx(dbutil.NewDB(tx))
	err = gatewayService.DeleteGateway(ctx, gatewayInfo.InstanceUUID, gatewayInfo.GatewayUUID)
	tx.Commit()
	go func() {
		// 在百舸CCE集群中卸载istio
		ctx.CsmLogger().Infof("uninstall istio cluster on ==>cce<==")

		// 异步卸载 istio，不阻塞主 goroutine
		err = s.UnInstallIstio(ctx, &gatewayInfo)
		if err != nil {
			ctx.CsmLogger().Errorf("uninstall istio failed, err: %v", err)

		}
	}()
	return err
}

// UnInstallIstio 从CCE集群中卸载Istio
//
// 参数：
//
//	ctx: 上下文信息，类型为csmContext.CsmContext
//	gatewayInfo: 包含要卸载的Istio实例信息的GatewayModel指针
//
// 返回值：
//
//	如果卸载成功，则返回nil；如果卸载失败，则返回相应的错误信息
func (s *Service) UnInstallIstio(ctx csmContext.CsmContext, gatewayInfo *meta.GatewayModel) error {
	// 获取网格卸载参数
	region := gatewayInfo.Region
	clusterID := gatewayInfo.ClusterUUID
	meshType := gatewayInfo.DeployMode
	cluserName := gatewayInfo.ClusterName
	ingressNamespace := gatewayInfo.Namespace
	ingressIstioVersion := gatewayInfo.IstioVersion
	ingressInstanceUUID := gatewayInfo.InstanceUUID
	// 获取 k8s client
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterID, meta.MeshType(meshType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}

	// deploy params
	deployParams := deploy.NewParams(
		ingressIstioVersion,
		ingressNamespace,
		region,
		ingressInstanceUUID,
		cluserName,
		clusterID)

	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("get pwd error %v", err)
		return err
	}
	deployParams.ConfigPath = path.Join(pwd, constants.Templates)
	deployParams.InstanceRegion = region
	deploy := deploy.NewDeploy(deployParams, k8sClient)
	deploy.PrimaryClient = k8sClient
	// 卸载 istio
	err = deploy.UnDeployIstioForBG(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("Undeploy istio error %v", err)
		return err
	}

	return err
}

// GetIngressDetail 根据集群ID获取Ingress的详细信息
//
// 参数：
//
//	ctx: 上下文信息，类型为csmContext.CsmContext
//	clusterId: 集群ID，类型为string
//
// 返回值：
//
//	ingress: Ingress详细信息，类型为*meta.IngressDetail
//	err: 错误信息，如果获取成功则为nil，否则为具体的错误信息
func (s *Service) GetIngressDetail(ctx csmContext.CsmContext, clusterId string) (ingress *meta.IngressDetail, err error) {
	gatewayInfoList, err := s.gatewayModel.GetGatewayInfoListByClusterID(ctx, clusterId)
	var gatewayInfo *meta.GatewayModel
	if len(*gatewayInfoList) > 0 {
		gatewayInfo = &(*gatewayInfoList)[0]
	} else {
		return nil, csmErr.CrdIsNotExistError(notFoundStatusCode, "gateway not found")
	}

	ingressNamespace := gatewayInfo.Namespace
	ingressUUID := gatewayInfo.GatewayUUID
	istioVersion := gatewayInfo.IstioVersion
	gatewayType := gatewayInfo.GatewayType
	clusterId = gatewayInfo.ClusterUUID
	clusterName := gatewayInfo.ClusterName

	region := ctx.Get(reg.ContextRegion).(string)
	lbArgs := &appblb.DescribeLoadBalancersArgs{}
	lbArgs.Name = fmt.Sprintf("CCE/svc/%s/istio-system-ai/istio-ingressgateway", clusterId)
	blbListResult, err := s.modelBlb.GetAllBlb(ctx, region, lbArgs)
	csmlog.Infof("blbListResult: %+v", blbListResult)

	var blbInfo meta.BlbDetailResult
	if blbListResult != nil && len(blbListResult.BlbList) > 0 {
		// 遍历所有BLB实例，找到状态为Running的实例
		for _, blb := range blbListResult.BlbList {
			if blb.Name == lbArgs.Name {
				blbInfo = meta.BlbDetailResult{
					BlbId:    blb.BlbId,
					Name:     blb.Name,
					Address:  blb.Address,
					PublicIp: blb.PublicIp,
					Cidr:     blb.Address,
					VpcId:    blb.VpcId,
					SubnetId: blb.SubnetId,
				}
				break
			}
		}
	}

	var req meta.CreateVpcEndpointRequest
	req.VpcId = blbInfo.VpcId
	req.Protocol = "tcp"
	req.BackendPort = 80
	req.BackendIp = blbInfo.Address
	req.Name = "aihc-BAendpoint"
	req.Type = "vpc_inside"

	baEndpointResult, err := s.baendpoint.CreateVpcEndpoint(ctx, &req, region)
	csmlog.Infof("baEndpointResult: %+v", baEndpointResult)

	instanceDetail := meta.IngressDetail{
		Namespace:     ingressNamespace,
		IngressId:     ingressUUID,
		IstioVersion:  istioVersion,
		GatewayType:   gatewayType,
		IngressStatus: constants.SmiRunning,
		ClusterInfo: meta.ClusterInfo{
			ClusterId:   clusterId,
			ClusterName: clusterName,
		},
		BlbDetailResult: blbInfo,
		BAendpointResult: meta.VpcEndpoint{
			VpcEndpointId: baEndpointResult.VpcEndpointId,
			VpcId:         baEndpointResult.VpcId,
			Protocol:      baEndpointResult.Protocol,
			BackendIp:     baEndpointResult.BackendIp,
			BackendPort:   baEndpointResult.BackendPort,
			EndpointIp:    baEndpointResult.EndpointIp,
			EndpointPort:  baEndpointResult.EndpointPort,
			Name:          baEndpointResult.Name,
			Description:   baEndpointResult.Description,
			Type:          baEndpointResult.Type,
			Status:        baEndpointResult.Status,
		},
	}

	return &instanceDetail, nil
}

// GetIngressStatus 根据Istio Pods获取网格实例状态
//
// 参数：
// - ctx csmContext.CsmContext: 上下文对象，包含请求范围内的信息。
// - ingress *meta.GatewayModel: Ingress模型对象，包含Ingress的相关信息。
//
// 返回值：
// - string: Ingress的状态，可以是"Running"（正常运行）、"Deploying"（部署中）、"Abnormal"（异常）
// - error: 如果在获取Ingress状态过程中发生错误，则返回错误信息；否则返回nil。
func (s *Service) GetIngressStatus(ctx csmContext.CsmContext, ingress *meta.GatewayModel) (string, error) {
	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout*2)
	defer cancel()

	ingressStatus := constants.SmiAbnormal
	client, err := s.cceService.NewClient(ctx, ingress.Region, ingress.ClusterUUID, meta.MeshType(ingress.DeployMode))
	if err != nil {
		return ingressStatus, err
	}
	istiodPods, err := client.Kube().CoreV1().Pods(ingress.Namespace).List(
		cxt, metav1.ListOptions{LabelSelector: constants.IstioLabelSelector})
	if err != nil {
		return ingressStatus, err
	}
	if len(istiodPods.Items) == 0 {
		ingressStatus = constants.SmiDeploying
	} else {
		ingressStatus = constants.SmiRunning
		for _, pod := range istiodPods.Items {
			status := pod.Status.Phase
			if status == v1.PodRunning {
				continue
			}
			if status == v1.PodPending {
				ingressStatus = constants.SmiDeploying
			} else {
				reason := strings.ToLower(pod.Status.Reason)
				if reason == constants.SmiEvicted {
					continue
				}
				ingressStatus = constants.SmiAbnormal
			}
			break
		}
	}
	return ingressStatus, nil
}

// CreateCrdForBG 创建一个CRD
//
// 参数:
//
//	ctx: 上下文对象，包含请求信息和参数
//	param: 包含创建CRD所需参数的meta.CrdParam结构体指针
//
// 返回值:
//
//	crd: 创建成功的CRD对象指针
//	err: 如果创建过程中出现错误，则返回错误信息；否则返回nil
func (s *Service) CreateCrdForBG(ctx csmContext.CsmContext, param *meta.CrdParam) (crd *meta.Crd, err error) {
	client, err := s.cceService.NewClient(ctx, param.Region, param.ClusterID, meta.StandaloneMeshType)
	if err != nil || client == nil {
		csmlog.Warnf("NewClient error %v", err)
		return nil, err
	}
	ctx.CsmLogger().Infof("start create crd for bg=========")
	crd, err = s.createOneCrdForBG(ctx, client, param.Content)
	if crd == nil && err != nil {
		csmlog.Warnf("crd is nil")
		errStr := err.Error()
		if strings.Contains(errStr, "already exists") {
			return nil, csmErr.GatewayIsExistError(isExistStatusCode, err.Error())
		}
	}
	ctx.CsmLogger().Infof("crd for bg is========= %v", crd)
	if err != nil {
		csmlog.Warnf("createOneCrdForBG error %v", err)
		return nil, err
	}
	return crd, err
}

// createOneCrdForBG 为后台组创建一个CRD
//
// 参数:
//
//	ctx: 上下文对象，包含请求信息和参数
//	client: kube.Client类型的客户端对象，用于与Kubernetes集群进行交互
//	content: 包含CRD定义的YAML字符串
//
// 返回值:
//
//	crd: 创建成功的CRD对象指针
//	err: 如果创建过程中出现错误，则返回错误信息；否则返回nil
//
// 说明:
// 根据传入的YAML字符串content创建相应的CRD。通过解析YAML字符串，根据CRD的kind属性执行相应的创建操作。
func (s *Service) createOneCrdForBG(ctx csmContext.CsmContext, client kube.Client, content string) (crd *meta.Crd, err error) {
	if len(content) == 0 {
		return nil, errors.New("No crd to create")
	}
	// 解析YAML字符串，获取kind和namespace
	m := make(map[string]interface{})
	ctx.CsmLogger().Infof("content is %v", content)
	err = yaml.Unmarshal([]byte(content), &m)
	if err != nil {
		return nil, errors.New("The format of crd is invalid.")
	}
	var kind meta.Kind
	if k, ok := m["kind"]; ok {
		kind = meta.Kind(k.(string))
	}
	var namespace string
	if metadata, ok := m["metadata"]; ok {
		md := metadata.(map[string]interface{})
		if n, ok := md["namespace"]; ok {
			namespace = n.(string)
		}
		// istio类型的CRD，namespace默认为default。aeraki类型的可能为cluster级别的CRD，这里暂不修改
		if meta.IsRegisteredCrdKindForIstio(kind) && len(strings.TrimSpace(namespace)) == 0 {
			namespace = "default"
		}
	}
	// 根据kind创建相应的CRD
	istioClient := client.Istio()
	switch kind {
	case meta.VirtualService:
		target := &v1alpha3.VirtualService{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := istioClient.NetworkingV1alpha3().VirtualServices(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.DestinationRule:
		target := &v1alpha3.DestinationRule{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := istioClient.NetworkingV1alpha3().DestinationRules(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.EnvoyFilter:
		target := &v1alpha3.EnvoyFilter{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := istioClient.NetworkingV1alpha3().EnvoyFilters(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.Gateway:
		target := &v1alpha3.Gateway{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := istioClient.NetworkingV1alpha3().Gateways(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.ServiceEntry:
		target := &v1alpha3.ServiceEntry{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := istioClient.NetworkingV1alpha3().ServiceEntries(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.WorkloadEntry:
		target := &v1alpha3.WorkloadEntry{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := istioClient.NetworkingV1alpha3().WorkloadEntries(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.WorkloadGroup:
		target := &v1alpha3.WorkloadGroup{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := istioClient.NetworkingV1alpha3().WorkloadGroups(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.AuthorizationPolicy:
		target := &v1beta1.AuthorizationPolicy{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := istioClient.SecurityV1beta1().AuthorizationPolicies(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.PeerAuthentication:
		target := &v1beta1.PeerAuthentication{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := istioClient.SecurityV1beta1().PeerAuthentications(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.RequestAuthentication:
		target := &v1beta1.RequestAuthentication{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := istioClient.SecurityV1beta1().RequestAuthentications(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.Telemetry:
		target := &v1alpha1.Telemetry{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := istioClient.TelemetryV1alpha1().Telemetries(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.WasmPlugin:
		target := &extensionsV1alpha1.WasmPlugin{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := istioClient.ExtensionsV1alpha1().WasmPlugins(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	default:
		if s.isAllowedCrdKind(string(kind)) {
			dec := k8sYaml.NewDecodingSerializer(unstructured.UnstructuredJSONScheme)
			obj := &unstructured.Unstructured{}
			_, _, err = dec.Decode([]byte(content), nil, obj)
			if err != nil {
				return nil, fmt.Errorf("error decoding YAML file %s: %v", content, err)
			}
			gvk := obj.GroupVersionKind()
			gvr, _ := k8sMeta.UnsafeGuessKindToResource(gvk)
			ic, dyErr := client.Dynamic().Resource(gvr).Namespace(namespace).Create(context.TODO(), obj, metav1.CreateOptions{})
			if !isValidStructPointerNoError(ctx, ic, dyErr) {
				ctx.CsmLogger().Errorf("dynamic create dyErr is %s", dyErr.Error())
				return nil, dyErr
			}
			crd, err = convertToCrd(ic, namespace, ic.GetKind(), ic.GetName(), time.Now())
			return crd, err
		}
		return nil, errors.New("Not support kind %s")
	}
	return crd, nil
}

// isAllowedCrdKind 判断给定的 CRD 种类是否被允许
//
// 参数：
//
//	crdKind: 待检查的 CRD 种类，类型为字符串
//
// 返回值：
//
//	如果 crdKind 在允许的种类列表中，则返回 true；否则返回 false
//
// 说明：
//
//	遍历 Service 结构中的 crdAllowedKind 切片，检查 crdKind 是否存在于其中。
func (s *Service) isAllowedCrdKind(crdKind string) bool {
	for _, allowedKind := range s.opt.crdAllowedKind {
		if strings.EqualFold(allowedKind, crdKind) {
			return true
		}
	}
	return false
}

// isValidStructPointerNoError 用于断言传入的指针是否为非空指针，并且指向一个结构体。
//
// 参数：
// ctx csmContext.CsmContext: 上下文对象，用于记录日志。
// ptr interface{}: 需要断言的指针变量。
// err error: 与指针相关的错误对象。
//
// 返回值：
// bool: 如果ptr是一个非空指针，并且指向一个结构体，同时err为nil，则返回true；否则返回false。
func isValidStructPointerNoError(ctx csmContext.CsmContext, ptr interface{}, err error) bool {
	// 判断ptr是否为非空指针，并且指向一个结构体
	t := reflect.TypeOf(ptr)
	if t.Kind() != reflect.Ptr {
		ctx.CsmLogger().Errorf("All types must be pointers to structs.")
		return false
	}
	t = t.Elem()
	if t.Kind() != reflect.Struct {
		ctx.CsmLogger().Errorf("All types must be pointers to structs.")
		return false
	}

	if err != nil || ptr == nil {
		return false
	}

	return true
}

// convertToCrd 将Istio CRD转换为Crd模型
//
// 参数:
//
//	istioCrd: Istio CRD对象
//	namespace: CRD所属的命名空间
//	kind: CRD的类型
//	name: CRD的名称
//	updatedAt: CRD的更新时间
//
// 返回值:
//
//	*meta.Crd: 转换后的Crd模型对象指针
//	error: 如果转换过程中出现错误，则返回错误信息；否则返回nil
func convertToCrd(istioCrd interface{}, namespace, kind, name string, updatedAt time.Time) (*meta.Crd, error) {
	// Convert istio crd to crd model
	crdYaml, err := util.StructToYamlString(istioCrd)
	csmlog.Infof("Start to convert StructToYamlString .............. %s", crdYaml)
	if err != nil {
		return nil, err
	}
	return &meta.Crd{
		Namespace: namespace,
		Kind:      kind,
		Name:      name,
		UpdatedAt: updatedAt,
		Content:   crdYaml,
	}, nil
}

// DeleteCrdForBG 函数用于删除指定的CrdForBG对象。
//
// 参数：
// ctx csmContext.CsmContext: 上下文对象，用于记录日志。
// crd *meta.CrdForBG: 需要删除的CrdForBG对象。
func (s *Service) DeleteCrdForBG(ctx csmContext.CsmContext, crd *meta.CrdParam) error {
	client, err := s.cceService.NewClient(ctx, crd.Region, crd.ClusterID, meta.StandaloneMeshType)
	ck := crd.CrdKey
	if err != nil || client == nil {
		return err
	}
	err = s.deleteCrdForBG(client, ck.Namespace, ck.Kind, ck.Name)
	errStr := err.Error()
	if strings.Contains(errStr, "not found") {
		return csmErr.CrdIsNotExistError(notFoundStatusCode, err.Error())
	}
	return err
}

// deleteCrdForBG 根据指定的命名空间、类型和名称删除Istio CRD
//
// 参数:
//
//	client: kube.Client类型的客户端对象，用于与Kubernetes集群进行交互
//	namespace: CRD所属的命名空间
//	kind: CRD的类型，对应Istio CRD的类型
//	name: CRD的名称
//
// 返回值:
//
//	error: 如果删除过程中出现错误，则返回错误信息；否则返回nil
func (s *Service) deleteCrdForBG(client kube.Client, namespace string, kind string, name string) (err error) {
	istioClient := client.Istio()
	switch kind {
	case string(meta.Sidecar):
		err = istioClient.NetworkingV1alpha3().Sidecars(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.VirtualService):
		err = istioClient.NetworkingV1alpha3().VirtualServices(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.DestinationRule):
		err = istioClient.NetworkingV1alpha3().DestinationRules(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.EnvoyFilter):
		err = istioClient.NetworkingV1alpha3().EnvoyFilters(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.Gateway):
		err = istioClient.NetworkingV1alpha3().Gateways(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.ServiceEntry):
		err = istioClient.NetworkingV1alpha3().ServiceEntries(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.WorkloadEntry):
		err = istioClient.NetworkingV1alpha3().WorkloadEntries(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.WorkloadGroup):
		err = istioClient.NetworkingV1alpha3().WorkloadGroups(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.AuthorizationPolicy):
		err = istioClient.SecurityV1beta1().AuthorizationPolicies(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.PeerAuthentication):
		err = istioClient.SecurityV1beta1().PeerAuthentications(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.RequestAuthentication):
		err = istioClient.SecurityV1beta1().RequestAuthentications(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.Telemetry):
		err = istioClient.TelemetryV1alpha1().Telemetries(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.WasmPlugin):
		err = istioClient.ExtensionsV1alpha1().WasmPlugins(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	default:
		return errors.New("Not support kind")
	}
	return err
}

// GetCrd 函数用于从指定的上下文中获取一个自定义资源定义（CRD）对象。
//
// 参数：
// ctx - csmContext.CsmContext 类型，表示当前的上下文环境。
// param - *meta.CrdParam 类型，包含获取 CRD 对象所需的参数，如区域、集群 ID、命名空间、资源类型和名称。
//
// 返回值：
// crd - *meta.Crd 类型，表示获取到的 CRD 对象。如果获取失败，则为 nil。
// err - error 类型，表示在获取过程中发生的错误。如果获取成功，则为 nil。
func (s *Service) GetCrd(ctx csmContext.CsmContext, param *meta.CrdParam) (crd *meta.Crd, err error) {
	k8sClient, err := s.cceService.NewClient(ctx, param.Region, param.ClusterID, meta.StandaloneMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to create k8s client: %v................", err)
		return nil, err
	}

	ctx.CsmLogger().Infof("begin get crd.......clusterId %s................................", param.ClusterID)

	crd, err = s.getCrd(ctx, k8sClient.Istio(), param.Namespace, meta.Kind(param.Kind), param.Name)
	if err != nil {
		ctx.CsmLogger().Warnf("getCrd error %v", err)
		return nil, err
	}

	return crd, nil
}

// getCrd 函数从给定的上下文中获取一个自定义资源定义（CRD）对象。
//
// 参数：
// ctx - csmContext.CsmContext 类型，表示当前的上下文环境。
// client - istioClient.Interface 类型，表示与 Istio API 交互的客户端接口。
// namespace - string 类型，表示资源所在的命名空间。
// kind - meta.Kind 类型，表示要获取的 CRD 对象的类型。
// name - string 类型，表示要获取的 CRD 对象的名称。
//
// 返回值：
// crd - *meta.Crd 类型，表示获取到的 CRD 对象。如果获取失败，则为 nil。
// err - error 类型，表示在获取过程中发生的错误。如果获取成功，则为 nil。
func (s *Service) getCrd(ctx csmContext.CsmContext, client istioClient.Interface, namespace string, kind meta.Kind, name string) (
	crd *meta.Crd, err error) {
	istioCrd, err := getIstioCrd(ctx, client, namespace, kind, name)
	if istioCrd == nil && err != nil {
		csmlog.Warnf("istioCrd is nil")
		errStr := err.Error()
		if strings.Contains(errStr, "not found") {
			return nil, csmErr.CrdIsNotExistError(notFoundStatusCode, errStr)
		}
	}

	csmlog.Infof("Start to convert crd .............. %s", istioCrd)
	if err != nil {
		csmlog.Warnf("getIstioCrd error %v", err)
		return nil, err
	}
	csmlog.Infof("Start to reflect istioCrd")
	v := reflect.ValueOf(istioCrd)
	csmlog.Infof("reflect istioCrd.............. %s", istioCrd)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	} else if v.Kind() != reflect.Struct {
		csmlog.Warnf("Cannot reflect to struct type")
		return nil, errors.New("Cannot reflect to struct type")
	}

	// 获取更新时间
	updatedAt := calUpdatedTime(istioCrd)

	// 添加 CRD
	csmlog.Infof("Start to convertToCrd .............. %s", istioCrd)
	crd, err = convertToCrd(istioCrd, namespace, string(kind), name, *updatedAt)
	if err != nil {
		csmlog.Warnf("convertToCrd error %v", err)
		return nil, err
	}
	return crd, nil
}

// calUpdatedTime 用于计算并返回一个给定自定义资源定义（Custom Resource Definition, CRD）对象的更新时间。
//
// 参数：
//
//	crd: interface{} 类型，表示自定义资源定义的对象。
//
// 返回值：
//
//	*time.Time 类型，表示自定义资源定义的更新时间。如果无法确定更新时间，则返回 nil。
func calUpdatedTime(crd interface{}) *time.Time {
	v := reflect.ValueOf(crd)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	} else if v.Kind() != reflect.Struct {
		return nil
	}
	creationTime := v.FieldByName(crdFieldCreationTimestamp).Interface().(metav1.Time)
	updatedAt := creationTime.Time
	itemsValue := v.FieldByName(crdManagedFields)
	for i := 0; i < itemsValue.Len(); i++ {
		item := itemsValue.Index(i)
		updatedTime := item.FieldByName(crdManagedFieldTime).Interface().(*metav1.Time)
		if updatedTime.Time.After(updatedAt) {
			updatedAt = updatedTime.Time
		}
	}
	return &updatedAt
}

// getIstioCrd 函数根据给定的上下文、客户端接口、命名空间、资源类型和名称，获取 Istio 自定义资源定义（CRD）对象。
//
// 参数：
// ctx - csmContext.CsmContext 类型，表示当前的上下文环境。
// client - istioClient.Interface 类型，表示与 Istio API 交互的客户端接口。
// namespace - string 类型，表示资源所在的命名空间。
// kind - meta.Kind 类型，表示要获取的资源类型。
// name - string 类型，表示要获取的资源名称。
//
// 返回值：
// istioCrd - interface{} 类型，表示获取到的 Istio CRD 对象。如果获取失败，则为 nil。
// err - error 类型，表示在获取过程中发生的错误。如果获取成功，则为 nil。
func getIstioCrd(ctx csmContext.CsmContext, client istioClient.Interface, namespace string, kind meta.Kind, name string) (
	istioCrd interface{}, err error) {
	var ic interface{}

	switch kind {
	case meta.DestinationRule:
		ic, err = client.NetworkingV1beta1().DestinationRules(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.EnvoyFilter:
		ic, err = client.NetworkingV1alpha3().EnvoyFilters(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.Gateway:
		ic, err = client.NetworkingV1beta1().Gateways(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.ServiceEntry:
		ic, err = client.NetworkingV1beta1().ServiceEntries(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.Sidecar:
		ic, err = client.NetworkingV1beta1().Sidecars(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.VirtualService:
		ic, err = client.NetworkingV1beta1().VirtualServices(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.WorkloadEntry:
		ic, err = client.NetworkingV1beta1().WorkloadEntries(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.WorkloadGroup:
		ic, err = client.NetworkingV1beta1().WorkloadGroups(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.AuthorizationPolicy:
		ic, err = client.SecurityV1beta1().AuthorizationPolicies(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.PeerAuthentication:
		ic, err = client.SecurityV1beta1().PeerAuthentications(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.RequestAuthentication:
		ic, err = client.SecurityV1beta1().RequestAuthentications(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.Telemetry:
		ic, err = client.TelemetryV1alpha1().Telemetries(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.WasmPlugin:
		ic, err = client.ExtensionsV1alpha1().WasmPlugins(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	default:
		csmlog.Warnf("not support crd kind: %s", kind)
		return nil, fmt.Errorf("not support crd kind: %s", kind)
	}
	if !isValidStructPointerNoError(ctx, ic, err) {
		csmlog.Warnf("isValidStructPointerNoError failed: %v", err)
		return nil, err
	}
	return ic, nil
}

// UpdateCrd 更新 Istio 自定义资源（CRD）对象。
//
// 参数：
// ctx - csmContext.CsmContext 类型，表示当前的上下文环境。
// client - istioClient.Interface 类型，表示与 Istio API 交互的客户端接口。
// crd - *meta.Crd 类型，表示要更新的 CRD 对象。
//
// 返回值：
// err - error 类型，表示在更新过程中发生的错误。如果更新成功，则为 nil。
func (s *Service) UpdateCrd(ctx csmContext.CsmContext, param *meta.CrdParam) (crd *meta.Crd, err error) {
	k8sClient, err := s.cceService.NewClient(ctx, param.Region, param.ClusterID, meta.StandaloneMeshType)
	if err != nil {
		return nil, err
	}

	crd, err = s.updateCrd(ctx, k8sClient, param.Namespace, param.Kind, param.Name, param.Content)
	if crd == nil && err != nil {
		csmlog.Warnf("crd is nil")
		errStr := err.Error()
		if strings.Contains(errStr, "not found") {
			return nil, csmErr.CrdIsNotExistError(notFoundStatusCode, err.Error())
		}
	}
	if err != nil {
		return nil, err
	}
	crd.Kind = param.Kind
	return crd, nil

}

// updateCrd 函数用于更新 Kubernetes 集群中的自定义资源。
//
// 参数：
// - ctx: csmContext.CsmContext 类型的上下文对象，包含请求的相关信息。
// - k8sClient: kube.Client 类型的 Kubernetes 客户端，用于与集群进行交互。
// - namespace: string 类型的命名空间，指定要操作的 CRD 所在的命名空间。
// - kind: string 类型的 CRD 种类。
// - name: string 类型的 CRD 名称。
// - content: string 类型的 YAML 内容，包含要更新的 CRD 定义。
//
// 返回值：
// - crd: *meta.Crd 类型的指针，指向更新后的 CRD 对象。
// - err: error 类型的错误对象，如果发生错误则返回错误信息，否则为 nil。
// nolint:gocyclo
func (s *Service) updateCrd(ctx csmContext.CsmContext, k8sClient kube.Client, namespace,
	kind, name, content string) (crd *meta.Crd, err error) {
	client := k8sClient.Istio()

	switch kind {
	case string(meta.VirtualService):
		target := &v1alpha3.VirtualService{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}
		oc, err := client.NetworkingV1alpha3().VirtualServices(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !isValidStructPointerNoError(ctx, oc, err) {
			return nil, err
		}
		target.ResourceVersion = oc.ResourceVersion
		ic, err := client.NetworkingV1alpha3().VirtualServices(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.DestinationRule):
		target := &v1alpha3.DestinationRule{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}
		oc, err := client.NetworkingV1alpha3().DestinationRules(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !isValidStructPointerNoError(ctx, oc, err) {
			return nil, err
		}
		target.ResourceVersion = oc.ResourceVersion
		ic, err := client.NetworkingV1alpha3().DestinationRules(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.EnvoyFilter):
		target := &v1alpha3.EnvoyFilter{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}
		oc, err := client.NetworkingV1alpha3().EnvoyFilters(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !isValidStructPointerNoError(ctx, oc, err) {
			return nil, err
		}
		target.ResourceVersion = oc.ResourceVersion
		ic, err := client.NetworkingV1alpha3().EnvoyFilters(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.Gateway):
		target := &v1alpha3.Gateway{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}
		oc, err := client.NetworkingV1alpha3().Gateways(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !isValidStructPointerNoError(ctx, oc, err) {
			return nil, err
		}
		target.ResourceVersion = oc.ResourceVersion
		ic, err := client.NetworkingV1alpha3().Gateways(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.ServiceEntry):
		target := &v1alpha3.ServiceEntry{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}
		oc, err := client.NetworkingV1alpha3().ServiceEntries(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !isValidStructPointerNoError(ctx, oc, err) {
			return nil, err
		}
		target.ResourceVersion = oc.ResourceVersion
		ic, err := client.NetworkingV1alpha3().ServiceEntries(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.WorkloadEntry):
		target := &v1alpha3.WorkloadEntry{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}
		oc, err := client.NetworkingV1alpha3().WorkloadEntries(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !isValidStructPointerNoError(ctx, oc, err) {
			return nil, err
		}
		target.ResourceVersion = oc.ResourceVersion
		ic, err := client.NetworkingV1alpha3().WorkloadEntries(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.WorkloadGroup):
		target := &v1alpha3.WorkloadGroup{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}
		oc, err := client.NetworkingV1alpha3().WorkloadGroups(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !isValidStructPointerNoError(ctx, oc, err) {
			return nil, err
		}
		target.ResourceVersion = oc.ResourceVersion
		ic, err := client.NetworkingV1alpha3().WorkloadGroups(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.AuthorizationPolicy):
		target := &v1beta1.AuthorizationPolicy{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}
		oc, err := client.SecurityV1beta1().AuthorizationPolicies(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !isValidStructPointerNoError(ctx, oc, err) {
			return nil, err
		}
		target.ResourceVersion = oc.ResourceVersion
		ic, err := client.SecurityV1beta1().AuthorizationPolicies(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.PeerAuthentication):
		target := &v1beta1.PeerAuthentication{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}
		oc, err := client.SecurityV1beta1().PeerAuthentications(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !isValidStructPointerNoError(ctx, oc, err) {
			return nil, err
		}
		target.ResourceVersion = oc.ResourceVersion
		ic, err := client.SecurityV1beta1().PeerAuthentications(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.RequestAuthentication):
		target := &v1beta1.RequestAuthentication{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}
		oc, err := client.SecurityV1beta1().RequestAuthentications(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !isValidStructPointerNoError(ctx, oc, err) {
			return nil, err
		}
		target.ResourceVersion = oc.ResourceVersion
		ic, err := client.SecurityV1beta1().RequestAuthentications(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.Telemetry):
		target := &v1alpha1.Telemetry{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}
		oc, err := client.TelemetryV1alpha1().Telemetries(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !isValidStructPointerNoError(ctx, oc, err) {
			return nil, err
		}
		target.ResourceVersion = oc.ResourceVersion
		ic, err := client.TelemetryV1alpha1().Telemetries(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.WasmPlugin):
		target := &extensionsV1alpha1.WasmPlugin{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}
		oc, err := client.ExtensionsV1alpha1().WasmPlugins(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !isValidStructPointerNoError(ctx, oc, err) {
			return nil, err
		}
		target.ResourceVersion = oc.ResourceVersion
		ic, err := client.ExtensionsV1alpha1().WasmPlugins(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !isValidStructPointerNoError(ctx, ic, err) {
			return nil, err
		}
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	default:
		if s.isAllowedCrdKind(kind) {
			dec := k8sYaml.NewDecodingSerializer(unstructured.UnstructuredJSONScheme)
			obj := &unstructured.Unstructured{}
			_, _, err = dec.Decode([]byte(content), nil, obj)
			if err != nil {
				return nil, fmt.Errorf("error decoding YAML file %s: %v", content, err)
			}
			gvk := obj.GroupVersionKind()
			gvr, _ := k8sMeta.UnsafeGuessKindToResource(gvk)
			var dynamicClient dynamic.ResourceInterface
			if namespace == "" {
				dynamicClient = k8sClient.Dynamic().Resource(gvr)
			} else {
				dynamicClient = k8sClient.Dynamic().Resource(gvr).Namespace(namespace)
			}
			ic, dyErr := dynamicClient.Update(context.TODO(), obj, metav1.UpdateOptions{})
			if !isValidStructPointerNoError(ctx, ic, dyErr) {
				return nil, dyErr
			}
			crd, err = convertToCrd(ic, namespace, ic.GetKind(), ic.GetName(), time.Now())
			return crd, err
		}
		return nil, errors.New("Not support kind")
	}

	return crd, nil
}

// GetJwtToken 函数用于生成JWT令牌
//
// 参数：
//
//	param *meta.TokenParam：令牌参数，包含公钥路径、KID、发行者、主题和服务名称等信息
//	tokenMap map[string]string：令牌映射表，用于存储生成的JWT令牌
//
// 返回值：
//
//	jwtToken *meta.JwtToken：生成的JWT令牌，包含JWK和令牌映射表
//	err error：如果函数执行过程中发生错误，则返回错误信息
func (s *Service) GetJwtToken(ctx csmContext.CsmContext, param *meta.TokenParam, tokenMap map[string]string) (jwtToken *meta.JwtToken, err error) {
	// 开启事务，并在发生异常时回滚事务
	tx := s.opt.DB.Begin()
	defer func() {
		rollbackErr := rollback.Rollback(ctx, tx, err, recover())
		if rollbackErr != nil {
			ctx.CsmLogger().Errorf("fail to commit transaction %v", err)
			err = rollbackErr
		}
	}()
	jwtCert, _ := s.jwtCertModel.GetJwtCertByClusterID(ctx, param.ClusterID)
	var publicKeyPem, privateKeyPem string
	if jwtCert == nil {
		// 生成公钥 Key
		tokenParam, err := generatePublicKey(ctx, param)
		if err != nil {
			return nil, errors.New("error loading public key")
		}
		publicKeyPem = tokenParam.PublicKey
		privateKeyPem = tokenParam.PrivateKey
		// 在数据库中创建新的记录
		jwtCertService := s.jwtCertModel.WithTx(dbutil.NewDB(tx))
		if err = jwtCertService.NewJwtCert(ctx, &meta.JwtCert{
			Region:        param.Region,
			ClusterUUID:   param.ClusterID,
			AccountId:     param.AccountID,
			PrivateKeyPem: privateKeyPem,
			PublicKeyPem:  publicKeyPem,
			Deleted:       csm.Int(0),
		}); err != nil {
			return nil, errors.New("fail to create new jwt")
		}
	} else {
		publicKeyPem = jwtCert.PublicKeyPem
		privateKeyPem = jwtCert.PrivateKeyPem
	}

	tx.Commit()

	publicKey, err := parsePublicKey(publicKeyPem)
	jwk := publicKeyToJWK(publicKey, param.KID)

	token, err := createToken(param, privateKeyPem)
	if err != nil {
		return nil, errors.New("error creating JWT token")
	}
	tokenMap[fmt.Sprintf("%s/%s/%s", param.KID, param.Issuer, param.Subject)] = "Bearer " + token

	jwtToken = &meta.JwtToken{
		JWK:   jwk,
		Token: tokenMap,
	}
	return jwtToken, err
}
func generatePublicKey(ctx csmContext.CsmContext, param *meta.TokenParam) (*meta.TokenParam, error) {
	// 1. 生成私钥
	var privateOut bytes.Buffer
	cmdGenKey := exec.Command("openssl", "genpkey",
		"-algorithm", "RSA",
		"-pkeyopt", "rsa_keygen_bits:2048")
	cmdGenKey.Stdout = &privateOut
	if err := cmdGenKey.Run(); err != nil {
		return nil, err
	}

	// 2. 提取公钥
	var publicOut bytes.Buffer
	cmdPubKey := exec.Command("openssl", "rsa", "-pubout")
	cmdPubKey.Stdin = bytes.NewReader(privateOut.Bytes())
	cmdPubKey.Stdout = &publicOut

	if err := cmdPubKey.Run(); err != nil {
		return nil, err
	}
	// 3. 将得到的私钥、公钥字符串保存到结构体中
	param.PrivateKey = privateOut.String()
	param.PublicKey = publicOut.String()
	return param, nil
}

// 解析私钥
func parsePrivateKey(pemStr string) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode([]byte(pemStr))
	key, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	rsaKey := key.(*rsa.PrivateKey)
	return rsaKey, err
}

// 解析公钥
func parsePublicKey(pemStr string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(pemStr))
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	rsaPub := pub.(*rsa.PublicKey)
	return rsaPub, err
}

// publicKeyToJWK 函数将RSA公钥转换为JWK（JSON Web Key）格式
//
// 参数：
//
//	key *rsa.PublicKey：待转换的RSA公钥
//	kid string：密钥ID
//
// 返回值：
//
//	map[string]interface{}：转换后的JWK对象
func publicKeyToJWK(key *rsa.PublicKey, kid string) map[string]interface{} {
	n := base64.RawURLEncoding.EncodeToString(key.N.Bytes())
	e := base64.RawURLEncoding.EncodeToString(big.NewInt(int64(key.E)).Bytes())

	return map[string]interface{}{
		"kty": "RSA",
		"use": "sig",
		"kid": kid,
		"alg": "RS256",
		"n":   n,
		"e":   e,
	}
}

// createToken 函数根据给定的配置参数生成JWT令牌
//
// 参数：
//
//	config *meta.TokenParam：包含生成JWT令牌所需参数的配置对象
//
// 返回值：
//
//	string：生成的JWT令牌字符串
//	error：如果发生错误，则返回错误信息
func createToken(config *meta.TokenParam, privateKeyPem string) (string, error) {

	privateKey, err := parsePrivateKey(privateKeyPem)

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": config.Issuer,
		"sub": config.Subject,
		"iat": time.Now().Unix(),
	})

	token.Header["kid"] = config.KID

	tokenStr, err := token.SignedString(privateKey)
	if err != nil {
		return "", errors.New("error signing token")
	}

	return tokenStr, err
}

func (s *Service) DeleteAIGateway(ctx csmContext.CsmContext, instanceId string) (err error) {
	// 获取网关实例信息
	gatewayInfo, err := s.aigatewayModel.GetAIGatewayInfo(ctx, instanceId, instanceId)
	if gatewayInfo == nil || err != nil {
		return csmErr.NewResourceNotFoundException("Gateway not found")
	}
	hostedClusterId := (*gatewayInfo).HostedClusterID
	// 1. 检查是否开启删除保护
	if (*gatewayInfo).DeletionProtection {
		//return csmErr.NewInvalidParameterValueException("实例已开启删除保护，无法被删除")
		return errors.New("实例已开启删除保护，无法被删除")
	}

	// 2. 检查实例是否有关联服务
	hasServices, err := s.checkInstanceHasServices(ctx, instanceId, hostedClusterId)
	if err != nil {
		return err
	}
	if hasServices {
		return csmErr.NewInvalidParameterInputValueException("实例中仍有服务，无法被删除")
		//return errors.New("实例中仍有服务，无法被删除")
	}

	// 3. 检查实例是否有关联路由
	hasRoutes, err := s.checkInstanceHasRoutes(ctx, instanceId, hostedClusterId)
	if err != nil {
		return err
	}
	if hasRoutes {
		return csmErr.NewInvalidParameterInputValueException("实例中仍有路由，无法被删除")
		//return errors.New("实例中仍有路由，无法被删除")
	}

	// 调用模型层删除网关
	err = s.aigatewayModel.DeleteAIGateway(ctx, instanceId, instanceId)
	if err != nil {
		return err
	}

	region := (*gatewayInfo).Region
	clusterId := (*gatewayInfo).AddedClusterID
	gatewayNamespace := (*gatewayInfo).Namespace
	accountId := (*gatewayInfo).AccountID
	subnetId := (*gatewayInfo).SubnetID
	securityGroupId := (*gatewayInfo).SecurityGroupId

	// 创建k8s客户端
	hostingClient, err := s.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create cluster client, region=%s, clusterId=%s, err=%v",
			region, clusterId, err)
	}

	vpcCidr := (*gatewayInfo).VpcNetworkID

	// 读取Higress安装模板
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get current directory, err=%v", err)
	}

	// 使用新的模板文件
	higressTemplatePath := path.Join(pwd, "templates/higress/hosting/higress-install.tmpl")
	templateData, err := os.ReadFile(higressTemplatePath)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to read Higress template file, err=%v", err)
	}

	// 准备模板数据
	data := meta.HigressTemplateData{
		Namespace:        gatewayNamespace,
		AccountId:        accountId,
		SubnetId:         subnetId,
		SecurityGroupIds: "g-u1m2x004q04y", // 默认安全组
		VpcCidr:          vpcCidr,
		IsInternal:       "true",
		Replicas:         1,
	}

	// 渲染模板
	tmpl, err := template.New("higress").Parse(string(templateData))
	if err != nil {
		ctx.CsmLogger().Errorf("failed to parse template, err=%v", err)
	}

	var rendered bytes.Buffer
	if err := tmpl.Execute(&rendered, data); err != nil {
		ctx.CsmLogger().Errorf("failed to render template, err=%v", err)
	}

	// 卸载Higress
	go func() {

		higressObjects, err := object.ManifestK8sObject(ctx, rendered.String())
		if err != nil {
			ctx.CsmLogger().Errorf("failed to parse Higress template manifest, err=%v", err)
		}
		err = kube.DeleteResources(ctx, hostingClient, higressObjects)
		if err != nil {
			ctx.CsmLogger().Errorf("failed to create Higress resources, err=%v", err)
		}
		ctx.CsmLogger().Infof("Successfully deleted AI gateway instance %s for cluster %s in region %s with namespace %s",
			instanceId, clusterId, region, gatewayNamespace)
		err = s.deleteEndpointAndEIP(ctx, region, instanceId, vpcCidr, subnetId)
		if err != nil {
			ctx.CsmLogger().Errorf("failed to delete endpoint and EIP, region=%s, vpcCidr=%s, subnetId=%s, err=%v",
				region, vpcCidr, subnetId, err)
		}
		// 删除安全组
		if securityGroupId != "" {
			deleteArgs := &meta.DeleteSecurityGroupRuleArgs{
				SecurityGroupId: securityGroupId,
			}

			// 实现轮询删除，重试一分钟
			maxRetries := 12 // 12次 * 5秒 = 60秒
			retryInterval := 5 * time.Second
			var deleteErr error

			for i := 0; i < maxRetries; i++ {
				deleteErr = s.vpcService.DeleteSecurityGroupRule(ctx, deleteArgs, region)
				if deleteErr == nil {
					ctx.CsmLogger().Infof("Successfully deleted security group %s", securityGroupId)
					break
				}

				ctx.CsmLogger().Warnf("Failed to delete security group (attempt %d/%d): %v",
					i+1, maxRetries, deleteErr)

				// 最后一次尝试不需要等待
				if i < maxRetries-1 {
					time.Sleep(retryInterval)
				}
			}

			if deleteErr != nil {
				ctx.CsmLogger().Errorf("Failed to delete security group after %d attempts: %v",
					maxRetries, deleteErr)
				// 继续执行，不阻断删除流程
			}
		}
	}()

	return nil
}

func (service *Service) deleteEndpointAndEIP(ctx csmContext.CsmContext, region string, instanceId,
	vpcNetworkId, subnetId string) error {
	// 删除服务网卡
	epargs := &endpoint.ListEndpointArgs{
		VpcId: vpcNetworkId,
	}
	res, err := service.vpcService.ListEndpointsWithEip(ctx, epargs, region)
	if err != nil {
		ctx.CsmLogger().Warnf("listEndpoints get error %+v", err)
	}
	description := fmt.Sprintf("the endpoint for %s instance", instanceId)
	deleteEip := ""
	deleteEndpointID := ""
	serviceName := ""
	// 找到对应的服务网卡
	for _, value := range res.Endpoints {
		if value.SubnetId == subnetId && value.Description == description {
			deleteEndpointID = value.EndpointId
			deleteEip = value.Eip
			serviceName = value.Service
			break
		}
	}

	if deleteEndpointID == "" {
		return nil
	}

	err = service.modelVpc.DeleteEndpoint(ctx, deleteEndpointID, region)
	if err != nil {
		return err
	}
	// 如果eip不为空，则再删除eip
	if deleteEip != "" {
		return service.eipServer.ReleaseEip(ctx, deleteEip, region)
	}

	// 删除服务发布点
	blbargs := &blbService.DeleteBlbServiceArgs{
		Service: serviceName,
	}
	ctx.CsmLogger().Infof("deleteBlbService Service=【%s】 in blb service", serviceName)
	err = service.blbService.DeleteBlbService(ctx, blbargs, region)
	if err != nil {
		ctx.CsmLogger().Warnf("deleteBlbService in blb service error %v", err)
	} else {
		ctx.CsmLogger().Infof("deleteBlbService Service=【%s】 in blb service success", serviceName)
	}

	return nil
}

// checkInstanceHasServices 检查实例是否有关联服务
func (s *Service) checkInstanceHasServices(ctx csmContext.CsmContext, instanceId, hostedClusterId string) (bool, error) {
	servicesList, err := s.aiServiceService.GetServicesList(ctx, instanceId, "", 1, 1, "createTime", "desc", hostedClusterId)
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to get services for instance %s: %v", instanceId, err)
		return false, err
	}

	// 检查服务列表是否为空
	return servicesList.Page.TotalCount > 0, nil
}

// checkInstanceHasRoutes 检查实例是否有关联路由
func (s *Service) checkInstanceHasRoutes(ctx csmContext.CsmContext, instanceId, hostedClusterId string) (bool, error) {

	region := ctx.Get(reg.ContextRegion).(string)

	// 获取目标网关的命名空间
	gatewayNamespace := "istio-system-" + instanceId

	// 创建集群客户端
	client, err := s.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create cluster client, region=%s, clusterId=%s, err=%v")
	}
	istioClient := client.Istio()

	// 查询VirtualService资源列表
	vsList, err := istioClient.NetworkingV1alpha3().VirtualServices(gatewayNamespace).List(context.TODO(), metav1.ListOptions{})
	return len(vsList.Items) > 0, nil
}

// 原方法更改为内部实现
func (s *Service) addClusterListInternal(ctx csmContext.CsmContext, instanceId string, request *meta.AssociateClusterRequest) (err error) {
	// 获取区域信息
	region := ctx.Get(reg.ContextRegion).(string)
	if region == "" {
		return csmErr.NewMissingParametersException("region is required")
	}

	// 绑定请求参数
	csmlog.Infof("AddClusterList: instanceId=%s, region=%s", instanceId, region)
	// 验证请求参数
	if len(request.Clusters) == 0 {
		return csmErr.NewInvalidParameterValueException("No clusters provided")
	}

	gatewayInfoModel, err := s.aigatewayModel.GetAIGatewayInfo(ctx, instanceId, instanceId)
	if err != nil {
		return csmErr.NewResourceNotFoundException("Gateway not found")
	}
	gatewayInfo := *gatewayInfoModel
	hostedClusterId := gatewayInfo.HostedClusterID
	// 创建AI网关集群的k8s客户端
	hostingClient, err := s.cceService.NewClient(ctx, region, hostedClusterId, meta.MeshType(gatewayInfo.DeployMode))
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create gateway cluster client, region=%s, clusterId=%s, err=%v",
			region, hostedClusterId, err)
	}

	// 获取网关所在的命名空间
	namespace := gatewayInfo.Namespace
	if namespace == "" {
		return csmErr.NewInvalidParameterValueException("Invalid gateway namespace")
	}

	// 处理每个要关联的集群
	for _, clusterInfo := range request.Clusters {
		clusterId := clusterInfo.ClusterId
		// 更新AI网关实例信息，将集群ID、集群名称和备注信息保存到数据库
		updatedGateway := gatewayInfo
		updatedGateway.AddedClusterID = clusterInfo.ClusterId
		updatedGateway.AddedClusterName = clusterInfo.ClusterName
		updatedGateway.Remark = request.Remark
		updatedGateway.RelationTime = time.Now().Format("2006-01-02 15:04:05")

		// 添加新增的字段
		if request.IngressSettings != nil {
			updatedGateway.EnableIngress = request.IngressSettings.EnableIngress
			updatedGateway.EnableAllIngressClass = request.IngressSettings.EnableAllIngressClasses
			updatedGateway.EnableAllNamespaces = request.IngressSettings.EnableAllNamespaces

			// 处理数组字段
			if len(request.IngressSettings.IngressClasses) > 0 {
				updatedGateway.IngressClasses = request.IngressSettings.IngressClasses[0]
			}

			if len(request.IngressSettings.WatchNamespaces) > 0 {
				updatedGateway.WatchNamespaces = request.IngressSettings.WatchNamespaces[0]
			}
		}

		// 更新AI网关实例
		err = s.aigatewayModel.UpdateAIGateway(ctx, gatewayInfoModel, &updatedGateway)
		if err != nil {
			ctx.CsmLogger().Errorf("failed to update AI gateway instance info for cluster %s, err=%v",
				clusterInfo.ClusterId, err)
			return err
		}
		go func() {
			err = s.AsysAddCluster(ctx, request, gatewayInfo, clusterId, hostingClient)
			if err != nil {
				ctx.CsmLogger().Errorf("failed to add cluster %s, err=%v", clusterId, err)
			}
		}()

	}
	return err
}

func (s *Service) AsysAddCluster(ctx csmContext.CsmContext, request *meta.AssociateClusterRequest,
	gatewayInfo *meta.AIGatewayInstanceModel, clusterId string, hostingClient kube.Client) (err error) {
	// 获取集群kubeconfig
	kct := cce_v2.KubeConfigTypeInternal
	if viper.GetBool("local.dev") {
		ctx.CsmLogger().Infof("*** local develop mode to get NewClient ***")
		kct = cce_v2.KubeConfigTypePublic
	}
	ctx.CsmLogger().Infof("kct=%v", kct)
	region := gatewayInfo.Region
	namespace := gatewayInfo.Namespace
	kubeConfig, err := s.cceService.GetCCEClusterKubeConfigByClusterUUID(
		ctx, region, clusterId, kct, meta.StandaloneMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get kubeconfig for cluster %s, err=%v", clusterId, err)
	}

	// 为每个集群创建临时kubeconfig文件
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get current directory, err=%v", err)
	}

	kubeConfigName := fmt.Sprintf("%s-%s-temp.yaml", region, clusterId)
	kubeConfigPath := path.Join(pwd, "templates/higress/hosting", kubeConfigName)

	err = os.WriteFile(kubeConfigPath, []byte(kubeConfig), 0644)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to write kubeconfig file, err=%v", err)
	}

	vpcKubeConfig, err := s.cceService.GetCCEClusterKubeConfigByClusterUUID(
		ctx, region, clusterId, cce_v2.KubeConfigTypeVPC, meta.StandaloneMeshType)
	vpcKubeConfigName := fmt.Sprintf("%s-%s-vpc-temp.yaml", region, clusterId)
	vpcKubeConfigPath := path.Join(pwd, "templates/higress/hosting", vpcKubeConfigName)
	err = os.WriteFile(vpcKubeConfigPath, []byte(vpcKubeConfig), 0644)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to write kubeconfig file, err=%v", err)
	}
	restConfig, err := clientcmd.BuildConfigFromFlags("", vpcKubeConfigPath)
	if err != nil {
		return fmt.Errorf("failed to create k8s rest client: %s", err)
	}
	vpcAPIServer := restConfig.Host
	csmlog.Infof("vpcAPIServer: %s", vpcAPIServer)
	// 创建remote-secret
	remoteSecretName := fmt.Sprintf("%s-%s", region, clusterId)
	istioctlBin := path.Join(pwd, "templates/higress/hosting/bin", util.GetIstioCtl(ctx))

	// 使用istioctl创建remote-secret
	createRemoteSecretCmd := ""
	createRemoteSecretCmd = fmt.Sprintf("%s create-remote-secret --kubeconfig=%s --name=%s "+
		"--type=remote --server=%s --namespace %s",
		istioctlBin, kubeConfigPath, remoteSecretName, vpcAPIServer, namespace)

	// 使用opt.ExecCommand创建执行客户端
	execClient := s.opt.ExecCommand(ctx)
	outStr, errStr, err := execClient.Exec(ctx, createRemoteSecretCmd)
	if len(errStr) > 0 || err != nil {
		csmlog.Errorf("istioctl manifest generate errStr %s err %v", string(errStr), err)
	}
	objectsList, err := object.ManifestK8sObject(ctx, string(outStr))

	if err != nil {
		return err
	}

	err = kube.CreateOrUpdateK8sResource(ctx, hostingClient, objectsList)
	if err != nil {
		csmlog.Errorf("Failed to create remote secret: %v", err)
		return err
	}

	// 获取托管集群中的higress-controller deployment
	if request.IngressSettings != nil && request.IngressSettings.EnableIngress {
		// 用户集群下发 crd 资源
		crdPath := path.Join(pwd, "templates/higress/hosting/higress-crd.yaml")
		client, err := s.cceService.NewClient(ctx, region, clusterId, meta.StandaloneMeshType)
		if err != nil {
			ctx.CsmLogger().Errorf("failed to get k8s client for cluster %s, err=%v", clusterId, err)
			return err
		}

		// 读取CRD文件并应用到用户集群
		crdData, err := os.ReadFile(crdPath)
		if err != nil {
			ctx.CsmLogger().Errorf("failed to read higress-crd.yaml, err=%v", err)
			return err
		}

		// 解析CRD数据为K8s对象列表
		crdobjectsList, err := object.ManifestK8sObject(ctx, string(crdData))
		if err != nil {
			ctx.CsmLogger().Errorf("failed to parse higress-crd.yaml manifest, err=%v", err)
			return err
		}

		// 将CRD资源应用到用户集群
		err = kube.CreateOrUpdateK8sResource(ctx, client, crdobjectsList)
		if err != nil {
			ctx.CsmLogger().Errorf("failed to create CRD resources in user cluster, err=%v", err)
			return err
		}
		ctx.CsmLogger().Infof("Successfully applied CRD resources to cluster %s", clusterId)

		if !request.IngressSettings.EnableAllIngressClasses && len(request.IngressSettings.IngressClasses) > 0 {
			ingressClass := request.IngressSettings.IngressClasses[0]

			// 创建 kubeconfig ConfigMap
			kubeconfigData := map[string]string{
				"kubeconfig": string(vpcKubeConfig),
			}
			err = s.CreateConfigMap(ctx, hostingClient, namespace, "kubeconfig-configmap", kubeconfigData)
			if err != nil {
				csmlog.Errorf("Failed to create kubeconfig ConfigMap: %v", err)
				return err
			}

			// 使用 namespace 构建地址，格式为 xds://higress-core.{namespace}.svc:15051
			configSourceAddress := fmt.Sprintf("xds://higress-core.%s.svc:15051", namespace)
			err = s.UpdateHigressConfigMap(ctx, hostingClient, namespace, configSourceAddress)
			if err != nil {
				ctx.CsmLogger().Errorf("Failed to update higress-config ConfigMap: %v", err)
				// 继续执行，不阻断流程
			}

			// 根据用户指定的ingressClass和watchNamespace部署新的higress-core
			watchNamespace := ""
			if !request.IngressSettings.EnableAllNamespaces && len(request.IngressSettings.WatchNamespaces) > 0 {
				watchNamespace = request.IngressSettings.WatchNamespaces[0]
			}

			err = s.DeployHigressCore(ctx, hostingClient, gatewayInfo, ingressClass, watchNamespace)
			if err != nil {
				ctx.CsmLogger().Errorf("Failed to deploy higress-core: %v", err)
				return err
			}
			// 重启 higress-controller 和 higress-gateway 容器
			err = s.RestartHigressPods(ctx, hostingClient, namespace)
			if err != nil {
				ctx.CsmLogger().Errorf("Failed to restart higress pods: %v", err)
				// 继续执行，不阻断流程
			}
		}
	}

	// 清理临时文件
	os.Remove(kubeConfigPath)
	os.Remove(vpcKubeConfigPath)

	return err
}

// 保持原有的API兼容性
func (s *Service) AddClusterList(ctx csmContext.CsmContext, request *meta.AssociateClusterRequest) (err error) {
	// 获取AI网关实例ID
	instanceId := ctx.Param("InstanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("InstanceId is required")
	}

	return s.addClusterListInternal(ctx, instanceId, request)
}

func (s *Service) GetAllIngressInstances(ctx csmContext.CsmContext) (ingressList *meta.AiGatewayList, err error) {
	// 获取当前账户ID
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, csmErr.NewUnauthorizedException("user is nil", err)
	}

	// 创建查询参数
	mrp := meta.NewCsmMeshRequestParams()
	mrp.AccountID = accountId

	// 获取所有网关列表
	gatewayList, err := s.aigatewayModel.GetAIGatewayList(ctx, mrp)
	if err != nil {
		return nil, err
	}

	var result []meta.AiGateway

	// 过滤并转换为IngressDetail列表
	for _, gateway := range *gatewayList {
		if gateway.DeployMode != "hosting" {
			continue
		}

		namespace := gateway.Namespace
		region := gateway.Region
		hostedClusterId := gateway.HostedClusterID
		createTime := gateway.CreateTime.String()
		createTimeFormatted := strings.Split(createTime, " ")[0] + " " + strings.Split(createTime, " ")[1]

		var internalIp, externalIp string
		accessAddress := gateway.AccessAddress
		internalIp = strings.Split(accessAddress, ",")[0]
		if gateway.PublicAccessible {
			externalIp = strings.Split(accessAddress, ",")[1]
		}
		// 获取 pod 运行状态
		ingressStatus := "running" // 默认设置为running状态

		// 获取客户端
		client, err := s.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
		if err != nil {
			ctx.CsmLogger().Errorf("failed to get k8s client: %v", err)
			ingressStatus = "creating" // 如果无法获取客户端，设置为creating状态
		} else {
			// 检查higress-controller状态
			controllerPods, err := client.Kube().CoreV1().Pods(namespace).List(
				context.Background(),
				metav1.ListOptions{LabelSelector: constants.HigressLabelSelector}) // app=higress-controller
			if err != nil || len(controllerPods.Items) == 0 {
				ctx.CsmLogger().Errorf("failed to get higress-controller pods or no pods found: %v", err)
				ingressStatus = "creating"
			} else {
				// 检查所有controller pods是否都在运行
				for _, pod := range controllerPods.Items {
					if pod.Status.Phase != v1.PodRunning {
						ingressStatus = "creating"
						break
					}
				}
			}

			// 如果controller状态正常，继续检查gateway状态
			if ingressStatus == "running" {
				gatewayPods, err := client.Kube().CoreV1().Pods(namespace).List(
					context.Background(),
					metav1.ListOptions{LabelSelector: "app=higress-gateway"}) // app=higress-gateway
				if err != nil || len(gatewayPods.Items) == 0 {
					ctx.CsmLogger().Errorf("failed to get gateway pods or no pods found: %v", err)
					ingressStatus = "creating"
				} else {
					// 检查所有gateway pods是否都在运行
					for _, pod := range gatewayPods.Items {
						if pod.Status.Phase != v1.PodRunning {
							ingressStatus = "creating"
							break
						}
					}
				}
			}
		}
		//if internalIp == "" {
		//	ingressStatus = "creating"
		//}

		detail := meta.AiGateway{
			Namespace:          namespace,
			IngressId:          gateway.GatewayUUID,
			IngressStatus:      ingressStatus,
			InternalIP:         internalIp,
			ExternalIP:         externalIp,
			CreateTime:         createTimeFormatted,
			Region:             region,
			Replicas:           gateway.Replicas,
			Description:        gateway.Description,
			VpcCidr:            gateway.VpcCIDR,
			VpcId:              gateway.VpcNetworkID,
			SubnetId:           gateway.SubnetID,
			PublicAccessible:   gateway.PublicAccessible,
			DeletionProtection: gateway.DeletionProtection,
			Name:               gateway.GatewayName,
		}
		result = append(result, detail)
	}

	return &meta.AiGatewayList{
		TotalCount: int64(len(result)),
		Result:     result,
	}, nil
}

func (s *Service) GetAIGatewayDetail(ctx csmContext.CsmContext, instanceId, srcProduct string) (ingress *meta.AiGateway, err error) {
	// 从新数据库中查询 AI 网关信息
	gatewayInfo, err := s.aigatewayModel.GetAIGatewayInfo(ctx, instanceId, instanceId)
	if err != nil {
		return nil, err
	}

	if gatewayInfo == nil || *gatewayInfo == nil {
		return nil, csmErr.NewResourceNotFoundException("Gateway not found")
	}

	// 提取网关信息
	gateway := **gatewayInfo
	namespace := gateway.Namespace
	region := gateway.Region

	var internalIp, externalIp string
	accessAddress := gateway.AccessAddress
	internalIp = strings.Split(accessAddress, ",")[0]
	if gateway.PublicAccessible {
		externalIp = strings.Split(accessAddress, ",")[1]
	}

	// 获取创建时间
	createTimeStr := ""
	if gateway.BaseModel.CreateTime != nil {
		createTimeStr = gateway.BaseModel.CreateTime.Format("2006-01-02 15:04:05")
	}

	// 资源配额
	gatewayType := "small"
	resourceQuota := gateway.ResourceQuota
	if resourceQuota == "4c8g" {
		gatewayType = "medium"
	} else if resourceQuota == "8c16g" {
		gatewayType = "large"
	}
	detail := &meta.AiGateway{}
	if srcProduct == constants.AIGatewayProductAibox {
		ctx.CsmLogger().Infof("为srcProduct=aibox创建baEndpoint，实例ID: %s", instanceId)
		hostedClusterId := gateway.HostedClusterID
		serviceName := "higress-gateway"
		// 获取客户端
		client, err := s.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
		if err != nil {
			ctx.CsmLogger().Errorf("failed to get k8s client: %v", err)
			return nil, err
		}

		svc, err := client.Kube().CoreV1().Services(namespace).Get(context.TODO(), serviceName, metav1.GetOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("failed to get service %s: %v", serviceName, err)
			return nil, err
		}
		var backendIp string

		// 获取 LoadBalancer 的 external IP
		externalIP := svc.Status.LoadBalancer
		ctx.CsmLogger().Infof("获取到service %s的external IP: %v", serviceName, externalIP)
		if len(svc.Status.LoadBalancer.Ingress) > 0 {
			for _, ingress := range svc.Status.LoadBalancer.Ingress {
				if ingress.IP != "" {
					backendIp = ingress.IP
					ctx.CsmLogger().Infof("获取到LoadBalancer External IP: %s", ingress.IP)
					break
				} else if ingress.Hostname != "" {
					ctx.CsmLogger().Infof("获取到LoadBalancer External Hostname: %s", ingress.Hostname)
				}
			}
		} else {
			ctx.CsmLogger().Warnf("LoadBalancer尚未分配External IP")
		}

		if backendIp == "" {
			ctx.CsmLogger().Errorf("无法获取有效的backend IP，跳过创建baEndpoint")
			return nil, csmErr.NewResourceNotFoundException("无法获取LoadBalancer IP")
		}

		var req meta.CreateVpcEndpointRequest
		req.VpcId = constants.GetVpcHostingID()
		req.Protocol = "tcp"
		req.BackendPort = 80
		req.BackendIp = backendIp
		req.Name = fmt.Sprintf("aibox-endpoint-%s", instanceId) // 使用实例ID生成唯一名称
		req.Type = "vpc_inside"

		ctx.CsmLogger().Infof("开始创建baEndpoint，请求参数: %+v", req)
		baEndpointResult, err := s.baendpoint.CreateVpcEndpoint(ctx, &req, region)
		if err != nil {
			ctx.CsmLogger().Errorf("创建baEndpoint失败: %v", err)
			return nil, err
		}
		ctx.CsmLogger().Infof("baEndpoint创建成功: %+v", baEndpointResult)

		// 构建返回结果
		detail = &meta.AiGateway{
			Namespace:               namespace,
			IngressId:               gateway.GatewayUUID,
			IngressStatus:           constants.SmiRunning, // 默认设置为运行状态
			InternalIP:              internalIp,
			ExternalIP:              externalIp,
			CreateTime:              createTimeStr,
			VpcCidr:                 gateway.VpcCIDR,
			VpcId:                   gateway.VpcNetworkID,
			SubnetId:                gateway.SubnetID,
			Name:                    gateway.GatewayName,
			Region:                  region,
			GatewayType:             gatewayType,
			Replicas:                gateway.Replicas,
			DeletionProtection:      gateway.DeletionProtection,
			PublicAccessible:        gateway.PublicAccessible,
			Description:             gateway.Description,
			EnableIngress:           gateway.EnableIngress,
			EnableAllIngressClasses: gateway.EnableAllIngressClass,
			EnableAllNamespaces:     gateway.EnableAllNamespaces,
			BAendpointResult: meta.VpcEndpoint{
				VpcEndpointId: baEndpointResult.VpcEndpointId,
				VpcId:         baEndpointResult.VpcId,
				Protocol:      baEndpointResult.Protocol,
				BackendIp:     baEndpointResult.BackendIp,
				BackendPort:   baEndpointResult.BackendPort,
				EndpointIp:    baEndpointResult.EndpointIp,
				EndpointPort:  baEndpointResult.EndpointPort,
				Name:          baEndpointResult.Name,
				Description:   baEndpointResult.Description,
				Type:          baEndpointResult.Type,
				Status:        baEndpointResult.Status,
			},
		}
	} else {
		// 构建返回结果
		detail = &meta.AiGateway{
			Namespace:               namespace,
			IngressId:               gateway.GatewayUUID,
			IngressStatus:           constants.SmiRunning, // 默认设置为运行状态
			InternalIP:              internalIp,
			ExternalIP:              externalIp,
			CreateTime:              createTimeStr,
			VpcCidr:                 gateway.VpcCIDR,
			VpcId:                   gateway.VpcNetworkID,
			SubnetId:                gateway.SubnetID,
			Name:                    gateway.GatewayName,
			Region:                  region,
			GatewayType:             gatewayType,
			Replicas:                gateway.Replicas,
			DeletionProtection:      gateway.DeletionProtection,
			PublicAccessible:        gateway.PublicAccessible,
			Description:             gateway.Description,
			EnableIngress:           gateway.EnableIngress,
			EnableAllIngressClasses: gateway.EnableAllIngressClass,
			EnableAllNamespaces:     gateway.EnableAllNamespaces,
		}
	}

	// 处理JSON字段
	if gateway.IngressClasses != "" {
		var ingressClasses []string
		if err := json.Unmarshal([]byte(gateway.IngressClasses), &ingressClasses); err == nil {
			detail.IngressClasses = ingressClasses
		} else {
			ctx.CsmLogger().Warnf("failed to unmarshal IngressClasses: %v", err)
			detail.IngressClasses = []string{}
		}
	} else {
		detail.IngressClasses = []string{}
	}

	if gateway.WatchNamespaces != "" {
		var watchNamespaces []string
		if err := json.Unmarshal([]byte(gateway.WatchNamespaces), &watchNamespaces); err == nil {
			detail.WatchNamespaces = watchNamespaces
		} else {
			ctx.CsmLogger().Warnf("failed to unmarshal WatchNamespaces: %v", err)
			detail.WatchNamespaces = []string{}
		}
	} else {
		detail.WatchNamespaces = []string{}
	}

	return detail, nil
}

// 添加GetAIGatewayClusterList方法，实现获取AI网关关联的集群列表功能
func (s *Service) GetAIGatewayClusterList(ctx csmContext.CsmContext) (*meta.AIGatewayClusterListResponse, error) {
	// 获取AI网关实例ID
	instanceId := ctx.Param("InstanceId")
	if instanceId == "" {
		return nil, csmErr.NewMissingParametersException("InstanceId is required")
	}

	// 获取区域信息
	region := ctx.Get(reg.ContextRegion).(string)
	if region == "" {
		return nil, csmErr.NewMissingParametersException("region is required")
	}

	// 获取查询参数
	pageNo, _ := strconv.Atoi(ctx.QueryParam("pageNo"))
	if pageNo <= 0 {
		pageNo = 1
	}

	pageSize, _ := strconv.Atoi(ctx.QueryParam("pageSize"))
	if pageSize <= 0 {
		pageSize = 10
	}

	orderBy := ctx.QueryParam("orderBy")
	if orderBy == "" {
		orderBy = "relationTime"
	}

	order := ctx.QueryParam("order")
	if order == "" {
		order = "desc"
	}

	// 获取网关实例信息
	gatewayInfoModel, err := s.aigatewayModel.GetAIGatewayInfo(ctx, instanceId, instanceId)
	if err != nil {
		return nil, csmErr.NewResourceNotFoundException("Gateway not found")
	}
	gatewayInfo := *gatewayInfoModel

	// 如果网关没有关联集群，返回空列表
	if gatewayInfo.AddedClusterID == "" {
		return &meta.AIGatewayClusterListResponse{
			Success: true,
			Status:  http.StatusOK,
			Page: meta.AIGatewayClusterPage{
				OrderBy:    orderBy,
				Order:      order,
				PageNo:     pageNo,
				PageSize:   pageSize,
				TotalCount: 0,
				Result:     []meta.AIGatewayClusterItem{},
			},
		}, nil
	}

	// 查询集群信息和状态
	namespace := gatewayInfo.Namespace
	hostedClusterID := gatewayInfo.HostedClusterID
	clusterStatus := "RUNNING"      // 默认为运行中状态
	relationStatus := "ASSOCIATING" // 默认为关联中状态

	client, err := s.cceService.NewClient(ctx, region, hostedClusterID, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get k8s client: %v", err)
		relationStatus = "ERROR"
	} else {
		// 检查higress-controller状态
		controllerPods, err := client.Kube().CoreV1().Pods(namespace).List(
			context.Background(),
			metav1.ListOptions{LabelSelector: constants.HigressLabelSelector}) // app=higress-controller
		if err != nil || len(controllerPods.Items) == 0 {
			ctx.CsmLogger().Errorf("failed to get higress-controller pods or no pods found: %v", err)
			// 默认已经是 ASSOCIATING，不需要再次设置
		} else {
			// 检查所有controller pods是否都在运行
			allRunning := true
			for _, pod := range controllerPods.Items {
				if pod.Status.Phase != v1.PodRunning {
					allRunning = false
					break
				}
			}

			// 只有当所有controller pods都在运行时才继续检查gateway
			if allRunning {
				// 继续检查gateway状态
				gatewayPods, err := client.Kube().CoreV1().Pods(namespace).List(
					context.Background(),
					metav1.ListOptions{LabelSelector: "app=higress-gateway"}) // app=higress-gateway
				if err != nil || len(gatewayPods.Items) == 0 {
					ctx.CsmLogger().Errorf("failed to get gateway pods or no pods found: %v", err)
				} else {
					// 检查所有gateway pods是否都在运行
					allGwRunning := true
					for _, pod := range gatewayPods.Items {
						if pod.Status.Phase != v1.PodRunning {
							allGwRunning = false
							break
						}
					}

					// 只有当所有controller和gateway pods都在运行时才设置为ASSOCIATED
					if allGwRunning {
						relationStatus = "ASSOCIATED"
					}
				}
			}
		}
	}

	// 创建响应结果
	clusterItem := meta.AIGatewayClusterItem{
		ClusterId:      gatewayInfo.AddedClusterID,
		ClusterName:    gatewayInfo.AddedClusterName,
		Status:         clusterStatus,
		RelationStatus: relationStatus,
		Remark:         gatewayInfo.Remark,
		RelationTime:   gatewayInfo.RelationTime,
		IngressSettings: &meta.IngressSettings{
			EnableAllIngressClasses: gatewayInfo.EnableAllIngressClass,
			EnableAllNamespaces:     gatewayInfo.EnableAllNamespaces,
			EnableIngress:           gatewayInfo.EnableIngress,
			IngressClasses:          []string{gatewayInfo.IngressClasses},
			WatchNamespaces:         []string{gatewayInfo.WatchNamespaces},
		},
		UpdateTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 构建分页响应
	response := &meta.AIGatewayClusterListResponse{
		Success: true,
		Status:  http.StatusOK,
		Page: meta.AIGatewayClusterPage{
			OrderBy:    orderBy,
			Order:      order,
			PageNo:     pageNo,
			PageSize:   pageSize,
			TotalCount: 1, // 当前每个实例只支持关联一个集群
			Result:     []meta.AIGatewayClusterItem{clusterItem},
		},
	}

	return response, nil
}

// UpdateAIGateway 更新AI网关实例的基本信息
func (s *Service) UpdateAIGateway(ctx csmContext.CsmContext, instanceId string,
	request *meta.UpdateAIGatewayRequest) (result *meta.UpdateAIGatewayResult, err error) {
	ctx.CsmLogger().Infof("开始更新AI网关实例服务层处理，实例ID: %s", instanceId)

	name := request.Name
	description := request.Description
	deleteProtection := request.DeleteProtection
	replicas := request.Replicas
	publicAccessible := request.PublicAccessible

	// 获取现有的网关信息
	gatewayInfo, err := s.aigatewayModel.GetAIGatewayInfo(ctx, instanceId, instanceId)
	if err != nil {
		ctx.CsmLogger().Errorf("获取网关信息失败，实例ID: %s，错误: %v", instanceId, err)
		return nil, csmErr.NewResourceNotFoundException("Gateway not found")
	}
	if gatewayInfo == nil {
		ctx.CsmLogger().Errorf("网关信息为空，实例ID: %s", instanceId)
		return nil, csmErr.NewResourceNotFoundException("Gateway not found")
	}

	ctx.CsmLogger().Infof("成功获取网关信息，实例ID: %s，当前副本数: %d", instanceId, (*gatewayInfo).Replicas)

	// 创建更新后的模型
	updateGateway := **gatewayInfo

	// 更新字段
	if name != "" {
		updateGateway.GatewayName = name
	}

	if description != "" {
		updateGateway.Description = description
	}

	// 更新删除保护设置
	updateGateway.DeletionProtection = deleteProtection

	// 处理副本数更新
	var needScaleGateway bool
	var originalReplicas int
	if replicas != nil {
		// 验证副本数范围
		if *replicas < 2 || *replicas > 5 {
			ctx.CsmLogger().Errorf("副本数超出有效范围，实例ID: %s，副本数: %d，有效范围: 2-5", instanceId, *replicas)
			return nil, csmErr.NewInvalidParameterValueException("replicas must be between 1 and 5")
		}

		// 检查副本数是否发生变化
		originalReplicas = updateGateway.Replicas
		if originalReplicas != *replicas {
			ctx.CsmLogger().Infof("检测到副本数变化，实例ID: %s，当前副本数: %d，目标副本数: %d", instanceId, originalReplicas, *replicas)
			updateGateway.Replicas = *replicas
			needScaleGateway = true
		} else {
			ctx.CsmLogger().Infof("副本数无变化，实例ID: %s，副本数: %d", instanceId, *replicas)
		}
	}

	// 有公网 ip=**********,**************
	// 无公网 ip=**********
	// 获取当前访问地址，以便于判断是否已有绑定 EIP
	accessIPs := strings.Split(updateGateway.AccessAddress, ",")
	internalIP := accessIPs[0] // 内网IP始终是第一个
	publicIP := ""
	if len(accessIPs) > 1 {
		publicIP = accessIPs[1] // 公网IP是第二个
	}
	region := ctx.Get(reg.ContextRegion).(string)

	// 更新公网访问设置，如果发生变化可能需要额外操作
	if updateGateway.PublicAccessible != publicAccessible {
		updateGateway.PublicAccessible = publicAccessible

		// 如果从不可公网访问变为可公网访问，需要创建公网资源
		if publicAccessible {
			ctx.CsmLogger().Infof("Enabling public access for gateway %s", instanceId)
			// 创建 EIP
			createEipArgs := &eipSDK.CreateEipArgs{
				BandWidthInMbps: 1,
				Billing: &eipSDK.Billing{
					PaymentTiming: "Postpaid",
					BillingMethod: "ByTraffic",
				},
			}
			eipResult, createErr := s.eipServer.CreateEip(ctx, createEipArgs, region)
			if createErr != nil {
				ctx.CsmLogger().Errorf("Failed to create EIP: %v", createErr)
				return nil, createErr
			}

			// 获取创建的EIP地址
			eipAddress := eipResult.Eip
			ctx.CsmLogger().Infof("Created EIP %s for gateway %s", eipAddress, instanceId)

			// 查找服务网卡
			epargs := &endpoint.ListEndpointArgs{
				VpcId: updateGateway.VpcNetworkID,
			}
			endpointList, endpointErr := s.vpcService.ListEndpointsWithEip(ctx, epargs, region)
			if endpointErr != nil {
				ctx.CsmLogger().Errorf("Failed to list endpoints: %v", endpointErr)
				// 释放已创建的EIP
				releaseErr := s.eipServer.ReleaseEip(ctx, eipAddress, region)
				if releaseErr != nil {
					ctx.CsmLogger().Errorf("Failed to release EIP %s after endpoint list error: %v", eipAddress, releaseErr)
				}
				return nil, endpointErr
			}

			// 寻找属于此Gateway的服务网卡
			description := fmt.Sprintf("the endpoint for %s instance", instanceId)
			var endpointID string

			for _, endpoint := range endpointList.Endpoints {
				if endpoint.SubnetId == updateGateway.SubnetID && endpoint.Description == description {
					endpointID = endpoint.EndpointId
					break
				}
			}

			if endpointID == "" {
				ctx.CsmLogger().Errorf("No matching endpoint found for gateway %s", instanceId)
				// 释放已创建的EIP
				releaseErr := s.eipServer.ReleaseEip(ctx, eipAddress, region)
				if releaseErr != nil {
					ctx.CsmLogger().Errorf("Failed to release EIP %s after no endpoint found: %v", eipAddress, releaseErr)
				}
				return nil, errors.New("no matching endpoint found for binding EIP")
			}

			// 绑定EIP到服务网卡
			bindArgs := &eipSDK.BindEipArgs{
				InstanceType: "SNIC", // 服务网卡类型
				InstanceId:   endpointID,
			}

			err = s.eipServer.BindEip(ctx, bindArgs, eipAddress, region)
			if err != nil {
				ctx.CsmLogger().Errorf("Failed to bind EIP %s to endpoint %s: %v", eipAddress, endpointID, err)
				// 如果绑定失败，释放创建的EIP
				releaseErr := s.eipServer.ReleaseEip(ctx, eipAddress, region)
				if releaseErr != nil {
					ctx.CsmLogger().Errorf("Failed to release EIP %s after binding failure: %v", eipAddress, releaseErr)
				}
				return nil, err
			}

			// 更新访问地址，添加公网IP
			updateGateway.AccessAddress = internalIP + "," + eipAddress
			ctx.CsmLogger().Infof("Successfully enabled public access with EIP %s for gateway %s", eipAddress, instanceId)

		} else {
			ctx.CsmLogger().Infof("Disabling public access for gateway %s", instanceId)
			// 如果存在公网IP，需要解绑并释放
			if publicIP != "" {
				// 解绑 EIP
				err = s.eipServer.UnbindEip(ctx, publicIP, region)
				if err != nil {
					ctx.CsmLogger().Errorf("Failed to unbind EIP %s from gateway %s: %v", publicIP, instanceId, err)
					return nil, err
				}

				// 释放 EIP
				err = s.eipServer.ReleaseEip(ctx, publicIP, region)
				if err != nil {
					ctx.CsmLogger().Errorf("Failed to release EIP %s: %v", publicIP, err)
					return nil, err
				}

				// 更新访问地址，移除公网IP
				updateGateway.AccessAddress = internalIP
				ctx.CsmLogger().Infof("Successfully disabled public access for gateway %s", instanceId)
			}
		}
	}

	// 更新数据库记录
	gatewayPtr := &updateGateway
	err = s.aigatewayModel.UpdateAIGateway(ctx, gatewayInfo, &gatewayPtr)
	if err != nil {
		return nil, err
	}

	// 如果需要扩容网关，执行Kubernetes Deployment更新
	if needScaleGateway {
		ctx.CsmLogger().Infof("开始执行网关扩容操作，实例ID: %s，副本数: %d -> %d", instanceId, originalReplicas, *replicas)

		err = s.scaleHigressGateway(ctx, &updateGateway, *replicas)
		if err != nil {
			ctx.CsmLogger().Errorf("网关扩容失败，实例ID: %s，错误: %v", instanceId, err)
			// 扩容失败时回滚数据库中的副本数
			rollbackGateway := updateGateway
			rollbackGateway.Replicas = originalReplicas
			rollbackPtr := &rollbackGateway
			rollbackErr := s.aigatewayModel.UpdateAIGateway(ctx, gatewayInfo, &rollbackPtr)
			if rollbackErr != nil {
				ctx.CsmLogger().Errorf("回滚数据库副本数失败，实例ID: %s，错误: %v", instanceId, rollbackErr)
			}
			return nil, csmErr.NewInvalidParameterValueException("网关扩容失败: " + err.Error())
		}

		ctx.CsmLogger().Infof("网关扩容成功，实例ID: %s，新副本数: %d", instanceId, *replicas)
	}

	// 构建返回结果
	updateTime := time.Now().Format("2006-01-02 15:04:05")
	result = &meta.UpdateAIGatewayResult{
		InstanceId:       instanceId,
		Name:             updateGateway.GatewayName,
		Description:      updateGateway.Description,
		DeleteProtection: updateGateway.DeletionProtection,
		PublicAccessible: updateGateway.PublicAccessible,
		UpdateTime:       updateTime,
	}

	// 如果更新了副本数，将其包含在返回结果中
	if replicas != nil {
		result.Replicas = replicas
	}

	return result, nil
}

// RemoveClusterFromAIGateway 从AI网关实例中移除关联的集群
func (s *Service) RemoveClusterFromAIGateway(ctx csmContext.CsmContext, instanceId, clusterId string) (err error) {
	// 获取网关实例信息
	gatewayInfo, err := s.aigatewayModel.GetAIGatewayInfo(ctx, instanceId, instanceId)
	if err != nil {
		return csmErr.NewResourceNotFoundException("Gateway not found")
	}
	if gatewayInfo == nil {
		return csmErr.NewResourceNotFoundException("Gateway not found")
	}

	region := ctx.Get(reg.ContextRegion).(string)
	hostedClusterId := (*gatewayInfo).HostedClusterID
	ingressClasses := (*gatewayInfo).IngressClasses
	enableIngress := (*gatewayInfo).EnableIngress
	namespace := fmt.Sprintf("istio-system-%s", instanceId)
	// 检查集群是否正在被使用
	hasServices, err := s.checkInstanceHasServices(ctx, instanceId, hostedClusterId)
	if err != nil {
		return err
	}
	if hasServices {
		//return csmErr.NewInvalidParameterValueException("实例中仍有服务来源当前集群，无法被移除")
		return errors.New("实例中仍有服务来源当前集群，无法被移除")
	}
	client, err := s.cceService.NewClient(ctx, region, clusterId, meta.StandaloneMeshType)
	// 删除用户集群的命名空间
	err = client.Kube().CoreV1().Namespaces().Delete(context.TODO(), namespace, metav1.DeleteOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("failed to delete namespace %s in cluster %s, err=%v", namespace, clusterId, err)
	}
	if ingressClasses != "" || enableIngress {
		// 删除用户集群 crd
		pwd, err := os.Getwd()
		if err != nil {
			ctx.CsmLogger().Errorf("failed to get current directory, err=%v", err)
		}
		crdPath := path.Join(pwd, "templates/higress/hosting/higress-crd.yaml")
		if err != nil {
			ctx.CsmLogger().Errorf("failed to get k8s client for cluster %s, err=%v", clusterId, err)
			return err
		}

		// 读取CRD文件并应用到用户集群
		crdData, err := os.ReadFile(crdPath)
		if err != nil {
			ctx.CsmLogger().Errorf("failed to read higress-crd.yaml, err=%v", err)
			return err
		}

		// 解析CRD数据为K8s对象列表
		crdobjectsList, err := object.ManifestK8sObject(ctx, string(crdData))
		if err != nil {
			ctx.CsmLogger().Errorf("failed to parse higress-crd.yaml manifest, err=%v", err)
			return err
		}

		err = kube.DeleteResources(ctx, client, crdobjectsList)
		if err != nil {
			ctx.CsmLogger().Errorf("failed to delete CRD resources in user cluster, err=%v", err)
			return err
		}
		ctx.CsmLogger().Infof("Successfully applied CRD resources to cluster %s", clusterId)

		// 删除托管集群的 secreat,清空 kubeconfig 信息
		hostingClient, err := s.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
		if err != nil {
			ctx.CsmLogger().Errorf("failed to get k8s client for hosting cluster, err=%v", err)
		}
		secreatName := fmt.Sprintf("istio-remote-secret-%s-%s", region, clusterId)
		err = hostingClient.Kube().CoreV1().Secrets(namespace).Delete(context.TODO(), secreatName, metav1.DeleteOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("failed to delete secret kubeconfig in namespace %s, err=%v", namespace, err)
		}
		kubeconfigData := map[string]string{
			"kubeconfig": "",
		}
		err = s.CreateConfigMap(ctx, hostingClient, namespace, "kubeconfig-configmap", kubeconfigData)
		if err != nil {
			csmlog.Errorf("Failed to create kubeconfig ConfigMap: %v", err)
			return err
		}

		// 直接删除 higress-core 组件
		err = s.DeleteHigressCore(ctx, hostingClient, namespace)
		if err != nil {
			ctx.CsmLogger().Errorf("Failed to delete higress-core components: %v", err)
			return err
		}
		ctx.CsmLogger().Infof("Successfully removed higress-core components")
	}

	// 更新网关实例信息，清空关联的集群信息
	updateGateway := **gatewayInfo
	if updateGateway.AddedClusterID == clusterId {
		updateGateway.AddedClusterID = ""
		updateGateway.AddedClusterName = ""
		updateGateway.EnableIngress = false
		updateGateway.EnableAllIngressClass = false
		updateGateway.EnableAllIngressClass = false
		updateGateway.IngressClasses = ""
		updateGateway.WatchNamespaces = ""

		// 更新数据库记录
		gatewayPtr := &updateGateway
		err = s.aigatewayModel.UpdateAIGateway(ctx, gatewayInfo, &gatewayPtr)
		if err != nil {
			return err
		}
	} else {
		return csmErr.NewResourceNotFoundException("Cluster not found in gateway")
	}

	return nil
}

func (s *Service) ModifyIngressClass(ctx csmContext.CsmContext, client kube.Client,
	namespace, ingressclass string, enableAllNamespaces bool, watchNamespaces []string) error {

	deployment, err := client.Kube().AppsV1().Deployments(namespace).Get(
		context.TODO(), "higress-controller", metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get higress-controller deployment, err=%v", err)
		return err
	}

	// 构建patch数据：修改args参数中的ingressClass和watchNamespace
	for i, container := range deployment.Spec.Template.Spec.Containers {
		if container.Name == "higress-core" {
			// 处理ingressClass参数
			for j, arg := range container.Args {
				if strings.HasPrefix(arg, "--ingressClass=") {
					deployment.Spec.Template.Spec.Containers[i].Args[j] = "--ingressClass=" + ingressclass
					break
				}
			}
			// 处理watchNamespace参数
			for j, arg := range container.Args {
				if strings.HasPrefix(arg, "--watchNamespace=") {
					if enableAllNamespaces {
						deployment.Spec.Template.Spec.Containers[i].Args[j] = "--watchNamespace="
					} else if len(watchNamespaces) > 0 {
						deployment.Spec.Template.Spec.Containers[i].Args[j] = "--watchNamespace=" + strings.Join(watchNamespaces, ",")
					}
					break
				}
			}

			// 添加kubeconfig参数
			deployment.Spec.Template.Spec.Containers[i].Args = append(
				deployment.Spec.Template.Spec.Containers[i].Args,
				"--kubeconfig=/etc/kubeconfig/"+namespace+"/kubeconfig")

			break
		}
	}

	// 将修改后的deployment序列化为JSON
	patchData, err := json.Marshal(deployment)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to marshal deployment patch data, err=%v", err)
		return err
	}

	// 应用patch
	_, err = client.Kube().AppsV1().Deployments(namespace).Patch(
		context.TODO(), "higress-controller", types.StrategicMergePatchType, patchData, metav1.PatchOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("failed to patch higress-controller deployment, err=%v", err)
		return err
	}
	ctx.CsmLogger().Infof("Successfully updated ingressClass to %s and watchNamespace configuration in higress-controller", ingressclass)
	return err
}

// CreateConfigMap creates a ConfigMap resource in the given namespace
func (s *Service) CreateConfigMap(ctx csmContext.CsmContext, client kube.Client,
	namespace, configMapName string, data map[string]string) error {

	// Create ConfigMap object
	configMap := &v1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      configMapName,
			Namespace: namespace,
		},
		Data: data,
	}

	// Try to create the ConfigMap
	_, err := client.Kube().CoreV1().ConfigMaps(namespace).Create(context.TODO(), configMap, metav1.CreateOptions{})
	if err != nil {
		// If ConfigMap already exists, try to update it
		if kubeErrors.IsAlreadyExists(err) {
			ctx.CsmLogger().Infof("ConfigMap %s already exists, updating it", configMapName)
			_, err = client.Kube().CoreV1().ConfigMaps(namespace).Update(context.TODO(), configMap, metav1.UpdateOptions{})
			if err != nil {
				ctx.CsmLogger().Errorf("Failed to update ConfigMap %s: %v", configMapName, err)
				return err
			}
		} else {
			ctx.CsmLogger().Errorf("Failed to create ConfigMap %s: %v", configMapName, err)
			return err
		}
	}

	ctx.CsmLogger().Infof("Successfully created/updated ConfigMap %s in namespace %s", configMapName, namespace)
	return nil
}

// GetIngressInstancesWithPagination 获取分页的AI网关实例列表
func (s *Service) GetIngressInstancesWithPagination(ctx csmContext.CsmContext,
	mrp *meta.CsmMeshRequestParams) ([]meta.AiGateway, int64, error) {

	ctx.CsmLogger().Infof("获取AI网关实例列表，分页参数: pageNo=%d, pageSize=%d, srcProduct=%s",
		mrp.PageNo, mrp.PageSize, mrp.SrcProduct)

	// 使用新的分页方法获取数据库分页结果
	gatewayModels, totalCount, err := s.aigatewayModel.GetAIGatewayListWithPagination(ctx, mrp)
	if err != nil {
		ctx.CsmLogger().Errorf("获取AI网关实例列表失败: %v", err)
		return nil, 0, err
	}

	ctx.CsmLogger().Infof("从数据库获取到 %d 个网关实例，总数: %d", len(*gatewayModels), totalCount)

	// 转换数据并应用状态筛选
	var allAiGateways []meta.AiGateway

	// 按区域对实例进行分组以优化client创建
	regionGroups := make(map[string][]*meta.AIGatewayInstanceModel)
	for _, gateway := range *gatewayModels {
		region := gateway.Region
		if region == "" {
			ctx.CsmLogger().Warnf("Gateway %s has empty region, skipping", gateway.GatewayUUID)
			continue
		}
		regionGroups[region] = append(regionGroups[region], gateway)
	}

	ctx.CsmLogger().Infof("Processing %d gateways across %d regions", len(*gatewayModels), len(regionGroups))

	// 为每个区域创建client并处理该区域的实例
	for region, gateways := range regionGroups {
		ctx.CsmLogger().Infof("Processing %d gateways in region: %s", len(gateways), region)

		// 根据区域获取对应的托管集群ID
		regionClusterId := constants.GetHostingClusterByRegion(region)

		ctx.CsmLogger().Infof("Using hosting cluster %s for region %s", regionClusterId, region)

		// 处理当前区域的所有实例
		for _, gateway := range gateways {
			ingressStatus := s.getGatewayStatus(ctx, gateway)

			// 构建AiGateway对象
			aiGateway := s.buildAiGatewayFromModel(gateway, ingressStatus)

			// 根据状态筛选
			if s.shouldIncludeGateway(aiGateway, mrp.Status) {
				allAiGateways = append(allAiGateways, aiGateway)
			}
		}
	}

	ctx.CsmLogger().Infof("状态筛选后剩余 %d 个网关实例", len(allAiGateways))

	// 返回结果，totalCount保持数据库查询的原始总数
	return allAiGateways, totalCount, nil
}

// getGatewayStatus 获取网关实例的运行状态
func (s *Service) getGatewayStatus(ctx csmContext.CsmContext, gateway *meta.AIGatewayInstanceModel) string {
	namespace := gateway.Namespace
	srcProduct := gateway.SrcProduct

	// 默认设置为running状态
	ingressStatus := constants.AIGatewayStatusRunning

	// 检查内部IP是否可用（除开发机来源的实例需要检查）
	var internalIp string
	accessAddress := gateway.AccessAddress
	if accessAddress != "" {
		internalIp = strings.Split(accessAddress, ",")[0]
	}
	if srcProduct != constants.AIGatewayProductAibox && internalIp == "" {
		ctx.CsmLogger().Infof("Gateway %s in namespace %s has no internal IP", gateway.GatewayUUID, namespace)
		return constants.AIGatewayStatusCreating
	}

	return ingressStatus
}

// buildAiGatewayFromModel 从AIGatewayInstanceModel构建AiGateway对象
func (s *Service) buildAiGatewayFromModel(gateway *meta.AIGatewayInstanceModel, status string) meta.AiGateway {
	namespace := gateway.Namespace

	// 安全地处理创建时间
	var createTimeFormatted string
	if gateway.CreateTime != nil {
		createTime := gateway.CreateTime.String()
		timeParts := strings.Split(createTime, " ")
		if len(timeParts) >= 2 {
			createTimeFormatted = timeParts[0] + " " + timeParts[1]
		} else {
			createTimeFormatted = createTime
		}
	} else {
		createTimeFormatted = ""
	}

	var internalIp, externalIp string
	accessAddress := gateway.AccessAddress
	if accessAddress != "" {
		internalIp = strings.Split(accessAddress, ",")[0]
		if gateway.PublicAccessible && strings.Contains(accessAddress, ",") {
			externalIp = strings.Split(accessAddress, ",")[1]
		}
	}

	return meta.AiGateway{
		Namespace:          namespace,
		IngressId:          gateway.GatewayUUID,
		IngressStatus:      status,
		InternalIP:         internalIp,
		ExternalIP:         externalIp,
		CreateTime:         createTimeFormatted,
		Region:             gateway.Region,
		Replicas:           gateway.Replicas,
		Description:        gateway.Description,
		VpcCidr:            gateway.VpcCIDR,
		VpcId:              gateway.VpcNetworkID,
		SubnetId:           gateway.SubnetID,
		PublicAccessible:   gateway.PublicAccessible,
		DeletionProtection: gateway.DeletionProtection,
		Name:               gateway.GatewayName,
		AssociatedCluster:  gateway.AddedClusterID,
		SrcProduct:         gateway.SrcProduct,
	}
}

// shouldIncludeGateway 判断是否应该包含该网关实例（基于状态筛选）
func (s *Service) shouldIncludeGateway(gateway meta.AiGateway, statusFilter string) bool {
	if statusFilter == "" {
		return true
	}
	return gateway.IngressStatus == statusFilter
}

// HigressCoreTemplateData 存储Higress Core模板渲染所需数据
type HigressCoreTemplateData struct {
	Namespace        string
	AccountId        string
	SubnetId         string
	SecurityGroupIds string
	VpcCidr          string
	IngressClass     string
	WatchNamespace   string
}

// DeployHigressCore 使用模板部署或更新higress-core
func (s *Service) DeployHigressCore(ctx csmContext.CsmContext, hostingClient kube.Client,
	gatewayInfo *meta.AIGatewayInstanceModel, ingressClass string, watchNamespace string) error {
	// 获取网关实例信息
	namespace := gatewayInfo.Namespace
	accountId := gatewayInfo.AccountID
	subnetId := gatewayInfo.SubnetID
	securityGroupId := gatewayInfo.SecurityGroupId
	vpcCidr := gatewayInfo.VpcCIDR
	// 读取Higress Core模板
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get current directory, err=%v", err)
		return err
	}
	// 使用higress-core模板文件
	higressCoreTemplatePath := path.Join(pwd, "templates/higress/hosting/higress-core.tmpl")
	templateData, err := os.ReadFile(higressCoreTemplatePath)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to read Higress Core template file, err=%v", err)
		return err
	}
	// 准备模板数据
	data := HigressCoreTemplateData{
		Namespace:        namespace,
		AccountId:        accountId,
		SubnetId:         subnetId,
		SecurityGroupIds: securityGroupId,
		VpcCidr:          vpcCidr,
		IngressClass:     ingressClass,
		WatchNamespace:   watchNamespace,
	}
	// 渲染模板
	tmpl, err := template.New("higress-core").Parse(string(templateData))
	if err != nil {
		ctx.CsmLogger().Errorf("failed to parse higress-core template, err=%v", err)
		return err
	}
	var rendered bytes.Buffer
	if err := tmpl.Execute(&rendered, data); err != nil {
		ctx.CsmLogger().Errorf("failed to render higress-core template, err=%v", err)
		return err
	}
	// 将渲染后的模板内容解析为K8s对象
	higressCoreObjects, err := object.ManifestK8sObject(ctx, rendered.String())
	if err != nil {
		ctx.CsmLogger().Errorf("failed to parse higress-core template manifest, err=%v", err)
		return err
	}
	// 应用或更新资源
	err = kube.CreateOrUpdateK8sResource(ctx, hostingClient, higressCoreObjects)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create/update higress-core resources, err=%v", err)
		return err
	}
	ctx.CsmLogger().Infof("Successfully deployed/updated higress-core with ingressClass=%s and watchNamespace=%s",
		ingressClass, watchNamespace)
	return nil
}

// DeleteHigressCore 删除托管集群中的higress-core组件（deployment和service）
func (s *Service) DeleteHigressCore(ctx csmContext.CsmContext, hostingClient kube.Client, namespace string) error {
	// 删除 higress-core deployment
	err := hostingClient.Kube().AppsV1().Deployments(namespace).Delete(
		context.TODO(), "higress-core", metav1.DeleteOptions{})
	if err != nil && !kubeErrors.IsNotFound(err) {
		ctx.CsmLogger().Errorf("failed to delete higress-core deployment, err=%v", err)
		return err
	}
	ctx.CsmLogger().Infof("Successfully deleted higress-core deployment in namespace %s", namespace)

	// 删除 higress-core service
	err = hostingClient.Kube().CoreV1().Services(namespace).Delete(
		context.TODO(), "higress-core", metav1.DeleteOptions{})
	if err != nil && !kubeErrors.IsNotFound(err) {
		ctx.CsmLogger().Errorf("failed to delete higress-core service, err=%v", err)
		return err
	}
	ctx.CsmLogger().Infof("Successfully deleted higress-core service in namespace %s", namespace)

	return nil
}

// UpdateHigressConfigMap 更新 higress-config ConfigMap，添加指定的 configSources 地址
func (s *Service) UpdateHigressConfigMap(ctx csmContext.CsmContext, hostingClient kube.Client, namespace string, address string) error {
	// 获取现有的 ConfigMap
	configMap, err := hostingClient.Kube().CoreV1().ConfigMaps(namespace).Get(
		context.TODO(), "higress-config", metav1.GetOptions{})

	if err != nil {
		if kubeErrors.IsNotFound(err) {
			ctx.CsmLogger().Errorf("ConfigMap higress-config not found in namespace %s", namespace)
			return err
		}
		ctx.CsmLogger().Errorf("Failed to get ConfigMap higress-config: %v", err)
		return err
	}

	// 从 ConfigMap 中获取 mesh 配置
	meshConfig, exists := configMap.Data["mesh"]
	if !exists {
		ctx.CsmLogger().Errorf("mesh configuration not found in ConfigMap higress-config")
		return fmt.Errorf("mesh configuration not found in ConfigMap higress-config")
	}

	// 解析 mesh 配置
	meshLines := strings.Split(meshConfig, "\n")

	// 查找 configSources 部分
	configSourcesIndex := -1
	for i, line := range meshLines {
		if strings.TrimSpace(line) == "configSources:" {
			configSourcesIndex = i
			break
		}
	}

	if configSourcesIndex == -1 {
		ctx.CsmLogger().Errorf("configSources section not found in mesh configuration")
		return fmt.Errorf("configSources section not found in mesh configuration")
	}

	// 构建新的地址条目
	newSourceLine := "- address: " + address

	// 检查地址是否已存在
	addressExists := false
	for i := configSourcesIndex + 1; i < len(meshLines); i++ {
		line := meshLines[i]
		if !strings.HasPrefix(strings.TrimSpace(line), "-") {
			break // 已经超出了 configSources 部分
		}

		if strings.Contains(line, address) {
			addressExists = true
			break
		}
	}

	// 如果地址不存在，则添加
	if !addressExists {
		// 创建新的配置列表
		var newMeshLines []string
		newMeshLines = append(newMeshLines, meshLines[:configSourcesIndex+1]...)
		newMeshLines = append(newMeshLines, newSourceLine)

		// 添加 configSources 后面的条目
		for i := configSourcesIndex + 1; i < len(meshLines); i++ {
			line := meshLines[i]
			if strings.HasPrefix(strings.TrimSpace(line), "-") {
				newMeshLines = append(newMeshLines, line)
			} else {
				newMeshLines = append(newMeshLines, meshLines[i:]...)
				break
			}
		}

		// 更新 mesh 配置
		configMap.Data["mesh"] = strings.Join(newMeshLines, "\n")

		// 更新 ConfigMap
		_, err = hostingClient.Kube().CoreV1().ConfigMaps(namespace).Update(
			context.TODO(), configMap, metav1.UpdateOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("Failed to update ConfigMap higress-config: %v", err)
			return err
		}

		ctx.CsmLogger().Infof("Successfully added %s to configSources in higress-config", address)
	} else {
		ctx.CsmLogger().Infof("Address %s already exists in configSources, no update needed", address)
	}

	return nil
}

// RestartHigressPods 按顺序重启 higress 组件，确保每个组件就绪后再重启下一个
func (s *Service) RestartHigressPods(ctx csmContext.CsmContext, hostingClient kube.Client, namespace string) error {
	// 首先检查 higress-core 的就绪状态
	ctx.CsmLogger().Infof("Checking if higress-core is ready before restarting other components")
	err := s.waitForDeploymentReady(ctx, hostingClient, namespace, "higress-core", 5*time.Minute)
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to verify higress-core readiness: %v", err)
		return err
	}
	ctx.CsmLogger().Infof("higress-core is ready, proceeding with controlled restart")

	// 重启 higress-controller
	ctx.CsmLogger().Infof("Starting restart of higress-controller deployment")
	controllerDeployment, err := hostingClient.Kube().AppsV1().Deployments(namespace).Get(
		context.TODO(), "higress-controller", metav1.GetOptions{})
	if err != nil {
		if kubeErrors.IsNotFound(err) {
			ctx.CsmLogger().Warnf("Deployment higress-controller not found in namespace %s", namespace)
		} else {
			ctx.CsmLogger().Errorf("Failed to get higress-controller deployment: %v", err)
			return err
		}
	} else {
		// 通过更新 annotation 触发重启
		if controllerDeployment.Spec.Template.Annotations == nil {
			controllerDeployment.Spec.Template.Annotations = make(map[string]string)
		}
		controllerDeployment.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)

		_, err = hostingClient.Kube().AppsV1().Deployments(namespace).Update(
			context.TODO(), controllerDeployment, metav1.UpdateOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("Failed to restart higress-controller: %v", err)
			return err
		}
		ctx.CsmLogger().Infof("Triggered restart of higress-controller, waiting for it to become ready")

		// 等待 higress-controller 就绪
		err = s.waitForDeploymentReady(ctx, hostingClient, namespace, "higress-controller", 5*time.Minute)
		if err != nil {
			ctx.CsmLogger().Errorf("higress-controller failed to become ready after restart: %v", err)
			return err
		}
		ctx.CsmLogger().Infof("higress-controller restart completed successfully")
	}

	// 重启 higress-gateway
	ctx.CsmLogger().Infof("Starting restart of higress-gateway deployment")
	gatewayDeployment, err := hostingClient.Kube().AppsV1().Deployments(namespace).Get(
		context.TODO(), "higress-gateway", metav1.GetOptions{})
	if err != nil {
		if kubeErrors.IsNotFound(err) {
			ctx.CsmLogger().Warnf("Deployment higress-gateway not found in namespace %s", namespace)
		} else {
			ctx.CsmLogger().Errorf("Failed to get higress-gateway deployment: %v", err)
			return err
		}
	} else {
		// 通过更新 annotation 触发重启
		if gatewayDeployment.Spec.Template.Annotations == nil {
			gatewayDeployment.Spec.Template.Annotations = make(map[string]string)
		}
		gatewayDeployment.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)

		_, err = hostingClient.Kube().AppsV1().Deployments(namespace).Update(
			context.TODO(), gatewayDeployment, metav1.UpdateOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("Failed to restart higress-gateway: %v", err)
			return err
		}
		ctx.CsmLogger().Infof("Triggered restart of higress-gateway, waiting for it to become ready")

		// 等待 higress-gateway 就绪
		err = s.waitForDeploymentReady(ctx, hostingClient, namespace, "higress-gateway", 5*time.Minute)
		if err != nil {
			ctx.CsmLogger().Errorf("higress-gateway failed to become ready after restart: %v", err)
			return err
		}
		ctx.CsmLogger().Infof("higress-gateway restart completed successfully")
	}

	ctx.CsmLogger().Infof("All Higress components have been restarted successfully")
	return nil
}

// waitForDeploymentReady 等待指定的部署变为就绪状态
func (s *Service) waitForDeploymentReady(ctx csmContext.CsmContext, client kube.Client, namespace, deploymentName string, timeout time.Duration) error {
	ctx.CsmLogger().Infof("Waiting for deployment %s in namespace %s to become ready", deploymentName, namespace)

	// 创建一个带超时的上下文
	timeoutCtx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 检查间隔时间
	interval := 1 * time.Second

	for {
		select {
		case <-timeoutCtx.Done():
			return fmt.Errorf("timeout waiting for deployment %s to become ready", deploymentName)
		default:
			// 获取部署状态
			deployment, err := client.Kube().AppsV1().Deployments(namespace).Get(
				context.Background(), deploymentName, metav1.GetOptions{})

			if err != nil {
				if kubeErrors.IsNotFound(err) {
					ctx.CsmLogger().Warnf("Deployment %s not found in namespace %s", deploymentName, namespace)
					return fmt.Errorf("deployment %s not found", deploymentName)
				}
				ctx.CsmLogger().Warnf("Error checking deployment %s status: %v, retrying...", deploymentName, err)
				time.Sleep(interval)
				continue
			}

			// 检查部署状态
			if deployment.Status.ReadyReplicas == *deployment.Spec.Replicas &&
				deployment.Status.UpdatedReplicas == *deployment.Spec.Replicas {
				ctx.CsmLogger().Infof("Deployment %s is ready: %d/%d replicas available",
					deploymentName, deployment.Status.ReadyReplicas, *deployment.Spec.Replicas)
				return nil
			}

			ctx.CsmLogger().Infof("Deployment %s not ready yet. Ready: %d/%d, Updated: %d/%d",
				deploymentName, deployment.Status.ReadyReplicas, *deployment.Spec.Replicas,
				deployment.Status.UpdatedReplicas, *deployment.Spec.Replicas)

			time.Sleep(interval)
		}
	}
}

// scaleHigressGateway 扩容或缩容higress-gateway Deployment的副本数
func (s *Service) scaleHigressGateway(ctx csmContext.CsmContext, gateway *meta.AIGatewayInstanceModel, replicas int) error {
	ctx.CsmLogger().Infof("开始扩容higress-gateway，实例ID: %s，目标副本数: %d", gateway.InstanceUUID, replicas)

	// 获取托管集群信息
	region := gateway.Region
	hostedClusterId := gateway.HostedClusterID
	namespace := gateway.Namespace

	ctx.CsmLogger().Infof("网关扩容参数，实例ID: %s，区域: %s，托管集群ID: %s，命名空间: %s",
		gateway.InstanceUUID, region, hostedClusterId, namespace)

	if hostedClusterId == "" {
		ctx.CsmLogger().Errorf("托管集群ID为空，实例ID: %s", gateway.InstanceUUID)
		return fmt.Errorf("托管集群ID为空，无法执行扩容操作")
	}

	if namespace == "" {
		ctx.CsmLogger().Errorf("命名空间为空，实例ID: %s", gateway.InstanceUUID)
		return fmt.Errorf("命名空间为空，无法执行扩容操作")
	}

	// 创建Kubernetes客户端
	ctx.CsmLogger().Infof("创建托管集群客户端，实例ID: %s，区域: %s，集群ID: %s", gateway.InstanceUUID, region, hostedClusterId)
	hostingClient, err := s.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建托管集群客户端失败，实例ID: %s，区域: %s，集群ID: %s，错误: %v",
			gateway.InstanceUUID, region, hostedClusterId, err)
		return err
	}
	ctx.CsmLogger().Infof("成功创建托管集群客户端，实例ID: %s", gateway.InstanceUUID)

	// 获取higress-gateway Deployment
	deploymentName := "higress-gateway"
	ctx.CsmLogger().Infof("获取higress-gateway Deployment，实例ID: %s，命名空间: %s，Deployment名称: %s",
		gateway.InstanceUUID, namespace, deploymentName)

	deployment, err := hostingClient.Kube().AppsV1().Deployments(namespace).Get(
		context.TODO(), deploymentName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("获取higress-gateway Deployment失败，实例ID: %s，命名空间: %s，Deployment名称: %s，错误: %v",
			gateway.InstanceUUID, namespace, deploymentName, err)
		return err
	}

	ctx.CsmLogger().Infof("成功获取higress-gateway Deployment，实例ID: %s，当前副本数: %d",
		gateway.InstanceUUID, *deployment.Spec.Replicas)

	// 检查当前副本数
	currentReplicas := int32(replicas)
	if deployment.Spec.Replicas != nil && *deployment.Spec.Replicas == currentReplicas {
		ctx.CsmLogger().Infof("higress-gateway副本数已经是%d，无需更新，实例ID: %s", replicas, gateway.InstanceUUID)
		return nil
	}

	ctx.CsmLogger().Infof("准备更新higress-gateway副本数，实例ID: %s，当前副本数: %d，目标副本数: %d",
		gateway.InstanceUUID, *deployment.Spec.Replicas, replicas)

	// 更新副本数
	deployment.Spec.Replicas = &currentReplicas

	// 添加更新时间注解，确保Pod重启
	if deployment.Spec.Template.ObjectMeta.Annotations == nil {
		deployment.Spec.Template.ObjectMeta.Annotations = make(map[string]string)
	}
	deployment.Spec.Template.ObjectMeta.Annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)

	// 应用更新
	ctx.CsmLogger().Infof("应用higress-gateway Deployment更新，实例ID: %s", gateway.InstanceUUID)
	_, err = hostingClient.Kube().AppsV1().Deployments(namespace).Update(
		context.TODO(), deployment, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("更新higress-gateway Deployment失败，实例ID: %s，错误: %v", gateway.InstanceUUID, err)
		return err
	}
	return nil
}
