package aiingress

import (
	"github.com/jinzhu/gorm"
	"github.com/spf13/viper"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/command"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

const (
	crdAllowedKind = "crd.allowedKind"
)

type Option struct {
	DB             *dbutil.DB
	crdAllowedKind []string
	ExecCommand    func(ctx csmContext.CsmContext) command.ExecInterface
}

func NewOption(d *gorm.DB) *Option {
	return &Option{
		DB:             dbutil.NewDB(d),
		crdAllowedKind: viper.GetStringSlice(crdAllowedKind),
		ExecCommand:    command.NewExecClient,
	}
}
