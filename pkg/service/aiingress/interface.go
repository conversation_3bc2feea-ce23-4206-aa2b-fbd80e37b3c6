package aiingress

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

type ServiceInterface interface {
	CreateIngress(ctx context.CsmContext, ingress *meta.GatewayModel, installParams *meta.IngressGatewayParams) (err error)
	DeleteIngress(ctx context.CsmContext, clusterId string) (err error)
	GetIngressDetail(ctx context.CsmContext, ingressId string) (ingress *meta.IngressDetail, err error)
	CreateCrdForBG(ctx context.CsmContext, param *meta.CrdParam) (ingressCrd *meta.Crd, err error)
	DeleteCrdForBG(ctx context.CsmContext, param *meta.CrdParam) error
	GetCrd(ctx context.CsmContext, param *meta.CrdParam) (*meta.Crd, error)
	UpdateCrd(ctx context.CsmContext, param *meta.CrdParam) (crd *meta.Crd, err error)
	GetJwtToken(ctx context.CsmContext, param *meta.TokenParam, tokenMap map[string]string) (jwt *meta.JwtToken, err error)
	CreateHigress(ctx context.CsmContext, ingress *meta.GatewayModel, installParams *meta.IngressGatewayParams) (err error)
	DeleteHigress(ctx context.CsmContext, clusterId string) (err error)
	NewGateway(ctx context.CsmContext, ingress *meta.AIGatewayInstanceModel) (err error)
	AddClusterList(ctx context.CsmContext, request *meta.AssociateClusterRequest) (err error)
	GetAIGatewayClusterList(ctx context.CsmContext) (*meta.AIGatewayClusterListResponse, error)
	DeleteAIGateway(ctx context.CsmContext, instanceId string) (err error)
	GetAllIngressInstances(ctx context.CsmContext) (ingressList *meta.AiGatewayList, err error)
	GetIngressInstancesWithPagination(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) ([]meta.AiGateway, int64, error)
	GetAIGatewayDetail(ctx context.CsmContext, instanceId, srcProduct string) (ingress *meta.AiGateway, err error)
	UpdateAIGateway(ctx context.CsmContext, instanceId string,
		request *meta.UpdateAIGatewayRequest) (result *meta.UpdateAIGatewayResult, err error)
	RemoveClusterFromAIGateway(ctx context.CsmContext, instanceId, clusterId string) (err error)
	CreateConfigMap(ctx context.CsmContext, client kube.Client,
		namespace, configMapName string, data map[string]string) error
}
