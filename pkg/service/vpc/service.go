package vpc

import (
	"fmt"

	bccApi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/baidubce/bce-sdk-go/services/endpoint"
	"github.com/baidubce/bce-sdk-go/services/esg"
	"github.com/baidubce/bce-sdk-go/services/vpc"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"

	modelEIP "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/eip"
	modelMeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	modelVPC "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/vpc"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

type Service struct {
	modelVpc  modelVPC.ServiceInterface
	eipServer modelEIP.ServiceInterface
}

func NewVPCService() *Service {
	return &Service{
		modelVpc:  modelVPC.NewService(modelVPC.NewOption()),
		eipServer: modelEIP.NewService(modelEIP.NewOption()),
	}
}

func (service *Service) ListVPC(ctx csmContext.CsmContext, _ *meta.VPCArgs, region string) (*meta.VPCResult, error) {
	res := &meta.VPCResult{}
	args := &vpc.ListVPCArgs{}
	listVPC, err := service.modelVpc.ListVPC(ctx, args, region)
	if err != nil {
		ctx.CsmLogger().Errorf("ListVPC error. %v", err)
		return res, err
	}
	var allVpc []meta.VPC
	for _, vpc := range listVPC.VPCs {
		allVpc = append(allVpc, meta.VPC{
			VpcID: vpc.VPCID,
			Name:  vpc.Name,
			Cidr:  vpc.Cidr,
		})
	}
	res.VPCs = allVpc
	return res, nil
}

func (service *Service) ListSubnet(ctx csmContext.CsmContext, subnetArgs *meta.SubnetArgs,
	region string) (*meta.SubnetResult, error) {
	args := &vpc.ListSubnetArgs{}
	if subnetArgs != nil && len(subnetArgs.VpcID) > 0 {
		args.VpcId = subnetArgs.VpcID
	}
	res := &meta.SubnetResult{}
	listSubnet, err := service.modelVpc.ListSubnet(ctx, args, region)
	if err != nil {
		ctx.CsmLogger().Errorf("ListSubnet error. %v", err)
		return res, err
	}
	var allSubnet []meta.Subnet
	for _, subnet := range listSubnet.Subnets {
		allSubnet = append(allSubnet, meta.Subnet{
			SubnetID: subnet.SubnetId,
			Name:     subnet.Name,
			Cidr:     subnet.Cidr,
		})
	}
	res.Subnets = allSubnet
	return res, nil
}

func (service *Service) GetVPCAndSubnetDetail(ctx csmContext.CsmContext, vpcId, subnetId, region string) (*modelMeta.InstanceNetworkType, error) {
	VPCDetailResult, err := service.modelVpc.GetVPCDetail(ctx, vpcId, region)
	if err != nil {
		ctx.CsmLogger().Errorf("ListSubnet error. %v", err)
		return nil, err
	}
	vpc := VPCDetailResult.VPC
	res := &modelMeta.InstanceNetworkType{
		VpcNetworkId:   vpc.VPCId,
		VpcNetworkName: vpc.Name,
		VpcNetworkCidr: vpc.Cidr,
	}
	vpcSubnets := vpc.Subnets
	for _, value := range vpcSubnets {
		if value.SubnetId == subnetId {
			res.SubnetId = subnetId
			res.SubnetCidr = value.Cidr
			res.SubnetName = value.Name
		}
	}
	return res, nil
}

func (service *Service) ListSecurityGroup(ctx csmContext.CsmContext, securityGroupArgs *meta.SecurityGroupArgs,
	region string) (*meta.SecurityGroupResult, error) {
	args := &bccApi.ListSecurityGroupArgs{}
	if securityGroupArgs != nil && len(securityGroupArgs.VpcID) > 0 {
		args.VpcId = securityGroupArgs.VpcID
	}
	res := &meta.SecurityGroupResult{}
	listSecurityGroup, err := service.modelVpc.ListSecurityGroup(ctx, args, region)
	if err != nil {
		ctx.CsmLogger().Errorf("ListSecurityGroup error. %v", err)
		return res, err
	}
	var allSecurityGroup []meta.SecurityGroup
	for _, sg := range listSecurityGroup.SecurityGroups {
		allSecurityGroup = append(allSecurityGroup, meta.SecurityGroup{
			ID:   sg.Id,
			Name: sg.Name,
			Desc: sg.Desc,
		})
	}
	res.SecurityGroups = allSecurityGroup
	return res, nil
}

func (service *Service) CreateSecurityGroupRule(ctx csmContext.CsmContext,
	args *modelMeta.CreateSecurityGroupRuleArgs, region string) (securityGroupId string, err error) {
	return service.modelVpc.CreateSecurityGroupRule(ctx, args, region)
}

func (service *Service) DeleteSecurityGroupRule(ctx csmContext.CsmContext,
	args *modelMeta.DeleteSecurityGroupRuleArgs, region string) error {
	return service.modelVpc.DeleteSecurityGroupRule(ctx, args, region)
}

func (service *Service) CreateEndpoint(ctx csmContext.CsmContext, args *endpoint.CreateEndpointArgs,
	region string) (*endpoint.CreateEndpointResult, error) {
	return service.modelVpc.CreateEndpoint(ctx, args, region)
}

func (service *Service) CreateEndpointWithEip(ctx csmContext.CsmContext, args *modelMeta.CreateEndpointArgs,
	region string) (*endpoint.CreateEndpointResult, error) {
	return service.modelVpc.CreateEndpointWithEip(ctx, args, region)
}

func (service *Service) DeleteEndpoint(ctx csmContext.CsmContext, endpointId,
	region string) error {
	return service.modelVpc.DeleteEndpoint(ctx, endpointId, region)
}

func (service *Service) GetEndpointDetail(ctx csmContext.CsmContext, endpointId,
	region string) (*endpoint.Endpoint, error) {
	return service.modelVpc.GetEndpointDetail(ctx, endpointId, region)
}

func (service *Service) ListEndpoints(ctx csmContext.CsmContext, args *endpoint.ListEndpointArgs,
	region string) (*endpoint.ListEndpointResult, error) {
	return service.modelVpc.ListEndpoints(ctx, args, region)
}

func (service *Service) ListEndpointsWithEip(ctx csmContext.CsmContext, args *endpoint.ListEndpointArgs,
	region string) (*modelMeta.ListEndpointResult, error) {
	return service.modelVpc.ListEndpointsWithEip(ctx, args, region)
}

func (service *Service) DeleteEndpointWithEIPByCsmInstanceID(ctx csmContext.CsmContext, region string,
	csmInstance *modelMeta.Instances) error {
	args := &endpoint.ListEndpointArgs{
		VpcId: csmInstance.VpcNetworkId,
	}
	res, err := service.ListEndpointsWithEip(ctx, args, region)
	if err != nil {
		ctx.CsmLogger().Warnf("listEndpoints get error %+v", err)
	}
	description := fmt.Sprintf(constants.CsmHostingAPIServerVpcEndpointDescribe, csmInstance.InstanceUUID)
	deleteEip := ""
	deleteEndpointID := ""
	// 找到对应的服务网卡
	for _, value := range res.Endpoints {
		if value.SubnetId == csmInstance.SubnetId && value.Description == description {
			deleteEndpointID = value.EndpointId
			deleteEip = value.Eip
			break
		}
	}

	if deleteEndpointID == "" {
		return nil
	}

	err = service.modelVpc.DeleteEndpoint(ctx, deleteEndpointID, region)
	if err != nil {
		return err
	}
	// 如果eip不为空，则再删除eip
	if deleteEip != "" {
		return service.eipServer.ReleaseEip(ctx, deleteEip, region)
	}
	return nil
}

func (service *Service) ListEsg(ctx csmContext.CsmContext, args *esg.ListEsgArgs,
	region string) (*esg.ListEsgResult, error) {
	return service.modelVpc.ListEsg(ctx, args, region)
}
