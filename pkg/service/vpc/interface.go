package vpc

import (
	"github.com/baidubce/bce-sdk-go/services/endpoint"
	"github.com/baidubce/bce-sdk-go/services/esg"

	modelMeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

type ServiceInterface interface {
	ListVPC(ctx csmContext.CsmContext, args *meta.VPCArgs, region string) (*meta.VPCResult, error)

	ListSubnet(ctx csmContext.CsmContext, args *meta.SubnetArgs, region string) (*meta.SubnetResult, error)

	ListSecurityGroup(ctx csmContext.CsmContext, securityGroupArgs *meta.SecurityGroupArgs, region string) (*meta.SecurityGroupResult, error)

	CreateSecurityGroupRule(ctx csmContext.CsmContext, args *modelMeta.CreateSecurityGroupRuleArgs, region string) (securityGroupId string, err error)

	DeleteSecurityGroupRule(ctx csmContext.CsmContext, args *modelMeta.DeleteSecurityGroupRuleArgs, region string) error

	GetVPCAndSubnetDetail(ctx csmContext.CsmContext, vpcId, subnetId, region string) (*modelMeta.InstanceNetworkType, error)

	CreateEndpoint(ctx csmContext.CsmContext, args *endpoint.CreateEndpointArgs, region string) (*endpoint.CreateEndpointResult, error)

	DeleteEndpoint(ctx csmContext.CsmContext, endpointId, region string) error

	GetEndpointDetail(ctx csmContext.CsmContext, endpointId, region string) (*endpoint.Endpoint, error)

	ListEndpoints(ctx csmContext.CsmContext, args *endpoint.ListEndpointArgs, region string) (*endpoint.ListEndpointResult, error)

	CreateEndpointWithEip(ctx csmContext.CsmContext, args *modelMeta.CreateEndpointArgs, region string) (*endpoint.CreateEndpointResult, error)

	ListEndpointsWithEip(ctx csmContext.CsmContext, args *endpoint.ListEndpointArgs, region string) (*modelMeta.ListEndpointResult, error)

	DeleteEndpointWithEIPByCsmInstanceID(ctx csmContext.CsmContext, region string, csmInstance *modelMeta.Instances) error

	ListEsg(ctx csmContext.CsmContext, args *esg.ListEsgArgs, region string) (*esg.ListEsgResult, error)
}
