package diagnosis

import (
	"bytes"
	"errors"
	"fmt"
	"os/exec"
	"testing"

	"github.com/stretchr/testify/mock"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

// TestStr2Exception 测试函数
func TestStr2Exception(t *testing.T) {

	cases := []struct {
		str  string
		want *meta.Exception
		err  error
	}{
		{
			str: "Error [IST0101] (VirtualService default/details) Referenced host+subset in destinationrule not found: \"details+v1\"",
			want: &meta.Exception{
				Name:        "(VirtualService default/details)",
				Level:       "ERROR",
				Code:        "[IST0101]",
				Description: " Referenced host+subset in destinationrule not found: \"details+v1\"",
			},
			err: nil,
		},
		{
			str: "Error [IST0102] (VirtualService default/reviews) Referenced host+subset in destinationrule not found",
			want: &meta.Exception{
				Name:        "(VirtualService default/reviews)",
				Level:       "ERROR",
				Code:        "[IST0102]",
				Description: " Referenced host+subset in destinationrule not found",
			},
			err: nil,
		},
		{
			str:  "Error [IST0103]",
			want: nil,
			err:  fmt.Errorf("there is error"),
		},
	}
	for _, c := range cases {
		got, err := Str2Exception(c.str)
		if err != nil && c.err != nil {
			continue
		} else if ExceptionEqual(got, c.want) {
			continue
		} else {
			t.Errorf("Str2Exception(%s) = %v, want %v", c.str, got, c.want)
		}
	}
}

// TestGetUniqueName 测试获取唯一名称的方法
func TestGetUniqueName(t *testing.T) {
	cases := []struct {
		analyze *Analyze
		want    string
	}{
		{
			analyze: &Analyze{
				ClusterConf: &ClusterConf{
					Region:         "testRegion",
					ClusterName:    "testClusterName",
					CceClusterUuid: "testCceClusterUuid",
					Version:        "testVersion",
					Namespace:      "testNamespace",
				},
			},
			want: "testRegion_testClusterName_testCceClusterUuid_testVersion_testNamespace_analyze",
		},
		{
			want: "istio analyze is null",
		},
		{
			analyze: &Analyze{},
			want:    "istio cluster conf is null",
		},
	}
	for _, c := range cases {
		got, err := c.analyze.getUniqueName()
		if err != nil {
			if err.Error() != c.want {
				t.Errorf("getUniqueName = %q, want %q", err.Error(), c.want)
			}
		} else {
			if got != c.want {
				t.Errorf("getUniqueName = %q, want %q", got, c.want)
			}
		}
	}
}

// TestAnalyze_getUniqueName_Error 测试Analyze的getUniqueName函数，当istio区域为空时应该返回错误信息
func TestAnalyze_getUniqueName_Error(t *testing.T) {
	var analyze *Analyze
	_, err := analyze.getUniqueName()
	want := "istio analyze is null"
	if err.Error() != want {
		t.Errorf("getUniqueName = %q, want %q", err.Error(), want)
	}
}

// ExceptionEqual 判断两个异常是否相等。
// 参数：
//
//	a: 一个异常指针。
//	b: 另一个异常指针。
//
// 返回值：
//
//	如果两个异常相等，则返回true；否则返回false。
func ExceptionEqual(a, b *meta.Exception) bool {
	if a == nil && b == nil {
		return true
	} else if a != nil && b != nil {
		return a.Name == b.Name && a.Level == b.Level && a.Code == b.Code && a.Description == b.Description
	}
	return false
}

// TestExecCmdOut 测试 ExecCmdOut 函数
func TestExecCmdOut(t *testing.T) {
	cc, _ := csmContext.NewCsmContextMock()
	mockCmd := &mockCmd{}
	mockCmd.On("Start").Return(nil)
	mockCmd.On("Wait").Return(errors.New("timeout"))
	out, _, err := ExecCmdOut(cc, "ls")
	if err != nil {
		t.Errorf("TestExecCmdOut failed with error %v", err)
	}
	mockCmd.On("Start").Return(errors.New("timeout"))
	mockCmd.On("Wait").Return(nil)
	out, _, err = ExecCmdOut(cc, "ls")
	if err != nil {
		t.Errorf("TestExecCmdOut failed with error %v", err)
	}
	if len(out) == 0 {
		t.Errorf("TestExecCmdOut failed with no output")
	}

	mockCmd.On("Start").Return(nil)
	mockCmd.On("Wait").Return(nil)
	cmd := exec.Command("ls")
	mockCmd.On("Stdout").Return(cmd.Stdout)
	mockCmd.On("Stderr").Return(cmd.Stderr)
	out, _, err = ExecCmdOut(cc, "ls")
	if err != nil {
		t.Errorf("TestExecCmdOut failed, expect no error, got %v", err)
	}
	if len(out) == 0 {
		t.Errorf("TestExecCmdOut failed, expect output")
	}
}

type mockCmd struct {
	mock.Mock
}

func (m *mockCmd) Start() error {
	args := m.Called()
	return args.Error(0)
}

func (m *mockCmd) Wait() error {
	args := m.Called()
	return args.Error(0)
}

func (m *mockCmd) Stdout() *bytes.Buffer {
	args := m.Called()
	return args.Get(0).(*bytes.Buffer)
}

func (m *mockCmd) Stderr() *bytes.Buffer {
	args := m.Called()
	return args.Get(0).(*bytes.Buffer)
}
