package diagnosis

import (
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

type ServiceInterface interface {
	NamespaceList(ctx csmContext.CsmContext, instanceUUID string) (*meta.NamespacesResult, error)
	ProxyStatusList(ctx csmContext.CsmContext, instanceUUID string, page *vo.Page,
		listFilter *vo.DiagnosisListFilter) ([]*meta.ProxyStatus, *vo.PageResult, error)
	ProxyConfigList(ctx csmContext.CsmContext, instanceUUID string, pcr *meta.ProxyConfigRequest,
		page *vo.Page) ([]byte, *vo.PageResult, error)
	ExceptionList(ctx csmContext.CsmContext, instanceUUID, namespace string,
		page *vo.Page) ([]*meta.Exception, *vo.PageR<PERSON>ult, error)
	ConfigDump(ctx csmContext.CsmContext, instanceUUID, namespace, podName, clusterName string) ([]byte, error)
}
