// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	vo "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// ConfigDump mocks base method.
func (m *MockServiceInterface) ConfigDump(ctx context.CsmContext, instanceUUID, namespace, podName, clusterName string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigDump", ctx, instanceUUID, namespace, podName, clusterName)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfigDump indicates an expected call of ConfigDump.
func (mr *MockServiceInterfaceMockRecorder) ConfigDump(ctx, instanceUUID, namespace, podName, clusterName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigDump", reflect.TypeOf((*MockServiceInterface)(nil).ConfigDump), ctx, instanceUUID, namespace, podName, clusterName)
}

// ExceptionList mocks base method.
func (m *MockServiceInterface) ExceptionList(ctx context.CsmContext, instanceUUID, namespace string, page *vo.Page) ([]*meta.Exception, *vo.PageResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExceptionList", ctx, instanceUUID, namespace, page)
	ret0, _ := ret[0].([]*meta.Exception)
	ret1, _ := ret[1].(*vo.PageResult)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ExceptionList indicates an expected call of ExceptionList.
func (mr *MockServiceInterfaceMockRecorder) ExceptionList(ctx, instanceUUID, namespace, page interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExceptionList", reflect.TypeOf((*MockServiceInterface)(nil).ExceptionList), ctx, instanceUUID, namespace, page)
}

// NamespaceList mocks base method.
func (m *MockServiceInterface) NamespaceList(ctx context.CsmContext, instanceUUID string) (*meta.NamespacesResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NamespaceList", ctx, instanceUUID)
	ret0, _ := ret[0].(*meta.NamespacesResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NamespaceList indicates an expected call of NamespaceList.
func (mr *MockServiceInterfaceMockRecorder) NamespaceList(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NamespaceList", reflect.TypeOf((*MockServiceInterface)(nil).NamespaceList), ctx, instanceUUID)
}

// ProxyConfigList mocks base method.
func (m *MockServiceInterface) ProxyConfigList(ctx context.CsmContext, instanceUUID string, pcr *meta.ProxyConfigRequest, page *vo.Page) ([]byte, *vo.PageResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProxyConfigList", ctx, instanceUUID, pcr, page)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(*vo.PageResult)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ProxyConfigList indicates an expected call of ProxyConfigList.
func (mr *MockServiceInterfaceMockRecorder) ProxyConfigList(ctx, instanceUUID, pcr, page interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProxyConfigList", reflect.TypeOf((*MockServiceInterface)(nil).ProxyConfigList), ctx, instanceUUID, pcr, page)
}

// ProxyStatusList mocks base method.
func (m *MockServiceInterface) ProxyStatusList(ctx context.CsmContext, instanceUUID string, page *vo.Page, listFilter *vo.DiagnosisListFilter) ([]*meta.ProxyStatus, *vo.PageResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProxyStatusList", ctx, instanceUUID, page, listFilter)
	ret0, _ := ret[0].([]*meta.ProxyStatus)
	ret1, _ := ret[1].(*vo.PageResult)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ProxyStatusList indicates an expected call of ProxyStatusList.
func (mr *MockServiceInterfaceMockRecorder) ProxyStatusList(ctx, instanceUUID, page, listFilter interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProxyStatusList", reflect.TypeOf((*MockServiceInterface)(nil).ProxyStatusList), ctx, instanceUUID, page, listFilter)
}
