package diagnosis

import (
	"encoding/json"
	"sort"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

func getProxyStatus(statuses map[string][]byte) ([]*meta.ProxyStatus, error) {
	var proxyStatus []*meta.ProxyStatus

	fullStatus := make([]*meta.Status, 0, len(statuses))
	for pilot, status := range statuses {
		var ss []*meta.Status
		err := json.Unmarshal(status, &ss)
		if err != nil {
			return proxyStatus, err
		}
		for _, s := range ss {
			s.PilotName = pilot
		}
		fullStatus = append(fullStatus, ss...)
	}

	for _, status := range fullStatus {
		psr := statusView(status)
		proxyStatus = append(proxyStatus, psr)
	}

	sort.Slice(proxyStatus, func(i, j int) bool {
		if proxyStatus[i].ClusterName != proxyStatus[j].ClusterName {
			return proxyStatus[i].ClusterName < proxyStatus[j].ClusterName
		}
		return proxyStatus[i].Proxy < proxyStatus[j].Proxy
	})
	return proxyStatus, nil
}

func statusView(status *meta.Status) *meta.ProxyStatus {
	clusterSynced := xdsStatus(status.ClusterSent, status.ClusterAcked)
	listenerSynced := xdsStatus(status.ListenerSent, status.ListenerAcked)
	routeSynced := xdsStatus(status.RouteSent, status.RouteAcked)
	endpointSynced := xdsStatus(status.EndpointSent, status.EndpointAcked)
	extensionConfigSynced := xdsStatus(status.ExtensionConfigSent, status.ExtensionConfigAcked)
	version := status.IstioVersion
	if version == "" {
		version = status.ProxyVersion + "*"
	}
	ps := &meta.ProxyStatus{
		Proxy:       status.ProxyID,
		ClusterName: status.ClusterID,
		PilotName:   status.PilotName,
		Version:     version,
		CDS:         clusterSynced,
		LDS:         listenerSynced,
		EDS:         endpointSynced,
		RDS:         routeSynced,
		ECDS:        extensionConfigSynced,
	}
	return ps
}

func xdsStatus(sent, acked string) string {
	if sent == "" {
		return "NOT SENT"
	}
	if sent == acked {
		return "SYNCED"
	}
	// acked will be empty string when there is never Acknowledged
	if acked == "" {
		return "STALE (Never Acknowledged)"
	}
	// Since the Nonce changes to uuid, so there is no more any time diff info
	return "STALE"
}
