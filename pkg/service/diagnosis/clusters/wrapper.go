package clusters

import (
	adminapi "github.com/envoyproxy/go-control-plane/envoy/admin/v3"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/diagnosis/protomarshal"
)

// Wrapper is a wrapper around the Envoy Clusters
// It has extra helper functions for handling any/struct/marshal protobuf pain
type Wrapper struct {
	*adminapi.Clusters
}

// MarshalJSON is a custom marshaller to handle protobuf pain
func (w *Wrapper) MarshalJSON() ([]byte, error) {
	return protomarshal.Marshal(w)
}

// UnmarshalJSON is a custom unmarshaller to handle protobuf pain
func (w *Wrapper) UnmarshalJSON(b []byte) error {
	cd := &adminapi.Clusters{}
	err := protomarshal.UnmarshalAllowUnknown(b, cd)
	*w = Wrapper{cd}
	return err
}
