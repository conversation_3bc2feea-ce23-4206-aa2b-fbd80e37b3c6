package diagnosis

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	cluster_mock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	instance "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	instance_mock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	csmcontext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	cce_mock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/diagnosis/configdump"
	service_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	smeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/file"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/testdata"
)

var (
	testInstanceUUID          = "csm-xxxxxx"
	testClusterUUID           = "cce-cccccc"
	testClusterName           = "test01-xxx"
	testClusterTypePrimary    = "primary"
	testClusterTypeExternal   = "external"
	testRegion                = "bj"
	testIstioInstallNamespace = "istio-system"
	mockDB, _                 = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	ctx, _                    = csmContext.NewCsmContextMock()
	testInstanceName          = "test-instance"
	testInstanceType          = "standalone"
	testInstanceStatus        = "running"
	istioInstallNamespace     = "istio-system"
	region                    = "bj"
	namespace                 = "default"
	podName                   = "test-pod"

	//nolint
	mockProxyConfigClusters = []byte(`[{"domain":"fortio.default.svc.cluster.local","port":"8080","subset":"-","trafficDirection":"outbound","type":"EDS"},{"domain":"heapster.kube-system.svc.cluster.local","port":"80","subset":"-","trafficDirection":"outbound","type":"EDS"}]`)

	//nolint
	mockConfigDumpCluster = []byte(`{"configs":[{"@type":"type.googleapis.com/envoy.admin.v3.ClustersConfigDump","version_info":"2023-12-07T09:28:45Z/14","static_clusters":[{"cluster":{"@type":"type.googleapis.com/envoy.config.cluster.v3.Cluster","name":"agent","type":"STATIC","connect_timeout":"0.250s","load_assignment":{"cluster_name":"agent","endpoints":[{"lb_endpoints":[{"endpoint":{"address":{"socket_address":{"address":"127.0.0.1","port_value":15020}}}}]}]}},"last_updated":"2023-12-07T09:28:53.991Z"}],"dynamic_active_clusters":[{"version_info":"2023-12-07T09:28:45Z/14","cluster":{"@type":"type.googleapis.com/envoy.config.cluster.v3.Cluster","name":"BlackHoleCluster","type":"STATIC","connect_timeout":"10s"},"last_updated":"2023-12-07T09:28:54.087Z"},{"version_info":"2023-12-07T09:28:45Z/14","cluster":{"@type":"type.googleapis.com/envoy.config.cluster.v3.Cluster","name":"InboundPassthroughClusterIpv4","type":"ORIGINAL_DST","connect_timeout":"10s","lb_policy":"CLUSTER_PROVIDED","circuit_breakers":{"thresholds":[{"max_connections":**********,"max_pending_requests":**********,"max_requests":**********,"max_retries":**********,"track_remaining":true}]},"upstream_bind_config":{"source_address":{"address":"*********","port_value":0}},"typed_extension_protocol_options":{"envoy.extensions.upstreams.http.v3.HttpProtocolOptions":{"@type":"type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions","common_http_protocol_options":{"idle_timeout":"300s"},"use_downstream_protocol_config":{"http_protocol_options":{},"http2_protocol_options":{}}}}},"last_updated":"2023-12-07T09:28:54.089Z"},{"version_info":"2023-12-07T09:28:45Z/14","cluster":{"@type":"type.googleapis.com/envoy.config.cluster.v3.Cluster","name":"PassthroughCluster","type":"ORIGINAL_DST","connect_timeout":"10s","lb_policy":"CLUSTER_PROVIDED","circuit_breakers":{"thresholds":[{"max_connections":**********,"max_pending_requests":**********,"max_requests":**********,"max_retries":**********,"track_remaining":true}]},"typed_extension_protocol_options":{"envoy.extensions.upstreams.http.v3.HttpProtocolOptions":{"@type":"type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions","common_http_protocol_options":{"idle_timeout":"300s"},"use_downstream_protocol_config":{"http_protocol_options":{},"http2_protocol_options":{}}}},"filters":[{"name":"istio.metadata_exchange","typed_config":{"@type":"type.googleapis.com/envoy.tcp.metadataexchange.config.MetadataExchange","protocol":"istio-peer-exchange"}}]},"last_updated":"2023-12-07T09:28:54.088Z"}]}]}`)
	//nolint
	mockConfigDumpListener = []byte(`{"configs":[{"@type":"type.googleapis.com/envoy.admin.v3.ListenersConfigDump","version_info":"2023-12-07T09:28:45Z/14","static_listeners":[{"listener":{"@type":"type.googleapis.com/envoy.config.listener.v3.Listener","address":{"socket_address":{"address":"0.0.0.0","port_value":15090}},"filter_chains":[{"filters":[{"name":"envoy.filters.network.http_connection_manager","typed_config":{"@type":"type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager","stat_prefix":"stats","route_config":{"virtual_hosts":[{"name":"backend","domains":["*"],"routes":[{"match":{"prefix":"/stats/prometheus"},"route":{"cluster":"prometheus_stats"}}]}]},"http_filters":[{"name":"envoy.filters.http.router","typed_config":{"@type":"type.googleapis.com/envoy.extensions.filters.http.router.v3.Router"}}]}}]}]},"last_updated":"2023-12-07T09:28:54.035Z"},{"listener":{"@type":"type.googleapis.com/envoy.config.listener.v3.Listener","address":{"socket_address":{"address":"0.0.0.0","port_value":15021}},"filter_chains":[{"filters":[{"name":"envoy.filters.network.http_connection_manager","typed_config":{"@type":"type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager","stat_prefix":"agent","route_config":{"virtual_hosts":[{"name":"backend","domains":["*"],"routes":[{"match":{"prefix":"/healthz/ready"},"route":{"cluster":"agent"}}]}]},"http_filters":[{"name":"envoy.filters.http.router","typed_config":{"@type":"type.googleapis.com/envoy.extensions.filters.http.router.v3.Router"}}]}}]}]},"last_updated":"2023-12-07T09:28:54.036Z"}],"dynamic_listeners":[{"name":"*************_15012","active_state":{"version_info":"2023-12-07T09:28:45Z/14","listener":{"@type":"type.googleapis.com/envoy.config.listener.v3.Listener","name":"*************_15012","address":{"socket_address":{"address":"*************","port_value":15012}},"filter_chains":[{"filters":[{"name":"istio.stats","typed_config":{"@type":"type.googleapis.com/udpa.type.v1.TypedStruct","type_url":"type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm","value":{"config":{"root_id":"stats_outbound","vm_config":{"vm_id":"tcp_stats_outbound","runtime":"envoy.wasm.runtime.null","code":{"local":{"inline_string":"envoy.wasm.stats"}}},"configuration":{"@type":"type.googleapis.com/google.protobuf.StringValue","value":"{\n  \"debug\": \"false\",\n  \"stat_prefix\": \"istio\"\n}\n"}}}}},{"name":"envoy.filters.network.tcp_proxy","typed_config":{"@type":"type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy","stat_prefix":"outbound|15012||istiod.istio-system.svc.cluster.local","cluster":"outbound|15012||istiod.istio-system.svc.cluster.local"}}]}],"traffic_direction":"OUTBOUND","bind_to_port":false},"last_updated":"2023-12-07T09:28:54.152Z"}}]}]}`)
	//nolint
	mockConfigDumpRoute = []byte(`{"configs":[{"@type":"type.googleapis.com/envoy.admin.v3.RoutesConfigDump","static_route_configs":[{"route_config":{"@type":"type.googleapis.com/envoy.config.route.v3.RouteConfiguration","name":"InboundPassthroughClusterIpv4","virtual_hosts":[{"name":"inbound|http|0","domains":["*"],"routes":[{"match":{"prefix":"/"},"route":{"cluster":"InboundPassthroughClusterIpv4","timeout":"0s","max_stream_duration":{"max_stream_duration":"0s","grpc_timeout_header_max":"0s"}},"decorator":{"operation":":0/*"},"name":"default"}]}],"validate_clusters":false},"last_updated":"2023-12-07T09:28:54.183Z"},{"route_config":{"@type":"type.googleapis.com/envoy.config.route.v3.RouteConfiguration","virtual_hosts":[{"name":"backend","domains":["*"],"routes":[{"match":{"prefix":"/stats/prometheus"},"route":{"cluster":"prometheus_stats"}}]}]},"last_updated":"2023-12-07T09:28:54.035Z"}],"dynamic_route_configs":[{"version_info":"2023-12-07T09:28:45Z/14","route_config":{"@type":"type.googleapis.com/envoy.config.route.v3.RouteConfiguration","name":"kube-dns.kube-system.svc.cluster.local:9153","virtual_hosts":[{"name":"kube-dns.kube-system.svc.cluster.local:9153","domains":["*"],"routes":[{"match":{"prefix":"/"},"route":{"cluster":"outbound|9153||kube-dns.kube-system.svc.cluster.local","timeout":"0s","retry_policy":{"retry_on":"connect-failure,refused-stream,unavailable,cancelled,retriable-status-codes","num_retries":2,"retry_host_predicate":[{"name":"envoy.retry_host_predicates.previous_hosts","typed_config":{"@type":"type.googleapis.com/envoy.extensions.retry.host.previous_hosts.v3.PreviousHostsPredicate"}}],"host_selection_retry_max_attempts":"5","retriable_status_codes":[503]},"max_grpc_timeout":"0s"},"decorator":{"operation":"kube-dns.kube-system.svc.cluster.local:9153/*"},"name":"default"}],"include_request_attempt_count":true}],"validate_clusters":false,"ignore_port_in_host_matching":true},"last_updated":"2023-12-07T09:28:54.193Z"},{"version_info":"2023-12-07T09:28:45Z/14","route_config":{"@type":"type.googleapis.com/envoy.config.route.v3.RouteConfiguration","name":"8080","virtual_hosts":[{"name":"allow_any","domains":["*"],"routes":[{"match":{"prefix":"/"},"route":{"cluster":"PassthroughCluster","timeout":"0s","max_grpc_timeout":"0s"},"name":"allow_any"}],"include_request_attempt_count":true},{"name":"fortio.default.svc.cluster.local:8080","domains":["fortio.default.svc.cluster.local","fortio","fortio.default.svc","fortio.default","************"],"routes":[{"match":{"prefix":"/"},"route":{"cluster":"outbound|8080||fortio.default.svc.cluster.local","timeout":"0s","retry_policy":{"retry_on":"connect-failure,refused-stream,unavailable,cancelled,retriable-status-codes","num_retries":2,"retry_host_predicate":[{"name":"envoy.retry_host_predicates.previous_hosts","typed_config":{"@type":"type.googleapis.com/envoy.extensions.retry.host.previous_hosts.v3.PreviousHostsPredicate"}}],"host_selection_retry_max_attempts":"5","retriable_status_codes":[503]},"max_grpc_timeout":"0s"},"decorator":{"operation":"fortio.default.svc.cluster.local:8080/*"},"name":"default"}],"include_request_attempt_count":true},{"name":"nfd-master.kube-system.svc.cluster.local:8080","domains":["nfd-master.kube-system.svc.cluster.local","nfd-master.kube-system","nfd-master.kube-system.svc","************"],"routes":[{"match":{"prefix":"/"},"route":{"cluster":"outbound|8080||nfd-master.kube-system.svc.cluster.local","timeout":"0s","retry_policy":{"retry_on":"connect-failure,refused-stream,unavailable,cancelled,retriable-status-codes","num_retries":2,"retry_host_predicate":[{"name":"envoy.retry_host_predicates.previous_hosts","typed_config":{"@type":"type.googleapis.com/envoy.extensions.retry.host.previous_hosts.v3.PreviousHostsPredicate"}}],"host_selection_retry_max_attempts":"5","retriable_status_codes":[503]},"max_grpc_timeout":"0s"},"decorator":{"operation":"nfd-master.kube-system.svc.cluster.local:8080/*"},"name":"default"}],"include_request_attempt_count":true}],"validate_clusters":false,"ignore_port_in_host_matching":true},"last_updated":"2023-12-07T09:28:54.193Z"}]}]}`)
	//nolint
	mockConfigDumpEndpoint = []byte(`{"cluster_statuses":[{"name":"outbound|8080||nfd-master.kube-system.svc.cluster.local","added_via_api":true,"host_statuses":[{"address":{"socket_address":{"address":"*********","port_value":8080}},"stats":[{"name":"cx_connect_fail"},{"name":"cx_total"},{"name":"rq_error"},{"name":"rq_success"},{"name":"rq_timeout"},{"name":"rq_total"},{"type":"GAUGE","name":"cx_active"},{"type":"GAUGE","name":"rq_active"}],"health_status":{"eds_health_status":"HEALTHY"},"weight":1,"locality":{"region":"gz","zone":"zoneC"}}],"circuit_breakers":{"thresholds":[{"max_connections":**********,"max_pending_requests":**********,"max_requests":**********,"max_retries":**********},{"priority":"HIGH","max_connections":1024,"max_pending_requests":1024,"max_requests":1024,"max_retries":3}]},"observability_name":"outbound|8080||nfd-master.kube-system.svc.cluster.local","eds_service_name":"outbound|8080||nfd-master.kube-system.svc.cluster.local"},{"name":"inbound|5000||","added_via_api":true,"circuit_breakers":{"thresholds":[{"max_connections":**********,"max_pending_requests":**********,"max_requests":**********,"max_retries":**********},{"priority":"HIGH","max_connections":1024,"max_pending_requests":1024,"max_requests":1024,"max_retries":3}]},"observability_name":"inbound|5000||"}]}`)
	//nolint
	mockConfigDumpSecret = []byte(`{"configs":[{"@type":"type.googleapis.com/envoy.admin.v3.SecretsConfigDump","dynamic_active_secrets":[{"name":"default","last_updated":"2023-12-11T09:28:54.365Z","secret":{"@type":"type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.Secret","name":"default","tls_certificate":{"certificate_chain":{"inline_bytes":"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"},"private_key":{"inline_bytes":"W3JlZGFjdGVkXQ=="}}}},{"name":"ROOTCA","last_updated":"2023-12-07T09:28:54.141Z","secret":{"@type":"type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.Secret","name":"ROOTCA","validation_context":{"trusted_ca":{"inline_bytes":"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"}}}}]}]}`)
	//模拟configdump
	mockConfigDump = []byte(`test config dump`)

	kubeConfigName = "test-kubeconfig"
)

func buildMockClusters(clusterType string) *[]meta.Cluster {
	cluster := []meta.Cluster{
		{
			InstanceUUID:          testInstanceUUID,
			ClusterUUID:           testClusterUUID,
			ClusterName:           testClusterName,
			ClusterType:           clusterType,
			Region:                testRegion,
			IstioInstallNamespace: testIstioInstallNamespace,
		},
	}
	return &cluster
}

func buildMockNamespace() *v1.Namespace {
	ns1 := &v1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: "ns-1",
		},
	}
	return ns1
}

func TestNamespaceList(t *testing.T) {
	ctrl := gomock.NewController(t)
	cases := []struct {
		name          string
		instanceUUID  string
		option        *Option
		mockNamespace *v1.Namespace
		mockCluster   *[]meta.Cluster
		expectErr     bool
	}{
		{
			name:          "success-standalone",
			option:        NewOption(mockDB),
			instanceUUID:  testInstanceUUID,
			mockNamespace: buildMockNamespace(),
			mockCluster:   buildMockClusters(testClusterTypePrimary),
			expectErr:     false,
		},
		{
			name:          "success-hosting",
			option:        NewOption(mockDB),
			instanceUUID:  testInstanceUUID,
			mockNamespace: buildMockNamespace(),
			mockCluster:   buildMockClusters(testClusterTypeExternal),
			expectErr:     false,
		},
	}
	for _, c := range cases {
		service := NewDiagnosisService(c.option)

		cceService := func() cce.ClientInterface {
			mockK8sClient := cce_mock.NewMockClientInterface(ctrl)
			fakeClient := kube.NewFakeClient()
			_, _ = fakeClient.Kube().CoreV1().Namespaces().
				Create(context.TODO(), c.mockNamespace, metav1.CreateOptions{})
			mockK8sClient.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(fakeClient, nil).AnyTimes()
			return mockK8sClient
		}()

		clusterModel := func() cluster.ServiceInterface {
			mockClusterModel := cluster_mock.NewMockServiceInterface(ctrl)
			mockClusterModel.EXPECT().GetAllClusterByInstanceUUID(gomock.Any(), gomock.Any()).
				Return(c.mockCluster, nil).AnyTimes()
			return mockClusterModel
		}()

		service.cceService = cceService
		service.clusterModel = clusterModel

		_, err := service.NamespaceList(ctx, c.instanceUUID)
		if (err != nil) != c.expectErr {
			if err != nil {
				fmt.Errorf("%s", err)
			}
			t.Errorf("Case %s NamespaceList failed expect isError=%s but actual not", c.name, strconv.FormatBool(c.expectErr))
		}
	}
}

func buildInstanceInfo(scope meta.InstanceManageScope) *meta.Instances {
	instanceInfo := &meta.Instances{
		InstanceUUID:          testInstanceUUID,
		InstanceName:          testInstanceName,
		InstanceType:          testInstanceType,
		IstioVersion:          testdata.Version1146,
		IstioInstallNamespace: istioInstallNamespace,
		Region:                region,
		InstanceManageScope:   string(scope),
	}
	return instanceInfo
}

func buildMockCluster(clusterType string) *meta.Cluster {
	cluster := meta.Cluster{
		InstanceUUID:          testInstanceUUID,
		ClusterUUID:           testClusterUUID,
		ClusterName:           testClusterName,
		ClusterType:           clusterType,
		Region:                testRegion,
		IstioInstallNamespace: testIstioInstallNamespace,
	}
	return &cluster
}

func mockProxyStatus() map[string][]byte {
	data := `[{
        "cluster_id": "gz-cce-hh4z2ea4",
        "proxy": "istio-eastwestgateway-f49dcdfb-p54lv.istio-system",
        "istio_version": "1.16.5",
        "cluster_sent": "a86cf6e7-4717-4b8c-a8a6-2ee3accddf1a",
        "cluster_acked": "a86cf6e7-4717-4b8c-a8a6-2ee3accddf1a",
        "listener_sent": "62483ea6-fe5a-4d5e-92cd-58325430e0ce",
        "listener_acked": "62483ea6-fe5a-4d5e-92cd-58325430e0ce",
        "endpoint_sent": "cf6ee73b-6701-49c2-81d3-c9112a69c0c6",
        "endpoint_acked": "cf6ee73b-6701-49c2-81d3-c9112a69c0c6"
    },
    {
        "cluster_id": "gz-cce-hh4z2ea4",
        "proxy": "sleep-69cfb4968f-vvq9q.default",
        "istio_version": "1.16.5",
        "cluster_sent": "5a29eea0-705b-4610-ba2f-6c29a4e31633",
        "cluster_acked": "5a29eea0-705b-4610-ba2f-6c29a4e31633",
        "listener_sent": "b1e46c11-a104-4aff-9a18-2a80b7365c20",
        "listener_acked": "b1e46c11-a104-4aff-9a18-2a80b7365c20",
        "route_sent": "69cbd91e-7a50-4a71-9700-b0bc9f6b3134",
        "route_acked": "69cbd91e-7a50-4a71-9700-b0bc9f6b3134",
        "endpoint_sent": "d85b34d5-7dd9-42b9-b304-d8a05488a601",
        "endpoint_acked": "d85b34d5-7dd9-42b9-b304-d8a05488a601"
    }]`
	mockData := map[string][]byte{
		"istiod-66c5ff9789-vflhj": []byte(data),
	}
	return mockData
}

func TestProxyStatusList(t *testing.T) {
	ctrl := gomock.NewController(t)
	cases := []struct {
		name         string
		instanceUUID string
		namespace    string
		page         *vo.Page
		listFilter   *vo.DiagnosisListFilter
		option       *Option
		instance     *meta.Instances
		clusters     *meta.Cluster
		expectErr    bool
	}{
		{
			name:       "success-standalone",
			option:     NewOption(mockDB),
			listFilter: &vo.DiagnosisListFilter{Namespace: namespace},
			page:       vo.GetPageParam(ctx, nil),
			instance:   buildInstanceInfo(meta.InstanceManageClusterScope),
			clusters:   buildMockCluster(testClusterTypePrimary),
			expectErr:  false,
		},
		{
			name:       "success-hosting",
			option:     NewOption(mockDB),
			listFilter: &vo.DiagnosisListFilter{Namespace: namespace},
			page:       vo.GetPageParam(ctx, nil),
			instance:   buildInstanceInfo(meta.InstanceManageNamespaceScope),
			clusters:   buildMockCluster(testClusterTypePrimary),
			expectErr:  false,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			patches := gomonkey.ApplyFunc(NewClient, func(_ csmContext.CsmContext, _ kube.Client) CLIClient {
				return &Client{}
			})
			defer patches.Reset()

			client := &Client{}
			patches.ApplyMethod(reflect.TypeOf(client), "AllDiscoveryDo",
				func(_ *Client, _ context.Context, _ string, _ string) (map[string][]byte, error) {
					return mockProxyStatus(), nil
				})
			defer patches.Reset()

			cceService := func() cce.ClientInterface {
				mockK8sClient := cce_mock.NewMockClientInterface(ctrl)
				fakeClient := kube.NewFakeClient()
				mockK8sClient.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fakeClient, nil).AnyTimes()
				return mockK8sClient
			}()

			instanceModel := func() instance.ServiceInterface {
				mockInstanceModel := instance_mock.NewMockServiceInterface(ctrl)
				mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).
					Return(c.instance, nil).AnyTimes()
				if c.instance.InstanceManageScope == string(meta.InstanceManageClusterScope) {
					mockInstanceModel.EXPECT().GetInstanceIstiodCluster(gomock.Any(), gomock.Any()).
						Return(c.clusters, meta.StandaloneMeshType, nil).AnyTimes()
				}
				return mockInstanceModel
			}()

			if c.instance.InstanceManageScope == string(meta.InstanceManageNamespaceScope) {
				mockValue := map[string]interface{}{
					"clusterId":   "xxx",
					"clusterName": "xxx",
				}

				mockHostingRegion := map[string]interface{}{
					"bj": mockValue,
				}
				c.option.HostingRegion = mockHostingRegion
			}

			service := NewDiagnosisService(c.option)

			service.cceService = cceService
			service.instanceModel = instanceModel

			_, _, err := service.ProxyStatusList(ctx, c.instanceUUID, c.page, c.listFilter)
			if (err != nil) != c.expectErr {
				if err != nil {
					fmt.Errorf("%s", err)
				}
				t.Errorf("Case %s ProxyStatusList failed expect isError=%s but actual not, error %v",
					c.name, strconv.FormatBool(c.expectErr), err)
			}
		})
	}
}

func TestProxyConfigList(t *testing.T) {
	ctrl := gomock.NewController(t)
	cases := []struct {
		name         string
		instanceUUID string
		namespace    string
		page         *vo.Page
		option       *Option
		instance     *meta.Instances
		clusters     *meta.Cluster
		pcr          *service_meta.ProxyConfigRequest
		expectErr    bool
	}{
		{
			name:     "success-standalone-cluster",
			option:   NewOption(mockDB),
			page:     vo.GetPageParam(ctx, nil),
			instance: buildInstanceInfo(meta.InstanceManageClusterScope),
			clusters: buildMockCluster(testClusterTypePrimary),
			pcr: &service_meta.ProxyConfigRequest{
				Namespace: namespace,
				PodName:   podName,
				TypeName:  "cluster",
			},
			expectErr: false,
		},
		{
			name:     "success-standalone-listener",
			option:   NewOption(mockDB),
			page:     vo.GetPageParam(ctx, nil),
			instance: buildInstanceInfo(meta.InstanceManageClusterScope),
			clusters: buildMockCluster(testClusterTypePrimary),
			pcr: &service_meta.ProxyConfigRequest{
				Namespace: namespace,
				PodName:   podName,
				TypeName:  "listener",
			},
			expectErr: false,
		},
		{
			name:     "success-standalone-route",
			option:   NewOption(mockDB),
			page:     vo.GetPageParam(ctx, nil),
			instance: buildInstanceInfo(meta.InstanceManageClusterScope),
			clusters: buildMockCluster(testClusterTypePrimary),
			pcr: &service_meta.ProxyConfigRequest{
				Namespace: namespace,
				PodName:   podName,
				TypeName:  "route",
			},
			expectErr: false,
		},
		{
			name:     "success-standalone-endpoint",
			option:   NewOption(mockDB),
			page:     vo.GetPageParam(ctx, nil),
			instance: buildInstanceInfo(meta.InstanceManageClusterScope),
			clusters: buildMockCluster(testClusterTypePrimary),
			pcr: &service_meta.ProxyConfigRequest{
				Namespace: namespace,
				PodName:   podName,
				TypeName:  "endpoint",
			},
			expectErr: false,
		},
		{
			name:     "success-standalone-secret",
			option:   NewOption(mockDB),
			page:     vo.GetPageParam(ctx, nil),
			instance: buildInstanceInfo(meta.InstanceManageClusterScope),
			clusters: buildMockCluster(testClusterTypePrimary),
			pcr: &service_meta.ProxyConfigRequest{
				Namespace: namespace,
				PodName:   podName,
				TypeName:  "secret",
			},
			expectErr: false,
		},
		{
			name:     "success-standalone-configDump",
			option:   NewOption(mockDB),
			page:     vo.GetPageParam(ctx, nil),
			instance: buildInstanceInfo(meta.InstanceManageClusterScope),
			clusters: buildMockCluster(testClusterTypePrimary),
			pcr: &service_meta.ProxyConfigRequest{
				Namespace: namespace,
				PodName:   podName,
				TypeName:  "configDump",
			},
			expectErr: false,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			patches := gomonkey.ApplyFunc(NewClient, func(_ csmContext.CsmContext, _ kube.Client) CLIClient {
				return &Client{}
			})
			defer patches.Reset()

			client := &Client{}
			patches.ApplyMethod(reflect.TypeOf(client), "EnvoyDoWithPort",
				func(_ *Client, _ context.Context, _ string, _ string, _ string, _ string, _ int) ([]byte, error) {
					if configdump.ProxyConfig(c.pcr.TypeName) == configdump.Cluster {
						return mockConfigDumpCluster, nil
					}
					if configdump.ProxyConfig(c.pcr.TypeName) == configdump.Listener {
						return mockConfigDumpListener, nil
					}
					if configdump.ProxyConfig(c.pcr.TypeName) == configdump.Route {
						return mockConfigDumpRoute, nil
					}
					if configdump.ProxyConfig(c.pcr.TypeName) == configdump.Endpoint {
						return mockConfigDumpEndpoint, nil
					}
					if configdump.ProxyConfig(c.pcr.TypeName) == configdump.Secret {
						return mockConfigDumpSecret, nil
					}
					return nil, nil
				})
			defer patches.Reset()

			cceService := func() cce.ClientInterface {
				mockK8sClient := cce_mock.NewMockClientInterface(ctrl)
				fakeClient := kube.NewFakeClient()
				mockK8sClient.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fakeClient, nil).AnyTimes()
				return mockK8sClient
			}()

			// TODO 先保留，后续考虑删除以下代码
			//instanceModel := func() instance.ServiceInterface {
			//	mockInstanceModel := instance_mock.NewMockServiceInterface(ctrl)
			//	mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).
			//		Return(c.instance, nil).AnyTimes()
			//	if c.instance.InstanceManageScope == string(meta.InstanceManageClusterScope) {
			//		mockInstanceModel.EXPECT().GetInstanceIstiodCluster(gomock.Any(), gomock.Any()).
			//			Return(c.clusters, meta.StandaloneMeshType, nil).AnyTimes()
			//	}
			//	return mockInstanceModel
			//}()

			service := NewDiagnosisService(c.option)

			service.cceService = cceService
			//service.instanceModel = instanceModel

			_, _, err := service.ProxyConfigList(ctx, c.instanceUUID, c.pcr, c.page)
			if (err != nil) != c.expectErr {
				if err != nil {
					fmt.Errorf("%s", err)
				}
				t.Errorf("Case %s ProxyStatusList failed expect isError=%s but actual not, error %v",
					c.name, strconv.FormatBool(c.expectErr), err)
			}
		})
	}
}

// TestExceptionList 测试异常列表函数
func TestExceptionList(t *testing.T) {
	// var patches *gomonkey.Patches
	service := NewDiagnosisService(NewOption(mockDB))
	// patches = gomonkey.NewPatches()
	cases := []struct {
		service      *Service
		instanceUUID string
		namespace    string
		page         *vo.Page
		want         []*smeta.Exception
		errFlag      bool
		Str          string
		instance     *meta.Instances
		clusters     *meta.Cluster
	}{
		{
			instance: buildInstanceInfo(meta.InstanceManageClusterScope),
			clusters: buildMockCluster(testClusterTypePrimary),
			service:  service,
			page:     &vo.Page{PageNo: 1, PageSize: 20},
			Str:      `Error [IST0101] (VirtualService default/details) Referenced host+subset in destinationrule not found: "details+v1"`,
			want: []*smeta.Exception{
				{
					Name:        "(VirtualService default/details)",
					Level:       "ERROR",
					Code:        "[IST0101]",
					Description: " Referenced host+subset in destinationrule not found: \"details+v1\"",
				},
			},
			errFlag: false,
		},
		{
			instance: buildInstanceInfo(meta.InstanceManageClusterScope),
			clusters: buildMockCluster(testClusterTypePrimary),
			service:  service,
			page:     &vo.Page{PageNo: 1, PageSize: 20},
			Str:      `Error [IST0102] (VirtualService default/reviews) Referenced host+subset in destinationrule not found`,
			want: []*smeta.Exception{
				{
					Name:        "(VirtualService default/reviews)",
					Level:       "ERROR",
					Code:        "[IST0102]",
					Description: " Referenced host+subset in destinationrule not found",
				},
			},
			errFlag: false,
		},
	}
	// Mocking GetInstanceByInstanceUUID
	/* 	patches := gomonkey.ApplyMethod(reflect.TypeOf(service.instanceModel),
	"GetInstanceByInstanceUUID", func(_ *instances.Service, _ csmcontext.CsmContext, _ string) (*meta.Instances, error) {
		return &meta.Instances{}, nil
	}) */

	// Mocking Getwd
	patches := gomonkey.ApplyFunc(os.Getwd, func() (string, error) {
		return "/tmp", nil
	})

	// Mocking GetIstioCtl
	patches.ApplyFunc(util.GetIstioCtl, func(_ csmcontext.CsmContext) string {
		return "istioctl"
	})
	patches.ApplyFunc(file.RemoveFile, func(_ string) error {
		return nil
	})
	// Mocking GetAllClusterByInstanceUUID
	/* 	patches.ApplyMethod(reflect.TypeOf(service.clusterModel), "GetAllClusterByInstanceUUID",
	func(_ *cluster.Service, _ *csmContext.CsmContext, _ string) ([]*meta.Cluster, error) {
		return []*meta.Cluster{}, nil
	}) */

	// Mocking NewAnaylze
	patches.ApplyFunc(NewAnaylze, func(_ *ClusterConf) *Analyze {
		params := &ClusterConf{
			Region:         "testRegion",
			ClusterName:    "testClusterName",
			CceClusterUuid: "testCceClusterUuid",
			Version:        "testVersion",
			Namespace:      "testNamespace",
		}
		return &Analyze{
			ClusterConf: params,
			opt:         version.NewOption(),
			cceService:  cce.NewClientService(),
		}
	})

	// Mocking WriteKubeConfig
	a := NewAnaylze(nil)
	patches.ApplyMethod(reflect.TypeOf(a), "WriteKubeConfig", func(analyze *Analyze, _ csmContext.CsmContext) error {
		var kubeConfigPath string
		if analyze.IsRemote {
			kubeConfigPath = path.Join(analyze.ConfigPath, constants.HostingBaseIstioTemplate, analyze.Version, kubeConfigName)
		} else {
			kubeConfigPath = path.Join(analyze.ConfigPath, constants.BaseIstioTemplate, analyze.Version, kubeConfigName)
		}
		analyze.KubeConfigPath = kubeConfigPath
		return nil
	})

	// Mocking AnalyzeCluster
	defer patches.Reset()
	ctrl := gomock.NewController(t)
	clusterModel := func() cluster.ServiceInterface {
		mockClusterModel := cluster_mock.NewMockServiceInterface(ctrl)
		mockClusterModel.EXPECT().GetAllClusterByInstanceUUID(gomock.Any(), gomock.Any()).
			Return(&[]meta.Cluster{{}}, nil).AnyTimes()
		return mockClusterModel
	}()
	for _, c := range cases {
		t.Run(c.instanceUUID, func(t *testing.T) {
			instanceModel := func() instance.ServiceInterface {
				mockInstanceModel := instance_mock.NewMockServiceInterface(ctrl)
				mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).
					Return(c.instance, nil).AnyTimes()
				if c.instance.InstanceManageScope == string(meta.InstanceManageClusterScope) {
					mockInstanceModel.EXPECT().GetInstanceIstiodCluster(gomock.Any(), gomock.Any()).
						Return(c.clusters, meta.StandaloneMeshType, nil).AnyTimes()
				}

				return mockInstanceModel
			}()
			service.instanceModel = instanceModel
			service.clusterModel = clusterModel
			patches.ApplyMethod(reflect.TypeOf(a), "AnalyzeCluster", func(analyze *Analyze, _ csmContext.CsmContext) ([]byte, error) {
				/* 			if analyze.IsRemote && strings.Contains(analyze.KubeConfigPath, constants.HostingBaseIstioTemplate) {
				   				return []byte(c.Str), nil
				   			} else if !analyze.IsRemote && strings.Contains(analyze.KubeConfigPath, constants.BaseIstioTemplate) {
				   				return []byte(c.Str), nil
				   			}
				   			return nil, fmt.Errorf("wrong path") */
				return []byte(c.Str), nil
			})
			res, pageResult, err := service.ExceptionList(ctx, c.instanceUUID, c.namespace, c.page)
			if (err != nil) != c.errFlag {
				t.Errorf("Case %s ExceptionList failed, expect isError=%v but actual not, error %v",
					c.instanceUUID, c.errFlag, err)
			}
			if int(pageResult.TotalCount) != len(res) {
				t.Errorf("Case %s ExceptionList failed, expect: %v but actual: %v", c.instanceUUID, len(res), int(pageResult.TotalCount))
			}
			if !ExceptionsEqual(res, c.want) {
				t.Errorf("Case %s ExceptionList failed, expect: %v but actual: %v", c.instanceUUID, c.want, res)
			}
			// t.Logf("Case %s ExceptionList success", c.instanceUUID)
		})
	}
}

// ExceptionsEqual 函数比较两个 smeta.Exception 列表是否相等。
func ExceptionsEqual(a, b []*smeta.Exception) bool {
	if a == nil && b == nil {
		return true
	} else if a == nil || b == nil {
		return false
	}
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i].Name == b[i].Name && a[i].Level == b[i].Level && a[i].Code == b[i].Code &&
			a[i].Description == b[i].Description {
			continue
		} else {
			return false
		}
	}
	return true
}

// TestConfigDump is a test function for testing the ConfigDump method of DiagnosisService
func TestConfigDump(t *testing.T) {

	cases := []struct {
		option       *Option
		instanceUUID string
		namespace    string
		podName      string
		clusterName  string
		want         string
		instance     *meta.Instances
		clusters     *meta.Cluster
	}{
		{
			instance:     buildInstanceInfo(meta.InstanceManageClusterScope),
			clusters:     buildMockCluster(testClusterTypePrimary),
			option:       NewOption(mockDB),
			instanceUUID: "1",
			namespace:    "default",
			podName:      "test-123456789",
			clusterName:  "test-123456789",
			want:         "test config dump",
		},
		{
			instance:     buildInstanceInfo(meta.InstanceManageClusterScope),
			clusters:     buildMockCluster(testClusterTypePrimary),
			option:       NewOption(mockDB),
			instanceUUID: "2",
			namespace:    "default",
			podName:      "test-123456789",
			clusterName:  "test-123456789",
			want:         "test config dump",
		},
	}

	// var patches *gomonkey.Patches
	ctrl := gomock.NewController(t)

	cceService := func() cce.ClientInterface {
		mockK8sClient := cce_mock.NewMockClientInterface(ctrl)
		fakeClient := kube.NewFakeClient()
		mockK8sClient.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(fakeClient, nil).AnyTimes()
		return mockK8sClient
	}()
	//替换Newlient方法，返回空值
	patches := gomonkey.ApplyFunc(NewClient, func(_ csmContext.CsmContext, _ kube.Client) CLIClient {
		return &Client{}
	})
	patches.ApplyMethod(reflect.TypeOf(&Client{}), "EnvoyDoWithPort",
		func(_ *Client, _ context.Context, _ string, _ string, _ string, path string, _ int) ([]byte, error) {
			if path == "config_dump" {
				return mockConfigDump, nil
			}
			return nil, errors.New("error: get wrong path")
		})
	defer patches.Reset()

	for _, c := range cases {

		t.Run(c.instanceUUID, func(t *testing.T) {
			service := NewDiagnosisService(c.option)
			service.cceService = cceService
			if str, err := service.ConfigDump(ctx, c.instanceUUID, c.namespace, c.podName, c.clusterName); err != nil {
				t.Errorf("Case %s ConfigDump failed, error: %v", c.instanceUUID, err)
			} else if !strings.EqualFold(string(str), c.want) {
				t.Errorf("Case %s ConfigDump failed, expect: %v but actual: %v", c.instanceUUID, c.want, string(str))
			}

		})
	}
}
