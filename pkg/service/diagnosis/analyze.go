package diagnosis

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"os/exec"
	"path"
	"strings"

	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/spf13/viper"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	smeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	csmcontext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/file"
)

type Analyze struct {
	cceService cce.ClientInterface
	opt        *version.Option
	*ClusterConf
	IopTemplatePath   string
	KubeConfigPath    string
	VpcKubeConfigPath string // 添加 remote 集群时，用于生成 secret
	AnalyzeNamespace  string // 分析的 namespace
}

// ClusterConf 进行analyse 诊断的集群参数
type ClusterConf struct {
	// ConfigPath 表示参数路径，用于生成 kubeconfig 配置文件，具体为pwd + constants.Templates
	ConfigPath string
	// Version 表示安装的 istio 版本
	Version string
	// Namespace 表示 istio 安装的 namespace
	Namespace string
	// Region 表示 cce 集群所在的地域
	Region string
	// MeshInstanceId 版本 mesh 实例 id
	MeshInstanceId string
	// ClusterName 表示 cce 集群的名称
	ClusterName string
	// CceClusterUuid 表示 cce 集群 id
	CceClusterUuid string
	// IsRemote 表示是否远程集群
	IsRemote bool
	// InstanceRegion 表示实例所在的地域
	InstanceRegion string
	// IstioBinPath 表示 istioctl 路径
	IstioctlBin string
}

// NewAnaylze 使用 params 与 client 初始化 Anaylze
func NewAnaylze(params *ClusterConf) *Analyze {
	return &Analyze{
		ClusterConf: params,
		opt:         version.NewOption(),
		cceService:  cce.NewClientService(),
	}
}

// 获取kubeconfig信息，包括remote集群的kubeconfig信息
func (analyze *Analyze) WriteKubeConfig(ctx csmcontext.CsmContext) (err error) {
	ctx.CsmLogger().Infof("start get kubeconfig yaml ctx:%v, region:%s, cceClusterUuid:%s, KubeConfigTypeInternal:%s,meta MeshType:%s",
		ctx, analyze.Region, analyze.CceClusterUuid,
		cce_v2.KubeConfigTypeInternal, meta.StandaloneMeshType)
	kct := cce_v2.KubeConfigTypeInternal
	if viper.GetBool("local.dev") {
		ctx.CsmLogger().Infof("***analyze WriteKubeConfig: local develop mode to get NewClient ***")
		kct = cce_v2.KubeConfigTypePublic
	}
	kubeConfig, err := analyze.cceService.GetCCEClusterKubeConfigByClusterUUID(ctx, analyze.Region, analyze.CceClusterUuid,
		kct, meta.StandaloneMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("get kubeconfig yaml error %v", err)
		return err
	}
	uniqueName, err := analyze.getUniqueName()
	if err != nil {
		return err
	}
	kubeConfigName := uniqueName + ".yaml"
	var kubeConfigPath string
	if analyze.IsRemote {
		kubeConfigPath = path.Join(analyze.ConfigPath, constants.HostingBaseIstioTemplate, analyze.Version, kubeConfigName)
	} else {
		kubeConfigPath = path.Join(analyze.ConfigPath, constants.BaseIstioTemplate, analyze.Version, kubeConfigName)
	}
	err = file.RewriteFile(ctx, kubeConfigPath, kubeConfig)
	if err != nil {
		return err
	}
	analyze.KubeConfigPath = kubeConfigPath
	return nil
}

// getUniqueName 根据 istio 地区、版本、名称以及_analyze拼接唯一标识
func (analyze *Analyze) getUniqueName() (string, error) {
	if analyze == nil {
		return "", errors.New("istio analyze is null")
	}
	if analyze.ClusterConf == nil {
		return "", errors.New("istio cluster conf is null")
	}
	if analyze.Region == "" {
		return "", errors.New("istio region is null")
	}
	if analyze.ClusterName == "" {
		return "", errors.New("istio clusterName is null")
	}
	if analyze.CceClusterUuid == "" {
		return "", errors.New("istio CceClusterUuid is null")
	}
	if analyze.Version == "" {
		return "", errors.New("istio Version is null")
	}
	if analyze.Namespace == "" {
		return "", errors.New("istio namespace is null")
	}
	uniqueName := analyze.Region + "_" + analyze.ClusterName + "_" + analyze.CceClusterUuid + "_" + analyze.Version + "_" + analyze.Namespace + "_analyze"
	return uniqueName, nil
}

// AnalyzeCluster 执行analyze cluster的函数
func (analyze *Analyze) AnalyzeCluster(ctx csmcontext.CsmContext) ([]byte, error) {
	istioCtlBin := analyze.IstioctlBin
	analyzeCmd := istioCtlBin + fmt.Sprintf(" analyze --kubeconfig=%s -n %s", analyze.KubeConfigPath, analyze.AnalyzeNamespace)
	outStr, errStr, _ := ExecCmdOut(ctx, analyzeCmd)
	/* if len(errStr) > 0 || err != nil {
		return nil, fmt.Errorf("istioctl analyze err %v", err)
	} */
	if len(outStr) == 0 {
		return nil, fmt.Errorf("istioctl analyze err %v", string(errStr))
	}
	ctx.CsmLogger().Infof("kubectl analyze summary:%s", errStr)
	return outStr, nil
}

// Str2Exception 函数将字符串转换成 smeta.Exception 结构体
func Str2Exception(s string) (*smeta.Exception, error) {
	// var exception meta.Exception
	//区分方式：前两个空格 括号内容 其他为详情
	strs := strings.SplitN(s, " ", 3) //切分为3个字符串
	if len(strs) < 3 {
		csmlog.Debugf("exception format error, lenth of analyze result is less than 3 :" + s)
		return nil, nil
	}
	strs = append(strs[:len(strs)-1], strings.SplitAfterN(strs[2], ")", 2)...)
	return &smeta.Exception{
		Name:        strs[2],
		Level:       strings.ToUpper(strs[0]),
		Code:        strs[1],
		Description: strs[3],
	}, nil
}

// ExecCmdOut 执行命令并返回输出和错误
func ExecCmdOut(cc csmContext.CsmContext, cmdStr string) ([]byte, []byte, error) {
	cc.CsmLogger().Infof("command %s", cmdStr)
	ctx, cancel := context.WithTimeout(context.Background(), constants.CommandTimeOut)
	// 超时控制
	defer cancel()
	cmd := exec.CommandContext(ctx, "bash", "-c", cmdStr)
	var stdout, stderr bytes.Buffer
	// 标准输出
	cmd.Stdout = &stdout
	// 标准错误
	cmd.Stderr = &stderr
	if err := cmd.Start(); err != nil {
		errStr := stderr.Bytes()
		outStr := stdout.Bytes()
		info := fmt.Sprintf("Start() exec command: %s, outStr: %s, errString: %s, error: %v",
			cmdStr, outStr, errStr, err)
		// 返回异常，但是不报错，因为analyze包含error也会有异常
		if len(errStr) > 0 && strings.Contains(string(errStr), "Analyzers") {
			cc.CsmLogger().Infof(" the logs out:[%s] err:[%s]", string(outStr), string(errStr))
			return outStr, errStr, nil
		}
		cc.CsmLogger().Errorf(info)
		return nil, nil, fmt.Errorf("%v", info)
	}

	if err := cmd.Wait(); err != nil {
		errStr := stderr.Bytes()
		outStr := stdout.Bytes()
		info := fmt.Sprintf("Wait() exec command: %s, outStr: %s, errString: %s, error %v",
			cmdStr, outStr, errStr, err)
		// todo better way for removing crd in eks
		if len(errStr) > 0 && strings.Contains(string(errStr), "Analyzers") {
			cc.CsmLogger().Infof("the logs out:[%s] err:[%s]", string(outStr), string(errStr))
			return outStr, errStr, nil
		}
		cc.CsmLogger().Errorf(info)
		return nil, nil, fmt.Errorf("%v", info)
	}
	outStr, errStr := stdout.Bytes(), stderr.Bytes()
	cc.CsmLogger().Debugf("the log of executing command:[%s], out:[%s] err:[%s]", cmdStr, string(outStr), string(errStr))
	return outStr, errStr, nil
}
