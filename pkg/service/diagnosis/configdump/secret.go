package configdump

import (
	"fmt"
	"time"

	envoy_admin "github.com/envoyproxy/go-control-plane/envoy/admin/v3"
	auth "github.com/envoyproxy/go-control-plane/envoy/extensions/transport_sockets/tls/v3"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/diagnosis/sds"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

func GetSecret(wrapper *Wrapper) ([]*meta.PCSecret, error) {
	metaSecret := make([]*meta.PCSecret, 0)
	secretDump, err := wrapper.GetSecretConfigDump()
	if err != nil {
		return metaSecret, err
	}

	if len(secretDump.DynamicActiveSecrets) == 0 &&
		len(secretDump.DynamicWarmingSecrets) == 0 {
		fmt.Printf("No active or warming secrets found.")
		return metaSecret, nil
	}

	secretItems, err := GetEnvoySecrets(wrapper)
	if err != nil {
		return metaSecret, err
	}
	for _, s := range secretItems {
		notAfter, _ := time.Parse(time.RFC3339, s.NotAfter)
		notBefore, _ := time.Parse(time.RFC3339, s.NotBefore)
		metaSecret = append(metaSecret, &meta.PCSecret{
			ResourceName:  s.Name,
			Type:          s.Type,
			Status:        s.State,
			IsValid:       s.Valid,
			EffectiveDate: notBefore.Format("2006-01-02 15:04:05"),
			ExpiryDate:    notAfter.Format("2006-01-02 15:04:05"),
			SerialNumber:  s.SerialNumber,
		})
	}
	return metaSecret, nil
}

// GetEnvoySecrets parses the secrets section of the config dump into []SecretItem
func GetEnvoySecrets(wrapper *Wrapper) ([]sds.SecretItem, error) {
	secretConfigDump, err := wrapper.GetSecretConfigDump()
	if err != nil {
		return nil, err
	}

	proxySecretItems := make([]sds.SecretItem, 0)
	for _, warmingSecret := range secretConfigDump.DynamicWarmingSecrets {
		secret, err := parseDynamicSecret(warmingSecret, "WARMING")
		if err != nil {
			return nil, fmt.Errorf("failed building warming secret %s: %w",
				warmingSecret.Name, err)
		}
		proxySecretItems = append(proxySecretItems, secret)
	}
	for _, activeSecret := range secretConfigDump.DynamicActiveSecrets {
		secret, err := parseDynamicSecret(activeSecret, "ACTIVE")
		if err != nil {
			return nil, fmt.Errorf("failed building warming secret %s: %w",
				activeSecret.Name, err)
		}
		if activeSecret.VersionInfo == "uninitialized" {
			secret.State = "UNINITIALIZED"
		}
		proxySecretItems = append(proxySecretItems, secret)
	}
	return proxySecretItems, nil
}

func parseDynamicSecret(s *envoy_admin.SecretsConfigDump_DynamicSecret, state string) (sds.SecretItem, error) {
	builder := sds.NewSecretItemBuilder()
	builder.Name(s.Name).State(state)

	secretTyped := &auth.Secret{}
	err := s.GetSecret().UnmarshalTo(secretTyped)
	if err != nil {
		return sds.SecretItem{}, err
	}

	certChainSecret := secretTyped.
		GetTlsCertificate().
		GetCertificateChain().
		GetInlineBytes()
	caDataSecret := secretTyped.
		GetValidationContext().
		GetTrustedCa().
		GetInlineBytes()

	// seems as though the most straightforward way to tell whether this is a root ca or not
	// is to check whether the inline bytes of the cert chain or the trusted ca field is zero length
	if len(certChainSecret) > 0 {
		builder.Data(string(certChainSecret))
	} else if len(caDataSecret) > 0 {
		builder.Data(string(caDataSecret))
	}

	secret, err := builder.Build()
	if err != nil {
		return sds.SecretItem{}, fmt.Errorf("error building secret: %w", err)
	}

	return secret, nil
}
