package configdump

import (
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"strings"

	listener "github.com/envoyproxy/go-control-plane/envoy/config/listener/v3"
	route "github.com/envoyproxy/go-control-plane/envoy/config/route/v3"
	httpConn "github.com/envoyproxy/go-control-plane/envoy/extensions/filters/network/http_connection_manager/v3"
	tcp "github.com/envoyproxy/go-control-plane/envoy/extensions/filters/network/tcp_proxy/v3"
	"github.com/envoyproxy/go-control-plane/pkg/resource/v3"
	"github.com/envoyproxy/go-control-plane/pkg/wellknown"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

const (
	// HTTPListener identifies a listener as being of HTTP type by the presence of an HTTP connection manager filter
	HTTPListener = wellknown.HTTPConnectionManager

	// TCPListener identifies a listener as being of TCP type by the presence of TCP proxy filter
	TCPListener = wellknown.TCPProxy

	// BlackHoleCluster to catch traffic from routes with unresolved clusters. Traffic arriving here goes nowhere.
	BlackHoleCluster = "BlackHoleCluster"
)

var (
	plaintextHTTPALPNs = []string{"http/1.0", "http/1.1", "h2c"}
	istioHTTPPlaintext = []string{"istio", "istio-http/1.0", "istio-http/1.1", "istio-h2"}
	httpTLS            = []string{"http/1.0", "http/1.1", "h2c", "istio-http/1.0", "istio-http/1.1", "istio-h2"}
	tcpTLS             = []string{"istio-peer-exchange", "istio"}

	protDescrs = map[string][]string{
		"App: HTTP TLS":         httpTLS,
		"App: Istio HTTP Plain": istioHTTPPlaintext,
		"App: TCP TLS":          tcpTLS,
		"App: HTTP":             plaintextHTTPALPNs,
	}
)

// ListenerFilter is used to pass filter information into listener based config writer print functions
type ListenerFilter struct {
	Address string
	Port    uint32
	Type    string
	Verbose bool
}

// Verify returns true if the passed listener matches the filter fields
func (l *ListenerFilter) Verify(listener *listener.Listener) bool {
	if l.Address == "" && l.Port == 0 && l.Type == "" {
		return true
	}
	if l.Address != "" && !strings.EqualFold(retrieveListenerAddress(listener), l.Address) {
		return false
	}
	if l.Port != 0 && retrieveListenerPort(listener) != l.Port {
		return false
	}
	if l.Type != "" && !strings.EqualFold(retrieveListenerType(listener), l.Type) {
		return false
	}
	return true
}

func retrieveListenerAddress(l *listener.Listener) string {
	sockAddr := l.Address.GetSocketAddress()
	if sockAddr != nil {
		return sockAddr.Address
	}

	pipe := l.Address.GetPipe()
	if pipe != nil {
		return pipe.Path
	}

	return ""
}

func retrieveListenerPort(l *listener.Listener) uint32 {
	return l.Address.GetSocketAddress().GetPortValue()
}

// retrieveListenerType classifies a Listener as HTTP|TCP|HTTP+TCP|UNKNOWN
func retrieveListenerType(l *listener.Listener) string {
	nHTTP := 0
	nTCP := 0
	for _, filterChain := range getFilterChains(l) {
		for _, filter := range filterChain.GetFilters() {
			if filter.Name == HTTPListener {
				nHTTP++
			} else if filter.Name == TCPListener {
				if !strings.Contains(string(filter.GetTypedConfig().GetValue()), BlackHoleCluster) {
					nTCP++
				}
			}
		}
	}

	if nHTTP > 0 {
		if nTCP == 0 {
			return "HTTP"
		}
		return "HTTP+TCP"
	} else if nTCP > 0 {
		return "TCP"
	}

	return "UNKNOWN"
}

func getFilterChains(l *listener.Listener) []*listener.FilterChain {
	res := l.FilterChains
	if l.DefaultFilterChain != nil {
		res = append(res, l.DefaultFilterChain)
	}
	return res
}

func GetListener(wrapper *Wrapper, filter *ListenerFilter) ([]*meta.PCListener, error) {
	metaListener := make([]*meta.PCListener, 0)
	allListeners, err := retrieveSortedListenerSlice(wrapper)
	if err != nil {
		return metaListener, err
	}

	verifiedListeners := make([]*listener.Listener, 0, len(allListeners))
	for _, l := range allListeners {
		if filter.Verify(l) {
			verifiedListeners = append(verifiedListeners, l)
		}
	}

	// Sort by port, addr, type
	sort.Slice(verifiedListeners, func(i, j int) bool {
		iPort := retrieveListenerPort(verifiedListeners[i])
		jPort := retrieveListenerPort(verifiedListeners[j])
		if iPort != jPort {
			return iPort < jPort
		}
		iAddr := retrieveListenerAddress(verifiedListeners[i])
		jAddr := retrieveListenerAddress(verifiedListeners[j])
		if iAddr != jAddr {
			return iAddr < jAddr
		}
		iType := retrieveListenerType(verifiedListeners[i])
		jType := retrieveListenerType(verifiedListeners[j])
		return iType < jType
	})

	for _, l := range verifiedListeners {
		address := retrieveListenerAddress(l)
		port := retrieveListenerPort(l)
		matches := retrieveListenerMatches(l)
		sort.Slice(matches, func(i, j int) bool {
			return matches[i].destination > matches[j].destination
		})
		for _, match := range matches {
			metaListener = append(metaListener, &meta.PCListener{
				Address:     address,
				Port:        strconv.Itoa(int(port)),
				MatchRule:   match.match,
				Destination: match.destination,
			})
		}
	}
	return metaListener, nil
}

func retrieveSortedListenerSlice(wp *Wrapper) ([]*listener.Listener, error) {
	if wp == nil {
		return nil, fmt.Errorf("configdump writer has not been inited")
	}
	listenerDump, err := wp.GetListenerConfigDump()
	if err != nil {
		return nil, fmt.Errorf("listener dump: %w", err)
	}
	allListeners := make([]*listener.Listener, 0)
	for _, l := range listenerDump.DynamicListeners {
		if l.ActiveState != nil && l.ActiveState.Listener != nil {
			listenerTyped := &listener.Listener{}
			// Support v2 or v3 in config dump. See ads.go:RequestedTypes for more info.
			l.ActiveState.Listener.TypeUrl = resource.ListenerType
			err = l.ActiveState.Listener.UnmarshalTo(listenerTyped)
			if err != nil {
				return nil, fmt.Errorf("unmarshal listener: %w", err)
			}
			allListeners = append(allListeners, listenerTyped)
		}
	}

	for _, l := range listenerDump.StaticListeners {
		if l.Listener != nil {
			listenerTyped := &listener.Listener{}
			// Support v2 or v3 in config dump. See ads.go:RequestedTypes for more info.
			l.Listener.TypeUrl = resource.ListenerType
			err = l.Listener.UnmarshalTo(listenerTyped)
			if err != nil {
				return nil, fmt.Errorf("unmarshal listener: %w", err)
			}
			allListeners = append(allListeners, listenerTyped)
		}
	}

	return allListeners, nil
}

type filterchain struct {
	match       string
	destination string
}

func retrieveListenerMatches(l *listener.Listener) []filterchain {
	fChains := getFilterChains(l)
	resp := make([]filterchain, 0, len(fChains))
	for _, filterChain := range fChains {
		match := filterChain.FilterChainMatch
		if match == nil {
			match = &listener.FilterChainMatch{}
		}
		// filterChaince also has SuffixLen, SourceType, SourcePrefixRanges which are not rendered.
		var descrs []string
		if len(match.ServerNames) > 0 {
			descrs = append(descrs, fmt.Sprintf("SNI: %s", strings.Join(match.ServerNames, ",")))
		}
		if len(match.TransportProtocol) > 0 {
			descrs = append(descrs, fmt.Sprintf("Trans: %s", match.TransportProtocol))
		}

		if len(match.ApplicationProtocols) > 0 {
			found := false
			for protDescr, protocols := range protDescrs {
				if reflect.DeepEqual(match.ApplicationProtocols, protocols) {
					found = true
					descrs = append(descrs, protDescr)
					break
				}
			}
			if !found {
				descrs = append(descrs, fmt.Sprintf("App: %s", strings.Join(match.ApplicationProtocols, ",")))
			}
		}

		port := ""
		if match.DestinationPort != nil {
			port = fmt.Sprintf(":%d", match.DestinationPort.GetValue())
		}
		if len(match.PrefixRanges) > 0 {
			var pf []string
			for _, p := range match.PrefixRanges {
				pf = append(pf, fmt.Sprintf("%s/%d", p.AddressPrefix, p.GetPrefixLen().GetValue()))
			}
			descrs = append(descrs, fmt.Sprintf("Addr: %s%s", strings.Join(pf, ","), port))
		} else if port != "" {
			descrs = append(descrs, fmt.Sprintf("Addr: *%s", port))
		}
		if len(descrs) == 0 {
			descrs = []string{"ALL"}
		}

		fc := filterchain{
			destination: getFilterType(filterChain.GetFilters()),
			match:       strings.Join(descrs, "; "),
		}
		resp = append(resp, fc)
	}
	return resp
}

func getFilterType(filters []*listener.Filter) string {
	for _, filter := range filters {
		if filter.Name == HTTPListener {
			httpProxy := &httpConn.HttpConnectionManager{}
			// Allow Unmarshal to work even if Envoy and istioctl are different
			filter.GetTypedConfig().TypeUrl = "type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager"
			err := filter.GetTypedConfig().UnmarshalTo(httpProxy)
			if err != nil {
				return err.Error()
			}
			if httpProxy.GetRouteConfig() != nil {
				return describeRouteConfig(httpProxy.GetRouteConfig())
			}
			if httpProxy.GetRds().GetRouteConfigName() != "" {
				return fmt.Sprintf("Route: %s", httpProxy.GetRds().GetRouteConfigName())
			}
			return "HTTP"
		} else if filter.Name == TCPListener {
			if !strings.Contains(string(filter.GetTypedConfig().GetValue()), BlackHoleCluster) {
				tcpProxy := &tcp.TcpProxy{}
				// Allow Unmarshal to work even if Envoy and istioctl are different
				filter.GetTypedConfig().TypeUrl = "type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy"
				err := filter.GetTypedConfig().UnmarshalTo(tcpProxy)
				if err != nil {
					return err.Error()
				}
				if strings.Contains(tcpProxy.GetCluster(), "Cluster") {
					return tcpProxy.GetCluster()
				}
				return fmt.Sprintf("Cluster: %s", tcpProxy.GetCluster())
			}
		}
	}
	return "Non-HTTP/Non-TCP"
}

func describeRouteConfig(route *route.RouteConfiguration) string {
	if cluster := getMatchAllCluster(route); cluster != "" {
		return cluster
	}
	vhosts := []string{}
	for _, vh := range route.GetVirtualHosts() {
		if describeDomains(vh) == "" {
			vhosts = append(vhosts, describeRoutes(vh))
		} else {
			vhosts = append(vhosts, fmt.Sprintf("%s %s", describeDomains(vh), describeRoutes(vh)))
		}
	}
	return fmt.Sprintf("Inline Route: %s", strings.Join(vhosts, "; "))
}

// If this is a route that matches everything and forwards to a cluster, just report the cluster.
func getMatchAllCluster(er *route.RouteConfiguration) string {
	if len(er.GetVirtualHosts()) != 1 {
		return ""
	}
	vh := er.GetVirtualHosts()[0]
	if !reflect.DeepEqual(vh.Domains, []string{"*"}) {
		return ""
	}
	if len(vh.GetRoutes()) != 1 {
		return ""
	}
	r := vh.GetRoutes()[0]
	if r.GetMatch().GetPrefix() != "/" {
		return ""
	}
	a, ok := r.GetAction().(*route.Route_Route)
	if !ok {
		return ""
	}
	cl, ok := a.Route.ClusterSpecifier.(*route.RouteAction_Cluster)
	if !ok {
		return ""
	}
	if strings.Contains(cl.Cluster, "Cluster") {
		return cl.Cluster
	}
	return fmt.Sprintf("Cluster: %s", cl.Cluster)
}

func describeDomains(vh *route.VirtualHost) string {
	if len(vh.GetDomains()) == 1 && vh.GetDomains()[0] == "*" {
		return ""
	}
	return strings.Join(vh.GetDomains(), "/")
}

func describeRoutes(vh *route.VirtualHost) string {
	allRoutes := make([]string, 0, len(vh.GetRoutes()))
	for _, route := range vh.GetRoutes() {
		allRoutes = append(allRoutes, describeMatch(route.GetMatch()))
	}
	return strings.Join(allRoutes, ", ")
}

func describeMatch(match *route.RouteMatch) string {
	var conds []string
	if match.GetPrefix() != "" {
		conds = append(conds, fmt.Sprintf("%s*", match.GetPrefix()))
	}
	if match.GetPath() != "" {
		conds = append(conds, match.GetPath())
	}
	if match.GetSafeRegex() != nil {
		conds = append(conds, fmt.Sprintf("regex %s", match.GetSafeRegex().Regex))
	}
	// Ignore headers
	return strings.Join(conds, " ")
}
