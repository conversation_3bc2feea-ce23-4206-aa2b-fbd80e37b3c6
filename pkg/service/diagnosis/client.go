package diagnosis

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/rest"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

// CLIClient is an extended client with additional helpers/functionality.
type CLIClient interface {
	// AllDiscoveryDo makes a http request to each Istio discovery instance.
	AllDiscoveryDo(ctx context.Context, istiodNamespace, path string) (map[string][]byte, error)
	// EnvoyDoWithPort makes a http request to the Envoy in the specified pod and port.
	EnvoyDoWithPort(ctx context.Context, podName, podNamespace, method, path string, port int) ([]byte, error)
}

// Client is a helper wrapper around the Kube RESTClient for istioctl -> Pilot/Envoy/Mesh related things
type Client struct {
	kube.Client
	restConfig *rest.Config
	ctx        csmContext.CsmContext
	// http is a client for HTTP requests
	http *http.Client
}

// NewClient creates a Kubernetes client and tools for interacting with Istio.
func NewClient(ctx csmContext.CsmContext, kubeClient kube.Client) CLIClient {
	c := Client{}
	c.Client = kubeClient
	c.restConfig = kubeClient.RESTConfig()
	c.ctx = ctx
	c.http = &http.Client{
		Timeout: time.Second * 15,
	}
	return &c
}

func (c *Client) EnvoyDoWithPort(ctx context.Context, podName, podNamespace, method, path string, port int) ([]byte, error) {
	return c.portForwardRequest(ctx, podName, podNamespace, method, path, port)
}

func (c *Client) AllDiscoveryDo(ctx context.Context, namespace, path string) (map[string][]byte, error) {
	istiods, err := c.GetIstioPods(namespace, metav1.ListOptions{
		LabelSelector: constants.IstioLabelSelector,
		FieldSelector: constants.RunningStatus,
	})
	if err != nil {
		return nil, err
	}
	if len(istiods) == 0 {
		return nil, errors.New("unable to find any Istiod instances")
	}

	result := map[string][]byte{}
	for _, istiod := range istiods {
		monitoringPort := findIstiodMonitoringPort(&istiod)
		res, err := c.portForwardRequest(ctx, istiod.Name, istiod.Namespace, http.MethodGet, path, monitoringPort)
		if err != nil {
			return nil, err
		}
		if len(res) > 0 {
			result[istiod.Name] = res
		}
	}
	if len(result) > 0 {
		return result, nil
	}
	return nil, nil
}

func findIstiodMonitoringPort(pod *v1.Pod) int {
	if v, ok := pod.GetAnnotations()["prometheus.io/port"]; ok {
		if port, err := strconv.Atoi(v); err == nil {
			return port
		}
	}
	return constants.DefaultPrometheusPort
}

func (c *Client) GetIstioPods(namespace string, opts metav1.ListOptions) ([]v1.Pod, error) {
	pl, err := c.Client.Kube().CoreV1().Pods(namespace).List(context.TODO(), opts)
	if err != nil {
		return nil, fmt.Errorf("unable to retrieve Pods: %v", err)
	}
	return pl.Items, nil
}

func (c *Client) portForwardRequest(ctx context.Context, podName, podNamespace, method, path string, port int) ([]byte, error) {
	formatError := func(err error) error {
		return fmt.Errorf("failure running port forward process: %v", err)
	}

	// Port forward to the istiod pod
	fw, err := c.NewPortForwarder(podName, podNamespace, "", 0, port)
	if err != nil {
		return nil, err
	}
	if err = fw.Start(); err != nil {
		return nil, formatError(err)
	}
	defer fw.Close()

	// HTTP request to the port forwarder
	req, err := http.NewRequest(method, fmt.Sprintf("http://%s/%s", fw.Address(), path), nil)
	if err != nil {
		return nil, formatError(err)
	}
	resp, err := c.http.Do(req.WithContext(ctx))
	if err != nil {
		return nil, formatError(err)
	}
	defer closeQuietly(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}
	out, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, formatError(err)
	}

	return out, nil
}

func (c *Client) NewPortForwarder(podName, ns, localAddress string, localPort int, podPort int) (PortForwarder, error) {
	return newPortForwarder(c, podName, ns, localAddress, localPort, podPort)
}

func closeQuietly(c io.Closer) {
	_ = c.Close()
}
