package diagnosis

import (
	"context"
	"strings"
	"testing"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/rest"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

func mockIstiodPod(ns, name string) *v1.Pod {
	pod := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:        name,
			Namespace:   ns,
			Labels:      map[string]string{"app": "istiod"},
			Annotations: map[string]string{"prometheus.io/port": "15014"},
		},
		Status: v1.PodStatus{
			Phase: v1.PodRunning,
		},
	}
	return pod
}

func TestAllDiscoveryDo(t *testing.T) {
	cases := []struct {
		name      string
		pod       *v1.Pod
		expectErr string
	}{
		{
			name:      "AllDiscoveryDo-success",
			pod:       mockIstiodPod("istio-system", "pod-istiod"),
			expectErr: "failure",
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			_, _ = fakeClient.Kube().CoreV1().Pods(c.pod.Namespace).Create(context.TODO(), c.pod, metav1.CreateOptions{})
			client := &Client{}
			client.Client = fakeClient
			config := &rest.Config{
				Host: "localhost:6443",
			}
			client.restConfig = kube.SetRestDefaults(config)
			_, err := client.AllDiscoveryDo(context.TODO(), c.pod.Namespace, "/debug/syncz")
			if err != nil {
				if !strings.Contains(err.Error(), c.expectErr) {
					t.Errorf("Case %s AllDiscoveryDo failed, error %v", c.name, err)
				}
			}
		})
	}
}
