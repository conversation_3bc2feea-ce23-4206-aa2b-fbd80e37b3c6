package diagnosis

import (
	"context"
	"os"
	"path"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/jucardi/go-streams/streams"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	model_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmcontext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/diagnosis/configdump"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/file"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

// Service for diagnosis
type Service struct {
	option        *Option
	cceService    cce.ClientInterface
	clusterModel  cluster.ServiceInterface
	instanceModel instances.ServiceInterface
}

// NewDiagnosisService 初始化 diagnosis
func NewDiagnosisService(option *Option) *Service {
	gormDB := option.DB.DB
	addonService := &Service{
		option:        NewOption(gormDB),
		cceService:    cce.NewClientService(),
		clusterModel:  cluster.NewClusterService(cluster.NewOption(gormDB)),
		instanceModel: instances.NewInstancesService(instances.NewOption(gormDB)),
	}
	return addonService
}

// NamespaceList 获取 namespace list
func (service *Service) NamespaceList(ctx csmcontext.CsmContext, instanceUUID string) (*meta.NamespacesResult, error) {
	// 独立网格与托管网格区别对待
	// 独立网格：获取主集群 + 从集群中所有 namespace
	// 托管网格：获取托管实例所在的 namespace + 从集群的 namespace
	var res meta.NamespacesResult
	allCluster, err := service.clusterModel.GetAllClusterByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return &res, err
	}
	var nsMtx sync.Mutex
	var nsWg sync.WaitGroup
	var allNamespace []meta.Namespace
	for _, c := range *allCluster {
		nsWg.Add(1)
		go func(c model_meta.Cluster) {
			cxt, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer func() {
				cancel()
				nsWg.Done()
			}()
			clusterUuid := c.ClusterUUID
			region := c.Region
			clusterType := c.ClusterType

			// 独立网格
			if clusterType == string(model_meta.ClusterTypePrimary) || clusterType == string(model_meta.ClusterTypeRemote) ||
				clusterType == string(model_meta.ClusterTypeConfig) {
				client, clientErr := service.cceService.NewClient(ctx, region, clusterUuid, model_meta.StandaloneMeshType)
				if clientErr != nil {
					ctx.CsmLogger().Errorf("cce newClient region=%s,clusterId=%s,clusterType=%s error %v",
						region, clusterUuid, clusterType, clientErr)
					return
				}
				nsList, nsError := client.Kube().CoreV1().Namespaces().List(cxt, metav1.ListOptions{})
				if nsError != nil {
					ctx.CsmLogger().Errorf("get namespaces from cce region=%s,clusterId=%s,clusterType=%s error %v",
						region, clusterUuid, clusterType, nsError)
					return
				}
				ctx.CsmLogger().Infof("get namespaces from cce region=%s,clusterId=%s,clusterType=%s successful",
					region, clusterUuid, clusterType)
				for _, ns := range nsList.Items {
					if namespaceJudge(ns.Name) { // 跳过Istio系统命名空间、kube-system、kube-public、kube-node-lease
						continue
					}
					nsMtx.Lock()
					allNamespace = append(allNamespace, meta.Namespace{
						Name:   ns.Name,
						Status: string(ns.Status.Phase),
					})
					nsMtx.Unlock()
				}
			}

		}(c)
	}
	nsWg.Wait()

	// allNamespace 去重与排序
	allNamespace = service.removeDuplicateNamespace(allNamespace)
	sort.Slice(allNamespace, func(i, j int) bool {
		return allNamespace[i].Name < allNamespace[j].Name
	})

	res.Namespace = allNamespace

	return &res, nil
}

// namespaceJudge 判断给定的命名空间是否属于Istio系统命名空间、kube-system、kube-public、kube-node-lease
// 参数：
//   - namespaceName: 待判断的命名空间
//
// 返回值：
//   - bool: 如果属于Istio系统命名空间、kube-system、kube-public、kube-node-lease，则返回true，否则返回false
func namespaceJudge(namespaceName string) bool {
	if strings.Contains(namespaceName, constants.IstioNamespace) || namespaceName == constants.KubeSystem ||
		namespaceName == constants.KubePublic || namespaceName == constants.KubeNodeLease {
		return true
	}
	return false
}

func (service *Service) removeDuplicateNamespace(namespaces []meta.Namespace) []meta.Namespace {
	encountered := map[meta.Namespace]bool{}
	var result []meta.Namespace
	for _, ns := range namespaces {
		if !encountered[ns] {
			encountered[ns] = true
			result = append(result, ns)
		}
	}
	return result
}

func (service *Service) ProxyStatusList(ctx csmcontext.CsmContext, instanceUUID string, page *vo.Page,
	listFilter *vo.DiagnosisListFilter) ([]*meta.ProxyStatus, *vo.PageResult, error) {
	pageResult := &vo.PageResult{
		PageSize:   page.PageSize,
		PageNo:     page.PageNo,
		TotalCount: 0,
	}

	proxyStatus := make([]*meta.ProxyStatus, 0)
	instance, err := service.instanceModel.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("ProxyStatusList get istiod status error %v", err)
		return proxyStatus, pageResult, err
	}

	namespace := listFilter.Namespace
	istiodNamespace := instance.IstioInstallNamespace
	var k8sClient kube.Client
	if strings.EqualFold(instance.InstanceManageScope, string(model_meta.InstanceManageClusterScope)) {
		// 独立网格
		ctx.CsmLogger().Infof("init k8s client from cluster with type %s, instanceUUID=%s",
			model_meta.InstanceManageClusterScope, instanceUUID)
		k8sClient, err = service.getK8sClient(ctx, instanceUUID)
		if err != nil {
			ctx.CsmLogger().Errorf("ProxyStatusList get istiod status error %v", err)
			return proxyStatus, pageResult, err
		}
	} else {
		// 托管网格
		region := instance.Region
		clusterId, _ := service.GetHostingCluster(ctx, region)
		ctx.CsmLogger().Infof("init k8s client from cluster with type %s, clusterID=%s and region=%s",
			model_meta.HostingMeshType, clusterId, region)
		instanceClient, instanceErr := service.cceService.NewClient(ctx, instance.Region, clusterId, model_meta.HostingMeshType)
		if instanceErr != nil {
			ctx.CsmLogger().Errorf("cceService.NewClient get k8s client error %v", instanceErr)
			return proxyStatus, pageResult, instanceErr
		}
		k8sClient = instanceClient
	}
	c := NewClient(ctx, k8sClient)
	statuses, err := c.AllDiscoveryDo(context.TODO(), istiodNamespace, "debug/syncz")
	if err != nil {
		ctx.CsmLogger().Errorf("ProxyStatusList get istiod status error %v", err)
		return proxyStatus, pageResult, err
	}
	proxyStatus, err = getProxyStatus(statuses)
	if err != nil {
		ctx.CsmLogger().Errorf("ProxyStatusList get istiod status error %v", err)
		return proxyStatus, pageResult, err
	}

	pss := streams.
		FromArray(proxyStatus).
		Filter(func(v interface{}) bool {
			if len(namespace) > 0 {
				ps := v.(*meta.ProxyStatus)
				proxy := strings.Split(ps.Proxy, ".")
				if len(proxy) != 2 {
					return false
				}
				return strings.EqualFold(proxy[1], namespace)
			} else {
				return true
			}
		}).
		Filter(func(v interface{}) bool {
			ps := v.(*meta.ProxyStatus)
			if strings.Contains(meta.QueryDiagnosisProxyName, listFilter.KeywordType) {
				return strings.Contains(ps.Proxy, listFilter.Keyword)
			}
			return true
		}).
		ToArray().([]*meta.ProxyStatus)

	if len(pss) <= 0 {
		return []*meta.ProxyStatus{}, pageResult, nil
	}

	pageResult.TotalCount = int64(len(pss))
	start := (page.PageNo - 1) * page.PageSize
	end := util.MinInt64(page.PageNo*page.PageSize, int64(len(pss)))
	if start >= end {
		return []*meta.ProxyStatus{}, pageResult, nil
	}
	sliceOfPss := pss[start:end]

	return sliceOfPss, pageResult, nil
}

func (service *Service) GetHostingCluster(ctx csmcontext.CsmContext, region string) (string, string) {
	ctx.CsmLogger().Infof("GetHostingCluster region %s", region)
	return service.option.GetHostingCluster(region)
}

// getK8sClient 获取k8s客户端
func (service *Service) getK8sClient(ctx csmcontext.CsmContext, meshInstanceID string) (kube.Client, error) {
	c, meshType, err := service.instanceModel.GetInstanceIstiodCluster(ctx, meshInstanceID)
	if err != nil {
		return nil, err
	}
	return service.cceService.NewClient(ctx, c.Region, c.ClusterUUID, meshType)

}

// ExceptionList 根据实例ID和命名空间获取指定异常列表
func (service *Service) ExceptionList(ctx csmcontext.CsmContext, instanceUUID,
	namespace string, page *vo.Page) ([]*meta.Exception, *vo.PageResult, error) {

	var res []*meta.Exception

	instance, err := service.instanceModel.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("ExceptionList get istiod status error %v", err)
		return nil, nil, err
	}
	pwd, err := os.Getwd()
	if err != nil {
		return nil, nil, err
	}
	ctx.CsmLogger().Infof("get instance and pwd successful")
	configPath := path.Join(pwd, constants.Templates)
	versionIstio := instance.IstioVersion
	ctl := util.GetIstioCtl(ctx)
	var istioCtlBin string
	var isRemote bool
	//独立网格
	if strings.EqualFold(instance.InstanceManageScope, string(model_meta.InstanceManageClusterScope)) {
		istioCtlBin = path.Join(configPath, constants.BaseIstioTemplate, versionIstio,
			constants.BaseIstioBin, ctl)
	} else { //托管网格
		isRemote = true
		versionBase := path.Join(configPath, constants.HostingBaseIstioTemplate, versionIstio)
		istioCtlBin = path.Join(versionBase, constants.BaseIstioBin, util.GetIstioCtl(ctx))
	}

	//获取所有的集群信息
	clusterInfo, err := service.clusterModel.GetAllClusterByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return res, nil, err
	}

	ctx.CsmLogger().Infof("start get mesh analyze result")
	var analyzeMtx sync.Mutex
	var analyzeWg sync.WaitGroup
	for _, c := range *clusterInfo {
		if c.ClusterType == string(model_meta.ClusterTypeExternal) {
			continue
		}
		analyzeWg.Add(1)
		go func(c model_meta.Cluster) {

			defer func() {
				analyzeWg.Done()
			}()
			clusterUuid := c.ClusterUUID
			region := c.Region
			params := ClusterConf{
				IsRemote:       isRemote,
				IstioctlBin:    istioCtlBin,
				InstanceRegion: instance.Region,
				Region:         region,
				Version:        versionIstio,
				Namespace:      c.IstioInstallNamespace,
				CceClusterUuid: clusterUuid,
				ClusterName:    c.ClusterName,
			}
			var analyze *Analyze
			ctx.CsmLogger().Infof("start analyze cluster %s", c.ClusterName)
			analyze = NewAnaylze(&params)
			analyze.ConfigPath = configPath
			analyze.AnalyzeNamespace = namespace
			defer func() {
				err := file.RemoveFile(analyze.KubeConfigPath) //删除临时文件
				if err != nil {
					ctx.CsmLogger().Errorf("Exception analyze remove kubeconfig error %v", err)
				}
			}()
			if err := analyze.WriteKubeConfig(ctx); err != nil {
				ctx.CsmLogger().Errorf("Exception analyze write kubeconfig error %v", err)
				return
			}
			out, err := analyze.AnalyzeCluster(ctx)
			if err != nil {
				ctx.CsmLogger().Errorf("Exception analyze error %v", err)
				return
			}
			outstrs := strings.Split(string(out), "\n") //按照换行符分割，每条为一条
			analyzeMtx.Lock()
			for _, outstr := range outstrs {
				if temp, err := Str2Exception(outstr); err != nil {
					ctx.CsmLogger().Errorf("Exception analyze str2exception error %v", err)
					continue
				} else if temp == nil {
					continue
				} else {
					ctx.CsmLogger().Infof("Exception analyze str2exception success %v", temp)
					temp.Version = versionIstio
					res = append(res, temp)
				}
			}
			analyzeMtx.Unlock()
		}(c)
	}
	analyzeWg.Wait()
	ctx.CsmLogger().Infof("end get mesh analyze result")
	pageResult := &vo.PageResult{
		PageSize:   page.PageSize,
		PageNo:     page.PageNo,
		TotalCount: int64(len(res)),
	}
	start := (page.PageNo - 1) * page.PageSize
	end := util.MinInt64(page.PageNo*page.PageSize, int64(len(res)))
	if start >= end {
		return nil, pageResult, nil
	}
	return res[start:end], pageResult, nil
}

// ConfigDump 从给定的参数中生成ConfigMap的调试信息
// 参数：
//
//	ctx: CsmContext，CsmContext对象
//	instanceUUID: string，实例UUID
//	namespace: string，命名空间
//	podName: string，pod名称
//	clusterName: string，集群名
//
// 返回值：
//
//	[]byte，ConfigMap的调试信息字节数组
//	error，如果发生错误，则返回错误
func (service *Service) ConfigDump(ctx csmcontext.CsmContext, instanceUUID, namespace, podName, clusterName string) ([]byte, error) {

	strs := strings.SplitN(clusterName, "-", 2)
	clusterID := strs[1]
	region := strs[0]
	var k8sClient kube.Client
	k8sClient, instanceErr := service.cceService.NewClient(ctx, region, clusterID, model_meta.StandaloneMeshType)
	if instanceErr != nil {
		ctx.CsmLogger().Errorf("cceService.NewClient get k8s client error %v", instanceErr)
		return nil, instanceErr
	}
	c := NewClient(ctx, k8sClient)
	debug, err := getConfigDump(c, podName, namespace)
	if err != nil {
		return nil, err
	}
	return debug, nil
}

func (service *Service) ProxyConfigList(ctx csmcontext.CsmContext, instanceUUID string,
	pcr *meta.ProxyConfigRequest, page *vo.Page) ([]byte, *vo.PageResult, error) {
	pageResult := &vo.PageResult{
		PageSize:   page.PageSize,
		PageNo:     page.PageNo,
		TotalCount: 0,
	}

	// 获取数据面信息
	k8sClient, err := service.cceService.NewClient(ctx, pcr.Region, pcr.ClusterID, model_meta.StandaloneMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("ProxyConfigList NewClient error %v", err)
		return nil, pageResult, err
	}
	c := NewClient(ctx, k8sClient)
	podName := pcr.PodName
	podNamespace := pcr.Namespace
	typeName := pcr.TypeName
	path := getPath(typeName)

	value, err := c.EnvoyDoWithPort(context.TODO(), podName, podNamespace, "GET", path, defaultProxyAdminPort)
	if err != nil {
		ctx.CsmLogger().Errorf("ProxyStatusList EnvoyDoWithPort error %v", err)
		return nil, pageResult, err
	}

	filter := configdump.Filter{
		Page:           page,
		ProxyConfig:    configdump.ConvertToProxyConfig(typeName),
		ClusterFilter:  &configdump.ClusterFilter{},
		ListenerFilter: &configdump.ListenerFilter{},
		RouteFilter:    &configdump.RouteFilter{},
		EndpointFilter: &configdump.EndpointFilter{},
	}

	if typeName == configDump {
		resStr := `[` + string(value) + `]`
		pageResult.TotalCount = 1
		return []byte(resStr), pageResult, nil
	} else {
		res, total, err := analyzeProxyConfig(value, filter)
		if err != nil {
			ctx.CsmLogger().Errorf("ProxyConfigList analyzeProxyConfig error %v", err)
			return nil, pageResult, err
		}
		pageResult.TotalCount = total

		return res, pageResult, nil
	}
}

// getConfigDump 通过给定的 CLI 客户端和 Pod 和命名空间，获取配置转储。
func getConfigDump(c CLIClient, podName, namespace string) ([]byte, error) {

	path := "config_dump"
	debug, err := c.EnvoyDoWithPort(context.TODO(), podName, namespace, "GET", path, defaultProxyAdminPort)
	if err != nil {
		return nil, err
	}
	return debug, nil
}
