package discoveryselector

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	servicemeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

type ServiceInterface interface {
	UpdateDiscoverySelector(ctx context.CsmContext, eks bool, instance *meta.Instances, selector *servicemeta.DiscoverySelector) error
	GetDiscoverySelector(ctx context.CsmContext, instanceUUID string) (*servicemeta.DiscoverySelector, error)
}
