package discoveryselector

import (
	"context"
	"errors"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	admissionregistrationv1 "k8s.io/api/admissionregistration/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	mockCluster "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	mockInstances "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	contextCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	mockCceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	serviceMeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/testdata"
)

var (
	ctx, _ = csmContext.NewCsmContextMock()

	mockDB, _ = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))

	instanceUUID               = "csm-nxzqosxx"
	instanceName               = "tanjunchen-test"
	instanceType               = "standalone"
	istioVersion               = "1.13.2"
	region                     = "bj"
	accountId                  = "1"
	discoverySelectorEnabled   = true
	discoverySelectorLabels    = "{\"user\": \"test\"}"
	discoverySelectorLabelsMap = map[string]string{"user": "test"}
	istioInstallNamespace      = "istio-system"
	InstanceManageScope        = "cluster"
	Status                     = ""
	istiodName                 = "istiod-6d5cccd675-tc765"

	dsEnabledFalse = false
	dsEnabledTrue  = true
	dsLabels       = map[string]string{"user": "foo"}
	dsLabelsNil    map[string]string

	clusterUUID1 = "cce-0k355plq"
	clusterUUID2 = "cce-0k355plq"

	clusterName1 = "istio-test01"
	clusterName2 = "istio-test01-remote"

	data1 = `discoverySelectors:
- matchLabels:
  mesh-instance-id: csm-69ps6bcs
- matchExpressions:
  - key: user
    operator: In
    values:
    - bbb
`
	namespace     = "istio-system"
	configMapName = constants.IstioConfimapName
	mockConfigMap = buildMockConfigmap(configMapName, namespace, data1)
)

func buildMockDiscoverySelectorService() *Service {
	option := NewOption(mockDB)
	return NewDiscoverySelectorService(option)
}

func buildMockInstance() *meta.Instances {
	instances := &meta.Instances{
		InstanceUUID:             instanceUUID,
		InstanceName:             instanceName,
		InstanceType:             instanceType,
		IstioVersion:             istioVersion,
		Region:                   region,
		AccountId:                accountId,
		DiscoverySelectorEnabled: csm.Bool(discoverySelectorEnabled),
		DiscoverySelectorLabels:  discoverySelectorLabels,
		IstioInstallNamespace:    istioInstallNamespace,
		InstanceManageScope:      InstanceManageScope,
		Status:                   Status,
	}
	return instances
}

func buildMockHostingInstance() *meta.Instances {
	instances := buildMockInstance()
	instances.InstanceType = string(version.HostingVersionType)
	return instances
}

func buildMockHostingClusters() *[]meta.Cluster {
	clusters := make([]meta.Cluster, 0)
	cluster1 := buildClusters()
	clusters = append(clusters, cluster1)

	cluster2 := buildClusters()
	cluster2.ClusterType = string(meta.ClusterTypeRemote)
	clusters = append(clusters, cluster2)
	return &clusters
}

func buildClusters() meta.Cluster {
	clusters := meta.Cluster{
		InstanceUUID:          instanceUUID,
		ClusterUUID:           clusterUUID1,
		ClusterName:           clusterName1,
		ClusterType:           string(meta.ClusterTypeExternal),
		Region:                region,
		AccountId:             accountId,
		IstioInstallNamespace: istioInstallNamespace,
	}
	return clusters
}

func buildMockClusters() *[]meta.Cluster {
	clusters := make([]meta.Cluster, 0)
	cluster1 := buildClusters()
	clusters = append(clusters, cluster1)
	return &clusters
}

func TestNewService(t *testing.T) {
	service := buildMockDiscoverySelectorService()
	assert.NotNil(t, service)
}

func TestUpdateDiscoverySelector(t *testing.T) {
	tests := []struct {
		name              string
		context           contextCsm.CsmContext
		discoverySelector *serviceMeta.DiscoverySelector
		configmap         *v1.ConfigMap
		instance          *meta.Instances
		clusters          *[]meta.Cluster
		wantErr           error
	}{
		{
			name:              "test01-UpdateDiscoverySelector",
			context:           ctx,
			discoverySelector: serviceMeta.NewDiscoverySelector(dsEnabledTrue, dsLabels),
			configmap:         mockConfigMap,
			instance:          buildMockInstance(),
			clusters:          buildMockClusters(),
			wantErr:           nil,
		},
		{
			name:              "test02-UpdateDiscoverySelector",
			context:           ctx,
			discoverySelector: serviceMeta.NewDiscoverySelector(dsEnabledFalse, dsLabels),
			configmap:         mockConfigMap,
			instance:          buildMockInstance(),
			clusters:          buildMockClusters(),
			wantErr:           nil,
		},
		{
			name:              "test03-UpdateDiscoverySelector",
			context:           ctx,
			discoverySelector: serviceMeta.NewDiscoverySelector(dsEnabledFalse, dsLabels),
			configmap:         mockConfigMap,
			instance:          buildMockHostingInstance(),
			clusters:          buildMockHostingClusters(),
			wantErr:           nil,
		},
	}
	ctrl := gomock.NewController(t)
	mockInstanceModel := mockInstances.NewMockServiceInterface(ctrl)
	mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	mockPod := buildMockPod(istiodName, istioInstallNamespace)
	fakeClient := kube.NewFakeClient()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				clusterModel:   mockClusterModel,
				instancesModel: mockInstanceModel,
				cceService:     mockCceService,
			}

			mockInstanceModel.EXPECT().UpdateInstance(tt.context, gomock.Any(),
				gomock.Any()).Return(nil, nil)
			mockClusterModel.EXPECT().GetAllClusterByInstanceUUID(tt.context, gomock.Any()).Return(tt.clusters, nil)
			//nolint:errcheck // 单测
			mockCceService.EXPECT().NewClient(gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any()).Return(fakeClient, nil)

			//nolint:errcheck // 单测
			fakeClient.Kube().CoreV1().Pods(namespace).Create(context.TODO(),
				mockPod, metav1.CreateOptions{})
			//nolint:errcheck // 单测
			fakeClient.Kube().CoreV1().ConfigMaps(namespace).Create(context.TODO(),
				tt.configmap, metav1.CreateOptions{})
			if tt.instance.InstanceType == string(version.HostingVersionType) {
				mockCceService.EXPECT().NewClient(gomock.Any(),
					gomock.Any(),
					gomock.Any(),
					gomock.Any()).Return(fakeClient, nil)
				//nolint:errcheck // 单测
				fakeClient.Kube().AdmissionregistrationV1().MutatingWebhookConfigurations().Create(context.TODO(),
					testdata.BuildMutatingWebhookConfiguration(
						constants.IstioSidecarInjectorMutatingWebhookConfiguration+"-"+tt.instance.IstioInstallNamespace),
					metav1.CreateOptions{})
			}

			got := s.UpdateDiscoverySelector(ctx, false, tt.instance, tt.discoverySelector)
			assert.Equal(t, tt.wantErr, got)
		})
	}
}

func buildLabelSelector() *metav1.LabelSelector {
	return &metav1.LabelSelector{
		MatchExpressions: []metav1.LabelSelectorRequirement{
			{
				Key:      "istio-injection",
				Operator: "DoesNotExist",
			},
			{
				Key:      "istio.io/rev",
				Operator: "DoesNotExist",
			},
		},
	}
}

func buildUpdatedLabelSelector() *metav1.LabelSelector {
	return &metav1.LabelSelector{
		MatchExpressions: []metav1.LabelSelectorRequirement{
			{
				Key:      "user",
				Operator: "in",
				Values:   []string{"bbb"},
			},
		},
	}
}

func buildMockExpectLabelSelector() *metav1.LabelSelector {
	ls := &metav1.LabelSelector{}
	ls.MatchExpressions = []metav1.LabelSelectorRequirement{
		{
			Key:      "istio-injection",
			Operator: "DoesNotExist",
		},
		{
			Key:      "istio.io/rev",
			Operator: "DoesNotExist",
		},
		{
			Key:      "user",
			Operator: "in",
			Values:   []string{"bbb"},
		},
	}
	return ls
}

func buildLabelSelector2() *metav1.LabelSelector {
	return &metav1.LabelSelector{}
}

func buildUpdatedLabelSelector2() *metav1.LabelSelector {
	return &metav1.LabelSelector{
		MatchExpressions: []metav1.LabelSelectorRequirement{
			{
				Key:      "user",
				Operator: "in",
				Values:   []string{"bbb"},
			},
		},
	}
}

func buildMockExpectLabelSelector2() *metav1.LabelSelector {
	ls := &metav1.LabelSelector{}
	ls.MatchExpressions = []metav1.LabelSelectorRequirement{
		{
			Key:      "user",
			Operator: "in",
			Values:   []string{"bbb"},
		},
	}
	return ls
}

func TestGetLabelSelector(t *testing.T) {
	ls := buildLabelSelector()
	updatedLs := buildUpdatedLabelSelector()
	expectLs := buildMockExpectLabelSelector()
	discoverySelectorService := buildMockDiscoverySelectorService()
	res := discoverySelectorService.getLabelSelector(ls, updatedLs)
	assert.Equal(t, res, expectLs)

	res2 := discoverySelectorService.getLabelSelector(buildLabelSelector2(),
		buildUpdatedLabelSelector2())
	assert.Equal(t, res2, buildMockExpectLabelSelector2())
}

func buildMockConfigmap(name, namespace, data string) *v1.ConfigMap {
	return &v1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Data: map[string]string{
			constants.IstioConfigMapMeshName: data,
		},
	}
}

func buildMockPod(name, namespace string) *v1.Pod {
	return &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Spec: v1.PodSpec{},
	}
}

func TestUpdateDiscoverySelectorsConfigMapIstio(t *testing.T) {
	discoverySelectorService := buildMockDiscoverySelectorService()

	fakeClient := kube.NewFakeClient()
	//nolint:errcheck // 单测
	fakeClient.Kube().CoreV1().ConfigMaps(namespace).Create(context.TODO(),
		mockConfigMap, metav1.CreateOptions{})

	err := discoverySelectorService.UpdateDiscoverySelectorsConfigMapIstio(fakeClient, instanceUUID, namespace, dsLabels)
	assert.Nil(t, err)

	//nolint:errcheck // 单测
	fakeClient.Kube().CoreV1().ConfigMaps(namespace).Create(context.TODO(),
		mockConfigMap, metav1.CreateOptions{})

	er2 := discoverySelectorService.UpdateDiscoverySelectorsConfigMapIstio(fakeClient, instanceUUID, namespace, dsLabelsNil)
	assert.Nil(t, er2)
}

var errNotFound = errors.New("not found")

func TestUpdateMutatingWebhookConfiguration(t *testing.T) {
	ts := []struct {
		name                         string
		mutatingWebhookConfiguration []admissionregistrationv1.MutatingWebhookConfiguration
		webHookName                  string
		labels                       map[string]string
		err                          string
	}{
		{
			"UpdateMutatingWebhookConfiguration",
			[]admissionregistrationv1.MutatingWebhookConfiguration{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name: "istio-sidecar-injector-istio-systemcsm-69ps6bcs",
					},
					Webhooks: []admissionregistrationv1.MutatingWebhook{
						{
							Name:              "rev.validation.istio.io",
							NamespaceSelector: buildLabelSelector(),
						},
						{
							Name:              "object.sidecar-injector.istio.io",
							NamespaceSelector: buildLabelSelector(),
						},
					},
				},
			},
			"istio-sidecar-injector-istio-systemcsm-69ps6bcs",
			dsLabels,
			"",
		},
		{
			"UpdateValidatingWebhookConfiguration-NotFound",
			[]admissionregistrationv1.MutatingWebhookConfiguration{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name: "istio-sidecar-injector-istio-systemcsm-69ps6bcs",
					},
					Webhooks: []admissionregistrationv1.MutatingWebhook{
						{
							Name:              "rev.validation.istio.io",
							NamespaceSelector: buildLabelSelector(),
						},
						{
							Name:              "object.sidecar-injector.istio.io",
							NamespaceSelector: buildLabelSelector(),
						},
					},
				},
			},
			"istio-sidecar-injector-istio-systemcsm-69ps6bcs-xxx",
			dsLabels,
			errNotFound.Error(),
		},
	}
	for _, tc := range ts {
		t.Run(tc.name, func(t *testing.T) {
			client := kube.NewFakeClient()
			for _, wh := range tc.mutatingWebhookConfiguration {
				if _, err := client.Kube().AdmissionregistrationV1().MutatingWebhookConfigurations().
					Create(context.Background(), wh.DeepCopy(),
						metav1.CreateOptions{}); err != nil {
					t.Fatal(err)
				}
			}
			discoverySelectorService := buildMockDiscoverySelectorService()
			err := discoverySelectorService.UpdateMutatingWebhookConfiguration(client, tc.webHookName, tc.labels)
			if err != nil {
				if !strings.Contains(err.Error(), tc.err) {
					t.Fatalf("Got %q, want %q", err, tc.err)
				}
			}
		})
	}
}

func TestUpdateValidatingWebhookConfiguration(t *testing.T) {
	ts := []struct {
		name                           string
		validatingWebhookConfiguration []admissionregistrationv1.ValidatingWebhookConfiguration
		instanceUUID                   string
		webHookName                    string
		labels                         map[string]string
		err                            string
	}{
		{
			"UpdateValidatingWebhookConfiguration-test01",
			[]admissionregistrationv1.ValidatingWebhookConfiguration{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name: "istio-validator-istio-systemcsm-69ps6bcs",
					},
					Webhooks: []admissionregistrationv1.ValidatingWebhook{
						{
							Name:              "rev.validation.istio.io",
							NamespaceSelector: buildLabelSelector(),
						},
					},
				},
			},
			instanceUUID,
			"istio-validator-istio-systemcsm-69ps6bcs",
			dsLabels,
			"",
		},
		{
			"UpdateValidatingWebhookConfiguration-test02",
			[]admissionregistrationv1.ValidatingWebhookConfiguration{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name: "istio-validator-istio-systemcsm-69ps6bcs",
					},
					Webhooks: []admissionregistrationv1.ValidatingWebhook{
						{
							Name:              "rev.validation.istio.io",
							NamespaceSelector: buildLabelSelector(),
						},
					},
				},
			},
			instanceUUID,
			"istio-validator-istio-systemcsm-69ps6bcs",
			nil,
			"",
		},
		{
			"UpdateValidatingWebhookConfiguration-notfound",
			[]admissionregistrationv1.ValidatingWebhookConfiguration{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name: "istio-validator-istio-systemcsm-69ps6bcs",
					},
					Webhooks: []admissionregistrationv1.ValidatingWebhook{
						{
							Name:              "rev.validation.istio.io",
							NamespaceSelector: buildLabelSelector(),
						},
					},
				},
			},
			instanceUUID,
			"istio-validator-istio-systemcsm-69ps6bcs-xxx",
			dsLabels,
			"",
		},
	}
	for _, tc := range ts {
		t.Run(tc.name, func(t *testing.T) {
			client := kube.NewFakeClient()
			for _, wh := range tc.validatingWebhookConfiguration {
				if _, err := client.Kube().AdmissionregistrationV1().ValidatingWebhookConfigurations().
					Create(context.Background(), wh.DeepCopy(),
						metav1.CreateOptions{}); err != nil {
					t.Fatal(err)
				}
			}
			discoverySelectorService := buildMockDiscoverySelectorService()
			err := discoverySelectorService.UpdateValidatingWebhookConfiguration(client, tc.webHookName, tc.labels)
			if err != nil {
				if !strings.Contains(err.Error(), tc.err) {
					t.Fatalf("Got %q, want %q", err, tc.err)
				}
			}
		})
	}
}

func buildServiceMetaDiscoverySelector() *serviceMeta.DiscoverySelector {
	return serviceMeta.NewDiscoverySelector(discoverySelectorEnabled, discoverySelectorLabelsMap)
}

func TestGetDiscoverySelector(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockInstanceModel := mockInstances.NewMockServiceInterface(ctrl)
	service := &Service{
		opt:            NewOption(mockDB),
		instancesModel: mockInstanceModel,
	}
	instances := buildMockInstance()
	mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(),
		gomock.Any()).Return(instances, nil)

	want := buildServiceMetaDiscoverySelector()

	got, err := service.GetDiscoverySelector(ctx, instanceUUID)
	assert.Equal(t, nil, err)
	if !reflect.DeepEqual(got, want) {
		t.Fatalf("Got %v, want %v", got, want)
	}
}
