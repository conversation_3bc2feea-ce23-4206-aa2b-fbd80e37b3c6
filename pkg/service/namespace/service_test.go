package namespace

import (
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"testing"
	"time"

	eipSDK "github.com/baidubce/bce-sdk-go/services/eip"
	"github.com/baidubce/bce-sdk-go/services/endpoint"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/sqlite"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	clusterMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	instanceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	contextCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	contextMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context/mock"
	mockCceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	mockEipService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/eip/mock"
	mockVpcService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/vpc/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

var (
	mockDB, _ = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	mockCtx   = new(contextMock.CsmContext)

	ipAddress             = "*******"
	vpcId                 = "vpc-id"
	endpointID            = "endpoint-id"
	subnetId              = "subnetId"
	vpcName               = "vpc-name"
	testInstanceName      = "test-instance"
	testInstanceUUID      = "csm-123456"
	testInstanceType      = "hosting"
	testIstioVersion      = "1.14.6"
	testClusterName       = "test-cluster"
	testClusterUUID       = "cce-123456"
	testRegion            = "bj"
	istioInstallNamespace = "sample"
	region                = "gz"
	caCert                = []byte("-----BEGIN CERTIFICATE-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2V" +
		"1deLSU+0ROuj6p6wTh\nBbnCU5DH5H8E+th77g==\n-----END CERTIFICATE-----")
	token = []byte("eyJhbGciOiJSUzI1NiIsImtpZCI6Ik4wIn0.eyJpc3MiOiJrdWJlcm5ldGVzL3NlcnZ" +
		"pY2VhY2NvdW50Iiwia3ViZXJuZXRlcy5pby9r")

	gz                = "gz"
	gzId              = "cce-123456"
	gzApiServerDomain = "gz-123456"
)

func buildNameSpace() *v1.Namespace {
	return &v1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: "sample",
			Labels: map[string]string{
				"name": "sample",
			},
		},
	}
}

func buildSecret() *v1.Secret {
	caCertBase64 := base64.StdEncoding.EncodeToString(caCert)
	return &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name: meta.ServiceAccount,
			Labels: map[string]string{
				"name": "sample",
			},
			Annotations: map[string]string{
				meta.SecretsAnnotation: meta.ServiceAccount,
			},
		},
		Data: map[string][]byte{
			"ca.crt": []byte(caCertBase64),
			"token":  token,
		},
	}
}

func mockRegion() {
	regions := map[string]interface{}{
		gz: map[string]interface{}{
			cloudHostingRegionClusterId:       gzId,
			cloudHostingRegionApiServerDomain: gzApiServerDomain,
		},
	}
	viper.Set(cloudHostingRegion, regions)
}

func TestGetKubeConfig(t *testing.T) {
	mockRegion()
	testInfos := []struct {
		name           string
		region         string
		kubeConfigType string
		instanceUUID   string
		listEndpoints  *meta.ListEndpointResult
		createEndpoint *endpoint.CreateEndpointResult
		success        bool
	}{
		{
			name:           "success",
			region:         "gz",
			kubeConfigType: "vpc",
			instanceUUID:   testInstanceUUID,
			listEndpoints:  buildListEndpointResult(),
			createEndpoint: buildMockCreateEndpointResult(),
			success:        true,
		},
		{
			name:           "success-public",
			region:         "gz",
			kubeConfigType: "public",
			instanceUUID:   testInstanceUUID,
			listEndpoints:  buildListEndpointResult(),
			createEndpoint: buildMockCreateEndpointResult(),
			success:        true,
		},
		{
			name:           "success-create",
			region:         "gz",
			kubeConfigType: "public",
			instanceUUID:   testInstanceUUID,
			listEndpoints: &meta.ListEndpointResult{
				Endpoints: []meta.Endpoint{},
			},
			createEndpoint: buildMockCreateEndpointResult(),
			success:        true,
		},
	}
	for _, tt := range testInfos {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockClusterModel := clusterMock.NewMockServiceInterface(ctrl)
			mockCce := mockCceService.NewMockClientInterface(ctrl)
			mockInstanceModel := instanceMock.NewMockServiceInterface(ctrl)
			mockVpcServer := mockVpcService.NewMockServiceInterface(ctrl)
			mockEipServer := mockEipService.NewMockServiceInterface(ctrl)

			fakeClient := kube.NewFakeClient()
			mockCce.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			_, _ = fakeClient.Kube().CoreV1().Namespaces().Create(context.TODO(), buildNameSpace(), metav1.CreateOptions{})
			_, _ = fakeClient.Kube().CoreV1().Secrets(istioInstallNamespace).Create(context.TODO(), buildSecret(), metav1.CreateOptions{})
			serviceAccount := &v1.ServiceAccount{
				ObjectMeta: metav1.ObjectMeta{
					Name:      meta.ServiceAccount,
					Namespace: istioInstallNamespace,
				},
				Secrets: []v1.ObjectReference{
					{
						Name: meta.ServiceAccount,
					},
				},
			}
			_, _ = fakeClient.Kube().CoreV1().ServiceAccounts(istioInstallNamespace).Create(context.TODO(), serviceAccount, metav1.CreateOptions{})
			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).AnyTimes().Return(buildInstance(), nil)

			mockClusterModel.EXPECT().GetIstiodCluster(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(buildExternalCluster(), nil)

			mockVpcServer.EXPECT().ListEndpointsWithEip(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.listEndpoints, nil)
			mockVpcServer.EXPECT().CreateEndpointWithEip(gomock.Any(), gomock.Any(),
				gomock.Any()).AnyTimes().Return(tt.createEndpoint, nil)
			mockEipServer.EXPECT().CreateEIP(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(buildCreateEipResult(), nil)
			mockEipServer.EXPECT().ListEIP(gomock.Any(), gomock.Any()).AnyTimes().Return(buildListEipResult(), nil)
			service := &Service{
				opt:            NewOption(mockDB),
				clusterModel:   mockClusterModel,
				cceService:     mockCce,
				instancesModel: mockInstanceModel,
				vpcService:     mockVpcServer,
				eipService:     mockEipServer,
			}

			_, err := service.GetKubeConfig(mockCtx, tt.instanceUUID, tt.region, tt.kubeConfigType)
			if tt.success {
				assert.Nil(t, err)
			} else {

			}

		})
	}

}

func buildListEipResult() *eipSDK.ListEipResult {
	return &eipSDK.ListEipResult{
		EipList: []eipSDK.EipModel{{
			InstanceId:   endpointID,
			InstanceType: "SNIC",
			Eip:          vpcId,
		}},
	}
}

func buildCreateEipResult() *eipSDK.CreateEipResult {
	return &eipSDK.CreateEipResult{
		Eip: "*******",
	}
}

func TestService_GetNamespaceList(t *testing.T) {
	testInstanceUUID := "csm-xxxxxx"
	testClusterUUID := "cce-cccccc"
	testClusterName := "test01-xxx"
	testClusterTypePrimary := "primary"
	testRegion := "bj"
	testIstioInstallNamespace := "istio-system"
	testTime, _ := time.Parse("12/1/2015 12:00:00", "12/8/2015 12:00:00")

	cluster := []meta.Cluster{
		{
			InstanceUUID:          testInstanceUUID,
			ClusterUUID:           testClusterUUID,
			ClusterName:           testClusterName,
			ClusterType:           testClusterTypePrimary,
			Region:                testRegion,
			IstioInstallNamespace: testIstioInstallNamespace,
		},
	}

	ctx := contextCsm.MockNewCsmContext()

	tests := []struct {
		name                 string
		instanceUUID         string
		cceClientError       error
		namespaceListRequest *meta.PageParam
		wantNamespaceList    *meta.Namespaces
		wantErr              bool
	}{
		{
			name:                 "namespace-list-ok",
			instanceUUID:         "csm-xxxxxx",
			cceClientError:       nil,
			namespaceListRequest: meta.NewRequestParams(),
			wantNamespaceList: &meta.Namespaces{
				PageSize:   10,
				PageNo:     1,
				Order:      "desc",
				OrderBy:    "createTime",
				TotalCount: 1,
				Result: []meta.Result{
					{
						ClusterUUID: testClusterUUID,
						ClusterName: testClusterName,
						Namespace:   "sample",
						Region:      testRegion,
						CreateTime:  testTime,
						Labels:      []string{"name:sample"},
					},
				},
			},
			wantErr: false,
		},
		{
			name:                 "namespace-cceClient-error",
			instanceUUID:         "csm-xxxxxx",
			cceClientError:       fmt.Errorf("cce Client error"),
			namespaceListRequest: meta.NewRequestParams(),
			wantNamespaceList: &meta.Namespaces{
				PageSize:   10,
				PageNo:     1,
				Order:      "desc",
				OrderBy:    "createTime",
				TotalCount: 0,
				Result:     nil,
			},
			wantErr: false,
		},
	}

	ctrl := gomock.NewController(t)
	mockClusterModel := clusterMock.NewMockServiceInterface(ctrl)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	mockInstanceModel := instanceMock.NewMockServiceInterface(ctrl)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{
				opt:            NewOption(mockDB),
				clusterModel:   mockClusterModel,
				cceService:     mockCceService,
				instancesModel: mockInstanceModel,
			}

			mockClusterModel.EXPECT().GetAllClusterByInstanceUUID(ctx, gomock.Any()).Return(&cluster, nil)

			if tt.cceClientError != nil {
				mockCceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, tt.cceClientError)
			} else {
				fakeClient := kube.NewFakeClient()
				mockCceService.EXPECT().NewClient(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
				fakeClient.Kube().CoreV1().Namespaces().Create(context.TODO(), buildNameSpace(), metav1.CreateOptions{})
			}

			gotNamespaceList, err := service.GetNamespaceList(ctx, tt.instanceUUID, tt.namespaceListRequest)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNamespaceList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotNamespaceList, tt.wantNamespaceList) {
				t.Errorf("GetNamespaceList() gotNamespaceList = %v, want %v", gotNamespaceList, tt.wantNamespaceList)
			}
		})
	}
}

func buildInstance() *meta.Instances {
	instanceInfo := &meta.Instances{
		InstanceUUID:          testInstanceUUID,
		InstanceName:          testInstanceName,
		InstanceType:          testInstanceType,
		IstioVersion:          testIstioVersion,
		IstioInstallNamespace: istioInstallNamespace,
		Region:                region,
		VpcNetworkId:          vpcId,
		SubnetId:              subnetId,
		APIServerEip:          csm.Bool(false),
	}
	return instanceInfo
}

func buildExternalCluster() *meta.Cluster {
	cluster := &meta.Cluster{
		InstanceUUID:          testInstanceUUID,
		ClusterUUID:           testClusterUUID,
		ClusterName:           testClusterName,
		Region:                testRegion,
		ClusterType:           string(meta.HostingMeshType),
		IstioInstallNamespace: istioInstallNamespace,
	}
	return cluster
}

func buildMockCreateEndpointResult() *endpoint.CreateEndpointResult {
	return &endpoint.CreateEndpointResult{
		Id:        "xxx",
		IpAddress: ipAddress,
	}
}

func buildListEndpointResult() *meta.ListEndpointResult {
	return &meta.ListEndpointResult{
		Endpoints: []meta.Endpoint{
			{
				Service:     gzApiServerDomain,
				SubnetId:    subnetId,
				IpAddress:   ipAddress,
				EndpointId:  endpointID,
				Description: fmt.Sprintf(constants.CsmHostingAPIServerVpcEndpointDescribe, testInstanceUUID),
			},
		},
	}
}
