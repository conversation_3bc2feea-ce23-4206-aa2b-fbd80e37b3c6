// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// CreateSNICWithNewCsmInstance mocks base method.
func (m *MockServiceInterface) CreateSNICWithNewCsmInstance(ctx context.CsmContext, clusterId, region, kubeConfigType string, csmInstance *meta.Instances) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSNICWithNewCsmInstance", ctx, clusterId, region, kubeConfigType, csmInstance)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSNICWithNewCsmInstance indicates an expected call of CreateSNICWithNewCsmInstance.
func (mr *MockServiceInterfaceMockRecorder) CreateSNICWithNewCsmInstance(ctx, clusterId, region, kubeConfigType, csmInstance interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSNICWithNewCsmInstance", reflect.TypeOf((*MockServiceInterface)(nil).CreateSNICWithNewCsmInstance), ctx, clusterId, region, kubeConfigType, csmInstance)
}

// GetKubeConfig mocks base method.
func (m *MockServiceInterface) GetKubeConfig(ctx context.CsmContext, instanceUUID, region, kubeConfigType string) (*meta.KubeConfigResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKubeConfig", ctx, instanceUUID, region, kubeConfigType)
	ret0, _ := ret[0].(*meta.KubeConfigResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKubeConfig indicates an expected call of GetKubeConfig.
func (mr *MockServiceInterfaceMockRecorder) GetKubeConfig(ctx, instanceUUID, region, kubeConfigType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKubeConfig", reflect.TypeOf((*MockServiceInterface)(nil).GetKubeConfig), ctx, instanceUUID, region, kubeConfigType)
}

// GetNamespaceList mocks base method.
func (m *MockServiceInterface) GetNamespaceList(ctx context.CsmContext, instanceUUID string, namespaceListRequest *meta.PageParam) (*meta.Namespaces, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNamespaceList", ctx, instanceUUID, namespaceListRequest)
	ret0, _ := ret[0].(*meta.Namespaces)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNamespaceList indicates an expected call of GetNamespaceList.
func (mr *MockServiceInterfaceMockRecorder) GetNamespaceList(ctx, instanceUUID, namespaceListRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNamespaceList", reflect.TypeOf((*MockServiceInterface)(nil).GetNamespaceList), ctx, instanceUUID, namespaceListRequest)
}
