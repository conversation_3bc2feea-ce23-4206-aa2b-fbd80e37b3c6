package namespace

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strings"
	"sync"

	"github.com/baidubce/bce-sdk-go/model"
	eipSDK "github.com/baidubce/bce-sdk-go/services/eip"
	"github.com/baidubce/bce-sdk-go/services/endpoint"
	v1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/clientcmd/api"

	bceUtil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/util"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/eip"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/vpc"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

type Service struct {
	opt *Option

	instancesModel instances.ServiceInterface
	clusterModel   cluster.ServiceInterface
	cceService     cce.ClientInterface
	vpcService     vpc.ServiceInterface
	eipService     eip.ServiceInterface
}

func NewNamespaceService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt:            option,
		instancesModel: instances.NewInstancesService(instances.NewOption(gormDB)),
		clusterModel:   cluster.NewClusterService(cluster.NewOption(gormDB)),
		vpcService:     vpc.NewVPCService(),
		cceService:     cce.NewClientService(),
		eipService:     eip.NewEIPService(),
	}
}

// GetNamespaceList 获取当前实例下namespace列表
func (service *Service) GetNamespaceList(ctx csmContext.CsmContext, instanceUUID string,
	namespaceListRequest *meta.PageParam) (namespaceList *meta.Namespaces, err error) {
	allCluster, err := service.clusterModel.GetAllClusterByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return nil, err
	}
	var nsMtx sync.Mutex
	var res []meta.Result
	var wg sync.WaitGroup
	for _, c := range *allCluster {
		wg.Add(1)
		go func(c meta.Cluster) {
			cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
			defer func() {
				cancel()
				wg.Done()
			}()
			clusterUuid := c.ClusterUUID
			region := c.Region
			clusterName := c.ClusterName
			if c.ClusterType != string(meta.ClusterTypeExternal) {
				client, clientErr := service.cceService.NewClient(ctx, region, clusterUuid, meta.StandaloneMeshType)
				if clientErr != nil {
					ctx.CsmLogger().Errorf("cce newClient region=%s,clusterId=%s, error %v", region, clusterUuid, clientErr)
					return
				}
				nameSpaceList, namespaceErr := client.Kube().CoreV1().Namespaces().List(cxt, metav1.ListOptions{})
				if namespaceErr != nil {
					ctx.CsmLogger().Errorf("get namespaces from cce region=%s,clusterId=%s, error %v", region, clusterUuid, namespaceErr)
					return
				}
				ctx.CsmLogger().Infof("get namespaces from cce region=%s,clusterId=%s successful", region, clusterUuid)
				for _, nsList := range nameSpaceList.Items {
					if namespaceJudge(nsList.Name) {
						continue
					}
					var labels []string
					for k, v := range nsList.Labels {
						str := k + ":" + v
						labels = append(labels, str)
					}
					Result := meta.Result{
						ClusterUUID: clusterUuid,
						ClusterName: clusterName,
						Namespace:   nsList.Name,
						Region:      region,
						Status:      string(nsList.Status.Phase),
						CreateTime:  nsList.CreationTimestamp.Time,
						Labels:      labels,
					}
					nsMtx.Lock()
					res = append(res, Result)
					nsMtx.Unlock()
				}
			}
		}(c)
	}
	wg.Wait()
	res, total := pageNs(res, *namespaceListRequest)

	namespaceList = &meta.Namespaces{
		PageSize:   namespaceListRequest.PageSize,
		PageNo:     namespaceListRequest.PageNo,
		Order:      namespaceListRequest.Order,
		OrderBy:    namespaceListRequest.OrderBy,
		TotalCount: int(total),
		Result:     res,
	}
	return namespaceList, nil
}

// namespaceJudge 判断当前namespace是否为istio,k8s namespace
func namespaceJudge(namespaceName string) bool {
	if strings.Contains(namespaceName, constants.IstioNamespace) || namespaceName == constants.KubeSystem ||
		namespaceName == constants.KubePublic || namespaceName == constants.KubeNodeLease {
		return true
	}
	return false
}

// pageNs namespace列表分页
func pageNs(nsList []meta.Result, pageParam meta.PageParam) ([]meta.Result, int64) {

	if pageParam.KeywordType != "" {
		if pageParam.KeywordType == constants.ClusterName {
			if pageParam.Keyword != "" {
				k := 0
				for _, ns := range nsList {
					if strings.Contains(ns.ClusterName, pageParam.Keyword) {
						nsList[k] = ns
						k++
					}
				}
				nsList = nsList[:k]
			}
		} else {
			if pageParam.Keyword != "" {
				k := 0
				for _, ns := range nsList {
					if strings.Contains(ns.Namespace, pageParam.Keyword) {
						nsList[k] = ns
						k++
					}
				}
				nsList = nsList[:k]
			}
		}
	}

	//默认desc排序
	sort.Slice(nsList, func(i, j int) bool {
		switch {
		case pageParam.OrderBy == constants.ClusterId:
			return nsList[i].ClusterUUID > nsList[j].ClusterUUID
		case pageParam.OrderBy == constants.ClusterName:
			return nsList[i].ClusterName > nsList[j].ClusterName
		case pageParam.OrderBy == constants.Namespace:
			return nsList[i].Namespace > nsList[j].Namespace
		default:
			return nsList[i].CreateTime.Unix() > nsList[j].CreateTime.Unix()
		}
	})
	if pageParam.Order == constants.Asc {
		for i, j := 0, len(nsList)-1; i < j; i, j = i+1, j-1 {
			nsList[i], nsList[j] = nsList[j], nsList[i]
		}

	}

	totalCount := int64(len(nsList))

	realPageSize := int64(math.Min(float64(pageParam.PageSize), float64(constants.MaxPageSize)))

	maxPageNo := (totalCount + realPageSize - 1) / realPageSize
	realPageNo := int64(math.Min(float64(pageParam.PageNo), math.Max(float64(maxPageNo), 1.0)))

	startItem := (realPageNo - 1) * realPageSize

	return nsList[startItem:int64(math.Min(float64(startItem+realPageSize), float64(totalCount)))], totalCount
}

// defaultNsRoleRules 默认命名空间级别权限
// role 权限示例如下：
// apiVersion: rbac.authorization.k8s.io/v1
// kind: Role
// metadata:
//
//	name: ns-role
//	namespace: istio-system-xxxxx  # 请根据您的命名空间调整
//
// rules:
//   - apiGroups: ["extensions.istio.io"]
//     resources: ["wasmplugins"]
//     verbs: ["get", "list", "watch", "create", "update", "delete"]
//   - apiGroups: ["networking.istio.io"]
//     resources: ["destinationrules", "envoyfilters", "gateways", "proxyconfigs", "serviceentries", "sidecars",
//     "virtualservices", "workloadentries", "workloadgroups"]
//     verbs: ["get", "list", "watch", "create", "update", "delete"]
//   - apiGroups: ["security.istio.io"]
//     resources: ["authorizationpolicies", "peerauthentications", "requestauthentications"]
//     verbs: ["get", "list", "watch", "create", "update", "delete"]
//   - apiGroups: ["telemetry.istio.io"]
//     resources: ["telemetries"]
//     verbs: ["get", "list", "watch", "create", "update", "delete"]
func defaultNsRoleRules() []rbacv1.PolicyRule {
	verbs := []string{meta.RoleVerbCreate, meta.RoleVerbList, meta.RoleVerbDelete, meta.RoleVerbUpdate,
		meta.RoleVerbGet, meta.RoleVerbWatch}
	return []rbacv1.PolicyRule{
		{
			APIGroups: []string{meta.RoleAPIGroupExtensions},
			Resources: []string{meta.RoleResourceWasmPlugins},
			Verbs:     verbs,
		},
		{
			APIGroups: []string{meta.RoleAPIGroupNetworking},
			Resources: []string{meta.RoleResourceDestinationRules, meta.RoleResourceEnvoyFilters, meta.RoleResourceGateways,
				meta.RoleResourceProxyConfigs, meta.RoleResourceServiceEntries, meta.RoleResourceSidecars,
				meta.RoleResourceVirtualServices, meta.RoleResourceWorkloadEntries, meta.RoleResourceWorkloadGroups},
			Verbs: verbs,
		},
		{
			APIGroups: []string{meta.RoleAPIGroupSecurity},
			Resources: []string{meta.RoleResourceAuthorizationPolicies, meta.RoleResourcePeerAuthentications,
				meta.RoleResourceRequestAuthentications},
			Verbs: verbs,
		},
		{
			APIGroups: []string{meta.RoleAPIGroupTelemetry},
			Resources: []string{meta.RoleResourcesTelemetries},
			Verbs:     verbs,
		},
	}
}

// GetKubeConfig 目前仅支持托管网格获取当前实例部署的命名空间级别权限的kubeConfig
// 处理逻辑
// 1、获取托管集群的kubeClient
// 2、创建当前实例命名空间下的sa、role和roleBinding，其中sa默认名字为ns-admin
// 3、获取secret_name、ca和token
// 4、返回填充后kubeConfig的yaml文件
func (service *Service) GetKubeConfig(ctx csmContext.CsmContext, instanceUUID, region, kubeConfigType string) (*meta.KubeConfigResult, error) {
	if len(instanceUUID) <= 0 || len(region) <= 0 {
		return nil, csmErr.NewInvalidParameterValueException("instanceId and region are necessary")
	}

	csmInstance, err := service.instancesModel.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return nil, err
	}
	if csmInstance == nil {
		return nil, csmErr.NewInvalidParameterValueException(fmt.Sprintf("invalid intanceId %s", instanceUUID))
	}
	// 非托管网格类型直接报错
	if !strings.EqualFold(csmInstance.InstanceType, string(meta.HostingMeshType)) {
		return nil, csmErr.NewInvalidParameterValueException(fmt.Sprintf("intanceId %s is not hosting", instanceUUID))
	}

	externalCluster, err := service.clusterModel.GetIstiodCluster(ctx, instanceUUID, csmInstance.InstanceType)
	if err != nil {
		return nil, err
	}
	// 获取external集群的kubeClient
	client, clientErr := service.cceService.NewClient(ctx, region, externalCluster.ClusterUUID, meta.HostingMeshType)
	if clientErr != nil {
		ctx.CsmLogger().Errorf("failed to get external cceClient region=%s,clusterId=%s, err is %s", region,
			externalCluster.ClusterUUID, clientErr.Error())
		return nil, clientErr
	}
	namespace := csmInstance.IstioInstallNamespace
	// 创建sa、role和roleBinding
	// 创建 ServiceAccount
	serviceAccount := &v1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      meta.ServiceAccount,
			Namespace: namespace,
		},
	}
	existingSA, err := client.Kube().CoreV1().ServiceAccounts(namespace).Create(context.TODO(), serviceAccount, metav1.CreateOptions{})
	if err != nil && !kubeErrors.IsAlreadyExists(err) {
		return nil, err
	}

	// 创建 Role
	role := &rbacv1.Role{
		ObjectMeta: metav1.ObjectMeta{
			Name:      meta.RoleName,
			Namespace: namespace,
		},
		Rules: defaultNsRoleRules(),
	}

	_, err = client.Kube().RbacV1().Roles(namespace).Create(context.TODO(), role, metav1.CreateOptions{})
	if err != nil {
		if kubeErrors.IsAlreadyExists(err) {
			_, err = client.Kube().RbacV1().Roles(namespace).Update(context.TODO(), role, metav1.UpdateOptions{})
			if err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}

	// 创建 RoleBinding
	roleBinding := &rbacv1.RoleBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("%s-%s", meta.RoleBindingNamePrefix, namespace),
			Namespace: namespace,
		},
		Subjects: []rbacv1.Subject{
			{
				Kind:      meta.TypeServiceAccount,
				Name:      meta.ServiceAccount,
				Namespace: namespace,
			},
		},
		RoleRef: rbacv1.RoleRef{
			Kind:     meta.TypeRole,
			Name:     meta.RoleName,
			APIGroup: meta.RBACAPIGroup,
		},
	}
	_, err = client.Kube().RbacV1().RoleBindings(namespace).Create(context.TODO(), roleBinding, metav1.CreateOptions{})
	if err != nil {
		if kubeErrors.IsAlreadyExists(err) {
			_, err = client.Kube().RbacV1().RoleBindings(namespace).Update(context.TODO(), roleBinding, metav1.UpdateOptions{})
			if err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}

	// 获取secret
	existingSA, err = client.Kube().CoreV1().ServiceAccounts(namespace).Get(context.TODO(),
		meta.ServiceAccount, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	if len(existingSA.Secrets) <= 0 || len(existingSA.Secrets[0].Name) <= 0 {
		return nil, csmErr.NewInvalidParameterInputValueException("get serviceAccount ns-admin failed")
	}

	secretName := existingSA.Secrets[0].Name
	// 获取与 ServiceAccount 关联的 Secret
	secret, err := client.Kube().CoreV1().Secrets(namespace).Get(context.TODO(), secretName, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}

	if secret == nil {
		return nil, csmErr.NewUnauthorizedException("Secret for ServiceAccount not found")
	}

	// 提取 CA 证书和 Token
	ca := secret.Data["ca.crt"]
	token := string(secret.Data["token"])

	// TODO 待更新获取用户vpc or public host
	host, HostErr := service.createOrGetServiceNIC(ctx, externalCluster.ClusterUUID, region, kubeConfigType, csmInstance)
	if HostErr != nil {
		return nil, HostErr
	}
	server := fmt.Sprintf("https://%s:%d", host, meta.HostingApiServerPort)

	// 填充kubeConfig
	nsKubeConfig := api.NewConfig()
	nsKubeConfig.Clusters[meta.DefaultClusterName] = &api.Cluster{
		Server:                   server,
		CertificateAuthorityData: ca,
		TLSServerName:            meta.HostingDefaultServerName,
	}
	nsKubeConfig.AuthInfos[meta.ServiceAccount] = &api.AuthInfo{
		Token: token,
	}
	contextName := fmt.Sprintf("%s@%s", meta.ServiceAccount, meta.DefaultClusterName)
	nsKubeConfig.Contexts[contextName] = &api.Context{
		Cluster:   meta.DefaultClusterName,
		AuthInfo:  meta.ServiceAccount,
		Namespace: namespace,
	}
	nsKubeConfig.CurrentContext = contextName

	// 将 kubeConfig 返回
	kcBytes, writeErr := clientcmd.Write(*nsKubeConfig)
	if writeErr != nil {
		return nil, writeErr
	}

	result := &meta.KubeConfigResult{
		KubeConfigType: meta.VPCKubeConfigType,
		KubeConfig:     string(kcBytes),
	}

	return result, nil
}

// CreateSNICWithNewCsmInstance  异步方法在创建托管网格的时候创建APIServer服务网卡
// 原因：创建服务网卡是异步操作，如果用户在基本信息页面再点击获取公网访问凭证，可能会报错。
func (service *Service) CreateSNICWithNewCsmInstance(ctx csmContext.CsmContext, clusterId, region, kubeConfigType string,
	csmInstance *meta.Instances) (string, error) {
	return service.createOrGetServiceNIC(ctx, clusterId, region, kubeConfigType, csmInstance)
}

// nolint
// createOrGetServiceNIC 创建或发布服务网卡
// 处理逻辑：根据kubeConfigType分别处理
// kubeConfigType为vpc时，通过域名和标签查找用户vpc内是否有关联该集群的服务网卡。
// - 有，则获取用户服务网卡的IP地址
// - 没有，则使用域名创建服务网卡，获取IP地址
// kubeConfigType为public时，通过域名和标签查找用户vpc内是否有关联该集群的服务网卡。
// - 有，则获取用户服务网卡的eip地址
// - 没有，则先创建eip，然后使用域名创建服务网卡绑定先创建的eip，获取eip地址
func (service *Service) createOrGetServiceNIC(ctx csmContext.CsmContext, clusterId, region, kubeConfigType string,
	csmInstance *meta.Instances) (string, error) {
	host := ""
	// 获取服务发布点的域名
	hostingCccId, serviceDomain := service.opt.GetCluster(region)
	// 服务域名对应的clusterId与传入的托管集群id不一致，则返回
	if hostingCccId != clusterId {
		return host, csmErr.NewInvalidParameterValueException(fmt.Sprintf("clusterId %s get serviceDomain failed", clusterId))
	}
	// tags 打上标签，key为csm-hosting，value为csm实例ID
	snicTag := []model.TagModel{{
		TagKey:   constants.VPCEndpointTagKey,
		TagValue: csmInstance.InstanceUUID,
	}}

	description := fmt.Sprintf(constants.CsmHostingAPIServerVpcEndpointDescribe, csmInstance.InstanceUUID)

	args := &endpoint.ListEndpointArgs{
		VpcId: csmInstance.VpcNetworkId,
	}

	res, err := service.vpcService.ListEndpointsWithEip(ctx, args, region)
	if err != nil {
		ctx.CsmLogger().Warnf("listEndpoints get error %+v", err)
	} else {
		for _, value := range res.Endpoints {
			//如果域名和Description匹配，则直接获取当前服务网卡地址，当做host
			if value.Service == serviceDomain && value.SubnetId == csmInstance.SubnetId &&
				value.Description == description {
				host = value.IpAddress
				endpointId := value.EndpointId
				if kubeConfigType == constants.KubeConfigTypePublic {
					// TODO 待vpc列表接口透传eip字段，取消
					listEip, eipErr := service.eipService.ListEIP(ctx, region)
					if eipErr != nil {
						return "", eipErr
					}
					if listEip == nil || len(listEip.EipList) <= 0 {
						return "", csmErr.NewResourceNotFoundException("not found eip")
					}
					for _, e := range listEip.EipList {
						// 找到绑定当前服务网卡的eip，返回ip
						if e.InstanceType == constants.EIPInstanceTypeSNIC && e.InstanceId == endpointId {
							host = e.Eip
							break
						}
					}

					if host == "" {
						ctx.CsmLogger().Infof("instanceId %s APIServer is not bind eip",
							csmInstance.InstanceUUID)
						return "", csmErr.NewResourceNotFoundException(fmt.Sprintf("please to "+
							"https://console.bce.baidu.com/network/?_=1717587594519#/vpc/endpoint/list. Then bind "+
							"an EIP to the instance with the instance ID %s", value.EndpointId))
					}
					//host = value.Eip
				}
				return host, nil
			}
		}
	}
	// 如果host没找到，则创建
	if host == "" {
		createEip := ""
		// 创建eip，第一次创建的时候就绑定eip
		if kubeConfigType == constants.KubeConfigTypePublic || *csmInstance.APIServerEip {
			createEipArgs := &eipSDK.CreateEipArgs{
				BandWidthInMbps: 1,
				Billing: &eipSDK.Billing{
					PaymentTiming: "Postpaid",
					BillingMethod: "ByTraffic",
				},
				Tags: snicTag,
			}
			eipResult, createErr := service.eipService.CreateEIP(ctx, createEipArgs, region)
			if createErr != nil {
				if kubeConfigType == constants.KubeConfigTypePublic {
					return "", createErr
				}
			} else {
				createEip = eipResult.Eip
			}
		}

		createEndpointArgs := &meta.CreateEndpointArgs{
			ClientToken: bceUtil.GetClientToken(),
			VpcId:       csmInstance.VpcNetworkId,
			Name:        csmInstance.InstanceUUID,
			SubnetId:    csmInstance.SubnetId,
			Service:     serviceDomain,
			Description: description,
			Billing: &meta.Billing{
				PaymentTiming: "Postpaid",
			},
			// key为csm-hosting，value为csm实例ID
			Tags: snicTag,
			Eip:  createEip,
		}

		createEndpointResult, createErr := service.vpcService.CreateEndpointWithEip(ctx, createEndpointArgs, region)
		if createErr != nil {
			ctx.CsmLogger().Errorf("createEndpoint in vpc endpoint error %v", createErr)
			return "", createErr
		}
		host = createEndpointResult.IpAddress

		if kubeConfigType == constants.KubeConfigTypePublic {
			host = createEip
		}
	}
	return host, nil
}
