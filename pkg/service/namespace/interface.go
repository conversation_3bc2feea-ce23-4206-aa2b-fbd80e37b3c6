package namespace

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	GetNamespaceList(ctx context.CsmContext, instanceUUID string, namespaceListRequest *meta.PageParam) (*meta.Namespaces, error)
	GetKubeConfig(ctx context.CsmContext, instanceUUID, region, kubeConfigType string) (*meta.KubeConfigResult, error)
	CreateSNICWithNewCsmInstance(ctx context.CsmContext, clusterId, region, kubeConfigType string,
		csmInstance *meta.Instances) (string, error)
}
