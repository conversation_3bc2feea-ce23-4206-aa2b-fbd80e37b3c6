package multiprotocol

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/object"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/tmpl"
)

type Service struct{}

func NewService() *Service {
	return &Service{}
}

func (s *Service) CheckVersionSupport(ctx context.CsmContext, version string) bool {
	supportVersion := getAerakiSupportVersion()
	if containsElement(supportVersion, version) {
		return true
	}
	return false
}

func (s *Service) ParseAerakiTmpl(ctx context.CsmContext, option *Option) ([]string, error) {
	// 解析 aeraki 模板
	aerakiYaml, err := tmpl.Evaluate(ctx, aerakiInstallYaml, option)
	if err != nil {
		return nil, err
	}
	return object.ManifestK8sObject(ctx, aerakiYaml)
}

func containsElement(arr []string, target string) bool {
	for _, element := range arr {
		if element == target {
			return true
		}
	}
	return false
}
