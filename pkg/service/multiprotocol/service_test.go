package multiprotocol

import (
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"

	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	mockCtx, _ = ctxCsm.NewCsmContextMock()
)

func TestCheckVersionSupport(t *testing.T) {
	tests := []struct {
		name    string
		version string
		expect  bool
	}{
		{
			name:    "support",
			version: "1.14.6",
			expect:  true,
		},
		{
			name:    "unsupport",
			version: "1.14.7",
			expect:  false,
		},
	}
	viper.Set(aerakiSupportVersion, map[string]string{"1.14.6": "1.2.3"})
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := NewService()
			ok := service.CheckVersionSupport(mockCtx, tt.version)
			assert.Equal(t, tt.expect, ok)
		})
	}
}

func TestParseAerakiTmpl(t *testing.T) {
	tests := []struct {
		name      string
		option    *Option
		length    int
		namespace string
	}{
		{
			name: "ok-ParseAerakiTmpl",
			option: NewOptionWith("istio-system", "ghcr.io/aeraki-mesh/aeraki:1.2.1",
				"Always", "istiod.istio-system:15010", "eraki.istio-system",
				":15010", true, true),
			length:    14,
			namespace: "istio-system",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := NewService()
			res, err := service.ParseAerakiTmpl(mockCtx, tt.option)
			assert.Nil(t, err)
			assert.Equal(t, tt.length, len(res))
		})
	}
}
