package registercenter

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/baidubce/bce-sdk-go/services/esg"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	"github.com/spf13/viper"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/vpc"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"strings"

	v1 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/crd/apis/registercenter/v1"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/register"
	registercentersdk2 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/registercentersdk"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

type Service struct {
	opt                   *Option
	cceService            cce.ClientInterface
	registerInstanceModel register.ServiceInterface
	VpcService            vpc.ServiceInterface
	instanceIdToAccountId map[string]string
}

func NewRegisterCenterService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt:                   option,
		cceService:            cce.NewClientService(),
		registerInstanceModel: register.NewRegisterInstancesService(register.NewOption(gormDB)),
		VpcService:            vpc.NewVPCService(),
	}
}

func (s *Service) GetServiceList(ctx csmContext.CsmContext, req *registercentersdk2.RegisterCenterServiceListRequest) (
	*registercentersdk2.RegisterCenterServiceListResponse, error) {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return nil, csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return nil, err
	}

	if registerInstance == nil {
		return nil, csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}
	return s.opt.Client.GetRegisterCenterServiceList(ctx, req, option)
}

func (s *Service) CreateServiceInstance(ctx csmContext.CsmContext, req *registercentersdk2.CreateServiceInstanceRequest) error {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}

	response, err := s.opt.Client.CreateRegisterServiceInstance(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) UpdateServiceInstance(ctx csmContext.CsmContext, req *registercentersdk2.CreateServiceInstanceRequest) error {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}

	response, err := s.opt.Client.UpdateRegisterServiceInstance(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) DeleteServiceInstance(ctx csmContext.CsmContext, req *registercentersdk2.DeleteServiceInstanceRequest) error {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}

	response, err := s.opt.Client.DeleteRegisterServiceInstance(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) GetServiceInstanceList(ctx csmContext.CsmContext, req *registercentersdk2.ServiceInstanceListRequest) (
	*registercentersdk2.ServiceInstanceListResponse, error) {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return nil, csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return nil, err
	}

	if registerInstance == nil {
		return nil, csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}
	response, err := s.opt.Client.GetServiceInstanceList(ctx, req, option)
	if err != nil {
		return nil, err
	}
	return response, registercentersdk2.NewPolarisError(response)
}

func intToBool(i int) bool {
	if i == 0 {
		return false
	}
	return true
}

func (s *Service) NewRegisterInstance(ctx csmContext.CsmContext, instance *meta.RegisterInstance, monitorToken, esgId string) error {
	region := instance.Region
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return csmErr.NewUnauthorizedException("user is nil", err)
	}

	// 获取esgName
	esgName := ""
	if esgId != "" {
		esgName, err = s.getEsgNameById(ctx, esgId)
		if err != nil {
			return err
		}
	}

	client, err := s.GetRegisterKubeClient(ctx, region)
	if err != nil {
		return err
	}

	// 3. create register instance CR
	cr := &v1.RegisterInstance{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Registry",
			APIVersion: "cse.baidubce.com/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: instance.InstanceId,
			Labels: map[string]string{
				"bce-account-id": accountId,
			},
		},
		Spec: v1.RegisterInstanceSpec{
			AccountID: accountId,
			VpcID:     instance.VpcId,
			SubnetID:  instance.SubnetId,
			ServerSpec: v1.ServerSpec{
				CPU:      "100m",
				Memory:   "500Mi",
				Replicas: 1,
			},
			EsgID:   esgId,
			EsgName: esgName,
			Release: viper.GetString(Release),
		},
	}

	enable := intToBool(instance.MonitorEnabled)
	cr.Spec.Args = &v1.Args{
		MonitorEnable: &enable,
	}

	if enable {
		cr.Spec.Args.MonitorCpromID = instance.MonitorInstanceId
		cr.Spec.Args.MonitorCpromToken = monitorToken
	}

	// 4. create register instance CR
	gvr := schema.GroupVersionResource{
		Group:    "cse.baidubce.com",
		Version:  "v1",
		Resource: "registries",
	}

	ins, _ := json.Marshal(cr)

	ctx.CsmLogger().Infof("create register instance CR: %s", string(ins))

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(cr)
	if err != nil {
		// handle error
		return err
	}
	unstructuredCr := &unstructured.Unstructured{Object: unstructuredObj}
	_, err = client.Dynamic().Resource(gvr).Create(context.TODO(), unstructuredCr, metav1.CreateOptions{})
	if err != nil {
		// handle error
		return err
	}

	// todo install vmagent

	// insert db
	err = s.registerInstanceModel.NewRegisterInstance(ctx, instance)
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) GetRegisterKubeClient(ctx csmContext.CsmContext, region string) (kube.Client, error) {
	// 1. get clusterId from config yaml
	clusterId := viper.GetString(RegisterCluster)

	ctx.CsmLogger().Infof("get clusterId: %s from config", clusterId)

	// 2. get admin kube config & create k8s client
	client, err := s.cceService.NewClient(ctx, region, clusterId, meta.HostingMeshType)
	if err != nil {
		return nil, err
	}
	return client, nil
}

func (s *Service) UpdateRegisterInstance(ctx csmContext.CsmContext, instanceId, endpointId, esgId string) error {
	// 0. 参数校验
	esgName, err := s.getEsgNameById(ctx, esgId)
	if err != nil {
		return err
	}

	// 1. 获取cr
	cr, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	// 2. 写安全组id到cr
	cr.Spec.EsgID = esgId
	cr.Spec.EsgName = esgName

	// 4. 提交cr
	if err = s.PutRegisterCenterCR(ctx, cr); err != nil {
		return err
	}

	return nil
}

func (s *Service) DeleteRegisterInstanceByInstanceId(ctx csmContext.CsmContext, id string) error {
	err := s.registerInstanceModel.DeleteRegisterInstanceByInstanceId(ctx, id)
	if err != nil {
		return err
	}

	err = s.DeleteRegisterCenterCR(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

func (s *Service) GetRegisterInstances(ctx csmContext.CsmContext, query *meta.QueryRegisterInstance) (*meta.PageResponse, error) {
	pageRes, insList, err := s.registerInstanceModel.GetRegisterInstances(ctx, query)
	if err != nil {
		return nil, err
	}

	// get cr by label bce-account-id
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, csmErr.NewUnauthorizedException("user is nil", err)
	}

	region := viper.GetString(Region)
	client, err := s.GetRegisterKubeClient(ctx, region)
	// get register instance CR
	gvr := schema.GroupVersionResource{
		Group:    "cse.baidubce.com",
		Version:  "v1",
		Resource: "registries",
	}
	labelSelector := "bce-account-id=" + accountId
	listOptions := metav1.ListOptions{
		LabelSelector: labelSelector,
	}
	unstructuredList, err := client.Dynamic().Resource(gvr).List(context.TODO(), listOptions)
	ctx.CsmLogger().Infof("get register instance CR list by selector: %s, list size: %s", labelSelector, len(unstructuredList.Items))
	if err != nil {
		ctx.CsmLogger().Errorf("get register instance CR list err: %s", err.Error())
	}

	registerInstances := make([]v1.RegisterInstance, 0)
	for _, u := range unstructuredList.Items {
		var registerInstance v1.RegisterInstance
		err := runtime.DefaultUnstructuredConverter.FromUnstructured(u.Object, &registerInstance)
		if err != nil {
			// handle error
			return nil, err
		}
		registerInstances = append(registerInstances, registerInstance)
	}

	instanceStatusMap := make(map[string]v1.RegisterInstanceStatus)
	for _, registerInstance := range registerInstances {
		instanceStatusMap[registerInstance.Name] = registerInstance.Status
	}

	registerInsRepsList := make([]meta.RegisterInsListRep, 0)
	for _, ins := range *insList {
		registryStatus := instanceStatusMap[ins.InstanceId]
		serverPort, serverProtocol := registryStatus.GetServerPortProtocol()
		registerInsRepsList = append(registerInsRepsList,
			meta.RegisterInsListRep{
				Id:             ins.InstanceId,
				Name:           ins.InstanceName,
				Region:         ins.Region,
				Status:         int(convertStatus(registryStatus.Phase)),
				ServerProtocol: serverProtocol,
				ServerPort:     serverPort,
				CreateTime:     *ins.CreateTime,
				UpdateTime:     *ins.UpdateTime,
			})
	}

	return &meta.PageResponse{
		PageNo:     pageRes.PageNo,
		PageSize:   pageRes.PageSize,
		TotalCount: pageRes.TotalCount,
		Result:     registerInsRepsList,
	}, nil
}

func (s *Service) GetRegisterInstancesByInstanceId(ctx csmContext.CsmContext, instanceId string) (*meta.RegisterInsDetailRep, error) {

	registerInstanceModel, err := s.registerInstanceModel.GetRegisterInstancesByInstanceId(ctx, instanceId)
	if err != nil {
		return nil, err
	}

	registerInsDetail := &meta.RegisterInsDetailRep{
		Id:                registerInstanceModel.InstanceId,
		Name:              registerInstanceModel.InstanceName,
		Region:            registerInstanceModel.Region,
		Status:            registerInstanceModel.Status,
		CreateTime:        *registerInstanceModel.CreateTime,
		UpdateTime:        *registerInstanceModel.UpdateTime,
		MonitorInstanceId: registerInstanceModel.MonitorInstanceId,
		ServerPort:        registerInstanceModel.ServerPort,
		ServerProtocol:    registerInstanceModel.ServerProtocol,
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return nil, err
	}

	registerInsDetail.ServerPort, registerInsDetail.ServerProtocol = registerInstance.Status.GetServerPortProtocol()

	// if monitor enable, set monitor instance id
	if registerInstance.Spec.Args.MonitorEnable != nil &&
		*registerInstance.Spec.Args.MonitorEnable &&
		len(registerInstance.Spec.Args.MonitorCpromID) > 0 {
		registerInsDetail.MonitorInstanceId = registerInstance.Spec.Args.MonitorCpromID
	}

	// status
	status := convertStatus(registerInstance.Status.Phase)
	registerInsDetail.Status = int(status)

	// loadBalancer
	loadBalances := make([]meta.RegisterLoadBalancer, 0)

	// get load balancer from register instance CR
	vpcDetail, err := s.VpcService.GetVPCAndSubnetDetail(ctx, registerInstanceModel.VpcId,
		registerInstanceModel.SubnetId, viper.GetString(Region))
	if err != nil {
		ctx.CsmLogger().Errorf("get vpc detail err: %s", err.Error())
	}
	for _, endpoint := range registerInstance.Status.EndpointList {
		var vpcName, subnetName string
		if vpcDetail != nil {
			vpcName = vpcDetail.VpcNetworkName
			subnetName = vpcDetail.SubnetName
		}

		loadBalances = append(loadBalances, meta.RegisterLoadBalancer{
			Type:       string(endpoint.Type),
			Ip:         endpoint.IP,
			VpcId:      registerInstanceModel.VpcId,
			VpcName:    vpcName,
			SubnetId:   registerInstanceModel.SubnetId,
			SubnetName: subnetName,
			Id:         endpoint.ID,
			EsgId:      registerInstance.Spec.EsgID,
			EsgName:    registerInstance.Spec.EsgName,
		})
	}
	registerInsDetail.LoadBalanceList = loadBalances

	return registerInsDetail, nil

}

func (s *Service) PutRegisterCenterCR(ctx csmContext.CsmContext, cr *v1.RegisterInstance) error {
	region := viper.GetString(Region)
	client, err := s.GetRegisterKubeClient(ctx, region)
	if err != nil {
		return err
	}
	gvr := schema.GroupVersionResource{
		Group:    "cse.baidubce.com",
		Version:  "v1",
		Resource: "registries",
	}

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(cr)
	if err != nil {
		return err
	}
	unstructuredCr := &unstructured.Unstructured{Object: unstructuredObj}

	_, err = client.Dynamic().Resource(gvr).Update(context.TODO(), unstructuredCr, metav1.UpdateOptions{})
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) GetRegisterCenterCR(ctx csmContext.CsmContext, instanceId string) (*v1.RegisterInstance, error) {

	region := viper.GetString(Region)
	client, err := s.GetRegisterKubeClient(ctx, region)
	if err != nil {
		return nil, err
	}

	// get register instance CR
	gvr := schema.GroupVersionResource{
		Group:    "cse.baidubce.com",
		Version:  "v1",
		Resource: "registries",
	}
	unstructuredObj, err := client.Dynamic().Resource(gvr).Get(context.TODO(), instanceId, metav1.GetOptions{})
	if err != nil {
		if kubeErrors.IsNotFound(err) {
			// handle not found error
			ctx.CsmLogger().Errorf("register instance: %s not found", instanceId)
			return nil, nil
		} else {
			// handle other errors
			return nil, err
		}
	}
	var registerInstance v1.RegisterInstance
	err = runtime.DefaultUnstructuredConverter.FromUnstructured(unstructuredObj.Object, &registerInstance)
	if err != nil {
		// handle error
		return nil, err
	}
	return &registerInstance, nil
}

func convertStatus(crStatus v1.CCRPhase) meta.RegisterInstanceStatus {
	switch crStatus {
	case v1.CCRPending, v1.CCRCreating:
		return meta.CreatingStatus
	case v1.CCRRunning, v1.CCRCreated, v1.CCRUpgraded:
		return meta.RunningStatus
	case v1.CCRUpgrading:
		return meta.AdjustingStatus
	case v1.CCRStopping, v1.CCRTerminating:
		return meta.ReleasingStatus
	case v1.CCRStartingFailed:
		return meta.CreateFailedStatus
	case v1.CCRFailed, v1.CCRUpgradeFailed, v1.CCRStoppingFailed:
		return meta.ErrorStatus
	default:
		return meta.InitStatus
	}
}

func (s *Service) UpdateRegisterInstanceArgs(ctx csmContext.CsmContext, instanceId string, args map[string]interface{}) error {
	// 1. 获取cr
	cr, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	// 2. 写args到cr
	updated, err := cr.WriteArgs(args)
	if err != nil {
		return err
	}

	// 3. 不需要更新直接退出
	if !updated {
		return nil
	}

	// 4. 提交cr
	if err = s.PutRegisterCenterCR(ctx, cr); err != nil {
		return err
	}

	return nil
}

func (s *Service) DeleteRegisterCenterCR(ctx csmContext.CsmContext, instanceId string) error {

	region := viper.GetString(Region)
	client, err := s.GetRegisterKubeClient(ctx, region)
	if err != nil {
		return err
	}

	// get register instance CR
	gvr := schema.GroupVersionResource{
		Group:    "cse.baidubce.com",
		Version:  "v1",
		Resource: "registries",
	}
	unstructuredObj, err := client.Dynamic().Resource(gvr).Get(context.TODO(), instanceId, metav1.GetOptions{})
	if err != nil {
		if kubeErrors.IsNotFound(err) {
			// handle not found error
			ctx.CsmLogger().Errorf("register instance CR: %s not found", instanceId)
			return nil
		} else {
			// handle other errors
			return err
		}
	}
	var registerInstance v1.RegisterInstance
	err = runtime.DefaultUnstructuredConverter.FromUnstructured(unstructuredObj.Object, &registerInstance)
	if err != nil {
		// handle error
		return err
	}

	if registerInstance.Status.Phase == v1.CCRTerminating {
		ctx.CsmLogger().Infof("register instance CR: %s is terminating", instanceId)
		return nil
	}

	// delete register instance CR
	err = client.Dynamic().Resource(gvr).Delete(context.TODO(), instanceId, metav1.DeleteOptions{})
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) GetRegisterInstanceArgs(ctx csmContext.CsmContext, instanceId string) (map[string]interface{}, error) {
	cr, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return nil, err
	}
	return cr.ReadArgs(), nil
}

func (s *Service) BatchDeleteServiceInstance(ctx csmContext.CsmContext, req []*registercentersdk2.BatchRequest) error {
	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}
	response, err := s.opt.Client.BatchDeleteServiceInstance(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) BatchIsolateServiceInstance(ctx csmContext.CsmContext, req []*registercentersdk2.BatchRequest) error {
	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}
	response, err := s.opt.Client.BatchIsolateServiceInstance(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

// getEsgNameById 通过安全组id获取安全组name
func (s *Service) getEsgNameById(ctx csmContext.CsmContext, esgId string) (string, error) {
	if esgId == "" {
		return "", fmt.Errorf("empty enterprise security group id")
	}

	esgName := ""
	marker := ""
	for {
		result, err := s.VpcService.ListEsg(ctx, &esg.ListEsgArgs{
			MaxKeys: 1000,
			Marker:  marker,
		}, viper.GetString(Region))
		if err != nil {
			return "", err
		}
		marker = result.NextMarker
		for _, esgInfo := range result.EnterpriseSecurityGroups {
			if esgInfo.Id == esgId {
				esgName = esgInfo.Name
				break
			}
		}
		if esgName != "" || !result.IsTruncated {
			break
		}
	}

	if esgName == "" {
		return "", fmt.Errorf("enterprise security group %s not found", esgId)
	}
	return esgName, nil
}

func (s *Service) GetNamespaceList(ctx csmContext.CsmContext,
	req *registercentersdk2.RegisterCenterNamespaceListRequest) (
	*apiservice.BatchQueryResponse, error) {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return nil, csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return nil, err
	}

	if registerInstance == nil {
		return nil, csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}
	response, err := s.opt.Client.GetRegisterCenterNamespaceList(ctx, req, option)
	if err != nil {
		return nil, err
	}
	return response, registercentersdk2.NewPolarisError(response)
}

func (s *Service) CreateNamespaces(ctx csmContext.CsmContext, req []registercentersdk2.CreateNamespaceRequest) error {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}

	response, err := s.opt.Client.CreateRegisterNamespaces(ctx, req, option)
	if err != nil {
		return err
	}

	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) UpdateNamespaces(ctx csmContext.CsmContext, req []registercentersdk2.CreateNamespaceRequest) error {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}

	response, err := s.opt.Client.UpdateRegisterNamespaces(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) DeleteNamespaces(ctx csmContext.CsmContext, req []registercentersdk2.CreateNamespaceRequest) error {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}

	response, err := s.opt.Client.DeleteRegisterNamespaces(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) GetInstanceAccountId(ctx csmContext.CsmContext, instanceId string) (string, error) {
	if s.instanceIdToAccountId == nil {
		s.instanceIdToAccountId = make(map[string]string)
	}
	if instanceId == "" {
		return "", fmt.Errorf("instanceId empty")
	}
	if accountId, ok := s.instanceIdToAccountId[instanceId]; ok {
		return accountId, nil
	}

	registerInstanceModel, err := s.registerInstanceModel.GetRegisterInstancesByInstanceId(ctx, instanceId)
	if err != nil {
		if strings.Contains(err.Error(), "record not found") {
			return "", nil
		}
		return "", err
	}

	accountId := registerInstanceModel.AccountId
	s.instanceIdToAccountId[instanceId] = accountId
	return accountId, nil
}
