package registercenter

import (
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/registercentersdk"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type Interface interface {
	UpdateRegisterInstance(ctx csmContext.CsmContext, instanceId, endpointId, esgId string) error
	DeleteRegisterInstanceByInstanceId(ctx csmContext.CsmContext, id string) error
	GetRegisterInstances(ctx csmContext.CsmContext, query *meta.QueryRegisterInstance) (*meta.PageResponse, error)
	GetRegisterInstancesByInstanceId(ctx csmContext.CsmContext, instanceId string) (*meta.RegisterInsDetailRep, error)
	GetServiceList(ctx csmContext.CsmContext, req *registercentersdk.RegisterCenterServiceListRequest) (
		*registercentersdk.RegisterCenterServiceListResponse, error)

	NewRegisterInstance(ctx csmContext.CsmContext, instance *meta.RegisterInstance, monitorToken, esgId string) error
	CreateServiceInstance(ctx csmContext.CsmContext, req *registercentersdk.CreateServiceInstanceRequest) error
	UpdateServiceInstance(ctx csmContext.CsmContext, req *registercentersdk.CreateServiceInstanceRequest) error
	DeleteServiceInstance(ctx csmContext.CsmContext, req *registercentersdk.DeleteServiceInstanceRequest) error
	GetServiceInstanceList(ctx csmContext.CsmContext, req *registercentersdk.ServiceInstanceListRequest) (
		*registercentersdk.ServiceInstanceListResponse, error)

	UpdateRegisterInstanceArgs(ctx csmContext.CsmContext, instanceId string, args map[string]interface{}) error
	GetRegisterInstanceArgs(ctx csmContext.CsmContext, instanceId string) (map[string]interface{}, error)

	BatchDeleteServiceInstance(ctx csmContext.CsmContext, req []*registercentersdk.BatchRequest) error
	BatchIsolateServiceInstance(ctx csmContext.CsmContext, req []*registercentersdk.BatchRequest) error

	GetNamespaceList(ctx csmContext.CsmContext, req *registercentersdk.RegisterCenterNamespaceListRequest) (
		*apiservice.BatchQueryResponse, error)
	CreateNamespaces(ctx csmContext.CsmContext, req []registercentersdk.CreateNamespaceRequest) error
	UpdateNamespaces(ctx csmContext.CsmContext, req []registercentersdk.CreateNamespaceRequest) error
	DeleteNamespaces(ctx csmContext.CsmContext, req []registercentersdk.CreateNamespaceRequest) error

	GetInstanceAccountId(ctx csmContext.CsmContext, instanceId string) (string, error)
}
