package registercenter

import (
	"github.com/jinzhu/gorm"
	"github.com/spf13/viper"
	registercentersdk2 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/registercentersdk"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

const (
	RegisterCluster = "register.cluster"
	Region          = "register.region"
	Release         = "register.release"
)

func InitServingOptions() {
	viper.SetDefault(Release, "1.1.0")
}

type Option struct {
	DB     *dbutil.DB
	Client *registercentersdk2.Client
}

// NewOption 初始化 vpc 配置
func NewOption(d *gorm.DB) *Option {
	return &Option{
		DB:     dbutil.NewDB(d),
		Client: registercentersdk2.NewClient(),
	}
}
