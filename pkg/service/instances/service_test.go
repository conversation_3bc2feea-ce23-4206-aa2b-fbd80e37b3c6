package instances

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"testing"
	"time"

	cce_v1 "github.com/baidubce/bce-sdk-go/services/cce"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/sqlite"
	"github.com/pkg/errors"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	mockCluster "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	mockInstance "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	model_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/monitor"
	mockMonitor "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/monitor/mock"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	mockBlsService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/bls/mock"
	mockCceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	discoveryselectorMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/discoveryselector/mock"
	mockLaneService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/lane/mock"
	serviceMeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	multiProtocolServiceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/multiprotocol/mock"
	mockNamespaceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/namespace/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	mockVpcService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/vpc/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	versionUtil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/testdata"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

var (
	mockDB, _  = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	mockCtx, _ = ctxCsm.NewCsmContextMock()

	instanceUUID               = "test"
	instanceName               = "test"
	instanceType               = "standalone"
	instanceHostingType        = "hosting"
	accountId                  = "1"
	discoverySelectorEnabled   = true
	discoverySelectorLabels    = "{\"user\": \"test\"}"
	discoverySelectorLabelsMap = map[string]string{"user": "test"}
	istioInstallNamespace      = "istio-system"
	instanceManageScope        = "cluster"
	clusterUUID                = "test-1"
	meshInstanceId             = "test01"

	testNamespace      = "istio-system"
	name               = "test"
	clusterID          = "test-123456"
	clusterName        = "istio-test01"
	clusterType        = "standalone"
	vpcId              = "aaaaaaaa"
	vpcName            = "test-sss"
	status             = "running"
	region             = "bj"
	testInstanceName   = "test-instance"
	testInstanceUUID   = "csm-123456"
	testInstanceType   = "standalone"
	testInstanceStatus = "running"

	testClusterName           = "test-cluster"
	testClusterUUID           = "cce-123456"
	testRegion                = "bj"
	testIstioInstallNamespace = "istio-system"

	clusterUUID1 = "cce-0k355plq"
	clusterUUID2 = "cce-0k355plq"

	clusterName1 = "istio-test01"
	clusterName2 = "istio-test01-remote"

	bj     = "bj"
	bjId   = "bj-cce-123456"
	bjName = "bj-123456"

	testVpcId       = "xxx"
	testVpcSubnetId = "xxx"

	fakeCceClient *cce.Client
)

func changeDir() string {
	currentDir, _ := os.Getwd()
	os.Chdir("../../../")
	viper.Set(version.IopStandaloneHub, testdata.Version1146)
	viper.Set(monitor.IstioScrapeJobTemplate, "istio_scrape_job.yaml")
	viper.Set(monitor.EnvoyScrapeJobTemplate, "envoy_scrape_job.yaml")
	viper.Set(monitor.GatewayScrapeJobTemplate, "gateway_scrape_job.tmpl")
	viper.Set(monitor.HostingIstioScrapeJobTemplate, "hosting_istio_scrape_job.tmpl")
	return currentDir
}

func recoverDir(stashDir string) {
	os.Chdir(stashDir)
}

func isArrayDesc(arr []meta.MeshInstance, length int64) bool {
	if length == 1 {
		return true
	}
	if arr[length-1].CreateTime.Unix() > arr[length-2].CreateTime.Unix() {
		return false
	}
	return isArrayDesc(arr, length-1)
}

func isArrayAsc(arr []meta.MeshInstance, length int64) bool {
	if length == 1 {
		return true
	}
	if arr[length-1].CreateTime.Unix() < arr[length-2].CreateTime.Unix() {
		return false
	}
	return isArrayAsc(arr, length-1)
}

func buildClusterList(clusterType string) *[]meta.Cluster {
	if clusterType == "" {
		return &[]meta.Cluster{}
	}
	return &[]meta.Cluster{
		{
			ClusterType:       clusterType,
			MonitorJobIds:     "jobId",
			MonitorAgentID:    "agentId",
			MonitorInstanceId: "instanceId",
			MonitorRegion:     region,
			Region:            region,
		},
	}
}

func TestGetServiceMeshInstances(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// mock数据库返回的实例列表数据
	testCreateTime := new(time.Time)
	testCreateTimes := []time.Time{
		testCreateTime.Add(time.Second * 3), testCreateTime.Add(time.Second * 1), testCreateTime.Add(time.Second * 2),
		testCreateTime.Add(time.Second * 4), testCreateTime.Add(time.Second * 5), testCreateTime.Add(time.Second * 0),
	}
	testInstanceIDs := []string{"id-0", "id-1", "id-2", "id-3", "id-4", "id-5"}
	testInstanceNames := []string{"inst-0", "inst-1", "inst-2", "inst-3", "inst-4", "inst-5"}

	mockCtx.SetResource("UnitTest")

	var mockInstancesList []meta.Instances
	for i := 0; i < len(testInstanceIDs); i++ {
		mockInstancesList = append(mockInstancesList, meta.Instances{
			BaseModel: dbutil.BaseModel{
				CreateTime: &testCreateTimes[i],
			},
			InstanceUUID:  testInstanceIDs[i],
			InstanceName:  testInstanceNames[i],
			IstioVersion:  testdata.Version1146,
			InstanceType:  testInstanceType,
			PublicEnabled: csm.Bool(false),
		})
	}

	tests := []struct {
		name         string
		mrp          *meta.CsmMeshRequestParams
		expectNum    int64
		clusterType  string
		clusterList  *[]meta.Cluster
		needAscOrder bool
	}{
		{
			name: "test_pageSlice",
			mrp: &meta.CsmMeshRequestParams{
				PageSize: 4,
				PageNo:   1,
			},
			expectNum:    4,
			clusterType:  "remote",
			clusterList:  buildClusterList(clusterType),
			needAscOrder: false,
		},
		{
			name: "test_pageSlice2",
			mrp: &meta.CsmMeshRequestParams{
				PageSize: 4,
				PageNo:   2,
			},
			expectNum:    2,
			clusterList:  buildClusterList("external"),
			needAscOrder: false,
		},
		{
			name: "test_filter",
			mrp: &meta.CsmMeshRequestParams{
				PageSize: 3,
				PageNo:   1,
				// Status:   "deploying",
			},
			expectNum:    3,
			clusterList:  buildClusterList("external"),
			needAscOrder: false,
		},
		{
			name: "test_order",
			mrp: &meta.CsmMeshRequestParams{
				PageSize: 3,
				PageNo:   1,
				// Status:   "running",
				Order: "Asc",
			},
			expectNum:    3,
			clusterList:  buildClusterList("external"),
			needAscOrder: true,
		},
	}

	for _, tt := range tests {

		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockInstancesModel := mockInstance.NewMockServiceInterface(ctrl)

		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstancesModel,
				clusterModel:   mockClusterModel,
			}

			mockInstancesModel.EXPECT().GetInstancesList(mockCtx, tt.mrp).Return(&mockInstancesList, nil).AnyTimes()
			mockClusterModel.EXPECT().GetAllClusterByInstanceUUID(mockCtx, gomock.Any()).Return(tt.clusterList, nil).AnyTimes()
			mockInstancesModel.EXPECT().GetInstanceStatus(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return("Running", nil).AnyTimes()
			mockInstancesModel.EXPECT().GetInstanceClustersCount(mockCtx, gomock.Any(), gomock.Any()).Return(int64(0), int64(0), nil).AnyTimes()
			if tt.clusterType == "" || tt.clusterType == "remote" {
				mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).
					Return(nil, meta.HostingMeshType, errors.New("test")).AnyTimes()
			}
			res, _ := s.GetServiceMeshInstances(mockCtx, tt.mrp)
			assert.Equal(t, tt.expectNum, int64(len(res.Result)))

			if tt.needAscOrder {
				assert.True(t, isArrayAsc(res.Result, int64(len(res.Result))))
			} else {
				assert.True(t, isArrayDesc(res.Result, int64(len(res.Result))))
			}
		})
	}
}

func TestGetIstioInstallNamespace(t *testing.T) {
	tests := []struct {
		name           string
		meshInstanceId string
		expect         string
	}{
		{
			name:           "getIstioInstallNamespace",
			meshInstanceId: "test",
			expect:         "istio-system-test",
		},
	}
	stash := changeDir()
	for _, tt := range tests {
		service := NewInstanceService(NewOption(mockDB))
		t.Run(tt.name, func(t *testing.T) {
			if got := service.getIstioInstallNamespace(tt.meshInstanceId); got != tt.expect {
				t.Errorf("getIstioInstallNamespace() got = %v, expect %v", got, tt.expect)
			}
		})
	}
	recoverDir(stash)
}

func TestGetNamespaceLabels(t *testing.T) {
	tests := []struct {
		name           string
		region         string
		meshInstanceId string
		labels         map[string]string
		expect         map[string]string
	}{

		{
			name:           "getIstioInstallNamespace",
			region:         region,
			meshInstanceId: meshInstanceId,
			labels:         map[string]string{"user": "test01"},
			expect:         map[string]string{"user": "test01", constants.TopologyIstioIoNetWork: region, constants.MeshInstanceId: meshInstanceId},
		},
	}
	stash := changeDir()
	for _, tt := range tests {
		service := NewInstanceService(NewOption(mockDB))
		t.Run(tt.name, func(t *testing.T) {
			got := service.getNamespaceLabels(tt.region, tt.meshInstanceId, tt.labels)
			if !reflect.DeepEqual(got, tt.expect) {
				t.Errorf("getNamespaceLabels() got = %v, expect %v", got, tt.expect)
			}
		})
	}
	recoverDir(stash)
}

func TestDiscoverySelectorEnabled(t *testing.T) {
	tests := []struct {
		name    string
		enabled int
		labels  string
		expect  bool
	}{

		{
			name:    "getIstioInstallNamespace1",
			labels:  `{"user": "test"}`,
			enabled: 1,
			expect:  true,
		},
		{
			name:    "getIstioInstallNamespace2",
			labels:  "",
			enabled: 0,
			expect:  false,
		},
		{
			name:    "getIstioInstallNamespace3",
			labels:  "",
			enabled: 1,
			expect:  false,
		},
	}

	stash := changeDir()
	for _, tt := range tests {
		service := NewInstanceService(NewOption(mockDB))
		t.Run(tt.name, func(t *testing.T) {
			got := service.DiscoverySelectorEnabled(tt.enabled, tt.labels)
			if got != tt.expect {
				t.Errorf("got = %v, expect %v", got, tt.expect)
			}
		})
	}
	recoverDir(stash)
}

func TestInstallMeshCluster(t *testing.T) {
	ctrl := gomock.NewController(t)
	tests := []struct {
		name      string
		instances *meta.Instances
		clusters  *meta.Cluster
		paasType  meta.PaaSType
		traceInfo *meta.TraceInfo
		wantErr   bool
	}{
		{
			name: "InstallMeshCluster-1",
			instances: &meta.Instances{
				InstanceUUID:             instanceUUID,
				InstanceName:             instanceName,
				InstanceType:             instanceType,
				IstioVersion:             testdata.Version1146,
				Region:                   region,
				AccountId:                accountId,
				DiscoverySelectorEnabled: csm.Bool(discoverySelectorEnabled),
				DiscoverySelectorLabels:  discoverySelectorLabels,
				IstioInstallNamespace:    istioInstallNamespace,
				InstanceManageScope:      instanceManageScope,
				MultiProtocolEnabled:     csm.Bool(true),
				TraceEnabled:             csm.Bool(false),
			},
			clusters: &meta.Cluster{
				InstanceUUID:          instanceUUID,
				ClusterUUID:           clusterUUID,
				ClusterName:           clusterName,
				ClusterType:           clusterType,
				Region:                region,
				AccountId:             accountId,
				IstioInstallNamespace: istioInstallNamespace,
			},
			traceInfo: &meta.TraceInfo{
				TraceEnabled: false,
			},
			paasType: meta.PaaSTypeCCE,
			wantErr:  true,
		},
	}
	mockCtx.Set(iam.ContextIAMUser, &sdkIAM.User{
		ID:   "",
		Name: "",
		Domain: sdkIAM.UserDomain{
			ID:   "123",
			Name: "",
		},
	})

	mockCceService := mockCceService.NewMockClientInterface(ctrl)

	stash := changeDir()
	for _, tt := range tests {
		service := &Service{
			opt:        NewOption(mockDB),
			cceService: mockCceService,
		}

		fakeClient := kube.NewFakeClient()
		mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

		t.Run(tt.name, func(t *testing.T) {
			err := service.InstallMeshCluster(mockCtx, tt.instances, tt.clusters, tt.paasType, tt.traceInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("InstallMeshCluster() got error %v, expect %v", err, tt.wantErr)
			}
		})
	}
	pwd, _ := os.Getwd()
	// todo（better way need to be required） remove the test file
	fileName := fmt.Sprintf("istio_iop_cce_bj_istio-test01_test-1_%s_istio-system.yaml", testdata.Version1146)
	filePath := filepath.Join(pwd, constants.Templates, constants.BaseIstioTemplate, testdata.Version1146, fileName)
	_, _ = os.ReadFile(filePath)
	recoverDir(stash)
}

func TestInstallRemoteMeshCluster(t *testing.T) {
	ctrl := gomock.NewController(t)
	tests := []struct {
		name            string
		instances       *meta.Instances
		primaryClusters *meta.Cluster
		clusters        *meta.Cluster
		paasType        meta.PaaSType
		wantErr         bool
	}{
		{
			name: "InstallRemoteMeshCluster-ok",
			instances: &meta.Instances{
				InstanceUUID:             instanceUUID,
				InstanceName:             instanceName,
				InstanceType:             instanceType,
				IstioVersion:             testdata.Version1146,
				Region:                   region,
				AccountId:                accountId,
				DiscoverySelectorEnabled: csm.Bool(discoverySelectorEnabled),
				DiscoverySelectorLabels:  discoverySelectorLabels,
				IstioInstallNamespace:    istioInstallNamespace,
				InstanceManageScope:      instanceManageScope,
				MultiProtocolEnabled:     csm.Bool(true),
			},
			clusters: &meta.Cluster{
				InstanceUUID:          instanceUUID,
				ClusterUUID:           clusterUUID,
				ClusterName:           clusterName,
				ClusterType:           clusterType,
				Region:                region,
				AccountId:             accountId,
				IstioInstallNamespace: istioInstallNamespace,
			},
			primaryClusters: &meta.Cluster{
				InstanceUUID:          instanceUUID,
				ClusterUUID:           clusterUUID,
				ClusterName:           clusterName,
				ClusterType:           clusterType,
				Region:                region,
				AccountId:             accountId,
				IstioInstallNamespace: istioInstallNamespace,
			},
			paasType: meta.PaaSTypeCCE,
			wantErr:  true,
		},
	}
	mockCtx.Set(iam.ContextIAMUser, &sdkIAM.User{
		ID:   "",
		Name: "",
		Domain: sdkIAM.UserDomain{
			ID:   "123",
			Name: "",
		},
		//Password: "",
	})

	mockCceService := mockCceService.NewMockClientInterface(ctrl)

	stash := changeDir()
	for _, tt := range tests {
		service := &Service{
			opt:        NewOption(mockDB),
			cceService: mockCceService,
		}

		fakeClient := kube.NewFakeClient()
		mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(fakeClient, nil)

		t.Run(tt.name, func(t *testing.T) {
			err := service.InstallRemoteMeshCluster(mockCtx, tt.instances, tt.primaryClusters, tt.clusters, tt.paasType)
			if (err != nil) != tt.wantErr {
				t.Errorf("InstallRemoteMeshCluster() got error %v, expect %v", err, tt.wantErr)
			}
		})
	}
	pwd, _ := os.Getwd()
	// todo（better way need to be required） remove the test file
	fileName := fmt.Sprintf("istio_iop_cce_bj_istio-test01_test-1_%s_istio-system.yaml", testdata.Version1146)
	filePath := filepath.Join(pwd, constants.Templates, constants.BaseIstioTemplate, testdata.Version1146, fileName)
	_, _ = os.ReadFile(filePath)
	recoverDir(stash)
}

func buildIstioService() *v1.Service {
	return &v1.Service{
		Status: v1.ServiceStatus{
			LoadBalancer: v1.LoadBalancerStatus{
				Ingress: []v1.LoadBalancerIngress{
					{
						IP:       "*******",
						Hostname: "test",
					},
				},
			},
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      constants.EastWestGateway,
			Namespace: testNamespace,
		},
	}
}

func buildClusterDetail() *cce_v1.GetClusterResult {
	return &cce_v1.GetClusterResult{
		ClusterUuid: clusterID,
		ClusterName: clusterName,
		Version:     testdata.Version1146,
		Region:      region,
		VpcId:       vpcId,
		VpcName:     vpcName,
	}
}

func buildMockInstance() meta.Instances {
	mockInstance := meta.Instances{
		InstanceUUID:             testInstanceUUID,
		InstanceName:             testInstanceName,
		InstanceType:             testInstanceType,
		IstioVersion:             testdata.Version1146,
		DiscoverySelectorEnabled: csm.Bool(discoverySelectorEnabled),
		DiscoverySelectorLabels:  discoverySelectorLabels,
		MultiProtocolEnabled:     csm.Bool(false),
		MonitorEnabled:           csm.Bool(false),
		VpcNetworkId:             testVpcId,
		SubnetId:                 testVpcSubnetId,
		APIServerEip:             csm.Bool(false),
	}
	return mockInstance
}

func buildMockCluster() meta.Cluster {
	mockCluster := meta.Cluster{
		InstanceUUID:          testInstanceUUID,
		ClusterUUID:           testClusterUUID,
		ClusterName:           testClusterName,
		Region:                testRegion,
		IstioInstallNamespace: testIstioInstallNamespace,
	}
	return mockCluster
}

func buildMockInstanceNetworkType() *meta.InstanceNetworkType {
	return &meta.InstanceNetworkType{
		VpcNetworkId:   "xxx",
		VpcNetworkName: "xxx",
		VpcNetworkCidr: "xxx",
		SubnetId:       "xxx",
		SubnetName:     "xxx",
		SubnetCidr:     "xxx",
	}
}

// TestGetInstanceDetail 测试函数，用于获取实例详情信息。
// 参数t：*testing.T类型，表示测试对象。
// 返回值nil；无返回值，函数内部会执行断言操作。
func TestGetInstanceDetail(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
	mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	mockVpcService := mockVpcService.NewMockServiceInterface(ctrl)

	mockInstance := buildMockInstance()
	mockCluster := buildMockCluster()

	mockHostingInstance := mockInstance
	mockHostingInstance.InstanceType = instanceHostingType
	mockHostingInstance.IstioVersion = testdata.Version1146

	mockHostingCluster := mockCluster

	tests := []struct {
		name         string
		instanceUUID string
		instance     *meta.Instances
		cluster      *meta.Cluster
		expect       *meta.InstanceDetail
		wantErr      error
	}{
		{
			name:         "standalone-instance-detail",
			instanceUUID: "csm-35xzcatt",
			instance:     &mockInstance,
			cluster:      &mockCluster,
			expect: &meta.InstanceDetail{
				BillingModel:        "free",
				Type:                "standalone",
				IstioVersion:        testdata.Version1146,
				Status:              "running",
				ControlPanelAddress: "*******",
				BlbInfo: meta.BlbInfo{
					BlbName:   "cce-123456/istio-system/istio-eastwestgateway",
					BlbStatus: "pending",
				},
				VpcInfo: meta.VpcInfo{
					VpcId:   vpcId,
					VpcName: vpcName,
				},
				ClusterInfo: meta.ClusterInfo{
					ClusterName: "test-cluster",
					ClusterId:   "cce-123456",
				},
				OverviewOfSidecar: meta.OverviewOfSidecar{
					InstanceId:   "csm-35xzcatt",
					InstanceName: "test-instance",
					Num:          0,
				},
				DiscoverySelector: &meta.DiscoverySelector{
					Enabled:     true,
					MatchLabels: discoverySelectorLabelsMap,
				},
				MultiProtocol: false,
				NetworkType:   &meta.InstanceNetworkType{},
			},
			wantErr: nil,
		},
		{
			name:         "hosting-instance-detail",
			instanceUUID: "csm-35xzcatt",
			instance:     &mockHostingInstance,
			cluster:      &mockHostingCluster,
			expect: &meta.InstanceDetail{
				BillingModel:        "free",
				Type:                "hosting",
				IstioVersion:        testdata.Version1146,
				Status:              "running",
				ControlPanelAddress: "",
				BlbInfo: meta.BlbInfo{
					BlbName:   "cce-123456/istio-system/istiod",
					BlbStatus: "notExist",
				},
				VpcInfo: meta.VpcInfo{
					VpcId:   "xxx",
					VpcName: "xxx",
					VpcCidr: "xxx",
				},
				ClusterInfo: meta.ClusterInfo{
					ClusterName: "test-cluster",
					ClusterId:   "cce-123456",
				},
				OverviewOfSidecar: meta.OverviewOfSidecar{
					InstanceId:   "csm-35xzcatt",
					InstanceName: "test-instance",
					Num:          0,
				},
				DiscoverySelector: &meta.DiscoverySelector{
					Enabled:     true,
					MatchLabels: discoverySelectorLabelsMap,
				},
				MultiProtocol: false,
				NetworkType:   buildMockInstanceNetworkType(),
				ConfigCluster: "EXTERNAL",
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstanceModel,
				clusterModel:   mockClusterModel,
				cceService:     mockCceService,
				VpcService:     mockVpcService,
			}

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(tt.instance, nil)
			mockClusterModel.EXPECT().GetIstiodCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.cluster, nil)
			mockInstanceModel.EXPECT().GetInstanceStatus(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(testInstanceStatus, nil)

			fakeClient := kube.NewFakeClient()
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			if tt.instance.InstanceType == string(version.StandaloneVersionType) {
				mockCceService.EXPECT().GetCCECluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildClusterDetail(), nil)
			} else {
				mockVpcService.EXPECT().GetVPCAndSubnetDetail(gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any()).Return(buildMockInstanceNetworkType(), nil)
			}
			//nolint:errcheck // 单测
			fakeClient.Kube().CoreV1().Services(testNamespace).Create(context.TODO(), buildIstioService(), metav1.CreateOptions{})

			gotInstance, err := service.GetInstanceDetail(mockCtx, tt.instanceUUID)
			if err == nil && !reflect.DeepEqual(tt.expect, gotInstance) {
				t.Errorf("GetInstanceDetail expect=%v,\nbut got%v", tt.expect, gotInstance)
			} else if err != tt.wantErr {
				t.Errorf("GetInstanceDetail wantErr %v,\nbut got=%v", tt.wantErr, err)
			}
		})
	}
}

func buildHostingInstanceInfo() *meta.Instances {
	instanceInfo := buildInstanceInfo()
	instanceInfo.InstanceType = "hosting"
	return instanceInfo
}

func buildInstanceInfo() *meta.Instances {
	instanceInfo := &meta.Instances{
		InstanceUUID:             testInstanceUUID,
		InstanceName:             testInstanceName,
		InstanceType:             testInstanceType,
		IstioVersion:             testdata.Version1146,
		IstioInstallNamespace:    istioInstallNamespace,
		Region:                   region,
		DiscoverySelectorLabels:  discoverySelectorLabels,
		DiscoverySelectorEnabled: csm.Bool(discoverySelectorEnabled),
		MultiProtocolEnabled:     csm.Bool(true),
		TraceEnabled:             csm.Bool(false),
	}
	return instanceInfo
}

func buildCluster() *meta.Cluster {
	cluster := &meta.Cluster{
		InstanceUUID:          testInstanceUUID,
		ClusterUUID:           testClusterUUID,
		ClusterName:           testClusterName,
		Region:                testRegion,
		IstioInstallNamespace: testIstioInstallNamespace,
	}
	return cluster
}

func buildCPromAgent() *meta.CPromAgent {
	return &meta.CPromAgent{
		AgentID: "test-agent",
		Cluster: &model_meta.CPromBindingCluster{
			Spec: &model_meta.CPromBindingClusterSpec{
				ClusterID: clusterUUID,
			},
		},
	}
}

func TestNewServiceMeshInstance(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
	mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
	mockMonitorModel := mockMonitor.NewMockServiceInterface(ctrl)
	tests := []struct {
		name      string
		instance  *meta.Instances
		cluster   *meta.Cluster
		traceInfo *meta.TraceInfo
		eks       bool
		wantErr   bool
	}{
		{
			name:     "NewServiceMeshInstance-test01",
			instance: buildInstanceInfo(),
			cluster:  buildCluster(),
			traceInfo: &meta.TraceInfo{
				TraceEnabled: false,
			},
			eks:     false,
			wantErr: true,
		},
		{
			name:     "NewServiceMeshInstance-test02",
			instance: buildInstanceInfo(),
			cluster:  buildCluster(),
			traceInfo: &meta.TraceInfo{
				TraceEnabled: false,
			},
			eks:     true,
			wantErr: true,
		},
	}
	mCtx, _ := ctxCsm.NewCsmContextMock()
	mCtx.Set(iam.ContextIAMUser, &sdkIAM.User{
		ID:   "aa",
		Name: "aa",
		Domain: sdkIAM.UserDomain{
			ID:   "123",
			Name: "aa",
		},
	})
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstanceModel,
				clusterModel:   mockClusterModel,
				cceService:     mockCceService,
				monitorModel:   mockMonitorModel,
			}
			if tt.eks {
				service.opt.EksAccountIds = []string{"123"}
			}
			mockInstanceModel.EXPECT().WithTx(gomock.Any()).Return(mockInstanceModel)
			mockInstanceModel.EXPECT().NewInstance(gomock.Any(), gomock.Any()).Return(nil)

			mockClusterModel.EXPECT().WithTx(gomock.Any()).Return(mockClusterModel)
			mockClusterModel.EXPECT().NewCluster(gomock.Any(), gomock.Any()).Return(errors.New("error"))

			// mockMonitorModel.EXPECT().GetCPromAgent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			// 	Return(buildCPromAgent(), nil)
			// mockMonitorModel.EXPECT().CreateIstioScrapeJob(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			// 	Return("jobId-1", nil)
			// mockMonitorModel.EXPECT().CreateEnvoyScrapeJob(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			// 	Return("jobId-2", nil)

			// todo optimize code due to goroutine
			err := service.NewServiceMeshInstance(mCtx, tt.instance, tt.cluster, tt.traceInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewServiceMeshInstance(), error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func buildMeshCluster() []serviceMeta.MeshCluster {
	meshCluster := make([]serviceMeta.MeshCluster, 0)
	meshCluster1 := serviceMeta.MeshCluster{
		ClusterName: clusterName1,
		ClusterType: clusterType,
		ClusterUuid: clusterUUID1,
		Region:      region,
		Admin:       true,
		Version:     testdata.Version1146,
	}
	meshCluster = append(meshCluster, meshCluster1)
	return meshCluster
}

func buildEksMeshCluster() []serviceMeta.MeshCluster {
	meshCluster := make([]serviceMeta.MeshCluster, 0)
	meshCluster1 := serviceMeta.MeshCluster{
		ClusterName:          clusterName1,
		ClusterType:          clusterType,
		ClusterUuid:          clusterUUID1,
		Region:               region,
		Version:              testdata.Version1146,
		Admin:                true,
		IstioInstalledStatus: false,
	}
	meshCluster = append(meshCluster, meshCluster1)
	return meshCluster
}

func buildMockClusters() *[]meta.Cluster {
	clusters := make([]meta.Cluster, 0)
	clusters1 := meta.Cluster{
		InstanceUUID:          instanceUUID,
		ClusterUUID:           clusterUUID1,
		ClusterName:           clusterName1,
		Region:                region,
		AccountId:             accountId,
		IstioInstallNamespace: istioInstallNamespace,
	}
	clusters = append(clusters, clusters1)
	return &clusters
}

func TestGetAllCceClusterByRegion(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
	tests := []struct {
		name    string
		want    []serviceMeta.MeshCluster
		region  string
		eks     bool
		wantErr bool
	}{
		{
			region: region,
			name:   "GetAllCceClusterByRegion-test01",
			eks:    true,
			want:   buildEksMeshCluster(),
		},
		{
			region: region,
			name:   "GetAllCceClusterByRegion-test02",
			eks:    false,
			want:   buildMeshCluster(),
		},
	}
	mockCtx.Set(iam.ContextIAMUser, &sdkIAM.User{
		ID:   "aa",
		Name: "aa",
		Domain: sdkIAM.UserDomain{
			ID:   "123",
			Name: "aa",
		},
	})
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.eks {
				viper.Set(meta.EksAccountIds, []string{"123"})
			}
			service := &Service{
				opt:          NewOption(mockDB),
				clusterModel: mockClusterModel,
				cceService:   mockCceService,
			}

			mockClusterModel.EXPECT().GetAllClusterByRegion(mockCtx, gomock.Any()).Return(buildMockClusters(), nil)
			mockCceService.EXPECT().GetCCEClusterList(gomock.Any(), gomock.Any()).Return(buildMeshCluster(), nil)

			got, err := service.GetAllCceClusterByRegion(mockCtx, tt.region)
			if err != nil || !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllCceClusterByRegion(), got = %v, want %v", got, tt.want)
				return
			}
		})
	}
}

func buildServiceMetaDiscoverySelector() *serviceMeta.DiscoverySelector {
	return serviceMeta.NewDiscoverySelector(discoverySelectorEnabled, discoverySelectorLabelsMap)
}

func TestGetDiscoverySelector(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockDiscoverySelector := discoveryselectorMock.NewMockServiceInterface(ctrl)

	service := &Service{
		opt:               NewOption(mockDB),
		discoverySelector: mockDiscoverySelector,
	}
	ds := buildServiceMetaDiscoverySelector()

	mockDiscoverySelector.EXPECT().GetDiscoverySelector(gomock.Any(), gomock.Any()).Return(ds, nil)

	got, err := service.GetDiscoverySelector(mockCtx, instanceUUID)
	assert.Equal(t, nil, err)
	assert.Equal(t, ds, got)
}

func TestGetAllInstances(t *testing.T) {
	ctrl := gomock.NewController(t)
	m := mockInstance.NewMockServiceInterface(ctrl)
	m.EXPECT().GetInstancesByUser(gomock.Any()).Return(&[]meta.Instances{{
		InstanceUUID: "mesh-instance-id",
	}}, nil)

	service := &Service{
		instancesModel: m,
	}

	ins, err := service.GetAllInstances(mockCtx)
	if assert.NoError(t, err) {
		assert.Equal(t, ins[0].InstanceUUID, "mesh-instance-id")
	}

}

func TestCreateNamespace(t *testing.T) {
	fakeClient := kube.NewFakeClient()

	ctrl := gomock.NewController(t)
	ms := mockCceService.NewMockClientInterface(ctrl)
	ms.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)

	mi := mockInstance.NewMockServiceInterface(ctrl)
	mi.EXPECT().GetInstanceIstiodCluster(gomock.Any(), gomock.Any()).Return(&meta.Cluster{
		ClusterUUID: clusterUUID,
		Region:      region,
	}, meta.StandaloneMeshType, nil)

	service := &Service{
		instancesModel: mi,
		cceService:     ms,
	}
	assert.NoError(t, service.CreateNamespace(mockCtx, "mesh-instance-id", "ns1"))
}

func TestDeleteNamespace(t *testing.T) {
	fakeClient := kube.NewFakeClient()
	fakeClient.Kube().CoreV1().Namespaces().Create(context.TODO(), &v1.Namespace{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Name:   "ns1",
			Labels: map[string]string{"mesh-instance-id": "mesh-instance-id"},
		},
		Spec:   v1.NamespaceSpec{},
		Status: v1.NamespaceStatus{},
	}, metav1.CreateOptions{})

	ctrl := gomock.NewController(t)
	ms := mockCceService.NewMockClientInterface(ctrl)
	ms.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)

	mi := mockInstance.NewMockServiceInterface(ctrl)
	mi.EXPECT().GetInstanceIstiodCluster(gomock.Any(), gomock.Any()).Return(&meta.Cluster{
		ClusterUUID: clusterUUID,
		Region:      region,
	}, meta.StandaloneMeshType, nil)

	service := &Service{
		instancesModel: mi,
		cceService:     ms,
	}
	assert.NoError(t, service.DeleteNamespace(mockCtx, "mesh-instance-id", "ns1"))
}

func buildCreateMeshRequest() *serviceMeta.CreateMeshRequest {
	return &serviceMeta.CreateMeshRequest{
		Instances: &serviceMeta.Instances{
			Type:                    string(version.HostingVersionType),
			Region:                  region,
			ServiceMeshInstanceName: instanceName,
			IstioVersion:            testdata.Version1146,
			InstallationClusterId:   clusterID,
			InstallationClusterName: instanceName,
			AccountId:               accountId,
			InstancesUUID:           instanceUUID,
			DiscoverySelector:       nil,
			Monitor:                 nil,
			Scope:                   string(meta.InstanceManageNamespaceScope),
			NetworkType:             serviceMeta.NetworkType{},
			SecurityGroupId:         "xxx",
			ElasticPublicNetwork:    serviceMeta.ElasticPublicNetwork{},
			TraceInfo: &serviceMeta.TraceInfo{
				TraceEnabled: false,
			},
		},
		PaaSType: meta.PaaSTypeCCE,
		InstancesModel: &model_meta.Instances{
			IstioInstallNamespace: "istio-system-csm-123456",
			VpcNetworkId:          testVpcId,
			SubnetId:              testVpcId,
			Region:                region,
		},
		ClusterModel: &model_meta.Cluster{
			ClusterUUID: clusterUUID,
		},
	}
}

func buildDeleteMeshRequest() *serviceMeta.DeleteMeshRequest {
	return &serviceMeta.DeleteMeshRequest{
		InstanceUUID:             instanceUUID,
		InstanceType:             string(version.HostingVersionType),
		PaaSType:                 meta.PaaSTypeCCE,
		IsReleaseControlPlaneBlb: true,
		IsReleaseEip:             true,
	}
}

func mockRegion() {
	regions := map[string]interface{}{
		bj: map[string]interface{}{
			cloudHostingRegionClusterId:   bjId,
			cloudHostingRegionClusterName: bjName,
		},
	}
	viper.Set(cloudHostingRegion, regions)
}

func TestGetCluster(t *testing.T) {
	tests := []struct {
		name            string
		region          string
		wantClusterId   string
		wantClusterName string
	}{
		{
			name:            "success-bj",
			region:          "bj",
			wantClusterId:   bjId,
			wantClusterName: bjName,
		},
		{
			name:            "success-nothing",
			region:          "xxx",
			wantClusterId:   "",
			wantClusterName: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRegion()
			service := &Service{
				opt: NewOption(mockDB),
			}
			actualId, actualName := service.GetHostingCluster(mockCtx, tt.region)
			assert.Equal(t, actualId, tt.wantClusterId)
			assert.Equal(t, actualName, tt.wantClusterName)
			viper.Reset()
		})
	}
}

func InitVersion() {
	viper.Set(version.IstioHostingVersion, []string{testdata.Version1146})
	viper.Set(version.IopHostingHub, "baidu.test")

	viper.Set(version.IstioStandaloneVersion, []string{testdata.Version1146})
	viper.Set(version.IopStandaloneHub, "baidu.test")
}

func buildListSecurityGroupExpectData() *serviceMeta.SecurityGroupResult {
	return &serviceMeta.SecurityGroupResult{
		SecurityGroups: []serviceMeta.SecurityGroup{
			{
				Name: "default",
				ID:   vpcId,
				Desc: "default",
			},
		},
	}
}

func buildInstanceNetworkTypeData() *model_meta.InstanceNetworkType {
	return &model_meta.InstanceNetworkType{
		VpcNetworkId:   vpcId,
		VpcNetworkName: vpcName,
		VpcNetworkCidr: "*******",
		SubnetId:       "xxx",
		SubnetName:     "xxx",
		SubnetCidr:     "*******",
	}
}

func TestAddMeshInstance(t *testing.T) {
	ctrl := gomock.NewController(t)
	createMeshRequest := buildCreateMeshRequest()
	tests := []struct {
		name                      string
		initVersion               bool
		createMeshRequest         *serviceMeta.CreateMeshRequest
		callListSecurityGroup     bool
		callGetVPCAndSubnetDetail bool
		wantErr                   error
	}{
		{
			initVersion:               true,
			name:                      "addMeshInstance-success",
			createMeshRequest:         createMeshRequest,
			callListSecurityGroup:     true,
			callGetVPCAndSubnetDetail: true,
			wantErr:                   nil,
		},
		{
			initVersion:       false,
			name:              "addMeshInstance-hub-version-error",
			createMeshRequest: createMeshRequest,
			wantErr:           fmt.Errorf("the hub version of istio is nil"),
		},
		{
			initVersion:               true,
			callGetVPCAndSubnetDetail: true,
			callListSecurityGroup:     true,
			name:                      "addMeshInstance-assume-role-failed",
			createMeshRequest:         createMeshRequest,
			wantErr:                   fmt.Errorf(string(meta.ClusterNotFound)),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.initVersion {
				InitVersion()
				testdata.MockHostingAKSK()
			}
			versionService := version.NewVersionService(version.NewOption())
			namespaceService := mockNamespaceService.NewMockServiceInterface(ctrl)
			service := &Service{
				opt:              NewOption(mockDB),
				versionService:   versionService,
				namespaceService: namespaceService,
			}
			namespaceService.EXPECT().CreateSNICWithNewCsmInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
				gomock.Any()).AnyTimes().Return("", nil)
			mockVpcService := mockVpcService.NewMockServiceInterface(ctrl)
			service.VpcService = mockVpcService

			if tt.callListSecurityGroup {
				mockVpcService.EXPECT().ListSecurityGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(buildListSecurityGroupExpectData(), nil)
			}
			if tt.callGetVPCAndSubnetDetail {
				mockVpcService.EXPECT().GetVPCAndSubnetDetail(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(buildInstanceNetworkTypeData(), nil)
			}

			err := service.AddMeshInstance(mockCtx, createMeshRequest)
			if tt.wantErr == nil {
				assert.Error(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
			if tt.initVersion {
				viper.Reset()
			}
		})
	}
}

// TestDeleteServiceMeshInstance 测试函数DeleteServiceMeshInstance，用于删除服务网格实例。
// 该函数接收一个*testing.T类型的参数t，并返回一个error类型的结果。
// 如果初始化版本信息，则使用InitVersion进行初始化。
// 测试包括以下几种情况：
// 1. 成功删除服务网格实例，返回nil错误；
// 2. 失败删除服务网格实例，返回非nil错误。
func TestDeleteServiceMeshInstance(t *testing.T) {
	ctrl := gomock.NewController(t)
	deleteMeshRequest := buildDeleteMeshRequest()
	tests := []struct {
		name              string
		initVersion       bool
		deleteMeshRequest *serviceMeta.DeleteMeshRequest
		laneGroupRes      *meta.LaneGroupParamsResponse
		csmInstance       *meta.Instances
		clusterType       string
		wantErr           error
	}{
		{
			initVersion:       true,
			name:              "success",
			deleteMeshRequest: deleteMeshRequest,
			laneGroupRes: &meta.LaneGroupParamsResponse{
				Result: []meta.LaneGroupParams{{InstanceUUID: instanceUUID}},
			},
			csmInstance: buildInstanceInfo(),
			clusterType: "",
			wantErr:     nil,
		},
		{
			name:              "delete monitor",
			initVersion:       true,
			clusterType:       "hosting",
			csmInstance:       buildHostingInstanceInfo(),
			deleteMeshRequest: deleteMeshRequest,
			laneGroupRes: &meta.LaneGroupParamsResponse{
				Result: []meta.LaneGroupParams{{InstanceUUID: instanceUUID}},
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.initVersion {
				InitVersion()
				testdata.MockHostingAKSK()
			}
			versionService := version.NewVersionService(version.NewOption())
			instanceModel := mockInstance.NewMockServiceInterface(ctrl)
			clusterModel := mockCluster.NewMockServiceInterface(ctrl)
			laneService := mockLaneService.NewMockServiceInterface(ctrl)
			blsService := mockBlsService.NewMockServiceInterface(ctrl)
			monitorService := mockMonitor.NewMockServiceInterface(ctrl)
			service := &Service{
				opt:            NewOption(mockDB),
				versionService: versionService,
				instancesModel: instanceModel,
				clusterModel:   clusterModel,
				laneService:    laneService,
				blsService:     blsService,
				monitorModel:   monitorService,
			}
			instanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).AnyTimes().Return(tt.csmInstance, nil)
			instanceModel.EXPECT().DeleteInstanceByInstanceUUID(gomock.Any(), gomock.Any()).AnyTimes().Return(nil)
			instanceModel.EXPECT().GetInstanceIstiodCluster(gomock.Any(), gomock.Any()).AnyTimes().Return(buildCluster(), meta.MeshType("hosting"), nil)

			clusterModel.EXPECT().GetAllClusterByInstanceUUID(gomock.Any(), gomock.Any()).AnyTimes().Return(buildClusterList(tt.clusterType), nil)
			clusterModel.EXPECT().DeleteClusterByInstanceUUID(gomock.Any(), gomock.Any()).AnyTimes().Return(nil)
			monitorService.EXPECT().DeleteScrapeJob(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			blsService.EXPECT().BlsClose(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil, nil)

			mockVpcService := mockVpcService.NewMockServiceInterface(ctrl)

			mockVpcService.EXPECT().DeleteEndpointWithEIPByCsmInstanceID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil)
			service.VpcService = mockVpcService

			laneService.EXPECT().DeleteAllLaneGroupByInstanceUUID(gomock.Any(), gomock.Any()).AnyTimes().Return(nil)

			err := service.DeleteServiceMeshInstance(mockCtx, tt.deleteMeshRequest)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
			if tt.initVersion {
				viper.Reset()
			}
		})
	}
}

func buildDeleteMeshInstance() *serviceMeta.DeleteMeshInstance {
	return &serviceMeta.DeleteMeshInstance{
		PaaSType:                 meta.PaaSTypeCCE,
		IsReleaseControlPlaneBlb: false,
		IsReleaseEip:             false,
		InstanceModel: &meta.Instances{
			VpcNetworkId:          vpcId,
			InstanceUUID:          instanceUUID,
			InstanceName:          instanceName,
			InstanceType:          string(version.HostingVersionType),
			IstioVersion:          testdata.Version1146,
			Region:                region,
			IstioInstallNamespace: istioInstallNamespace,
		},
		ClusterModel: &meta.Cluster{
			InstanceUUID:          instanceUUID,
			ClusterUUID:           clusterUUID,
			ClusterName:           clusterName,
			IstioInstallNamespace: istioInstallNamespace,
			ClusterType:           string(meta.ClusterTypeExternal),
			Region:                region,
		},
	}
}

func TestRemoveMeshInstance(t *testing.T) {
	deleteMeshInstance := buildDeleteMeshInstance()
	tests := []struct {
		name              string
		initVersion       bool
		deleteMeshCluster *serviceMeta.DeleteMeshInstance
		wantErr           error
	}{
		{
			initVersion:       true,
			name:              "removeMeshInstance-user-not-found",
			deleteMeshCluster: deleteMeshInstance,
			wantErr:           fmt.Errorf(string(meta.ClusterNotFound)),
		},
		{
			initVersion:       false,
			name:              "removeMeshInstance-hub-error",
			deleteMeshCluster: deleteMeshInstance,
			wantErr:           fmt.Errorf("the hub version of istio is nil"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.initVersion {
				InitVersion()
				testdata.MockHostingAKSK()
			}
			versionService := version.NewVersionService(version.NewOption())
			service := &Service{
				opt:            NewOption(mockDB),
				versionService: versionService,
			}
			err := service.RemoveMeshInstance(mockCtx, deleteMeshInstance)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
			if tt.initVersion {
				viper.Reset()
			}
		})
	}
}

func TestUnInstallMeshCluster(t *testing.T) {
	instance := buildInstanceInfo()
	cluster := buildCluster()
	tests := []struct {
		name     string
		instance *meta.Instances
		cluster  *meta.Cluster
		wantErr  error
	}{
		{
			name:     "UnInstallMeshCluster-success",
			instance: instance,
			cluster:  cluster,
		},
	}
	mockCtx.Set(iam.ContextIAMUser, &sdkIAM.User{
		ID:   "aa",
		Name: "aa",
		Domain: sdkIAM.UserDomain{
			ID:   "123",
			Name: "aa",
		},
	})
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			fakeClient := kube.NewFakeClient()
			cceService := mockCceService.NewMockClientInterface(ctrl)
			cceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()
			multiProtocolService := multiProtocolServiceMock.NewMockServiceInterface(ctrl)
			multiProtocolService.EXPECT().ParseAerakiTmpl(gomock.Any(), gomock.Any()).Return(nil, nil)
			service := &Service{
				opt:                  NewOption(mockDB),
				cceService:           cceService,
				MultiProtocolService: multiProtocolService,
			}
			err := service.UnInstallMeshCluster(mockCtx, tt.instance, tt.cluster)
			assert.NotNil(t, err)
		})
	}
}

func buildCreateRemoteUserMeshCluster() *serviceMeta.CreateRemoteUserMeshCluster {
	return &serviceMeta.CreateRemoteUserMeshCluster{
		Instances: &serviceMeta.Instances{
			Type:                    string(version.HostingVersionType),
			Region:                  region,
			ServiceMeshInstanceName: instanceName,
			IstioVersion:            testdata.Version1146,
			InstallationClusterId:   clusterID,
			InstallationClusterName: instanceName,
			AccountId:               accountId,
			InstancesUUID:           instanceUUID,
			Scope:                   string(meta.InstanceManageNamespaceScope),
			NetworkType:             serviceMeta.NetworkType{},
			SecurityGroupId:         "xxx",
			ElasticPublicNetwork:    serviceMeta.ElasticPublicNetwork{},
		},
		PaaSType: meta.PaaSTypeCCE,
		InstancesModel: &model_meta.Instances{
			VpcNetworkId:          vpcId,
			IstioInstallNamespace: "istio-system-csm-123456",
			IstioVersion:          testdata.Version1146,
			Region:                region,
			InstanceUUID:          instanceUUID,
		},
		ClusterModel: &model_meta.Cluster{
			InstanceUUID: instanceUUID,
			ClusterUUID:  clusterUUID,
			ClusterName:  clusterName,
			ClusterType:  string(meta.ClusterTypeRemote),
			Region:       region,
			AccountId:    accountId,
		},
	}
}

func TestAddRemoteUserMeshCluster(t *testing.T) {
	createRemoteUserMeshCluster := buildCreateRemoteUserMeshCluster()
	tests := []struct {
		createRequest *serviceMeta.CreateRemoteUserMeshCluster
		name          string
		wantErr       error
	}{
		{
			name:          "AddRemoteUserMeshCluster-credential-failed",
			createRequest: createRemoteUserMeshCluster,
			wantErr:       fmt.Errorf("failed"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{
				opt: NewOption(mockDB),
			}
			err := service.AddRemoteUserMeshCluster(mockCtx, tt.createRequest)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
		})
	}
}

func buildDeleteRemoteUserMeshCluster() *serviceMeta.DeleteRemoteUserMeshCluster {
	return &serviceMeta.DeleteRemoteUserMeshCluster{
		Instances: &serviceMeta.Instances{
			Type:                    string(version.HostingVersionType),
			Region:                  region,
			ServiceMeshInstanceName: instanceName,
			IstioVersion:            testdata.Version1146,
			InstallationClusterId:   clusterID,
			InstallationClusterName: instanceName,
			AccountId:               accountId,
			InstancesUUID:           instanceUUID,
			Scope:                   string(meta.InstanceManageNamespaceScope),
			NetworkType:             serviceMeta.NetworkType{},
			SecurityGroupId:         "xxx",
			ElasticPublicNetwork:    serviceMeta.ElasticPublicNetwork{},
		},
		PaaSType: meta.PaaSTypeCCE,
		InstancesModel: &model_meta.Instances{
			VpcNetworkId:          vpcId,
			IstioInstallNamespace: "istio-system-csm-123456",
			IstioVersion:          testdata.Version1146,
			Region:                region,
			InstanceUUID:          instanceUUID,
		},
		ClusterModel: &model_meta.Cluster{
			InstanceUUID: instanceUUID,
			ClusterUUID:  clusterUUID,
			ClusterName:  clusterName,
			ClusterType:  string(meta.ClusterTypeRemote),
			Region:       region,
			AccountId:    accountId,
		},
	}
}

func TestRemoveRemoteUserMeshCluster(t *testing.T) {
	deleteRequest := buildDeleteRemoteUserMeshCluster()
	tests := []struct {
		deleteRequest *serviceMeta.DeleteRemoteUserMeshCluster
		name          string
		wantErr       error
	}{
		{
			name:          "RemoveRemoteUserMeshCluster-credential-failed",
			deleteRequest: deleteRequest,
			wantErr:       fmt.Errorf("failed"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{
				opt: NewOption(mockDB),
			}
			err := service.RemoveRemoteUserMeshCluster(mockCtx, tt.deleteRequest)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
		})
	}
}

func TestGetInstanceStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	tests := []struct {
		name          string
		clusters      *[]meta.Cluster
		instance      *meta.Instances
		clusterID     string
		clusterRegion string
		wantErr       error
	}{
		{
			name:          "get all success",
			clusters:      buildMockClusters(),
			instance:      buildInstanceInfo(),
			clusterID:     "",
			clusterRegion: "",
		},
		{
			name:          "get special cluster ",
			clusters:      buildMockClusters(),
			instance:      buildInstanceInfo(),
			clusterID:     clusterUUID1,
			clusterRegion: region,
		},
		{
			name:          "get primary cluster ",
			clusters:      buildMockClusters(),
			instance:      buildInstanceInfo(),
			clusterID:     clusterUUID1,
			clusterRegion: "xx",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
			mockClusterModel.EXPECT().GetAllClusterByInstanceUUID(gomock.Any(), gomock.Any()).Return(tt.clusters, nil).AnyTimes()

			mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).Return(tt.instance, nil).AnyTimes()

			fakeClient := kube.NewFakeClient()
			istioLabelMap := map[string]string{
				"app": "istiod",
			}
			pod := &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Labels:    istioLabelMap,
					Name:      "test-istio",
					Namespace: "istio-system-test",
				},
				Status: v1.PodStatus{
					Reason: "evicted",
				},
			}
			_, _ = fakeClient.Kube().CoreV1().Pods("istio-system-test").Create(context.TODO(), pod, metav1.CreateOptions{})
			ms := mockCceService.NewMockClientInterface(ctrl)
			ms.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			service := &Service{
				opt:            NewOption(mockDB),
				clusterModel:   mockClusterModel,
				instancesModel: mockInstanceModel,
				cceService:     ms,
			}

			_, err := service.GetInstanceStatus(mockCtx, instanceUUID, tt.clusterID, tt.clusterRegion)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
		})
	}
}

func TestService_GetActiveSidecarNum(t *testing.T) {
	ctrl := gomock.NewController(t)

	tests := []struct {
		name    string
		cluster *meta.Cluster
		want    int
		wantErr bool
	}{
		{
			name:    "getActiveSidecarNum-test",
			cluster: buildCluster(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
			ms := mockCceService.NewMockClientInterface(ctrl)

			service := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstanceModel,
				cceService:     ms,
			}

			got, _ := service.GetActiveSidecarNum(mockCtx, instanceUUID, tt.cluster, meta.MeshType(instanceType))
			if got != tt.want {
				t.Errorf("GetActiveSidecarNum() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetSugarSidecarNum(t *testing.T) {
	ctrl := gomock.NewController(t)

	tests := []struct {
		name    string
		cluster *meta.Cluster
		want    int
		err     error
	}{
		{
			name:    "getSugarSidecarNum-test",
			cluster: buildCluster(),
			want:    0,
			err:     errors.Wrap(errors.New("iam client profile not found"), "assume role failed"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
			ms := mockCceService.NewMockClientInterface(ctrl)

			service := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstanceModel,
				cceService:     ms,
			}

			got, _ := service.GetSugarSidecarNum(mockCtx, instanceUUID, tt.cluster, meta.MeshType(instanceType))
			if got != tt.want {
				t.Errorf("GetSugarSidecarNum() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetIstioSupportK8sVersion(t *testing.T) {
	tests := []struct {
		name       string
		wantResult []versionUtil.IstioSupportK8sVersion
		wantErr    bool
	}{
		{
			name: "test-getIstioSupportK8sVersion",
			wantResult: []versionUtil.IstioSupportK8sVersion{
				{
					IstioVersion: constants.IstioVersion13,
					SupportedClusterVersionList: []string{
						constants.K8sVersion20,
						constants.K8sVersion22,
					},
				},
				{
					IstioVersion: constants.IstioVersion14,
					SupportedClusterVersionList: []string{
						constants.K8sVersion22,
						constants.K8sVersion24,
					},
				},
				{
					IstioVersion: constants.IstioVersion16,
					SupportedClusterVersionList: []string{
						constants.K8sVersion22,
						constants.K8sVersion24,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt: NewOption(mockDB),
			}
			gotResult := s.GetIstioSupportK8sVersion(mockCtx)
			if !reflect.DeepEqual(gotResult, tt.wantResult) {
				t.Errorf("GetVersionPairingList() gotResult = %v, want %v", gotResult, tt.wantResult)
			}
		})
	}
}
