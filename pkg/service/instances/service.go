package instances

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"math"
	"os"
	"path"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/viper"
	v1 "k8s.io/api/core/v1"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/remotecommand"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	certModel "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cert"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/monitor"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
	blsv3 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/bls"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cert"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/deploy"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/deploy/hosting"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/discoveryselector"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/lane"
	serviceMeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	monitorService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/monitor"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/multiprotocol"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/namespace"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/vpc"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil/rollback"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/sliceutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/uuid"
	versionUtil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

type Service struct {
	opt *Option

	instancesModel       instances.ServiceInterface
	clusterModel         cluster.ServiceInterface
	discoverySelector    discoveryselector.ServiceInterface
	cceService           cce.ClientInterface
	monitorModel         monitor.ServiceInterface
	monitorService       monitorService.ServiceInterface
	certModel            certModel.ServiceInterface
	versionService       version.ServiceInterface
	VpcService           vpc.ServiceInterface
	MultiProtocolService multiprotocol.ServiceInterface
	laneService          lane.ServiceInterface
	blsService           blsv3.ServiceInterface
	namespaceService     namespace.ServiceInterface
}

// NewInstanceService 新建一个实例服务，包含所有需要的模型和服务。
//
// 参数：
//
//	option *Option     可选参数，包含数据库连接、配置等信息，如果为nil则使用默认值。
//
// 返回值：
//
//	*Service          返回一个实例服务指针，包含所有需要的模型和服务。
func NewInstanceService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt:                  option,
		instancesModel:       instances.NewInstancesService(instances.NewOption(gormDB)),
		clusterModel:         cluster.NewClusterService(cluster.NewOption(gormDB)),
		discoverySelector:    discoveryselector.NewDiscoverySelectorService(discoveryselector.NewOption(gormDB)),
		cceService:           cce.NewClientService(),
		monitorModel:         monitor.NewService(monitor.NewOption()),
		certModel:            certModel.NewCertService(certModel.NewOption(gormDB)),
		versionService:       version.NewVersionService(version.NewOption()),
		VpcService:           vpc.NewVPCService(),
		MultiProtocolService: multiprotocol.NewService(),
		laneService:          lane.NewService(lane.NewOption(gormDB)),
		blsService:           blsv3.NewBlsService(blsv3.NewOption(gormDB)),
		monitorService:       monitorService.NewMonitorService(monitorService.NewOption(gormDB)),
		namespaceService:     namespace.NewNamespaceService(namespace.NewOption(gormDB)),
	}
}

// GetAllCceClusterByRegion 获取某个地域所有的 cce 集群
func (s *Service) GetAllCceClusterByRegion(ctx csmContext.CsmContext, region string) ([]serviceMeta.MeshCluster, error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, err
	}

	// api 获取 cce 集群
	meshCluster, err := s.cceService.GetCCEClusterList(ctx, region)
	if err != nil {
		return nil, err
	}

	// 获取数据库中的 cce 集群
	cluster, err := s.clusterModel.GetAllClusterByRegion(ctx, region)
	if err != nil {
		return nil, err
	}
	clusterMap := make(map[string]struct{})
	for _, c := range *cluster {
		clusterMap[c.ClusterUUID] = struct{}{}
	}
	var clusters []serviceMeta.MeshCluster
	for _, value := range meshCluster {
		// todo api 获取 cce 权限接口
		value.Admin = true
		// 在数据库存在表示 istio 已经安装
		if _, ok := clusterMap[value.ClusterUuid]; ok {
			value.IstioInstalledStatus = true
		}
		// todo 对于 eks 集群能够安装多个网格实例,需要放开该限制
		if sliceutil.StringContains(s.opt.EksAccountIds, accountId) {
			value.IstioInstalledStatus = false
		}
		clusters = append(clusters, value)
	}

	return clusters, nil
}

// RemoveMeshInstance 在托管集群下删除服务网格实例
// 2024.07.30逻辑：目前仅有config集群和external集群的托管网格实例会执行到此
func (s *Service) RemoveMeshInstance(ctx csmContext.CsmContext, deleteMeshInstance *serviceMeta.DeleteMeshInstance) error {
	version := deleteMeshInstance.InstanceModel.IstioVersion
	instanceRegion := deleteMeshInstance.InstanceModel.Region
	region := deleteMeshInstance.ClusterModel.Region
	clusterId := deleteMeshInstance.ClusterModel.ClusterUUID
	clusterName := deleteMeshInstance.ClusterModel.ClusterName
	instanceId := deleteMeshInstance.ClusterModel.InstanceUUID
	namespace := deleteMeshInstance.ClusterModel.IstioInstallNamespace
	clusterType := deleteMeshInstance.ClusterModel.ClusterType

	ctx.CsmLogger().Infof("RemoveMeshCluster info version=%s,region=%s,clusterId=%s,clusterName=%s,"+
		"meshInstanceId=%s,namespace=%s,clusterType=%s,instancesRegion=%s", version, region, clusterId, clusterName,
		instanceId, namespace, clusterType, instanceRegion)

	hubType := deleteMeshInstance.InstanceModel.InstanceType
	hub := s.versionService.GetHubWithVersionType(ctx, hubType)
	if len(hub) == 0 {
		ctx.CsmLogger().Errorf("get hub with version type error")
		return fmt.Errorf("get the hub version of istio is nil")
	}

	vpcID := deleteMeshInstance.InstanceModel.VpcNetworkId

	iopTemplates := &hosting.IopTemplates{
		VpcEniTemplates: &hosting.VpcEniTemplates{
			VpcID: vpcID,
			EniAnnotation: &hosting.EniAnnotation{
				EniRequests: &hosting.EniRequests{
					Eni: &hosting.Eni{
						Num: "1",
					},
				},
				EniLimits: &hosting.EniLimits{
					Eni: &hosting.Eni{
						Num: "1",
					},
				},
			},
		},
		PaasType: deleteMeshInstance.PaaSType,
		FrontTemplates: &hosting.FrontTemplates{
			Namespace:      namespace,
			Hub:            hub,
			Tag:            version,
			InstanceRegion: instanceRegion,
			InstanceId:     instanceId,
			ClusterRegion:  region,
			ClusterName:    clusterName,
			CceClusterUuid: clusterId,
		},
	}

	// 判断网格实例对应的集群是否为用户集群 TODO 2024。07.30逻辑，不会进入当前if语句中
	if strings.EqualFold(clusterType, string(meta.ClusterTypeRemote)) {
		istiodCluster, err := s.clusterModel.GetIstiodCluster(ctx, instanceId, clusterType)
		if err != nil {
			return err
		}
		iopTemplates.InstanceCceClusterUuid = istiodCluster.ClusterUUID
	}
	// 兼容config集群
	if strings.EqualFold(clusterType, string(meta.ClusterTypeConfig)) {
		// TODO
		return nil
	}

	deployerService := hosting.NewDeployerService(ctx, hosting.NewOption(s.opt.DB.DB))
	err := deployerService.UnInstallInstanceMesh(ctx, iopTemplates)
	if err != nil {
		ctx.CsmLogger().Errorf("UnInstallInstanceMesh error %v", err)
	}
	return err
}

// DeleteServiceMeshInstance 删除服务网格实例与关联的集群实例，卸载 istio 集群
func (s *Service) DeleteServiceMeshInstance(ctx csmContext.CsmContext, deleteMeshRequest *serviceMeta.DeleteMeshRequest) (err error) {
	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	instanceUUID := deleteMeshRequest.InstanceUUID
	instanceInfo, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return err
	}

	istioCluster, _, err := s.instancesModel.GetInstanceIstiodCluster(ctx, instanceUUID)
	if err != nil {
		return err
	}

	clusterInfo, err := s.clusterModel.GetAllClusterByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return err
	}

	instanceType := instanceInfo.InstanceType
	for _, c := range *clusterInfo {
		localCluster := c
		if instanceType == string(version.HostingVersionType) {
			if strings.EqualFold(localCluster.ClusterType, string(meta.ClusterTypeConfig)) {
				deleteRemoteUserMeshCluster := serviceMeta.ToDeleteRemoteUserMeshCluster(deleteMeshRequest.PaaSType, instanceInfo, &localCluster)
				deleteRemoteUserMeshCluster.InstallationClusterName = istioCluster.ClusterName
				deleteRemoteUserMeshCluster.InstallationClusterId = istioCluster.ClusterUUID
				go func(ctx csmContext.CsmContext) {
					removeErr := s.RemoveRemoteConfigMeshCluster(ctx, deleteRemoteUserMeshCluster)
					if removeErr != nil {
						ctx.CsmLogger().Errorf(fmt.Sprintf("RemoveRemoteConfigMeshCluster failed, err is %s", removeErr.Error()))
					}
				}(ctx)
			} else {
				deleteMeshInstance := serviceMeta.ToDeleteMeshInstance(deleteMeshRequest.PaaSType, instanceInfo, &localCluster)
				deleteMeshInstance.IsReleaseControlPlaneBlb = deleteMeshRequest.IsReleaseControlPlaneBlb
				deleteMeshInstance.IsReleaseEip = deleteMeshRequest.IsReleaseEip
				go func(ctx csmContext.CsmContext, deleteMeshInstance *serviceMeta.DeleteMeshInstance) {
					removeErr := s.RemoveMeshInstance(ctx, deleteMeshInstance)
					if removeErr != nil {
						ctx.CsmLogger().Errorf(fmt.Sprintf("RemoveMeshInstance failed, err is %s", removeErr.Error()))
					}
				}(ctx, deleteMeshInstance)
			}
		} else {
			go s.UnInstallMeshCluster(ctx, instanceInfo, &localCluster)
		}

		// 清理监控采集 job
		if len(c.MonitorJobIds) == 0 {
			continue
		}
		if len(c.MonitorInstanceId) == 0 || len(c.MonitorRegion) == 0 {
			continue
		}
		if len(c.MonitorInstanceId) != 0 && strings.EqualFold(c.Region, c.MonitorRegion) {
			// 兼容托管网格控制面监控删除
			if instanceType == string(version.HostingVersionType) {
				err = s.monitorModel.DeleteScrapeJob(ctx, c.Region, c.MonitorJobIds, c.MonitorInstanceId, c.MonitorAgentID)
				if err != nil {
					ctx.CsmLogger().Warnf("DeleteServiceMeshInstance DeleteScrapeJob error %v", err)
				}
				continue
			}
			agent, err := s.monitorModel.GetCPromAgent(ctx, c.MonitorInstanceId, c.Region, c.ClusterUUID)
			if err != nil {
				ctx.CsmLogger().Warnf("DeleteServiceMeshInstance GetCPromAgent error %v", err)
				continue
			}

			for _, jobId := range strings.Split(c.MonitorJobIds, ",") {
				err = s.monitorModel.DeleteScrapeJob(ctx, c.Region, jobId, c.MonitorInstanceId, agent.AgentID)
				if err != nil {
					ctx.CsmLogger().Warnf("DeleteServiceMeshInstance DeleteScrapeJob error %v", err)
					continue
				}
			}
		}
	}

	// 删除所有BLS实例
	region := instanceInfo.Region
	_, blsErr := s.blsService.BlsClose(ctx, region, instanceUUID)
	if blsErr != nil { //表示数据库有内容未删除成功
		return blsErr
	}

	// 删除所有泳道组
	lgErr := s.laneService.DeleteAllLaneGroupByInstanceUUID(ctx, instanceUUID)
	if lgErr != nil {
		return lgErr
	}

	// 托管网格删除服务网卡 and eip
	if instanceType == string(version.HostingVersionType) {
		err = s.VpcService.DeleteEndpointWithEIPByCsmInstanceID(ctx, region, instanceInfo)
		if err != nil {
			return err
		}
	}

	// 删除数据库服务网格实例
	err = s.instancesModel.DeleteInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return err
	}
	// 删除数据库服务网格实例关联的集群实例
	err = s.clusterModel.DeleteClusterByInstanceUUID(ctx, instanceUUID)
	// 兼容独立网格异常脏数据场景：主集群已经被移出，但网格实例依旧存在
	if err != nil && !strings.Contains(err.Error(), "record not found") {
		return err
	}
	ctx.CsmLogger().Infof("remove service mesh instance successful, instanceUUID=%s", instanceUUID)

	tx.Commit()
	return nil
}

func (s *Service) UnInstallMeshCluster(ctx csmContext.CsmContext, instances *meta.Instances,
	clusters *meta.Cluster) (err error) {
	version := instances.IstioVersion
	region := clusters.Region
	clusterId := clusters.ClusterUUID
	clusterName := clusters.ClusterName
	meshInstanceId := clusters.InstanceUUID
	namespace := clusters.IstioInstallNamespace

	ctx.CsmLogger().Infof("UnInstallMeshCluster info version=%s,region=%s,clusterId=%s,clusterName=%s,meshInstanceId=%s,namespace=%s",
		version, region, clusterId, clusterName, meshInstanceId, namespace)
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterId, meta.MeshType(instances.InstanceType))
	if err != nil {
		// 兼容独立网格异常脏数据场景：主集群已经被移出，但网格实例依旧存在
		// 这种场景下，主集群不存在，不需要删除其中的crd，直接返回即可
		if strings.Contains(err.Error(), "ClusterNotFound") {
			return nil
		}
		ctx.CsmLogger().Errorf("NewClient error %v", err)
		return err
	}
	deployParams := deploy.NewParams(version, namespace, region, meshInstanceId, clusterName, clusterId)
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("Getwd error %v", err)
		return err
	}
	deployParams.ConfigPath = path.Join(pwd, constants.Templates)

	deploy := deploy.NewDeploy(deployParams, k8sClient)

	// 判断是否为从集群
	if strings.EqualFold(clusters.ClusterType, string(meta.ClusterTypeRemote)) {
		deployParams.IsRemote = true
		istiodCluster, meshType, err := s.instancesModel.GetInstanceIstiodCluster(ctx, clusters.InstanceUUID)
		if err != nil {
			return err
		}
		primaryK8sClient, err := s.cceService.NewClient(ctx, istiodCluster.Region, istiodCluster.ClusterUUID, meshType)
		if err != nil {
			ctx.CsmLogger().Errorf("cce primary NewClient error %v", err)
			return err
		}
		deploy.PrimaryClient = primaryK8sClient
	}
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}
	eks := false
	if sliceutil.StringContains(s.opt.EksAccountIds, accountId) {
		eks = true
	}

	multiProtocolEnabled := *instances.MultiProtocolEnabled
	// 卸载第三方私有协议组件
	if multiProtocolEnabled {
		istiodAddr := fmt.Sprintf(multiprotocol.DefaultIstiodAddr, namespace)
		xdsAddr := fmt.Sprintf(multiprotocol.DefaultXdsAddr, namespace)
		xdsPort := multiprotocol.DefaultXdsPort
		opt := multiprotocol.NewOptionWith(namespace, multiprotocol.GetAerakiImageURL(version),
			multiprotocol.GetAerakiImagePullPolicy(), istiodAddr, xdsAddr, xdsPort, true, true)
		err = s.AerakiComponent(ctx, k8sClient, false, opt)
		if err != nil {
			ctx.CsmLogger().Errorf("InstallAerakiComponent error %v", err)
		}
	}

	// uninstall istio
	if eks {
		err = deploy.UnDeployIstio(ctx, true)
	} else {
		err = deploy.UnDeployIstio(ctx, false)
	}
	if err != nil {
		ctx.CsmLogger().Errorf("uninstall istio error %v", err)
		return err
	}
	ctx.CsmLogger().Infof("uninstall istio success")

	return nil
}

func (s *Service) GetHostingCluster(ctx csmContext.CsmContext, region string) (string, string) {
	return s.opt.GetCluster(region)
}

// AddHostingServiceMeshInstance 创建托管服务网格
func (s *Service) AddHostingServiceMeshInstance(ctx csmContext.CsmContext, createMeshRequest *serviceMeta.CreateMeshRequest) (err error) {
	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	instancesService := s.instancesModel.WithTx(dbutil.NewDB(tx))
	if err = instancesService.NewInstance(ctx, createMeshRequest.InstancesModel); err != nil {
		return err
	}

	clusterService := s.clusterModel.WithTx(dbutil.NewDB(tx))
	if err = clusterService.NewCluster(ctx, createMeshRequest.ClusterModel); err != nil {
		return err
	}

	// todo create mesh instance in k8s cluster, we can add timeout for InstallIstio goroutine
	go s.AddMeshInstance(ctx, createMeshRequest)

	tx.Commit()
	//go s.namespace.CreateSNICWithNewCsmInstance(ctx, createMeshRequest.ClusterModel.ClusterUUID,
	//	createMeshRequest.InstancesModel.Region, constants.KubeConfigTypeVPC, createMeshRequest.InstancesModel)
	return nil
}

func (s *Service) enableDiscoverySelectorOrNot(discoverySelector *serviceMeta.DiscoverySelector) map[string]string {
	if discoverySelector == nil {
		return nil
	}
	if discoverySelector.Enabled {
		return discoverySelector.MatchLabels
	}
	return nil
}

// RemoveRemoteUserMeshCluster 在托管实例下，删除用户集群 istio 组件
func (s *Service) RemoveRemoteUserMeshCluster(ctx csmContext.CsmContext, deleteRemoteUserMeshCluster *serviceMeta.DeleteRemoteUserMeshCluster) error {
	// 实例信息
	version := deleteRemoteUserMeshCluster.InstancesModel.IstioVersion
	instanceRegion := deleteRemoteUserMeshCluster.InstancesModel.Region
	instanceId := deleteRemoteUserMeshCluster.InstancesModel.InstanceUUID
	namespace := deleteRemoteUserMeshCluster.InstancesModel.IstioInstallNamespace
	installationClusterId := deleteRemoteUserMeshCluster.InstallationClusterId

	// 集群信息
	region := deleteRemoteUserMeshCluster.ClusterModel.Region
	clusterId := deleteRemoteUserMeshCluster.ClusterModel.ClusterUUID
	clusterName := deleteRemoteUserMeshCluster.ClusterModel.ClusterName
	discoverySelectorLabel := s.enableDiscoverySelectorOrNot(deleteRemoteUserMeshCluster.DiscoverySelector)

	ctx.CsmLogger().Infof("RemoveRemoteUserMeshCluster version=%s,instanceRegion=%s,instanceId=%s,"+
		"namespace=%s,region=%s,clusterId=%s,clusterName=%s,discoverySelectorLabel=%q",
		version, instanceRegion, instanceId, namespace, region, clusterId, clusterName, discoverySelectorLabel)

	vpcID := deleteRemoteUserMeshCluster.InstancesModel.VpcNetworkId

	remoteIopTemplates := &hosting.IopTemplates{
		VpcEniTemplates: &hosting.VpcEniTemplates{
			VpcID: vpcID,
		},
		PaasType: deleteRemoteUserMeshCluster.PaaSType,
		FrontTemplates: &hosting.FrontTemplates{
			Namespace:               namespace,
			Tag:                     version,
			InstanceId:              instanceId,
			InstanceRegion:          instanceRegion,
			InstanceCceClusterUuid:  installationClusterId,
			ClusterRegion:           region,
			ClusterName:             clusterName,
			CceClusterUuid:          clusterId,
			DiscoverySelectorLabels: discoverySelectorLabel,
		},
		AccountId: deleteRemoteUserMeshCluster.AccountId,
	}
	deployerService := hosting.NewDeployerService(ctx, hosting.NewOption(s.opt.DB.DB))
	err := deployerService.UnInstallRemoteUserMesh(ctx, remoteIopTemplates)
	if err != nil {
		ctx.CsmLogger().Errorf("UnInstallRemoteUserMesh error %v", err)
		return err
	}
	return nil
}

// RemoveRemoteConfigMeshCluster 在托管实例下，删除用户config集群 istio 组件
func (s *Service) RemoveRemoteConfigMeshCluster(ctx csmContext.CsmContext, deleteRemoteUserMeshCluster *serviceMeta.DeleteRemoteUserMeshCluster) error {
	// 实例信息
	version := deleteRemoteUserMeshCluster.InstancesModel.IstioVersion
	instanceRegion := deleteRemoteUserMeshCluster.InstancesModel.Region
	instanceId := deleteRemoteUserMeshCluster.InstancesModel.InstanceUUID
	namespace := deleteRemoteUserMeshCluster.InstancesModel.IstioInstallNamespace
	installationClusterId := deleteRemoteUserMeshCluster.InstallationClusterId

	// 集群信息
	region := deleteRemoteUserMeshCluster.ClusterModel.Region
	clusterId := deleteRemoteUserMeshCluster.ClusterModel.ClusterUUID
	clusterName := deleteRemoteUserMeshCluster.ClusterModel.ClusterName
	discoverySelectorLabel := s.enableDiscoverySelectorOrNot(deleteRemoteUserMeshCluster.DiscoverySelector)

	ctx.CsmLogger().Infof("RemoveRemoteConfigMeshCluster version=%s, instanceRegion=%s, instanceId=%s, "+
		"namespace=%s, region=%s, clusterId=%s, clusterName=%s, discoverySelectorLabel=%q",
		version, instanceRegion, instanceId, namespace, region, clusterId, clusterName, discoverySelectorLabel)

	vpcID := deleteRemoteUserMeshCluster.InstancesModel.VpcNetworkId

	remoteIopTemplates := &hosting.IopTemplates{
		VpcEniTemplates: &hosting.VpcEniTemplates{
			VpcID: vpcID,
		},
		PaasType: deleteRemoteUserMeshCluster.PaaSType,
		FrontTemplates: &hosting.FrontTemplates{
			Namespace:               namespace,
			Tag:                     version,
			InstanceId:              instanceId,
			InstanceRegion:          instanceRegion,
			InstanceCceClusterUuid:  installationClusterId,
			ClusterRegion:           region,
			ClusterName:             clusterName,
			CceClusterUuid:          clusterId,
			DiscoverySelectorLabels: discoverySelectorLabel,
		},
		AccountId: deleteRemoteUserMeshCluster.AccountId,
	}
	deployerService := hosting.NewDeployerService(ctx, hosting.NewOption(s.opt.DB.DB))
	err := deployerService.UnInstallRemoteConfigMesh(ctx, remoteIopTemplates)
	if err != nil {
		ctx.CsmLogger().Errorf("UnInstallRemoteUserMesh error %v", err)
		return err
	}
	return nil
}

// AddRemoteUserMeshCluster 在托管模式下添加远程用户集群
func (s *Service) AddRemoteUserMeshCluster(ctx csmContext.CsmContext, createRemoteUserMeshCluster *serviceMeta.CreateRemoteUserMeshCluster) error {
	// 实例信息
	version := createRemoteUserMeshCluster.InstancesModel.IstioVersion
	instanceRegion := createRemoteUserMeshCluster.InstancesModel.Region
	instanceId := createRemoteUserMeshCluster.InstancesModel.InstanceUUID
	namespace := createRemoteUserMeshCluster.InstancesModel.IstioInstallNamespace
	installationClusterId := createRemoteUserMeshCluster.InstallationClusterId

	// 集群信息
	region := createRemoteUserMeshCluster.ClusterModel.Region
	clusterId := createRemoteUserMeshCluster.ClusterModel.ClusterUUID
	clusterName := createRemoteUserMeshCluster.ClusterModel.ClusterName
	discoverySelectorLabel := s.enableDiscoverySelectorOrNot(createRemoteUserMeshCluster.DiscoverySelector)

	ctx.CsmLogger().Infof("AddRemoteUserMeshCluster version=%s,instanceRegion=%s,instanceId=%s,namespace=%s,"+
		"region=%s,clusterId=%s,clusterName=%s,discoverySelectorLabel=%q",
		version, instanceRegion, instanceId, namespace, region, clusterId, clusterName, discoverySelectorLabel)
	vpcID := createRemoteUserMeshCluster.InstancesModel.VpcNetworkId

	remoteIopTemplates := &hosting.IopTemplates{
		VpcEniTemplates: &hosting.VpcEniTemplates{
			VpcID: vpcID,
			EniAnnotation: &hosting.EniAnnotation{
				EniRequests: &hosting.EniRequests{
					Eni: &hosting.Eni{
						Num: "1",
					},
				},
				EniLimits: &hosting.EniLimits{
					Eni: &hosting.Eni{
						Num: "1",
					},
				},
			},
		},
		PaasType: createRemoteUserMeshCluster.PaaSType,
		FrontTemplates: &hosting.FrontTemplates{
			Namespace:               namespace,
			Tag:                     version,
			InstanceId:              instanceId,
			InstanceRegion:          instanceRegion,
			InstanceCceClusterUuid:  installationClusterId,
			ClusterRegion:           region,
			ClusterName:             clusterName,
			CceClusterUuid:          clusterId,
			DiscoverySelectorLabels: discoverySelectorLabel,
		},
		AccountId: createRemoteUserMeshCluster.AccountId,
	}
	deployerService := hosting.NewDeployerService(ctx, hosting.NewOption(s.opt.DB.DB))
	err := deployerService.InstallRemoteUserMesh(ctx, remoteIopTemplates)
	if err != nil {
		ctx.CsmLogger().Errorf("InstallRemoteUserMesh error %v", err)
		return err
	}
	return nil
}

// AddRemoteConfigMeshCluster 在托管模式下添加远程config集群
func (s *Service) AddRemoteConfigMeshCluster(ctx csmContext.CsmContext, createRemoteUserMeshCluster *serviceMeta.CreateRemoteUserMeshCluster) error {
	// 实例信息
	version := createRemoteUserMeshCluster.InstancesModel.IstioVersion
	instanceRegion := createRemoteUserMeshCluster.InstancesModel.Region
	instanceId := createRemoteUserMeshCluster.InstancesModel.InstanceUUID
	namespace := createRemoteUserMeshCluster.InstancesModel.IstioInstallNamespace
	installationClusterId := createRemoteUserMeshCluster.InstallationClusterId

	// 集群信息
	region := createRemoteUserMeshCluster.ClusterModel.Region
	clusterId := createRemoteUserMeshCluster.ClusterModel.ClusterUUID
	clusterName := createRemoteUserMeshCluster.ClusterModel.ClusterName
	discoverySelectorLabel := s.enableDiscoverySelectorOrNot(createRemoteUserMeshCluster.DiscoverySelector)

	ctx.CsmLogger().Infof("AddRemoteUserMeshCluster version=%s,instanceRegion=%s,instanceId=%s,namespace=%s,"+
		"region=%s,clusterId=%s,clusterName=%s,discoverySelectorLabel=%q",
		version, instanceRegion, instanceId, namespace, region, clusterId, clusterName, discoverySelectorLabel)
	vpcID := createRemoteUserMeshCluster.InstancesModel.VpcNetworkId

	remoteIopTemplates := &hosting.IopTemplates{
		VpcEniTemplates: &hosting.VpcEniTemplates{
			VpcID: vpcID,
			EniAnnotation: &hosting.EniAnnotation{
				EniRequests: &hosting.EniRequests{
					Eni: &hosting.Eni{
						Num: "1",
					},
				},
				EniLimits: &hosting.EniLimits{
					Eni: &hosting.Eni{
						Num: "1",
					},
				},
			},
		},
		PaasType: createRemoteUserMeshCluster.PaaSType,
		FrontTemplates: &hosting.FrontTemplates{
			Namespace:               namespace,
			Tag:                     version,
			InstanceId:              instanceId,
			InstanceRegion:          instanceRegion,
			InstanceCceClusterUuid:  installationClusterId,
			ClusterRegion:           region,
			ClusterName:             clusterName,
			CceClusterUuid:          clusterId,
			DiscoverySelectorLabels: discoverySelectorLabel,
		},
		AccountId: createRemoteUserMeshCluster.AccountId,
	}
	deployerService := hosting.NewDeployerService(ctx, hosting.NewOption(s.opt.DB.DB))
	err := deployerService.InstallRemoteConfigMesh(ctx, remoteIopTemplates)
	if err != nil {
		ctx.CsmLogger().Errorf("InstallRemoteUserMesh error %v", err)
		return err
	}
	return nil
}

// AddMeshInstance 在托管模式下添加 mesh 实例
func (s *Service) AddMeshInstance(ctx csmContext.CsmContext, createMeshRequest *serviceMeta.CreateMeshRequest) error {
	version := createMeshRequest.IstioVersion
	region := createMeshRequest.Region
	instanceId := createMeshRequest.InstancesUUID
	clusterId := createMeshRequest.InstallationClusterId
	clusterName := createMeshRequest.InstallationClusterName
	namespace := createMeshRequest.InstancesModel.IstioInstallNamespace
	discoverySelectorLabel := s.enableDiscoverySelectorOrNot(createMeshRequest.DiscoverySelector)
	ctx.CsmLogger().Infof("InstallIstio info version=%s,instanceRegion=%s,instanceId=%s,"+
		"clusterId=%s,clusterName=%s,namespace=%s,discoverySelectorLabel=%q",
		version, region, instanceId, clusterId, clusterName, namespace, discoverySelectorLabel)
	hubType := createMeshRequest.Type

	hub := s.versionService.GetHubWithVersionType(ctx, hubType)
	if len(hub) == 0 {
		ctx.CsmLogger().Errorf("get hub with version type error")
		return fmt.Errorf("get the hub version of istio is nil")
	}
	accountID := createMeshRequest.AccountId
	vpcID := createMeshRequest.NetworkType.VpcNetworkId
	subnetID := createMeshRequest.NetworkType.SubnetId
	securityArgs := &serviceMeta.SecurityGroupArgs{
		VpcID: vpcID,
	}

	// TODO 我们应当参考 CCE 去创建 CSM 默认安全组，目前使用 vpc 默认安全组 default
	vpcSecurityGroups, vpcErr := s.VpcService.ListSecurityGroup(ctx, securityArgs, region)
	if vpcErr != nil {
		ctx.CsmLogger().Errorf("fail to listSecurityGroup error %v", vpcErr)
		return vpcErr
	}
	var securityGroupID string
	for _, sg := range vpcSecurityGroups.SecurityGroups {
		// TODO 通过描述过滤，需要优化
		if sg.Desc == constants.VpcSecurityGroupDefaultName {
			securityGroupID = sg.ID
			break
		}
	}
	if len(securityGroupID) == 0 {
		errStr := fmt.Errorf("the default securityGroup is null")
		ctx.CsmLogger().Errorf("failed to listSecurityGroup error %v", errStr)
		return errStr
	}
	var securityGroupIDs []string
	securityGroupIDs = append(securityGroupIDs, securityGroupID)

	vpcInfo, err := s.VpcService.GetVPCAndSubnetDetail(ctx, vpcID, subnetID, region)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to getVPCAndSubnetDetail error %v", err)
		return err
	}

	iopTemplates := &hosting.IopTemplates{
		VpcEniTemplates: &hosting.VpcEniTemplates{
			AccountID:        accountID,
			VpcID:            vpcID,
			SubnetID:         subnetID,
			SecurityGroupIds: strings.Join(securityGroupIDs, ","),
			VpcCidr:          vpcInfo.VpcNetworkCidr,
			PrivateIPAddress: "",
			// eni 默认注解注解值
			EniAnnotation: &hosting.EniAnnotation{
				EniRequests: &hosting.EniRequests{
					Eni: &hosting.Eni{
						Num: "1",
					},
				},
				EniLimits: &hosting.EniLimits{
					Eni: &hosting.Eni{
						Num: "1",
					},
				},
			},
		},
		IsRemote: strings.EqualFold(createMeshRequest.InstancesModel.ConfigCluster, constants.ConfigClusterREMOTE),
		PaasType: createMeshRequest.PaaSType,
		FrontTemplates: &hosting.FrontTemplates{
			Namespace:               namespace,
			Hub:                     hub,
			Tag:                     version,
			InstanceId:              instanceId,
			ClusterRegion:           region,
			ClusterName:             clusterName,
			CceClusterUuid:          clusterId,
			DiscoverySelectorLabels: discoverySelectorLabel,
		},
		AccountId:    accountID,
		TraceEnabled: createMeshRequest.TraceInfo.TraceEnabled,
		SamplingRate: createMeshRequest.TraceInfo.SamplingRate,
		Address:      createMeshRequest.TraceInfo.Address,
	}
	deployerService := hosting.NewDeployerService(ctx, hosting.NewOption(s.opt.DB.DB))
	err = deployerService.InstallInstanceMesh(ctx, iopTemplates)
	if err != nil {
		ctx.CsmLogger().Errorf("InstallInstanceMesh error %v", err)
	}
	// 创建托管网格的时候，创建服务网卡
	_, snicErr := s.namespaceService.CreateSNICWithNewCsmInstance(ctx, createMeshRequest.ClusterModel.ClusterUUID,
		createMeshRequest.InstancesModel.Region, constants.KubeConfigTypeVPC, createMeshRequest.InstancesModel)
	if snicErr != nil {
		ctx.CsmLogger().Errorf("createSNICWithNewCsmInstance error %v", snicErr)
	}
	return err
}

// NewServiceMeshInstance 创建服务网格实例
func (s *Service) NewServiceMeshInstance(ctx csmContext.CsmContext, instances *meta.Instances, cluster *meta.Cluster, traceInfo *meta.TraceInfo) (err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}
	eks := false
	if sliceutil.StringContains(s.opt.EksAccountIds, accountId) {
		eks = true
	}
	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()

	// 监控采集
	jobIds, err := s.enableMonitorOrNot(ctx, cluster)
	if err != nil {
		ctx.CsmLogger().Errorf("enableMonitorOrNot failed. %v", err)
	} else {
		cluster.MonitorJobIds = jobIds
	}

	instancesService := s.instancesModel.WithTx(dbutil.NewDB(tx))
	if err = instancesService.NewInstance(ctx, instances); err != nil {
		return err
	}

	clusterService := s.clusterModel.WithTx(dbutil.NewDB(tx))
	if err = clusterService.NewCluster(ctx, cluster); err != nil {
		return err
	}
	if eks {
		ctx.CsmLogger().Infof("install istio cluster on ==>eks<==")
		go func() {
			err := s.InstallMeshCluster(ctx, instances, cluster, meta.PaaSTypeEKS, traceInfo)
			if err != nil {
				ctx.CsmLogger().Errorf("InstallMeshCluster failed. %v", err)
			}
		}()
	} else {
		ctx.CsmLogger().Infof("install istio cluster on ==>cce<==")
		go func() {
			err := s.InstallMeshCluster(ctx, instances, cluster, meta.PaaSTypeCCE, traceInfo)
			if err != nil {
				ctx.CsmLogger().Errorf("InstallMeshCluster failed. %v", err)
			}
		}()
	}

	tx.Commit()

	return nil
}

func (s *Service) enableMonitorOrNot(ctx csmContext.CsmContext, cluster *meta.Cluster) (jobIds string, err error) {
	// 创建网格实例不应该依赖第三方
	if len(cluster.MonitorInstanceId) > 0 && len(cluster.MonitorRegion) > 0 &&
		strings.EqualFold(cluster.Region, cluster.MonitorRegion) {
		agent, err := s.monitorModel.GetCPromAgent(ctx, cluster.MonitorInstanceId, cluster.Region, cluster.ClusterUUID)
		if err != nil {
			ctx.CsmLogger().Warnf("get cprom agent failed. %v", err)
			return "", err
		}
		jobIds := make([]string, 0)

		// 采集 istio 指标
		istioJobId, err := s.monitorModel.CreateIstioScrapeJob(ctx, cluster.Region, cluster.MonitorInstanceId, agent.AgentID)
		if err != nil {
			ctx.CsmLogger().Errorf("create istio scrape job failed. %v", err)
			return "", err
		}
		jobIds = append(jobIds, istioJobId)

		// 检查 Envoy envoy-stats 采集 Job 是否存在
		envoyJob := s.monitorModel.CheckScrapeJob(ctx, cluster.Region, cluster.MonitorInstanceId, agent.AgentID, EnvoyJobName)
		if !envoyJob {
			envoyJobId, err := s.monitorModel.CreateEnvoyScrapeJob(ctx, cluster.Region, cluster.MonitorInstanceId, agent.AgentID)
			if err != nil {
				ctx.CsmLogger().Errorf("create envoy scrape job failed. %v", err)
				return "", err
			}
			jobIds = append(jobIds, envoyJobId)
		}
		return strings.Join(jobIds, ","), nil
	}
	return "", nil
}

func (s *Service) getIstioInstallNamespace(meshInstanceId string) string {
	return constants.NamespacePrefix + meshInstanceId
}

func (s *Service) getNamespaceLabels(region, meshInstanceId string, labels map[string]string) map[string]string {
	tmpLabels := make(map[string]string)
	tmpLabels[constants.TopologyIstioIoNetWork] = region
	tmpLabels[constants.MeshInstanceId] = meshInstanceId
	for k, v := range labels {
		tmpLabels[k] = v
	}
	return tmpLabels
}

// DiscoverySelectorEnabled indicates whether to enable selective service discovery
func (s *Service) DiscoverySelectorEnabled(enabled int, labels string) bool {
	if enabled == 1 && len(labels) > 0 {
		return true
	}
	return false
}

// InstallMeshCluster 创建服务网格实例主集群
func (s *Service) InstallMeshCluster(ctx csmContext.CsmContext, instances *meta.Instances, clusters *meta.Cluster,
	paasType meta.PaaSType, traceInfo *meta.TraceInfo) error {
	version := instances.IstioVersion
	clusterId := clusters.ClusterUUID
	meshInstanceId := instances.InstanceUUID
	primaryName := clusters.ClusterName
	region := instances.Region
	namespace := instances.IstioInstallNamespace
	discoverySelectorEnabled := instances.DiscoverySelectorEnabled
	discoverySelectorLabels := instances.DiscoverySelectorLabels
	multiProtocolEnabled := *instances.MultiProtocolEnabled
	var labels map[string]string

	ctx.CsmLogger().Infof("InstallMeshCluster info version=%s,region=%s,clusterId=%s,clusterName=%s,meshInstanceId=%s,namespace=%s",
		version, clusterId, primaryName, meshInstanceId, namespace)

	if s.DiscoverySelectorEnabled(func() int {
		var bool2int int
		if *discoverySelectorEnabled {
			bool2int = 1
		}
		return bool2int
	}(), discoverySelectorLabels) {
		err := json.Unmarshal([]byte(discoverySelectorLabels), &labels)
		if err != nil {
			ctx.CsmLogger().Errorf("json unmarshal labels str %s, err %v", discoverySelectorLabels, err)
			return err
		}
	}
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	// k8s client
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterId, meta.MeshType(instances.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}

	// deploy params
	deployParams := deploy.NewParams(version, namespace, region, meshInstanceId, primaryName, clusterId)
	deployParams.DiscoverySelectorLabels = labels
	deployParams.MultiProtocolEnabled = multiProtocolEnabled
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("get pwd error %v", err)
		return err
	}
	deployParams.ConfigPath = path.Join(pwd, constants.Templates)
	deployParams.TraceEnabled = *instances.TraceEnabled
	deployParams.SamplingRate = traceInfo.SamplingRate
	deployParams.Address = traceInfo.Address
	deploy := deploy.NewDeploy(deployParams, k8sClient)
	// todo undeploy istio or check if istio is installed
	if paasType == meta.PaaSTypeCCE {
		err = deploy.UnDeployIstio(ctx, false)
		if err != nil {
			ctx.CsmLogger().Errorf("Undeploy istio error %v", err)
			return err
		}
		err = deploy.AddNamespace(ctx, s.getNamespaceLabels(deploy.Region, meshInstanceId, nil))
	} else if paasType == meta.PaaSTypeEKS {
		err = deploy.UnDeployIstio(ctx, true)
		if err != nil {
			ctx.CsmLogger().Errorf("Undeploy istio error %v", err)
			return err
		}
		err = deploy.AddNamespace(ctx, s.getNamespaceLabels(constants.EksNetWork, meshInstanceId, nil))
	} else {
		ctx.CsmLogger().Errorf("not support PaaS %s", paasType)
		return fmt.Errorf("not support PaaS %s", paasType)
	}
	if err != nil {
		ctx.CsmLogger().Errorf("Create namespace %s error %v", namespace, err)
		return err
	}
	managerCert := cert.NewManagerCert(region, version, primaryName, k8sClient)
	caCert := make(map[string][]byte)
	certData, _ := s.certModel.GetCertByClusterUUIDAndRegionAndAccountId(ctx, region, clusterId, accountId)
	if certData != nil {
		ctx.CsmLogger().Infof("get cert from database")
		// 从数据库中获取中间证书
		caCert[constants.CaCertPemName] = []byte(certData.CaCertPem)
		caCert[constants.CaKeyPemName] = []byte(certData.CaKeyPem)
		caCert[constants.CertChainPemName] = []byte(certData.CertChainPem)
		caCert[constants.RootCertPemName] = []byte(certData.RootCertPem)
	} else {
		ctx.CsmLogger().Infof("get cert from bash shell")
		// 命令脚本自动生成中间证书
		err = managerCert.GenerateCert(ctx)
		if err != nil {
			ctx.CsmLogger().Errorf("GenerateCert error %v", err)
			return err
		}
		caCert, err = managerCert.GenerateCa(ctx)
		if err != nil {
			ctx.CsmLogger().Errorf("CreateCa error %v", err)
			return err
		}
		caCertPem := string(caCert[constants.CaCertPemName])
		caKeyPem := string(caCert[constants.CaKeyPemName])
		certChainPem := string(caCert[constants.CertChainPemName])
		rootCertPem := string(caCert[constants.RootCertPemName])
		certInfo := meta.NewCert(region, clusterId, accountId, caCertPem, caKeyPem, certChainPem, rootCertPem)
		// 入库
		err = s.certModel.NewCert(ctx, certInfo)
		if err != nil {
			ctx.CsmLogger().Errorf("insert cert to db error %v", err)
			return err
		}
	}
	// 创建中间证书
	err = managerCert.CreateCaCerts(ctx, namespace, caCert)
	if err != nil {
		return err
	}

	// install istio
	ctx.CsmLogger().Infof("DeployIstio on %s", paasType)
	if paasType == meta.PaaSTypeCCE {
		err = deploy.DeployIstio(ctx)
	} else if paasType == meta.PaaSTypeEKS {
		err = deploy.DeployEksIstio(ctx)
	} else {
		ctx.CsmLogger().Errorf("not support PaaS %s", paasType)
		return fmt.Errorf("not support PaaS %s", paasType)
	}
	if err != nil {
		ctx.CsmLogger().Errorf("DeployIstio error %v", err)
		return err
	}

	// 安装支持第三方私有协议组件
	if multiProtocolEnabled {
		istiodAddr := fmt.Sprintf(multiprotocol.DefaultIstiodAddr, namespace)
		xdsAddr := fmt.Sprintf(multiprotocol.DefaultXdsAddr, namespace)
		xdsPort := multiprotocol.DefaultXdsPort
		opt := multiprotocol.NewOptionWith(namespace, multiprotocol.GetAerakiImageURL(version),
			multiprotocol.GetAerakiImagePullPolicy(), istiodAddr, xdsAddr, xdsPort, true, true)
		err = s.AerakiComponent(ctx, k8sClient, true, opt)
		if err != nil {
			ctx.CsmLogger().Errorf("InstallAerakiComponent error %v", err)
		}
	}

	return nil
}

func (s *Service) AerakiComponent(ctx csmContext.CsmContext, k8sClient kube.Client,
	install bool, option *multiprotocol.Option) (err error) {
	ctx.CsmLogger().Infof("Start AerakiComponent with install=%v,option=%q", install, option)
	multiProtocolobjectsList, multiProtocolErr := s.MultiProtocolService.ParseAerakiTmpl(ctx, option)
	if multiProtocolErr != nil {
		ctx.CsmLogger().Errorf("Start AerakiComponent error %v", multiProtocolErr)
		return multiProtocolErr
	}
	if install {
		err = kube.CreateOrUpdateK8sResource(ctx, k8sClient, multiProtocolobjectsList)
	} else {
		err = kube.DeleteK8sResource(ctx, k8sClient, multiProtocolobjectsList)
	}
	if err != nil {
		ctx.CsmLogger().Errorf("Start AerakiComponent error %v", err)
		return err
	}
	ctx.CsmLogger().Infof("Start AerakiComponent with install=%v successful", install)
	return nil
}

func (s *Service) InstallRemoteMeshCluster(ctx csmContext.CsmContext, instances *meta.Instances,
	primaryCluster, clusters *meta.Cluster, pt meta.PaaSType) error {
	version := instances.IstioVersion
	clusterId := clusters.ClusterUUID
	meshInstanceId := instances.InstanceUUID
	remoteName := clusters.ClusterName
	region := clusters.Region
	namespace := instances.IstioInstallNamespace
	discoverySelectorEnabled := *instances.DiscoverySelectorEnabled
	discoverySelectorLabels := instances.DiscoverySelectorLabels
	multiProtocolEnabled := *instances.MultiProtocolEnabled
	var labels map[string]string

	ctx.CsmLogger().Infof("InstallMeshCluster info version=%s,region=%s,clusterId=%s,clusterName=%s,meshInstanceId=%s,namespace=%s",
		version, clusterId, remoteName, meshInstanceId, namespace)

	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	// TODO: 优化
	if s.DiscoverySelectorEnabled(func() int {
		var bool2int int
		if discoverySelectorEnabled {
			bool2int = 1
		}
		return bool2int
	}(), discoverySelectorLabels) {
		err := json.Unmarshal([]byte(discoverySelectorLabels), &labels)
		if err != nil {
			ctx.CsmLogger().Errorf("json unmarshal labels str %s, err %v", discoverySelectorLabels, err)
			return err
		}
	}

	// k8s client
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterId, meta.MeshType(instances.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}
	primaryK8sClient, err := s.cceService.NewClient(ctx, primaryCluster.Region, primaryCluster.ClusterUUID, meta.MeshType(instances.InstanceType))

	if err != nil {
		ctx.CsmLogger().Errorf("cce primary NewClient error %v", err)
		return err
	}

	// deploy params
	deployParams := deploy.NewParams(version, namespace, region, meshInstanceId, remoteName, clusterId)
	deployParams.DiscoverySelectorLabels = labels
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("get pwd error %v", err)
		return err
	}
	deployParams.ConfigPath = path.Join(pwd, constants.Templates)
	deployParams.IsRemote = true
	deployParams.InstanceRegion = instances.Region
	deployParams.MultiProtocolEnabled = multiProtocolEnabled

	deploy := deploy.NewDeploy(deployParams, k8sClient)
	deploy.PrimaryClient = primaryK8sClient
	// namespace
	// todo undeploy istio or check if istio is installed
	if pt == meta.PaaSTypeCCE {
		err = deploy.UnDeployIstio(ctx, false)
		if err != nil {
			ctx.CsmLogger().Errorf("Undeploy istio error %v", err)
			return err
		}
		err = deploy.AddNamespace(ctx, s.getNamespaceLabels(deploy.Region, meshInstanceId, nil))
	} else if pt == meta.PaaSTypeEKS {
		err = deploy.UnDeployIstio(ctx, true)
		if err != nil {
			ctx.CsmLogger().Errorf("Undeploy istio error %v", err)
			return err
		}
		err = deploy.AddNamespace(ctx, s.getNamespaceLabels(constants.EksNetWork, meshInstanceId, nil))
	} else {
		ctx.CsmLogger().Errorf("not support PaaS %s", pt)
		return fmt.Errorf("not support PaaS %s", pt)
	}
	if err != nil {
		ctx.CsmLogger().Errorf("Create namespace %s error %v", namespace, err)
		return err
	}

	// ca cert
	managerCert := cert.NewManagerCert(region, version, remoteName, k8sClient)
	caCert := make(map[string][]byte)
	certData, _ := s.certModel.GetCertByClusterUUIDAndRegionAndAccountId(ctx, region, clusterId, accountId)
	if certData != nil {
		// 从数据库中获取中间证书
		caCert[constants.CaCertPemName] = []byte(certData.CaCertPem)
		caCert[constants.CaKeyPemName] = []byte(certData.CaKeyPem)
		caCert[constants.CertChainPemName] = []byte(certData.CertChainPem)
		caCert[constants.RootCertPemName] = []byte(certData.RootCertPem)
	} else {
		// 命令脚本自动生成中间证书
		err = managerCert.GenerateCert(ctx)
		if err != nil {
			ctx.CsmLogger().Errorf("GenerateCert error %v", err)
			return err
		}
		caCert, err = managerCert.GenerateCa(ctx)
		if err != nil {
			ctx.CsmLogger().Errorf("CreateCa error %v", err)
			return err
		}
		caCertPem := string(caCert[constants.CaCertPemName])
		caKeyPem := string(caCert[constants.CaKeyPemName])
		certChainPem := string(caCert[constants.CertChainPemName])
		rootCertPem := string(caCert[constants.RootCertPemName])
		certInfo := meta.NewCert(region, clusterId, accountId, caCertPem, caKeyPem, certChainPem, rootCertPem)
		// 入库
		err = s.certModel.NewCert(ctx, certInfo)
		if err != nil {
			return err
		}
	}
	// 创建中间证书
	err = managerCert.CreateCaCerts(ctx, namespace, caCert)
	if err != nil {
		return err
	}

	// install istio
	if pt == meta.PaaSTypeCCE {
		err = deploy.DeployIstio(ctx)
	} else if pt == meta.PaaSTypeEKS {
		err = deploy.DeployEksIstio(ctx)
	} else {
		ctx.CsmLogger().Errorf("not support PaaS %s", pt)
		return fmt.Errorf("not support PaaS %s", pt)
	}
	if err != nil {
		ctx.CsmLogger().Errorf("DeployIstio error %v", err)
		return err
	}

	// remote 远程集群安装第三方私有协议组件
	if multiProtocolEnabled {
		istiodAddr := fmt.Sprintf(multiprotocol.DefaultIstiodRemoteAddr, namespace)
		xdsAddr := fmt.Sprintf(multiprotocol.DefaultXdsRemoteAddr, namespace)
		xdsPort := multiprotocol.DefaultXdsRemotePort
		opt := multiprotocol.NewOptionWith(namespace, multiprotocol.GetAerakiImageURL(version),
			multiprotocol.GetAerakiImagePullPolicy(), istiodAddr, xdsAddr, xdsPort, false, true)
		err = s.AerakiComponent(ctx, k8sClient, true, opt)
		return err
	}

	// 网格实例对应的主集群开启了监控，开启从集群监控
	if instances.MonitorEnabled != nil && *instances.MonitorEnabled {
		cpromInstanceId := primaryCluster.MonitorInstanceId
		region := primaryCluster.Region
		monitorInstances := &vo.MonitorInstances{
			Enabled: true,
			Instances: []*vo.MonitorInstance{
				{
					Region: region,
					ID:     cpromInstanceId,
				},
			},
		}
		err := s.monitorService.UpdateRemoteMonitor(ctx, meshInstanceId, clusterId, "", monitorInstances)
		if err != nil {
			ctx.CsmLogger().Errorf("InstallRemoteMeshCluster UpdateMonitor error %v", err)
		}
	}

	return nil
}

func (s *Service) UpdateDiscoverySelector(ctx csmContext.CsmContext, instanceUUID string, selector *serviceMeta.DiscoverySelector) error {
	accountId, _ := iam.GetAccountId(ctx)
	eks := false
	if sliceutil.StringContains(s.opt.EksAccountIds, accountId) {
		ctx.CsmLogger().Infof("update mutatingwebhookconfiguration and validatingwebhookconfiguration on eks, set eks = true")
		eks = true
	}

	// 获取实例详情信息
	instanceInfo, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return err
	}

	return s.discoverySelector.UpdateDiscoverySelector(ctx, eks, instanceInfo, selector)
}

func (s *Service) GetDiscoverySelector(ctx csmContext.CsmContext, instanceUUID string) (
	*serviceMeta.DiscoverySelector, error) {
	return s.discoverySelector.GetDiscoverySelector(ctx, instanceUUID)
}

// GenerateInstancesID 生成服务网格实例 uuid
func (s *Service) GenerateInstancesID(ctx csmContext.CsmContext) (res string, err error) {
	// 最多尝试生成八次
	var result string
	for i := 0; i < 8; i++ {
		result = uuid.GetInstanceUUID()
		var instance meta.Instances
		notFound := s.opt.DB.Where("instance_uuid = ?", result).Find(&instance).RecordNotFound()
		if notFound {
			return result, nil
		}
	}
	return "", fmt.Errorf("duplicate cce cluster id in database")
}

// pageSlice 按照分页信息返回一个实例列表切片 TODO: move it to utils
func pageSlice(instList []meta.MeshInstance, mrp *meta.CsmMeshRequestParams) ([]meta.MeshInstance, int64) {
	// filter with instance status
	if mrp.Status != "" {
		l := 0
		for _, inst := range instList {
			if inst.Status == mrp.Status {
				instList[l] = inst
				l++
			}
		}
		instList = instList[:l]
	}

	// order the instList by its createTime first with desc
	sort.Slice(instList, func(i, j int) bool {
		return instList[i].CreateTime.Unix() > instList[j].CreateTime.Unix()
	})
	if strings.ToUpper(mrp.Order) == constants.DBOrderASC {
		// reverse the instList
		for i, j := 0, len(instList)-1; i < j; i, j = i+1, j-1 {
			instList[i], instList[j] = instList[j], instList[i]
		}
	}

	totalCount := int64(len(instList))
	realPageSize := int64(math.Max(float64(mrp.PageSize), float64(constants.MinPageSize)))

	maxPageNo := (totalCount + realPageSize - 1) / realPageSize
	realPageNo := int64(math.Min(float64(mrp.PageNo), math.Max(float64(maxPageNo), float64(constants.MinPageNo))))

	startItem := (realPageNo - 1) * realPageSize

	return instList[startItem:int64(math.Min(float64(startItem+realPageSize), float64(totalCount)))], totalCount
}

// GetServiceMeshInstances 获取服务网格实例列表
func (s *Service) GetServiceMeshInstances(ctx csmContext.CsmContext, mrp *meta.CsmMeshRequestParams) (mil *meta.MeshInstanceListResponse, err error) {
	instList, err := s.instancesModel.GetInstancesList(ctx, mrp)
	if err != nil {
		return nil, err
	}

	var tmpResult []meta.MeshInstance
	resourceBak := ctx.GetResource()
	for _, inst := range *instList {
		// IAM鉴权过滤
		if resourceBak != "UnitTest" {
			ctx.ResetResourceWith("instance/" + inst.InstanceUUID)
			err = middleware.CheckIAMDefaultDenyForUser(ctx, viper.GetString("serviceName"), permission.ReadInstance)
		}
		if err == nil {
			mi := meta.MeshInstance{
				InstanceId:     inst.InstanceUUID,
				InstanceName:   inst.InstanceName,
				IstioVersion:   inst.IstioVersion,
				InstanceType:   inst.InstanceType,
				CreateTime:     *inst.CreateTime,
				PublicEnabled:  *inst.PublicEnabled,
				BillingModel:   constants.BillingModel,
				Status:         constants.SmiAbnormal,
				SidecarCount:   0,
				RunningCluster: 0,
				ClusterCount:   0,
			}
			tmpResult = append(tmpResult, mi)
		}
	}
	if resourceBak != "UnitTest" {
		ctx.ResetResourceWith(resourceBak)
	}

	var wg sync.WaitGroup
	for i, mi := range tmpResult {
		// 访问数据库
		clusterList, instErr := s.clusterModel.GetAllClusterByInstanceUUID(ctx, mi.InstanceId)
		if err != nil {
			return nil, instErr
		}
		var istiodCluster *meta.Cluster
		var meshType meta.MeshType
		for _, c := range *clusterList {
			if c.ClusterType == string(meta.ClusterTypeExternal) || c.ClusterType == string(meta.ClusterTypePrimary) {
				istiodCluster = &c
				meshType = meta.MeshType(mi.InstanceType)
				break
			}
		}

		wg.Add(1)
		go func(inst *meta.MeshInstance) {
			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Infof("goroutine exited abnormally because: %v", e)
				}
				wg.Done()
			}()
			// 获取实例状态
			instanceStatus, statusErr := s.instancesModel.GetInstanceStatus(ctx, inst.InstanceId, istiodCluster, meshType)
			if statusErr != nil {
				ctx.CsmLogger().Infof("failed to get instance status because: ", statusErr)
			}
			inst.Status = instanceStatus
		}(&tmpResult[i])

		wg.Add(1)
		go func(inst *meta.MeshInstance) {
			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Infof("goroutine exited abnormally because: %v", e)
				}
				wg.Done()
			}()
			// 获取集群运行状态
			runningCluster, clusterCount, countErr := s.instancesModel.GetInstanceClustersCount(ctx, inst.InstanceId, clusterList)
			if countErr != nil {
				ctx.CsmLogger().Infof("failed to get instance clusters count because: ", countErr)
			}
			inst.RunningCluster, inst.ClusterCount = runningCluster, clusterCount
		}(&tmpResult[i])

		wg.Add(1)
		go func(inst *meta.MeshInstance) {
			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Infof("goroutine exited abnormally because: %v", e)
				}
				wg.Done()
			}()
			// 获取实例在线Sidecar数量
			sidecarCount, numErr := s.GetActiveSidecarNum(ctx, inst.InstanceId, istiodCluster, meshType)
			if numErr != nil {
				ctx.CsmLogger().Infof("failed to get instance online sidecar count because: ", numErr)
			}
			inst.SidecarCount = int64(sidecarCount)
		}(&tmpResult[i])

		// todo mock data to front, improve blb and eip info
		tmpResult[i].BlbInfo.BlbId = "xxx"
		tmpResult[i].EipInfo.EipId = "xxx.xxx.xxx.xxx"
	}
	wg.Wait()

	response := &meta.MeshInstanceListResponse{
		PageNo:     mrp.PageNo,
		PageSize:   mrp.PageSize,
		TotalCount: 0,
		Order:      mrp.Order,
		OrderBy:    mrp.OrderBy,
		Result:     []meta.MeshInstance{},
	}
	// pageSlice
	response.Result, response.TotalCount = pageSlice(tmpResult, mrp)

	return response, nil
}

func (s *Service) GetVpcAndSubnetInfo(ctx csmContext.CsmContext, vpcId, subnetId, region string) (*meta.InstanceNetworkType, error) {
	return s.VpcService.GetVPCAndSubnetDetail(ctx, vpcId, subnetId, region)
}

// GetAllInstances 获取所有服务网格实例
func (s *Service) GetAllInstances(ctx csmContext.CsmContext) (instances []meta.Instances, err error) {
	ins, err := s.instancesModel.GetInstancesByUser(ctx)
	if err != nil {
		return nil, err
	}

	return *ins, nil
}

// GetInstanceDetail 获取instance详细信息
func (s *Service) GetInstanceDetail(ctx csmContext.CsmContext, instanceUUID string) (instance *meta.InstanceDetail, error error) {
	// 获取服务网格实例 instance 详情
	instanceInfo, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return nil, err
	}
	instanceType := instanceInfo.InstanceType
	istioVersion := instanceInfo.IstioVersion
	instanceName := instanceInfo.InstanceName

	// 根据实例类型获取实例
	istiodCluster, err := s.clusterModel.GetIstiodCluster(ctx, instanceUUID, instanceType)
	if err != nil {
		ctx.CsmLogger().Warnf("failed to get instance cluster, error %v", err)
	}

	var wg sync.WaitGroup

	sidecarNum := 0
	wg.Add(1)
	go func() {
		defer func() {
			if e := recover(); e != nil {
				ctx.CsmLogger().Infof("goroutine exited abnormally because: %v", e)
			}
			wg.Done()
		}()
		sidecarNum, err = s.GetActiveSidecarNum(ctx, instanceUUID, istiodCluster, meta.MeshType(instanceType))
		if err != nil {
			ctx.CsmLogger().Warnf("failed to get instance sidecar nums, err %v", err)
		}
	}()

	// 获取实例状态
	instanceStatus := constants.SmiAbnormal
	wg.Add(1)
	go func() {
		defer func() {
			if e := recover(); e != nil {
				ctx.CsmLogger().Infof("goroutine exited abnormally because: %v", e)
			}
			wg.Done()
		}()
		instanceStatus, err = s.instancesModel.GetInstanceStatus(ctx, instanceUUID, istiodCluster, meta.MeshType(instanceType))
		if err != nil {
			ctx.CsmLogger().Warnf("failed to get instance status, error %v", err)
		}
	}()

	var clusterId, region, clusterName, controlPlaneAddress string
	var blbId, blbName, blbStatus, vpcName, vpcId, vpcCidr string
	networkType := &meta.InstanceNetworkType{}
	wg.Add(1)
	go func() {
		defer func() {
			if e := recover(); e != nil {
				ctx.CsmLogger().Infof("goroutine exited abnormally because: %v", e)
			}
			wg.Done()
		}()
		if istiodCluster != nil {
			installNamespace := istiodCluster.IstioInstallNamespace
			clusterId = istiodCluster.ClusterUUID
			clusterName = istiodCluster.ClusterName
			region = istiodCluster.Region
			ctx.CsmLogger().Infof("instance %s clusterId=%s,clusterName=%s,region=%s", instanceUUID, clusterId, clusterName, region)
			client, clientErr := s.cceService.NewClient(ctx, region, clusterId, meta.MeshType(instanceType))
			if clientErr != nil {
				return
			}
			svcName := constants.EastWestGateway
			if instanceType == string(version.HostingVersionType) {
				// 托管服务网格 vpc 信息
				svcName = constants.IstiodServiceName
				vpcAndSubnetValue, vpcAndSubnetErr := s.GetVpcAndSubnetInfo(ctx, instanceInfo.VpcNetworkId, instanceInfo.SubnetId, region)
				if vpcAndSubnetErr != nil {
					ctx.CsmLogger().Warnf("get vpc and subnet error %v", vpcAndSubnetErr)
				}
				networkType = vpcAndSubnetValue
				vpcId = vpcAndSubnetValue.VpcNetworkId
				vpcName = vpcAndSubnetValue.VpcNetworkName
				vpcCidr = vpcAndSubnetValue.VpcNetworkCidr
			} else {
				// 独立服务网格 vpc 信息
				cceResponse, cceErr := s.cceService.GetCCECluster(ctx, region, clusterId)
				if cceErr != nil {
					ctx.CsmLogger().Warnf("failed to GetCCECluster %v", cceErr)
				}
				ctx.CsmLogger().Debugf("cceResponse is %v", cceResponse)
				vpcId = cceResponse.VpcId
				vpcName = cceResponse.VpcName
				vpcCidr = cceResponse.VpcCidr
			}
			svc, svcErr := client.Kube().CoreV1().Services(installNamespace).Get(context.TODO(), svcName, metav1.GetOptions{})
			if svcErr != nil && !kubeErrors.IsNotFound(svcErr) {
				return
			}
			if kubeErrors.IsNotFound(svcErr) {
				ctx.CsmLogger().Warnf("services %s not found", svcName)
				blbStatus = constants.BLBNotExist
			} else {
				blbId = svc.Annotations[constants.BLBId]
				blbStatus = constants.BLBRunning
				if blbId == "" {
					blbStatus = constants.BLBPending
				}
			}
			var LoadBalancerIP []string
			if svc != nil {
				ingress := svc.Status.LoadBalancer.Ingress
				if len(ingress) > 0 {
					for _, v := range ingress {
						LoadBalancerIP = append(LoadBalancerIP, v.IP)
					}
				}
			}
			controlPlaneAddress = strings.Join(LoadBalancerIP, ",")
			blbName = path.Join(clusterId, installNamespace, svcName)
		}
	}()

	// 选择性服务发现
	discoverySelectorEnabled := *instanceInfo.DiscoverySelectorEnabled
	discoverySelectorLabels := make(map[string]string)
	wg.Add(1)
	go func() {
		defer func() {
			if e := recover(); e != nil {
				ctx.CsmLogger().Infof("goroutine exited abnormally because: %v", e)
			}
			wg.Done()
		}()
		if discoverySelectorEnabled {
			discoverySelectorBytes := []byte(instanceInfo.DiscoverySelectorLabels)
			err = json.Unmarshal(discoverySelectorBytes, &discoverySelectorLabels)
			if err != nil {
				return
			}
		}
	}()

	wg.Wait()
	status := instanceStatus

	// 多协议支持
	multiProtocolEnabled := *instanceInfo.MultiProtocolEnabled

	// 兼容存量网格实例，托管网格默认为"EXTERNAL"，独立网格默认为""
	configCluster := ""
	if instanceType == string(version.HostingVersionType) {
		if instanceInfo.ConfigCluster == "" {
			configCluster = constants.ConfigClusterEXTERNAL
		} else {
			configCluster = instanceInfo.ConfigCluster
		}
	}

	instanceDetail := meta.InstanceDetail{
		BillingModel:        constants.BillingModel,
		Type:                instanceType,
		IstioVersion:        istioVersion,
		Status:              status,
		ControlPanelAddress: controlPlaneAddress,
		NetworkType:         networkType,
		BlbInfo: meta.BlbInfo{
			BlbId:     blbId,
			BlbName:   blbName,
			BlbStatus: blbStatus,
		},
		VpcInfo: meta.VpcInfo{
			VpcId:   vpcId,
			VpcName: vpcName,
			VpcCidr: vpcCidr,
		},
		ClusterInfo: meta.ClusterInfo{
			ClusterId:   clusterId,
			ClusterName: clusterName,
		},
		OverviewOfSidecar: meta.OverviewOfSidecar{
			InstanceId:   instanceUUID,
			InstanceName: instanceName,
			Num:          sidecarNum,
		},
		DiscoverySelector: &meta.DiscoverySelector{
			Enabled:     discoverySelectorEnabled,
			MatchLabels: discoverySelectorLabels,
		},
		MultiProtocol: multiProtocolEnabled,
		APIServerEip:  *instanceInfo.APIServerEip,
		ConfigCluster: configCluster,
	}

	return &instanceDetail, nil
}

func (s *Service) GetActiveSidecarNum(ctx csmContext.CsmContext, instanceUUID string, istiodCluster *meta.Cluster, meshType meta.MeshType) (int, error) {
	if istiodCluster == nil {
		c, t, err := s.instancesModel.GetInstanceIstiodCluster(ctx, instanceUUID)
		if err != nil {
			return 0, err
		}
		istiodCluster = c
		meshType = t
	}
	wg := &sync.WaitGroup{}
	c := make(chan int, constants.ChannelBuffer)
	// 计算结果channel
	rc := make(chan int)
	// 开启计算逻辑
	go Caculate(c, rc)

	cherrors := make(chan error)
	wg.Add(1)
	go func() {
		err := CalCluster(ctx, *istiodCluster, meshType, c, wg)
		if err != nil {
			ctx.CsmLogger().Errorf("AccountID %s instance %s CalCluster has error : %v",
				istiodCluster.AccountId, instanceUUID, err)
			cherrors <- err
		}
	}()

	go func() {
		//等待所有的协程执行完成
		wg.Wait()
		// 关闭缓冲channel
		close(c)
	}()

	select {
	case sum := <-rc:
		ctx.CsmLogger().Infof("instance %s has %d active sidecar connection", instanceUUID, sum)
		return sum, nil
	case <-time.After(constants.MetricsTimeout):
		ctx.CsmLogger().Warnf("timeout for get instance %s active sidecar num", instanceUUID)
		return 0, errors.New(constants.Timeout)
	case err := <-cherrors:
		close(cherrors)
		return 0, err
	}
}

func (s *Service) GetSugarSidecarNum(ctx csmContext.CsmContext, instanceUUID string,
	istiodCluster *meta.Cluster, meshType meta.MeshType) (int, error) {
	wg := &sync.WaitGroup{}
	c := make(chan int, constants.ChannelBuffer)
	// 计算结果channel
	rc := make(chan int)
	// 开启计算逻辑
	go Caculate(c, rc)

	cherrors := make(chan error)
	wg.Add(1)
	go func() {
		err := CalSugarCluster(ctx, istiodCluster.AccountId, *istiodCluster, meshType, c, wg)
		if err != nil {
			ctx.CsmLogger().Errorf("AccountID %s instance %s CalCluster has error : %v",
				istiodCluster.AccountId, instanceUUID, err)
			cherrors <- err
		}
	}()

	go func() {
		//等待所有的协程执行完成
		wg.Wait()
		// 关闭缓冲channel
		close(c)
	}()

	select {
	case sum := <-rc:
		ctx.CsmLogger().Infof("instance %s has %d active sidecar connection", instanceUUID, sum)
		return sum, nil
	case err := <-cherrors:
		close(cherrors)
		return 0, err
	}
}

func Caculate(c chan int, rc chan int) {
	sum := 0
	for num := range c {
		sum += num
	}
	rc <- sum
}

func CalCluster(ctx csmContext.CsmContext, cluster meta.Cluster, meshType meta.MeshType, c chan int, wg *sync.WaitGroup) error {
	defer wg.Done()
	clientService := cce.NewClientService()
	client, err := clientService.NewClient(ctx, cluster.Region, cluster.ClusterUUID, meshType)
	if err != nil {
		return err
	}

	clientset := client.Kube()
	config := client.RESTConfig()

	// 获取控制面所有的pod
	podList, err := clientset.CoreV1().Pods(cluster.IstioInstallNamespace).List(context.TODO(),
		metav1.ListOptions{
			LabelSelector: constants.IstioLabelSelector,
		})
	// TODO 获取报错信息优化
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get instance pods, err is %v", err)
	}
	// 获取每个pod上连接的Sidecar数量
	for _, item := range podList.Items {
		wg.Add(1)
		item := item
		go func() {
			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Errorf("goroutine exited abnormally because: %v", e)
				}
			}()
			err := CalPod(ctx, item, clientset, config, c, wg, cluster)
			if err != nil {
				ctx.CsmLogger().Errorf("pod %s CalPod has error : %v", item.Name, err)
			}
		}()
	}

	return nil
}

func CalSugarCluster(ctx csmContext.CsmContext, accountID string, cluster meta.Cluster, meshType meta.MeshType, c chan int, wg *sync.WaitGroup) error {
	defer wg.Done()
	clientService := cce.NewClientService()
	client, err := clientService.NewSugarClient(ctx, accountID, cluster.Region, cluster.ClusterUUID, meshType)
	if err != nil {
		return err
	}

	clientset := client.Kube()
	config := client.RESTConfig()

	// 获取控制面所有的pod
	podList, err := clientset.CoreV1().Pods(cluster.IstioInstallNamespace).List(context.TODO(),
		metav1.ListOptions{
			LabelSelector: constants.IstioLabelSelector,
		})
	// TODO 获取报错信息优化
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get instance pods, err is %v", err)
	}
	// 获取每个pod上连接的Sidecar数量
	for _, item := range podList.Items {
		wg.Add(1)
		item := item
		go func() {
			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Errorf("goroutine exited abnormally because: %v", e)
				}
			}()
			err := CalPod(ctx, item, clientset, config, c, wg, cluster)
			if err != nil {
				ctx.CsmLogger().Errorf("pod %s CalPod has error : %v", item.Name, err)
			}
		}()
	}

	return nil
}

func CalPod(ctx csmContext.CsmContext, item v1.Pod, clientset kubernetes.Interface,
	config *rest.Config, c chan int, wg *sync.WaitGroup, cluster meta.Cluster) error {
	defer wg.Done()

	command := constants.MetricsCommand
	cmd := []string{
		"sh",
		"-c",
		command,
	}
	const tty = false
	req := clientset.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(item.Name).
		Namespace(cluster.IstioInstallNamespace).
		SubResource("exec")
	req.VersionedParams(
		&v1.PodExecOptions{
			Command: cmd,
			Stdin:   false,
			Stdout:  true,
			Stderr:  true,
			TTY:     tty,
		},
		scheme.ParameterCodec,
	)
	var stdout, stderr bytes.Buffer
	exec, err := remotecommand.NewSPDYExecutor(config, "POST", req.URL())
	if err != nil {
		return err
	}
	// 创建一个context，并设置超时时间
	ctx2, cancel := context.WithTimeout(context.Background(), time.Second*3) // 设置3秒的超时时间
	defer cancel()                                                           // 确保在函数结束时取消context

	err = exec.StreamWithContext(ctx2, remotecommand.StreamOptions{
		Stdin:  nil,
		Stdout: &stdout,
		Stderr: &stderr,
	})
	if err != nil {
		return err
	}
	stringSlice := strings.Split(stdout.String(), "\n")
	num := 0
	res := make([]int, len(stringSlice))
	for index, val := range stringSlice {
		res[index], _ = strconv.Atoi(val)
	}
	for _, count := range res {
		num += count
	}

	ctx.CsmLogger().Infof("pod %s has %d active sidecar connection", item.Name, num)
	c <- num
	return nil
}

func (s *Service) CreateNamespace(ctx csmContext.CsmContext, instanceId, namespace string) (err error) {
	kubeClient, err := s.getKubeClient(ctx, instanceId)
	if err != nil {
		return err
	}

	deployParams := deploy.NewParams("", namespace, "", instanceId, "", "")
	deploy := deploy.NewDeploy(deployParams, kubeClient)
	return deploy.AddNamespace(ctx, map[string]string{
		constants.MeshInstanceId: instanceId,
	})
}

// DeleteNamespace 删除命名空间
// 1. 判断命名空间标签是否属于当前 mesh 实例
// 2. 判断当前用户是否有权限
func (s *Service) DeleteNamespace(ctx csmContext.CsmContext, instanceId, namespace string) (err error) {
	kubeClient, err := s.getKubeClient(ctx, instanceId)
	if err != nil {
		return err
	}

	ns, err := kubeClient.Kube().CoreV1().Namespaces().Get(context.TODO(), namespace, metav1.GetOptions{})
	if err != nil {
		return nil
	}

	if ns == nil || ns.Labels == nil || len(ns.Labels) == 0 ||
		len(ns.Labels[constants.MeshInstanceId]) == 0 || ns.Labels[constants.MeshInstanceId] != instanceId {
		return errors.New("no match namespace")
	}

	err = kubeClient.Kube().CoreV1().Namespaces().Delete(context.TODO(), namespace, metav1.DeleteOptions{})
	if err != nil {
		return err
	}

	go func() {
		nsErr := wait.PollImmediate(constants.PollInternal, constants.Internal, func() (bool, error) {
			ctx.CsmLogger().Infof("delete %s namespace", namespace)
			_, getErr := kubeClient.Kube().CoreV1().Namespaces().Get(context.TODO(), namespace, metav1.GetOptions{})
			if kubeErrors.IsNotFound(getErr) {
				return true, nil // done
			}
			if getErr != nil {
				return false, getErr // stop wait with error
			}
			return false, nil
		})
		if nsErr != nil {
			nsDetail, getErr := kubeClient.Kube().CoreV1().Namespaces().Get(context.TODO(), namespace, metav1.GetOptions{})
			if getErr != nil {
				return
			}
			if nsDetail.Status.Phase == v1.NamespaceTerminating {
				ctx.CsmLogger().Infof("delete %s namespace with patch force...", namespace)
				err = kube.DeleteNamespaceWithFinalizers(ctx, kubeClient, ns)
				if getErr != nil {
					return
				}
				ctx.CsmLogger().Infof("delete %s namespace with patch force ok...", namespace)
			}
			return
		}
		ctx.CsmLogger().Infof("delete %s namespace successful", namespace)
	}()

	return nil
}

// func (s *Service) GetKubeconfig(ctx csmContext.CsmContext, instanceId, namespace string) (kubeconfig string, err error) {
// 	kubeClient, err := s.getKubeClient(ctx, instanceId)
// 	if err != nil {
// 		return err
// 	}
// }

func (s *Service) GetInstanceStatus(ctx csmContext.CsmContext, instanceId, remoteClusterID,
	remoteClusterRegion string) (status string, err error) {
	clusters, err := s.clusterModel.GetAllClusterByInstanceUUID(ctx, instanceId)
	if err != nil {
		return "", err
	}
	instanceInfo, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, instanceId)
	if err != nil {
		return "", err
	}

	for _, cluster := range *clusters {
		// remoteClusterID 和 remoteClusterRegion不为空时，只返回特定的remote集群和主集群
		if isSkipRemoteCluster(cluster, remoteClusterID, remoteClusterRegion) {
			continue
		}
		client, err := s.cceService.NewClient(ctx, cluster.Region, cluster.ClusterUUID, meta.MeshType(instanceInfo.InstanceType))
		if err != nil {
			return "", err
		}

		istiodPods, err := client.Kube().CoreV1().Pods(constants.IstioNamespace+"-"+instanceId).List(
			context.Background(), metav1.ListOptions{LabelSelector: constants.IstioLabelSelector})
		if err != nil {
			return constants.SmiDeploying, err
		}

		if istiodPods == nil || istiodPods.Items == nil || len(istiodPods.Items) == 0 {
			return constants.SmiUnknown, nil
		}

		for _, pod := range istiodPods.Items {
			status := pod.Status.Phase
			if status == v1.PodRunning {
				continue
			}
			reason := strings.ToLower(pod.Status.Reason)
			if reason == constants.SmiEvicted {
				continue
			}
			if status == v1.PodPending {
				return constants.SmiDeploying, nil
			}
			return constants.SmiAbnormal, nil
		}
	}

	return constants.SmiRunning, nil
}

func isSkipRemoteCluster(reCluster meta.Cluster, remoteClusterID, remoteClusterRegion string) bool {
	if remoteClusterID == "" || remoteClusterRegion == "" {
		return false
	}
	if reCluster.ClusterType == string(meta.ClusterTypePrimary) || reCluster.ClusterType == string(meta.ClusterTypeExternal) {
		return false
	}
	if reCluster.ClusterUUID == remoteClusterID && reCluster.Region == remoteClusterRegion {
		return false
	}
	return true
}

func (s *Service) getKubeClient(ctx csmContext.CsmContext, meshInstanceId string) (kube.Client, error) {
	c, meshType, err := s.instancesModel.GetInstanceIstiodCluster(ctx, meshInstanceId)
	if err != nil {
		// TODO: 添加日志，并错误处理
		return nil, err
	}
	client, err := s.cceService.NewClient(ctx, c.Region, c.ClusterUUID, meshType)
	if err != nil {
		return nil, err
	}

	return client, nil
}

func (s *Service) GetIstioSupportK8sVersion(ctx csmContext.CsmContext) []versionUtil.IstioSupportK8sVersion {
	versionPairingLists := versionUtil.GetIstioSupportK8sVersion()
	return versionPairingLists
}
