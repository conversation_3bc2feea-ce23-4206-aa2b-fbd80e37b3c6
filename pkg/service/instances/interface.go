package instances

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	servicemeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	versionUtil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/version"
)

type ServiceInterface interface {
	NewServiceMeshInstance(ctx context.CsmContext, instances *meta.Instances, clusters *meta.Cluster, traceInfo *meta.TraceInfo) error
	GenerateInstancesID(ctx context.CsmContext) (string, error)
	DeleteServiceMeshInstance(ctx context.CsmContext, deleteMeshRequest *servicemeta.DeleteMeshRequest) error
	GetServiceMeshInstances(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*meta.MeshInstanceListResponse, error)
	GetVpcAndSubnetInfo(ctx context.CsmContext, vpcId, subnetId, region string) (*meta.InstanceNetworkType, error)
	GetAllInstances(ctx context.CsmContext) ([]meta.Instances, error)
	GetAllCceClusterByRegion(ctx context.CsmContext, region string) ([]servicemeta.MeshCluster, error)
	GetInstanceDetail(ctx context.CsmContext, instanceUUID string) (*meta.InstanceDetail, error)
	InstallMeshCluster(ctx context.CsmContext, instances *meta.Instances, clusters *meta.Cluster, pt meta.PaaSType, traceInfo *meta.TraceInfo) error
	InstallRemoteMeshCluster(ctx context.CsmContext, instances *meta.Instances, primaryCluster, clusters *meta.Cluster, pt meta.PaaSType) error
	GetActiveSidecarNum(ctx context.CsmContext, instanceUUID string, istiodCluster *meta.Cluster, meshType meta.MeshType) (int, error)
	// GetSugarSidecarNum sugar获取独立网格sidecar接口
	GetSugarSidecarNum(ctx context.CsmContext, instanceUUID string, istiodCluster *meta.Cluster, meshType meta.MeshType) (int, error)
	// TODO: 调整 cluster 操作
	UnInstallMeshCluster(ctx context.CsmContext, instances *meta.Instances, cluster *meta.Cluster) error

	// UpdateDiscoverySelector 支持控制面选择性服务发现
	UpdateDiscoverySelector(ctx context.CsmContext, instanceUUID string, selector *servicemeta.DiscoverySelector) error
	GetDiscoverySelector(ctx context.CsmContext, instanceUUID string) (selector *servicemeta.DiscoverySelector, err error)

	// GetHostingCluster 获取托管集群信息
	GetHostingCluster(ctx context.CsmContext, region string) (string, string)
	// AddHostingServiceMeshInstance 添加托管集群
	AddHostingServiceMeshInstance(ctx context.CsmContext, createMeshRequest *servicemeta.CreateMeshRequest) error
	// RemoveMeshInstance 在托管集群下删除 mesh 实例
	RemoveMeshInstance(ctx context.CsmContext, deleteMeshCluster *servicemeta.DeleteMeshInstance) error
	// AddMeshInstance 在托管模式下添加 mesh 实例
	AddMeshInstance(ctx context.CsmContext, createMeshRequest *servicemeta.CreateMeshRequest) error
	// AddRemoteUserMeshCluster 在托管模式下添加远程用户集群
	AddRemoteUserMeshCluster(ctx context.CsmContext, createRemoteUserMeshCluster *servicemeta.CreateRemoteUserMeshCluster) error
	// AddRemoteConfigMeshCluster 在托管模式下添加远程config用户集群
	AddRemoteConfigMeshCluster(ctx context.CsmContext, createRemoteUserMeshCluster *servicemeta.CreateRemoteUserMeshCluster) error
	// RemoveRemoteUserMeshCluster 在托管模式下删除远程用户集群
	RemoveRemoteUserMeshCluster(ctx context.CsmContext, deleteRemoteUserMeshCluster *servicemeta.DeleteRemoteUserMeshCluster) error
	RemoveRemoteConfigMeshCluster(ctx context.CsmContext, deleteRemoteUserMeshCluster *servicemeta.DeleteRemoteUserMeshCluster) error
	CreateNamespace(ctx context.CsmContext, instanceId, namespace string) (err error)
	DeleteNamespace(ctx context.CsmContext, instanceId, namespace string) (err error)
	// GetKubeconfig(ctx context.CsmContext, instanceId, namespace string) (kubeconfig string, err error)
	GetInstanceStatus(ctx context.CsmContext, instanceId, remoteClusterID, remoteClusterRegion string) (status string, err error)
	GetIstioSupportK8sVersion(ctx context.CsmContext) []versionUtil.IstioSupportK8sVersion
}
