package redis

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// Service Redis服务实现
type Service struct {
	option      *Option
	redisClient *redis.Client
	connected   bool
}

// NewRedisService 创建Redis服务实例
func NewRedisService(option *Option) ServiceInterface {
	return &Service{
		option:    option,
		connected: false,
	}
}

// Connect 连接Redis
func (s *Service) Connect(ctx csmContext.CsmContext) error {
	if s.connected && s.redisClient != nil {
		return nil
	}

	// 创建Redis客户端配置
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", s.option.Address, s.option.Port),
		Password: s.option.Password,
		DB:       s.option.DB,

		// 连接池配置
		PoolSize:     10,
		MinIdleConns: 5,

		// 超时配置
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,

		// 重试配置
		MaxRetries:      3,
		MinRetryBackoff: 8 * time.Millisecond,
		MaxRetryBackoff: 512 * time.Millisecond,
	})

	// 测试连接
	ctxWithTimeout, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := rdb.Ping(ctxWithTimeout).Result()
	if err != nil {
		ctx.CsmLogger().Errorf("Redis连接失败: %v", err)
		return errors.Wrap(err, "failed to connect to redis")
	}

	s.redisClient = rdb
	s.connected = true

	ctx.CsmLogger().Infof("Redis连接成功: %s:%d", s.option.Address, s.option.Port)
	return nil
}

// Disconnect 断开Redis连接
func (s *Service) Disconnect() error {
	if s.redisClient != nil {
		err := s.redisClient.Close()
		s.connected = false
		s.redisClient = nil
		return err
	}
	return nil
}

// IsConnected 检查是否已连接
func (s *Service) IsConnected() bool {
	return s.connected && s.redisClient != nil
}

// Set 设置键值对
func (s *Service) Set(ctx csmContext.CsmContext, key, value string) error {
	if !s.IsConnected() {
		return errors.New("redis not connected")
	}

	ctxWithTimeout, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := s.redisClient.Set(ctxWithTimeout, key, value, 0).Err()
	if err != nil {
		ctx.CsmLogger().Errorf("Redis设置键值失败 key=%s: %v", key, err)
		return errors.Wrapf(err, "failed to set redis key: %s", key)
	}

	ctx.CsmLogger().Debugf("Redis设置键值成功 key=%s", key)
	return nil
}

// Get 获取值
func (s *Service) Get(ctx csmContext.CsmContext, key string) (string, error) {
	if !s.IsConnected() {
		return "", errors.New("redis not connected")
	}

	ctxWithTimeout, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	val, err := s.redisClient.Get(ctxWithTimeout, key).Result()
	if err != nil {
		if err == redis.Nil {
			ctx.CsmLogger().Debugf("Redis键不存在 key=%s", key)
			return "", errors.Errorf("redis key not found: %s", key)
		}
		ctx.CsmLogger().Errorf("Redis获取值失败 key=%s: %v", key, err)
		return "", errors.Wrapf(err, "failed to get redis key: %s", key)
	}

	ctx.CsmLogger().Debugf("Redis获取值成功 key=%s", key)
	return val, nil
}

// Delete 删除键
func (s *Service) Delete(ctx csmContext.CsmContext, key string) error {
	if !s.IsConnected() {
		return errors.New("redis not connected")
	}

	ctxWithTimeout, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := s.redisClient.Del(ctxWithTimeout, key).Err()
	if err != nil {
		ctx.CsmLogger().Errorf("Redis删除键失败 key=%s: %v", key, err)
		return errors.Wrapf(err, "failed to delete redis key: %s", key)
	}

	ctx.CsmLogger().Debugf("Redis删除键成功 key=%s", key)
	return nil
}

// Exists 检查键是否存在
func (s *Service) Exists(ctx csmContext.CsmContext, key string) (bool, error) {
	if !s.IsConnected() {
		return false, errors.New("redis not connected")
	}

	ctxWithTimeout, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	count, err := s.redisClient.Exists(ctxWithTimeout, key).Result()
	if err != nil {
		ctx.CsmLogger().Errorf("Redis检查键存在失败 key=%s: %v", key, err)
		return false, errors.Wrapf(err, "failed to check redis key exists: %s", key)
	}

	exists := count > 0
	ctx.CsmLogger().Debugf("Redis检查键存在 key=%s, exists=%v", key, exists)
	return exists, nil
}
