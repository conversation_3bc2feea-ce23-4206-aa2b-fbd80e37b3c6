package redis

import (
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// ServiceInterface Redis服务接口
type ServiceInterface interface {
	// Connect 连接Redis
	Connect(ctx csmContext.CsmContext) error

	// Disconnect 断开Redis连接
	Disconnect() error

	// IsConnected 检查是否已连接
	IsConnected() bool

	// Set 设置键值对
	Set(ctx csmContext.CsmContext, key, value string) error

	// Get 获取值
	Get(ctx csmContext.CsmContext, key string) (string, error)

	// Delete 删除键
	Delete(ctx csmContext.CsmContext, key string) error

	// Exists 检查键是否存在
	Exists(ctx csmContext.CsmContext, key string) (bool, error)
}
