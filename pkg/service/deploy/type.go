package deploy

import "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"

// Params 部署 istio 集群参数
type Params struct {
	// ConfigPath 表示参数路径，用于生成 istio 使用的证书
	ConfigPath string
	// Version 表示安装的 istio 版本
	Version string
	// Namespace 表示 istio 安装的 namespace
	Namespace string
	// Region 表示 cce 集群所在的地域
	Region string
	// MeshInstanceId 版本 mesh 实例 id
	MeshInstanceId string
	// ClusterName 表示 cce 集群的名称
	ClusterName string
	// CceClusterUuid 表示 cce 集群 id
	CceClusterUuid string
	// IsRemote 表示是否远程集群
	IsRemote bool
	// InstanceRegion 表示实例所在的地域
	InstanceRegion string
	// DiscoverySelectorLabels 表示 istiod 选择性服务发现标签
	DiscoverySelectorLabels map[string]string
	// 支持多协议
	MultiProtocolEnabled bool
	// 开启链路追踪
	TraceEnabled bool
	// trace 采样率
	SamplingRate float64
	// trace 服务地址
	Address string
	// 列表安装参数
	IngressGateways []IngressGateway
}
type IngressGateway struct {
	Name      string `json:"name"`
	TypeLabel string `json:"typeLabel"`
	BindPort  string `json:"bindPort"`
	Replicas  int    `json:"replicas"`
	BindVpc   string `json:"bindVpc"`
}

// 初始化Higress安装参数
func NewHigressParams(version, namespace, region, meshInstanceId, clusterId string) *Params {
	return &Params{
		Version:        version,
		Namespace:      namespace,
		Region:         region,
		MeshInstanceId: meshInstanceId,
		CceClusterUuid: clusterId,
	}
}

// NewParams 初始化安装参数
func NewParams(version, namespace, region, meshInstanceId, clusterName,
	CceClusterUuid string) *Params {
	return &Params{
		Version:        version,
		Namespace:      namespace,
		Region:         region,
		MeshInstanceId: meshInstanceId,
		ClusterName:    clusterName,
		CceClusterUuid: CceClusterUuid,
	}
}

// IopParseParams 解析 istiooperator-iop 模板参数
type IopParseParams struct {
	// Hub 表示 istio 镜像地址
	Hub string
	// Tag 表示 istio 版本
	Tag string
	// Namespace 表示 istio 安装命名空间
	Namespace string
	// MeshInstanceId 表示 istio 实例 id
	MeshInstanceId string
	// ClusterName 表示 istio 集群名称
	ClusterName string
	// Network 表示 istio 所在的网络 network
	Network string
	// EastWestGatewayIstioLabel 表示 istio 东西向网关 istiod、vs 匹配标签
	EastWestGatewayIstioLabel string
	// DiscoveryAddress 表示 istio 远程集群地址
	DiscoveryAddress string
	// DiscoverySelectorLabels 表示 istio 选择性服务发现使用的标签
	DiscoverySelectorLabels map[string]string
	// MatchExpressions 表示 istio 选择性服务发现 label 表达式
	MatchExpressions []*meta.LabelSelectorRequirement
	// AccessLogFile 表示 istio 安装是否开启 access log，默认不开启
	AccessLogFile bool
	// Proxy 代理初始化参数
	Proxy *Resources
	// InitProxy init 初始化参数
	InitProxy *Resources
	// 支持多协议参数，替换成支持多协议参数
	ProxyImage string
	// trace 采样率
	SamplingRate float64
	// trace 服务地址
	Address string

	// 列表安装参数
	IngressGateways []IngressGateway
}

// NewIopParseParams 初始化 iop 解析模板参数
func NewIopParseParams(meshInstanceId, cluster, network, Tag, Hub string) *IopParseParams {
	return &IopParseParams{
		MeshInstanceId: meshInstanceId,
		ClusterName:    cluster,
		Network:        network,
		Tag:            Tag,
		Hub:            Hub,
	}
}

// NewHigressParseParams 初始化 iop 解析模板参数
func NewHigressParseParams(IngressGateways []IngressGateway) *IopParseParams {
	return &IopParseParams{
		IngressGateways: IngressGateways,
	}
}

// NewIngressParseParams 初始化 iop 解析模板参数
func NewIngressParseParams(Tag, Hub string, IngressGateways []IngressGateway) *IopParseParams {
	return &IopParseParams{
		Tag:             Tag,
		Hub:             Hub,
		IngressGateways: IngressGateways,
	}
}

// NewEastWestGatewayParseParams 初始化 iop 网关解析模板参数
func NewEastWestGatewayParseParams(network, tag, hub, eastWestGatewayIstioLabel string) *IopParseParams {
	return &IopParseParams{
		Tag:                       tag,
		Hub:                       hub,
		Network:                   network,
		EastWestGatewayIstioLabel: eastWestGatewayIstioLabel,
	}
}

// IstiodExposeGatewayVsParams 暴露 istiod 服务参数
type IstiodExposeGatewayVsParams struct {
	// Namespace 表示命名空间
	Namespace string
	// IstiodGatewayName 表示 istiod gateway 名称
	IstiodGatewayName string
	// IstiodVsName 表示 istiod vs 名称
	IstiodVsName string
	// EastWestGatewayIstioLabel 东西网关名称
	EastWestGatewayIstioLabel string
}

// NewIstiodExposeGatewayVsParams 初始化暴露 istiod 服务参数
func NewIstiodExposeGatewayVsParams(namespace, istiodGatewayName, istiodVsName, eastWestGatewayIstioLabel string) *IstiodExposeGatewayVsParams {
	return &IstiodExposeGatewayVsParams{
		Namespace:                 namespace,
		IstiodGatewayName:         istiodGatewayName,
		IstiodVsName:              istiodVsName,
		EastWestGatewayIstioLabel: eastWestGatewayIstioLabel,
	}
}

var (
	cceProxyRequests = map[string]string{"cpu": "100m", "memory": "128Mi"}
	cceProxyLimits   = map[string]string{"cpu": "2000m", "memory": "1024Mi"}

	cceInitProxyRequests = map[string]string{"cpu": "10m", "memory": "10Mi"}
	cceInitProxyLimits   = map[string]string{"cpu": "2000m", "memory": "1024Mi"}

	eksProxyRequests = map[string]string{"cpu": "1", "eks.baidu-int.com/cpu": "15", "ephemeral-storage": "10Gi", "memory": "2Gi"}
	eksProxyLimits   = map[string]string{"cpu": "1", "eks.baidu-int.com/cpu": "15", "ephemeral-storage": "10Gi", "memory": "2Gi"}

	eksInitProxyRequests = map[string]string{"cpu": "1", "eks.baidu-int.com/cpu": "15", "ephemeral-storage": "10Gi", "memory": "2Gi"}
	eksInitProxyLimits   = map[string]string{"cpu": "1", "eks.baidu-int.com/cpu": "15", "ephemeral-storage": "10Gi", "memory": "2Gi"}
)

type Resources struct {
	Requests map[string]string
	Limits   map[string]string
}

// NewCceResources 初始化配置资源
func NewCceResources(requests, limits map[string]string) *Resources {
	return &Resources{
		Requests: requests,
		Limits:   limits,
	}
}

// NewCceDefaultProxyResources 初始化 proxy 参数
func NewCceDefaultProxyResources() *Resources {
	return NewCceResources(cceProxyRequests, cceProxyLimits)
}

// NewCceDefaultInitProxyResources 初始化 init_proxy 参数
func NewCceDefaultInitProxyResources() *Resources {
	return NewCceResources(cceInitProxyRequests, cceInitProxyLimits)
}

// NewEksDefaultProxyResources 初始化 proxy 参数
func NewEksDefaultProxyResources() *Resources {
	return NewCceResources(eksProxyRequests, eksProxyLimits)
}

// NewEksDefaultInitProxyResources 初始化 init_proxy 参数
func NewEksDefaultInitProxyResources() *Resources {
	return NewCceResources(eksInitProxyRequests, eksInitProxyLimits)
}

type ConfigMapValues struct {
	Global *ConfigMapValuesGlobal `json:"global"`
}

type ConfigMapValuesGlobal struct {
	Proxy *Proxy `json:"proxy"`
}

type Proxy struct {
	Resources *ProxyResources `json:"resources"`
}

type ProxyResources struct {
	Limits   map[string]string `json:"limits"`
	Requests map[string]string `json:"requests"`
}
