package aiservices

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// ServiceInterface 定义服务接口
type ServiceInterface interface {
	// AddServices 批量添加服务
	AddServices(ctx csmContext.CsmContext, instanceId string, request *meta.AddServiceRequest, clusterId string) (*meta.AddServiceResponse, error)

	// GetServicesByInstanceID 查询实例下的服务列表
	GetServicesByInstanceID(ctx csmContext.CsmContext, instanceId string) ([]*meta.AIService, error)

	// DeleteService 删除服务
	DeleteService(ctx csmContext.CsmContext, instanceId string, serviceName, namespace string) error

	// GetServicesList 获取服务列表（带分页）
	GetServicesList(ctx csmContext.CsmContext, instanceId, keyword string, pageNo, pageSize int,
		orderBy, order, clusterId string) (*meta.ServiceListResponse, error)

	// GetServiceDetail 获取服务详情
	GetServiceDetail(ctx csmContext.CsmContext, instanceId, serviceName string) (*meta.ServiceDetailResponse, error)
}
