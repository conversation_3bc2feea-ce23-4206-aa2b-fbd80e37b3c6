package aiservices

import (
	"encoding/json"
	"net/http"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"

	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	modelaiservices "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/aiservices"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type Service struct {
	opt          *Option
	modelService modelaiservices.ServiceInterface
	cceService   cce.ClientInterface
}

// NewAIServiceService 创建服务实例
func NewAIServiceService(option *Option) *Service {
	return &Service{
		opt:          option,
		modelService: modelaiservices.NewAIServiceService(modelaiservices.NewOption(option.DB)),
	}
}

// AddServices 批量添加服务
func (s *Service) AddServices(ctx csmContext.CsmContext, instanceId string,
	request *meta.AddServiceRequest, clusterId string) (*meta.AddServiceResponse, error) {
	// 获取账户ID
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, err
	}

	// 获取Region
	region := ctx.Get(reg.ContextRegion).(string)

	// 获取实例现有的服务列表，用于幂等性校验
	existingServices, err := s.modelService.ListServices(ctx, instanceId, "", 1, 1000, "", "", clusterId)
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to get existing services: %v", err)
		return nil, err
	}

	// 构建现有服务名和命名空间的组合映射，用于快速查找
	existingServiceMap := make(map[string]bool)
	for _, service := range existingServices.Result {
		// 使用serviceName和Namespace的组合作为键
		serviceKey := service.ServiceName + ":" + service.Namespace
		existingServiceMap[serviceKey] = true
	}

	// 创建服务实例，过滤掉在相同命名空间下已存在的服务名
	services := make([]*meta.AIService, 0)
	for _, serviceName := range request.ServiceList {
		// 构建当前服务的唯一键
		serviceKey := serviceName + ":" + request.Namespace

		// 幂等性校验，只有在相同命名空间下的相同服务名才会被过滤
		if existingServiceMap[serviceKey] {
			ctx.CsmLogger().Infof("Service %s in namespace %s already exists, skipping",
				serviceName, request.Namespace)
			continue
		}

		service := meta.NewAIService(
			instanceId,
			serviceName,
			request.ServiceSource,
			request.ClusterID,
			request.Namespace,
			accountId,
			region,
		)
		services = append(services, service)
	}

	// 如果没有需要添加的服务，直接返回成功
	if len(services) == 0 {
		return &meta.AddServiceResponse{
			Success: true,
			Status:  http.StatusOK,
			Result: struct {
				AddedCount int `json:"addedCount"`
			}{
				AddedCount: 0,
			},
		}, nil
	}

	// 批量创建服务
	addedCount, err := s.modelService.BatchCreateServices(ctx, services)
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to add services: %v", err)
		return nil, err
	}

	// 构建响应
	response := &meta.AddServiceResponse{
		Success: true,
		Status:  http.StatusOK,
		Result: struct {
			AddedCount int `json:"addedCount"`
		}{
			AddedCount: addedCount,
		},
	}

	return response, nil
}

// GetServicesByInstanceID 查询实例下的服务列表
func (s *Service) GetServicesByInstanceID(ctx csmContext.CsmContext, instanceId string) ([]*meta.AIService, error) {
	return s.modelService.GetServicesByInstanceID(ctx, instanceId)
}

// DeleteService 删除服务
func (s *Service) DeleteService(ctx csmContext.CsmContext, instanceId string, serviceName, namespace string) error {
	return s.modelService.DeleteService(ctx, instanceId, serviceName, namespace)
}

// GetServicesList 获取服务列表
func (s *Service) GetServicesList(ctx csmContext.CsmContext, instanceId, keyword string,
	pageNo, pageSize int, orderBy, order, clusterId string) (*meta.ServiceListResponse, error) {
	// 查询服务列表
	serviceList, err := s.modelService.ListServices(ctx, instanceId, keyword, pageNo, pageSize, orderBy, order, clusterId)
	if err != nil {
		return nil, err
	}

	// 构造响应
	response := &meta.ServiceListResponse{
		Success: true,
		Status:  http.StatusOK,
		Page:    *serviceList,
	}

	return response, nil
}

// GetServiceDetail 获取服务详情
func (s *Service) GetServiceDetail(ctx csmContext.CsmContext, instanceId, serviceName string) (*meta.ServiceDetailResponse, error) {
	// 查询服务
	service, err := s.modelService.GetServiceDetail(ctx, instanceId, serviceName)
	if err != nil {
		return nil, err
	}

	// 默认路由数量为0，实际项目中需要查询关联的路由数量
	routeCount := 0

	// 初始化端口信息
	var servicePorts []string

	// 解析端口信息字符串为数组
	if service.ServicePort != "" {
		err = json.Unmarshal([]byte(service.ServicePort), &servicePorts)
		if err != nil {
			ctx.CsmLogger().Errorf("Failed to unmarshal service port info: %v", err)
		}
	}

	// 构造响应
	response := &meta.ServiceDetailResponse{
		Success: true,
		Status:  http.StatusOK,
		Result: meta.ServiceDetailResult{
			ClusterID:     service.ClusterID,
			Namespace:     service.Namespace,
			RouteCount:    routeCount,
			ServiceSource: service.ServiceSource,
			ServicePort:   servicePorts,
		},
	}

	return response, nil
}
