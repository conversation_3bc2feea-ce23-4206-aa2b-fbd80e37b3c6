package whitelist

import "github.com/spf13/viper"

// RequestBody 白名单访问请求体 http://wiki.baidu.com/pages/viewpage.action?pageId=637881890#1%20查询用户是否在白名单内
type RequestBody struct {
	FeatureType string `json:"featureType"`
	AclType     string `json:"aclType"`
	AclName     string `json:"aclName"`
	// TODO: Region	string	`json:"region"` (optional)
}

// NewRequestBody 新建白名单访问请求体
func NewRequestBody(aclName string) *RequestBody {
	return &RequestBody{
		FeatureType: viper.GetString("userWhiteList.requestBody.featureType"),
		AclType:     viper.GetString("userWhiteList.requestBody.aclType"),
		AclName:     aclName,
		// TODO: Region: region, (optional)
	}
}
