package whitelist

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	CheckCsmWhiteList(ctx csmContext.CsmContext, mrp *meta.CsmMeshRequestParams) (*meta.WhiteListResult, error)
	CheckCseWhiteList(ctx csmContext.CsmContext, mrp *meta.CsmMeshRequestParams) (*meta.WhiteListResult, error)
}
