package whitelist

import (
	"context"
	"encoding/json"

	"github.com/spf13/viper"
	"gopkg.in/resty.v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/auth"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/monitor"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/restclient"
)

type Service struct {
	// white list client
	wlClient *resty.Client
}

func NewWhiteListService() *Service {
	return &Service{
		wlClient: restclient.NewBCERestClient("whiteList").SetHostURL(viper.GetString("userWhiteList.host")),
	}
}

func (s *Service) CheckCsmWhiteList(cc csmContext.CsmContext, mrp *meta.CsmMeshRequestParams) (
	*meta.WhiteListResult, error) {
	ctx, err := s.getRequestContext(cc)
	if err != nil {
		cc.CsmLogger().Infof("failed to get context with value, because ", err)
		return nil, err
	}
	req := s.wlClient.R().SetContext(ctx)
	req.SetBody(NewRequestBody(mrp.AccountID))
	res, err := req.Post(
		viper.GetString("userWhiteList.endpoint") + viper.GetString("userWhiteList.requestPath"))
	if err != nil {
		cc.CsmLogger().Infof("failed to check csm white list, because ", err)
		return nil, err
	}
	var result meta.WhiteListResult
	err = json.Unmarshal(res.Body(), &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (s *Service) getRequestContext(cc csmContext.CsmContext) (context.Context, error) {
	authKey := auth.NewBceAuthKey(iam.DefaultClient.AccessKey(), iam.DefaultClient.SecretKey())
	ctx := context.WithValue(context.Background(), restclient.BceAuthContextKey, *authKey)
	ctx = context.WithValue(ctx, monitor.ContextKeyBceRequestId, cc.RequestID())
	return ctx, nil
}

func (s *Service) CheckCseWhiteList(cc csmContext.CsmContext, mrp *meta.CsmMeshRequestParams) (
	*meta.WhiteListResult, error) {
	ctx, err := s.getRequestContext(cc)
	if err != nil {
		cc.CsmLogger().Infof("failed to get context with value, because ", err)
		return nil, err
	}
	req := s.wlClient.R().SetContext(ctx)
	req.SetBody(map[string]string{
		"featureType": cc.QueryParam("featureType"),
		"aclType":     "AccountId",
		"aclName":     mrp.AccountID,
		"region":      mrp.Region,
	})
	res, err := req.Post(
		viper.GetString("userWhiteList.endpoint") + viper.GetString("userWhiteList.requestPath"))
	if err != nil {
		cc.CsmLogger().Infof("failed to check cse white list, because ", err)
		return nil, err
	}
	var result meta.WhiteListResult
	err = json.Unmarshal(res.Body(), &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}
