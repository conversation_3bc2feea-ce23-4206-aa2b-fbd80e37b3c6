package cce

import (
	"testing"
	"time"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"

	model_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

func TestKubeClientCache(t *testing.T) {
	// 设置测试配置
	viper.Set("cloud.cce.kube_client_cache.enabled", true)
	viper.Set("cloud.cce.kube_client_cache.default_ttl", 1*time.Minute)
	viper.Set("cloud.cce.kube_client_cache.cleanup_interval", 30*time.Second)

	// 创建服务实例
	service := NewClientService()

	// 验证缓存初始化
	assert.NotNil(t, service.kubeClientCache)
	assert.True(t, service.option.KubeClientCacheEnabled)
	assert.Equal(t, 1*time.Minute, service.option.KubeClientCacheDefaultTTL)
	assert.Equal(t, 30*time.Second, service.option.KubeClientCacheCleanupInterval)
}

func TestBuildCacheKey(t *testing.T) {
	service := NewClientService()

	// 测试普通缓存键构建
	key1 := service.buildCacheKey("bj", "cluster-123", model_meta.StandaloneMeshType)
	expected1 := "kube_client:bj:cluster-123:standalone:prod"
	assert.Equal(t, expected1, key1)

	// 测试开发模式缓存键构建
	viper.Set("local.dev", true)
	key2 := service.buildCacheKey("bj", "cluster-123", model_meta.StandaloneMeshType)
	expected2 := "kube_client:bj:cluster-123:standalone:dev"
	assert.Equal(t, expected2, key2)

	// 重置配置
	viper.Set("local.dev", false)
}

func TestBuildSugarCacheKey(t *testing.T) {
	service := NewClientService()

	key := service.buildSugarCacheKey("account-123", "bj", "cluster-456", model_meta.HostingMeshType)
	expected := "sugar_kube_client:account-123:bj:cluster-456:hosting"
	assert.Equal(t, expected, key)
}

func TestCacheManagement(t *testing.T) {
	// 设置测试配置
	viper.Set("cloud.cce.kube_client_cache.enabled", true)

	service := NewClientService()
	ctx := context.MockNewCsmContext()

	// 测试缓存统计
	stats := service.GetKubeClientCacheStats(ctx)
	assert.True(t, stats["enabled"].(bool))
	assert.Equal(t, 0, stats["item_count"].(int))

	// 测试清理缓存（不会报错）
	service.ClearKubeClientCache(ctx)
	service.ClearKubeClientCacheByKey(ctx, "bj", "cluster-123", model_meta.StandaloneMeshType)
	service.ClearSugarKubeClientCacheByKey(ctx, "account-123", "bj", "cluster-456", model_meta.HostingMeshType)
}

func TestCacheDisabled(t *testing.T) {
	// 设置缓存禁用
	viper.Set("cloud.cce.kube_client_cache.enabled", false)

	service := NewClientService()
	ctx := context.MockNewCsmContext()

	// 验证缓存未初始化
	assert.Nil(t, service.kubeClientCache)
	assert.False(t, service.option.KubeClientCacheEnabled)

	// 测试缓存统计
	stats := service.GetKubeClientCacheStats(ctx)
	assert.False(t, stats["enabled"].(bool))

	// 测试清理缓存（不会报错）
	service.ClearKubeClientCache(ctx)
}
