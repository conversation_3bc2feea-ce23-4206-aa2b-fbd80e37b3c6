package cce

import (
	"fmt"
	"github.com/patrickmn/go-cache"
	"github.com/spf13/viper"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"strings"

	cce_v1 "github.com/baidubce/bce-sdk-go/services/cce"
	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	model_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	service_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

// Service for cce
type Service struct {
	option *Option
	// kubeClientCache 用于缓存 kube.Client 实例
	kubeClientCache *cache.Cache
}

// NewClientService 初始化某个地域的 cce client
func NewClientService() *Service {
	option := NewOption()
	svc := &Service{
		option: option,
	}

	// 初始化 kube client 缓存
	if option.KubeClientCacheEnabled {
		svc.kubeClientCache = cache.New(option.KubeClientCacheDefaultTTL, option.KubeClientCacheCleanupInterval)
	}

	return svc
}

func (service *Service) getCceClient(ctx csmContext.CsmContext, region string, meshType model_meta.MeshType) (*cce.Client, error) {
	endpoint := service.option.CceEndpoint
	switch {
	case meshType == model_meta.HostingMeshType:
		endpoint = fmt.Sprintf(constants.HostingMeshEndpoint, region)
	case strings.Contains(endpoint, "%s"):
		endpoint = fmt.Sprintf(endpoint, region)
	}
	ctx.CsmLogger().Infof("getCceClient endpoint=%s", endpoint)
	client := &cce.Client{}
	var err error
	if meshType == model_meta.HostingMeshType {
		ctx.CsmLogger().Infof("*** get GetAdminKubeConfig with aksk ***")
		client, err = cce.GetClientWithAkSk(ctx, service.option.AccessKey, service.option.SecretKey, endpoint)
	} else {
		ctx.CsmLogger().Infof("*** get GetAdminKubeConfig with sts ***")
		client, err = cce.GetClient(ctx, endpoint)
	}
	return client, err
}

func (service *Service) GetSugarCceClient(ctx csmContext.CsmContext, accountID, region string) (*cce.Client, error) {
	endpoint := fmt.Sprintf(service.option.CceEndpoint, region)
	client := &cce.Client{}
	var err error
	user := &sdkIAM.User{
		ID: accountID,
		Domain: sdkIAM.UserDomain{
			ID: accountID,
		},
	}
	ctx.Set(iam.ContextIAMUser, user)
	ctx.CsmLogger().Infof("*** accountID %s get sugar client ***", accountID)
	client, err = cce.GetSugarClient(ctx, service.option.RoleName, service.option.ClientProfile, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("accountID %s GetClient error is %v", accountID, err)
	}
	return client, err
}

// NewClient creates a Kubernetes client for CCE.
func (service *Service) NewClient(ctx csmContext.CsmContext, region, clusterUUID string, meshType model_meta.MeshType) (kube.Client, error) {
	ctx.CsmLogger().Infof("*** NewClient start: region=%s, clusterUUID=%s, meshType=%s ***", region, clusterUUID, meshType)

	// 如果启用了缓存，尝试从缓存中获取 kube client
	if service.option.KubeClientCacheEnabled && service.kubeClientCache != nil {
		cacheKey := service.buildCacheKey(region, clusterUUID, meshType)
		ctx.CsmLogger().Infof("*** Checking cache for kube client, cacheKey=%s ***", cacheKey)

		if cachedClient, found := service.kubeClientCache.Get(cacheKey); found {
			ctx.CsmLogger().Infof("*** Cache hit! Returning cached kube client for key=%s ***", cacheKey)
			return cachedClient.(kube.Client), nil
		}
		ctx.CsmLogger().Infof("*** Cache miss for key=%s, creating new kube client ***", cacheKey)
	}

	// 原有逻辑：创建新的 kube client
	ctx.CsmLogger().Infof("*** Creating new kube client: determining kube config type ***")
	kct := cce_v2.KubeConfigTypeInternal
	if viper.GetBool("local.dev") {
		ctx.CsmLogger().Infof("*** local develop mode to get NewClient ***")
		kct = cce_v2.KubeConfigTypePublic
	}

	ctx.CsmLogger().Infof("*** Getting admin kube config: region=%s, clusterUUID=%s, kct=%s ***", region, clusterUUID, kct)
	kubeConfig, err := service.GetAdminKubeConfig(ctx, region, clusterUUID, kct, meshType)
	if err != nil {
		ctx.CsmLogger().Errorf("*** Failed to get admin kube config: %v ***", err)
		return nil, err
	}

	ctx.CsmLogger().Infof("*** Creating kube client from config bytes ***")
	client, err := kube.NewClientWithKubeConfigBytes([]byte(kubeConfig.KubeConfig))
	if err != nil {
		ctx.CsmLogger().Errorf("*** Failed to create kube client: %v ***", err)
		return nil, err
	}

	// 如果启用了缓存，将新创建的 client 存入缓存
	if service.option.KubeClientCacheEnabled && service.kubeClientCache != nil {
		cacheKey := service.buildCacheKey(region, clusterUUID, meshType)
		ctx.CsmLogger().Infof("*** Caching kube client with key=%s, TTL=%v ***", cacheKey, service.option.KubeClientCacheDefaultTTL)
		service.kubeClientCache.Set(cacheKey, client, cache.DefaultExpiration)
	}

	ctx.CsmLogger().Infof("*** NewClient completed successfully: region=%s, clusterUUID=%s ***", region, clusterUUID)
	return client, nil
}

// buildCacheKey 构建缓存键
func (service *Service) buildCacheKey(region, clusterUUID string, meshType model_meta.MeshType) string {
	// 考虑本地开发模式的差异
	devMode := "prod"
	if viper.GetBool("local.dev") {
		devMode = "dev"
	}
	return fmt.Sprintf("kube_client:%s:%s:%s:%s", region, clusterUUID, meshType, devMode)
}

func (service *Service) NewSugarClient(ctx csmContext.CsmContext, accountID, region, clusterUUID string, meshType model_meta.MeshType) (kube.Client, error) {
	ctx.CsmLogger().Infof("*** NewSugarClient start: accountID=%s, region=%s, clusterUUID=%s, meshType=%s ***", accountID, region, clusterUUID, meshType)

	// 如果启用了缓存，尝试从缓存中获取 kube client
	if service.option.KubeClientCacheEnabled && service.kubeClientCache != nil {
		cacheKey := service.buildSugarCacheKey(accountID, region, clusterUUID, meshType)
		ctx.CsmLogger().Infof("*** Checking cache for sugar kube client, cacheKey=%s ***", cacheKey)

		if cachedClient, found := service.kubeClientCache.Get(cacheKey); found {
			ctx.CsmLogger().Infof("*** Cache hit! Returning cached sugar kube client for key=%s ***", cacheKey)
			return cachedClient.(kube.Client), nil
		}
		ctx.CsmLogger().Infof("*** Cache miss for key=%s, creating new sugar kube client ***", cacheKey)
	}

	// 原有逻辑：创建新的 sugar kube client
	ctx.CsmLogger().Infof("*** Creating new sugar kube client: determining kube config type ***")
	kct := cce_v2.KubeConfigTypeInternal

	ctx.CsmLogger().Infof("*** Getting sugar CCE client for accountID=%s, region=%s ***", accountID, region)
	client, err := service.GetSugarCceClient(ctx, accountID, region)
	if err != nil {
		ctx.CsmLogger().Errorf("*** Failed to get sugar CCE client: %v ***", err)
		return nil, err
	}

	ctx.CsmLogger().Infof("*** Getting admin kube config via sugar client ***")
	getKubeConfigArgs := &cce_v2.GetKubeConfigArgs{
		ClusterID:      clusterUUID,
		KubeConfigType: kct,
	}
	kubeConfig, err := client.GetAdminKubeConfig(getKubeConfigArgs)
	if err != nil {
		ctx.CsmLogger().Errorf("*** Failed to get admin kube config via sugar client: %v ***", err)
		return nil, err
	}

	ctx.CsmLogger().Infof("*** Creating kube client from sugar config bytes ***")
	kubeClient, err := kube.NewClientWithKubeConfigBytes([]byte(kubeConfig.KubeConfig))
	if err != nil {
		ctx.CsmLogger().Errorf("*** Failed to create kube client from sugar config: %v ***", err)
		return nil, err
	}

	// 如果启用了缓存，将新创建的 client 存入缓存
	if service.option.KubeClientCacheEnabled && service.kubeClientCache != nil {
		cacheKey := service.buildSugarCacheKey(accountID, region, clusterUUID, meshType)
		ctx.CsmLogger().Infof("*** Caching sugar kube client with key=%s, TTL=%v ***", cacheKey, service.option.KubeClientCacheDefaultTTL)
		service.kubeClientCache.Set(cacheKey, kubeClient, cache.DefaultExpiration)
	}

	ctx.CsmLogger().Infof("*** NewSugarClient completed successfully: accountID=%s, region=%s, clusterUUID=%s ***", accountID, region, clusterUUID)
	return kubeClient, nil
}

// buildSugarCacheKey 构建 Sugar Client 的缓存键
func (service *Service) buildSugarCacheKey(accountID, region, clusterUUID string, meshType model_meta.MeshType) string {
	return fmt.Sprintf("sugar_kube_client:%s:%s:%s:%s", accountID, region, clusterUUID, meshType)
}

// GetCCEClusterKubeConfigByClusterUUID 通过 cce cluster_uuid 查询对应的 kubeconfig
// TODO 我们应当使用 GetAdminKubeConfig
func (service *Service) GetCCEClusterKubeConfigByClusterUUID(cc csmContext.CsmContext, region, clusterUuid string,
	kct cce_v2.KubeConfigType, meshType model_meta.MeshType) ([]byte, error) {
	result, err := service.GetAdminKubeConfig(cc, region, clusterUuid, kct, meshType)
	if err != nil {
		return nil, err
	}
	return []byte(result.KubeConfig), nil
}

func (service *Service) GetAdminKubeConfig(ctx csmContext.CsmContext, region, clusterID string,
	kct cce_v2.KubeConfigType, meshType model_meta.MeshType) (*cce_v2.GetKubeConfigResponse, error) {
	client, err := service.getCceClient(ctx, region, meshType)
	if err != nil {
		return nil, err
	}
	getKubeConfigArgs := &cce_v2.GetKubeConfigArgs{
		ClusterID:      clusterID,
		KubeConfigType: kct,
	}
	return client.GetAdminKubeConfig(getKubeConfigArgs)
}

// GetCCECluster 获取集群的详细信息
func (service *Service) GetCCECluster(ctx csmContext.CsmContext, region, clusterID string) (*cce_v1.GetClusterResult, error) {
	client, err := service.getCceClient(ctx, region, model_meta.StandaloneMeshType)
	if err != nil {
		return nil, err
	}
	// v2 版本的接口缺少 vpcName，所以使用 v1 版本接口
	return client.CceClientV1.GetCluster(clusterID)
}

// GetCCEClusterList 通过 cce api 获取 cce 集群列表
func (service *Service) GetCCEClusterList(ctx csmContext.CsmContext, region string) ([]service_meta.MeshCluster, error) {
	client, err := service.getCceClient(ctx, region, model_meta.StandaloneMeshType)
	if err != nil {
		return nil, err
	}
	// 获取所有的集群，目前 CCE 集群数量不会超过 100
	args := &cce_v2.ListClustersArgs{
		PageNum:  1,
		PageSize: 1000,
	}
	result, err := client.CceClientV2.ListClusters(args)
	if err != nil {
		return nil, err
	}
	var meshCluster []service_meta.MeshCluster
	if result != nil && result.ClusterPage != nil {
		clusterList := result.ClusterPage.ClusterList
		for _, cce := range clusterList {
			cceCluster := service_meta.MeshCluster{
				ClusterName: cce.Spec.ClusterName,
				ClusterType: string(cce.Spec.ClusterType),
				ClusterUuid: cce.Spec.ClusterID,
				Version:     string(cce.Spec.K8SVersion),
				Region:      region,
				VpcId:       cce.Spec.VPCID,
				VpcCidr:     cce.Spec.VPCCIDR,
			}
			meshCluster = append(meshCluster, cceCluster)
		}
	}
	return meshCluster, nil
}

func (service *Service) GetCCEClusterV2(ctx csmContext.CsmContext, region, clusterID string) (*cce_v2.GetClusterResponse, error) {
	client, err := service.getCceClient(ctx, region, model_meta.StandaloneMeshType)
	if err != nil {
		return nil, err
	}
	return client.CceClientV2.GetCluster(clusterID)
}

func (service *Service) GetCCEClustersV2(ctx csmContext.CsmContext, region,
	keywordType, keyword string) (*cce_v2.ListClustersResponse, error) {
	client, err := service.getCceClient(ctx, region, model_meta.StandaloneMeshType)
	if err != nil {
		return nil, err
	}
	args := &cce_v2.ListClustersArgs{
		PageNum:     1,
		PageSize:    100,
		KeywordType: cce_v2.ClusterKeywordType(keywordType),
		Keyword:     keyword,
	}

	return client.CceClientV2.ListClusters(args)
}

// GetClusterAddonByClusterId 根据集群ID获取集群插件列表
//
// 参数：
//
//	service: Service 结构体指针，包含了需要使用的服务实例
//	ctx: CsmContext 结构体指针，表示上下文信息
//	region: string 类型，表示地域信息
//	clusterID: string 类型，表示集群ID
//	kct: KubeConfigType 类型，表示KubeConfig类型
//	meshType: MeshType 类型，表示网格类型
//	addons: string 类型，表示插件名称
//
// 返回值：
//
//	*AddonResponse: AddonResponse 结构体指针，表示插件列表信息
//	error: 错误信息，如果执行成功则为nil
func (service *Service) GetClusterAddonByClusterId(ctx csmContext.CsmContext, region, clusterID string,
	meshType model_meta.MeshType, addons string) (*cce.AddonResponse, error) {
	ctx.CsmLogger().Infof("Service GetClusterAddonByClusterId getCceClient start, clusterID: %s", clusterID)
	client, err := service.getCceClient(ctx, region, meshType)
	if err != nil {
		return nil, err
	}
	ctx.CsmLogger().Infof("GetClusterAddonByClusterId start, clusterID: %s", clusterID)
	result, err := client.GetClusterAddonByClusterId(clusterID, addons)
	if err != nil {
		ctx.CsmLogger().Warnf("GetClusterAddonByClusterId error, clusterID: %s", clusterID)

		return nil, err
	}
	ctx.CsmLogger().Infof("GetClusterAddonByClusterId successful, clusterID: %s", clusterID)
	return result, nil
}

// GetCCEClusterInstances 获取集群节点列表
func (service *Service) GetCCEClusterInstances(ctx csmContext.CsmContext, region, clusterID string) (*cce_v2.ListInstancesResponse, error) {
	client, err := service.getCceClient(ctx, region, model_meta.StandaloneMeshType)
	if err != nil {
		return nil, err
	}
	args := &cce_v2.ListInstancesByPageArgs{
		ClusterID: clusterID,
		Params:    &cce_v2.ListInstancesByPageParams{},
	}
	return client.CceClientV2.ListInstancesByPage(args)
}

// ClearKubeClientCache 清理所有 kube client 缓存
func (service *Service) ClearKubeClientCache(ctx csmContext.CsmContext) {
	if service.kubeClientCache != nil {
		ctx.CsmLogger().Infof("*** Clearing all kube client cache ***")
		service.kubeClientCache.Flush()
		ctx.CsmLogger().Infof("*** Kube client cache cleared successfully ***")
	}
}

// ClearKubeClientCacheByKey 根据键清理特定的 kube client 缓存
func (service *Service) ClearKubeClientCacheByKey(ctx csmContext.CsmContext, region, clusterUUID string, meshType model_meta.MeshType) {
	if service.kubeClientCache != nil {
		cacheKey := service.buildCacheKey(region, clusterUUID, meshType)
		ctx.CsmLogger().Infof("*** Clearing kube client cache for key=%s ***", cacheKey)
		service.kubeClientCache.Delete(cacheKey)
		ctx.CsmLogger().Infof("*** Kube client cache cleared for key=%s ***", cacheKey)
	}
}

// ClearSugarKubeClientCacheByKey 根据键清理特定的 sugar kube client 缓存
func (service *Service) ClearSugarKubeClientCacheByKey(ctx csmContext.CsmContext, accountID, region, clusterUUID string, meshType model_meta.MeshType) {
	if service.kubeClientCache != nil {
		cacheKey := service.buildSugarCacheKey(accountID, region, clusterUUID, meshType)
		ctx.CsmLogger().Infof("*** Clearing sugar kube client cache for key=%s ***", cacheKey)
		service.kubeClientCache.Delete(cacheKey)
		ctx.CsmLogger().Infof("*** Sugar kube client cache cleared for key=%s ***", cacheKey)
	}
}

// GetKubeClientCacheStats 获取缓存统计信息
func (service *Service) GetKubeClientCacheStats(ctx csmContext.CsmContext) map[string]interface{} {
	stats := make(map[string]interface{})

	if service.kubeClientCache != nil {
		itemCount := service.kubeClientCache.ItemCount()
		stats["enabled"] = true
		stats["item_count"] = itemCount
		stats["default_ttl"] = service.option.KubeClientCacheDefaultTTL.String()
		stats["cleanup_interval"] = service.option.KubeClientCacheCleanupInterval.String()

		ctx.CsmLogger().Infof("*** Kube client cache stats: item_count=%d, default_ttl=%v, cleanup_interval=%v ***",
			itemCount, service.option.KubeClientCacheDefaultTTL, service.option.KubeClientCacheCleanupInterval)
	} else {
		stats["enabled"] = false
		ctx.CsmLogger().Infof("*** Kube client cache is disabled ***")
	}

	return stats
}
