package cce

import (
	"time"

	"github.com/spf13/viper"
)

const (
	cceEndpoint    = "cloud.cce.endpoint"
	cceAccessKey   = "cloud.hosting.access_key"
	cceSecretKey   = "cloud.hosting.secret_key"
	BceIamProfile  = "cloud.iamProfile"
	BceServiceRole = "bceServiceRole"

	// KubeClient 缓存相关配置
	kubeClientCacheEnabled         = "cloud.cce.kube_client_cache.enabled"
	kubeClientCacheDefaultTTL      = "cloud.cce.kube_client_cache.default_ttl"
	kubeClientCacheCleanupInterval = "cloud.cce.kube_client_cache.cleanup_interval"
)

// Option CCE 配置
type Option struct {
	// cce endpoint
	CceEndpoint string
	// AccessKey 资源账号托管集群使用的 ak
	AccessKey string
	// SecretKey 资源账号托管集群使用的 sk
	SecretKey string

	RoleName      string
	ClientProfile string

	// KubeClient 缓存配置
	KubeClientCacheEnabled         bool
	KubeClientCacheDefaultTTL      time.Duration
	KubeClientCacheCleanupInterval time.Duration
}

// NewOption 初始化 cce 配置
func NewOption() *Option {
	// 设置缓存配置的默认值
	viper.SetDefault(kubeClientCacheEnabled, true)
	viper.SetDefault(kubeClientCacheDefaultTTL, 10*time.Minute)
	viper.SetDefault(kubeClientCacheCleanupInterval, 5*time.Minute)

	return &Option{
		CceEndpoint:   viper.GetString(cceEndpoint),
		AccessKey:     viper.GetString(cceAccessKey),
		SecretKey:     viper.GetString(cceSecretKey),
		RoleName:      viper.GetString(BceServiceRole),
		ClientProfile: viper.GetString(BceIamProfile),

		// 缓存配置
		KubeClientCacheEnabled:         viper.GetBool(kubeClientCacheEnabled),
		KubeClientCacheDefaultTTL:      viper.GetDuration(kubeClientCacheDefaultTTL),
		KubeClientCacheCleanupInterval: viper.GetDuration(kubeClientCacheCleanupInterval),
	}
}
