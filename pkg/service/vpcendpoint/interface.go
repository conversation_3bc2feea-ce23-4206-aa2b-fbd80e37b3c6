package vpcendpoint

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// ServiceInterface VPC Endpoint服务接口
type ServiceInterface interface {
	// CreateVpcEndpoint 创建VPC Endpoint
	CreateVpcEndpoint(ctx context.CsmContext, req *meta.CreateVpcEndpointRequest, region string) (*meta.CreateVpcEndpointResult, error)
}
