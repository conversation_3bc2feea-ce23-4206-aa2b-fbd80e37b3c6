package vpcendpoint

import (
	"fmt"
	"time"

	"github.com/baidubce/bce-sdk-go/services/bcc/api"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/http"
	"github.com/baidubce/bce-sdk-go/util"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/vpc/endpoint"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

const (
	URI_PREFIX         = bce.URI_PREFIX + "v1"
	ENDPOINT_URL_PATH  = "/vpcEndpoint/getOrCreate"
	RESOURCE_ACCOUNTID = "02fa0a988caf43a9a5c0270310347581"
	RESOURCE_HEXKEY    = "Hvw1j33tecGBBkVY"
	RESOURCE_SOURCE    = "cce"
)

// Service 实现VPC Endpoint服务接口
type Service struct {
	option *Option
}

// NewService 创建VPC Endpoint服务
func NewService(opt *Option) *Service {
	return &Service{
		option: opt,
	}
}

// CreateVpcEndpoint 创建VPC Endpoint
func (s *Service) CreateVpcEndpoint(ctx context.CsmContext, req *meta.CreateVpcEndpointRequest, region string) (*meta.CreateVpcEndpointResult, error) {
	// 构建BCE Client
	bceEndpoint := fmt.Sprintf("bcc.%s.baidubce.com", region)
	bceClient, err := endpoint.GetBceHostClient(ctx, bceEndpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to create BCE client: %v", err)
		return nil, err
	}

	// 生成ClientToken（如果未提供）
	clientToken := req.ClientToken
	if clientToken == "" {
		clientToken = util.NewUUID()
	}

	// 构建响应体
	result := &meta.CreateVpcEndpointResult{}

	// 调用API
	err = bce.NewRequestBuilder(bceClient).
		WithURL(URI_PREFIX+ENDPOINT_URL_PATH).
		WithMethod(http.POST).
		WithBody(req).
		WithQueryParamFilter("clientToken", clientToken).
		WithHeader("resource-accountId", encryptAccountId(RESOURCE_HEXKEY, RESOURCE_ACCOUNTID)).
		WithHeader("resource-source", RESOURCE_SOURCE).
		WithResult(result).
		Do()

	if err != nil {
		ctx.CsmLogger().Errorf("Failed to create VPC endpoint: %v", err)
		return nil, err
	}

	return result, nil
}
func encryptAccountId(hexKey string, accountId string) string {
	tmStr := time.Now().UTC().Format("2006-01-02T15:04:05Z")
	content := accountId + "/" + tmStr
	src, _ := api.Aes128EncryptUseSecreteKey(hexKey, content)
	return src
}
