package csm

import "fmt"

type GenericError struct {
	RequestID string `json:"requestId"`
	Code      string `json:"code"`
	Message   string `json:"message"`
}

func (e *GenericError) Error() string {
	return fmt.Sprintf("CSM error w/ message=%s, code=%s, requestID=%s",
		e.Message, e.Code, e.RequestID)
}

func (e *GenericError) String() string {
	return e.Message
}

type ResponseError struct {
	GenericError
	StatusCode int
}

func (e *ResponseError) Error() string {
	return fmt.Sprintf("CSM Response error: message=%s, code=%s, requestID=%s, status=%d",
		e.Message, e.Code, e.RequestID, e.StatusCode)
}

func (e *ResponseError) String() string {
	return e.Message
}
