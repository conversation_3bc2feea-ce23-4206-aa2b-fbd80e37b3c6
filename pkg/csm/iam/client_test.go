package iam

import (
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam/sts_credential"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
)

func TestNewAuthRequestByHttp(t *testing.T) {
	req1 := httptest.NewRequest(http.MethodGet, "/?authorization=param", nil)
	res1 := NewAuthRequestByHttp(req1)
	assert.Equal(t, "param", res1.Authorization, "authorization mismatch")

	req2 := httptest.NewRequest(http.MethodGet, "/", nil)
	req2.Header.Set("Authorization", "header")
	res2 := NewAuthRequestByHttp(req2)
	assert.Equal(t, "header", res2.Authorization, "authorization mismatch")
}

func TestClient_AuthenticateWithToken(t *testing.T) {
	ass := assert.New(t)
	tests := []struct {
		name         string
		subjectToken string
		requestID    string
		result       *Token
		err          error
		config       *util.TestServerConfig
	}{
		{
			name:         "normal",
			subjectToken: "subjectToken",
			requestID:    "request-id",
			result:       &Token{ID: "subjectToken"},
			config: &util.TestServerConfig{
				RequestMethod:   http.MethodGet,
				RequestURLPath:  "/v3/auth/tokens",
				RequestHeaders:  map[string]string{"X-Subject-Token": "subjectToken"},
				ResponseHeaders: map[string]string{"Content-Type": "application/json"},
				ResponseBody:    []byte(`{"token":{"id":"subjectToken","expires_at":"0001-01-01T00:00:00Z","issued_at":"0001-01-01T00:00:00Z","methods":null,"domain":null,"project":null,"user":{"name":""},"roles":null,"catalog":null}}`),
			},
		}, {
			name: "bce-error",
			err: &csm.ResponseError{
				GenericError: csm.GenericError{RequestID: "request-id-srk"},
				StatusCode:   http.StatusBadRequest,
			},
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
				ResponseHeaders:    map[string]string{"Content-Type": "application/json"},
				ResponseBody:       []byte(`{"requestId":"request-id-srk","code":"","message":""}`),
			},
		}, {
			name: "non-bce-error",
			err:  errors.New("cannot get bce.Error from response"),
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			svr := util.NewTestServer(t, test.config)
			defer svr.Close()
			client := NewMockClient(svr.URL)
			actualResult, actualErr := client.AuthenticateWithToken(test.subjectToken, test.requestID)
			ass.Equal(test.result, actualResult, "result mismatch")
			if test.err != nil {
				ass.NotNil(actualErr)
			}
		})
	}
}

func TestClient_AuthenticateAKSKSignature(t *testing.T) {
	ass := assert.New(t)
	ctx, _ := context.NewCsmContextMock()
	ctxReq := ctx.Request()
	ctxReq.Method = http.MethodPost
	ctxReq.URL = &url.URL{
		Path:     "/",
		RawQuery: "authorization=auth",
	}
	ctxReq.Header.Set(context.HeaderXRequestID, "request-id")

	tests := []struct {
		name    string
		context context.CsmContext
		result  *Token
		err     error
		config  *util.TestServerConfig
	}{
		{
			name:    "normal",
			context: ctx,
			result:  &Token{ID: "subjectToken"},
			config: &util.TestServerConfig{
				RequestMethod:   http.MethodPost,
				RequestURLPath:  "/v3/BCE-CRED/accesskeys",
				RequestBody:     []byte(`{"auth":{"authorization":"auth","request":{"method":"POST","uri":"/","params":{},"headers":{"Host":"example.com","X-Bce-Request-Id":"request-id"}}}}`),
				ResponseBody:    []byte(`{"token":{"id":"subjectToken","expires_at":"0001-01-01T00:00:00Z","issued_at":"0001-01-01T00:00:00Z","methods":null,"domain":null,"project":null,"user":{"name":""},"roles":null,"catalog":null}}`),
				ResponseHeaders: map[string]string{"Content-Type": "application/json", "X-Subject-Token": "subjectToken"},
			},
		}, {
			name:    "bce-error",
			context: ctx,
			err: &csm.ResponseError{
				GenericError: csm.GenericError{RequestID: "request-id-srk"},
				StatusCode:   http.StatusBadRequest,
			},
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
				ResponseHeaders:    map[string]string{"Content-Type": "application/json"},
				ResponseBody:       []byte(`{"requestId":"request-id-srk","code":"","message":""}`),
			},
		}, {
			name:    "non-bce-error",
			context: ctx,
			err:     errors.New("cannot get bce.Error from response"),
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			svr := util.NewTestServer(t, test.config)
			defer svr.Close()
			client := NewMockClient(svr.URL)
			actualResult, actualErr := client.AuthenticateAKSKSignature(test.context)
			ass.Equal(test.result, actualResult, "result mismatch")
			if test.err != nil {
				ass.NotNil(actualErr)
			}
		})
	}
}

func TestClient_PermissionVerifyWithUserID(t *testing.T) {
	ass := assert.New(t)
	tests := []struct {
		name    string
		userid  string
		request *SinglePermissionVerifyRequest
		result  *SinglePermissionVerifyResult
		err     error
		config  *util.TestServerConfig
	}{
		{
			name:    "normal",
			userid:  "userid",
			request: &SinglePermissionVerifyRequest{Service: "service"},
			result:  &SinglePermissionVerifyResult{Id: "id"},
			config: &util.TestServerConfig{
				RequestMethod:   http.MethodPost,
				RequestURLPath:  "/v3/users/userid/permissions",
				RequestBody:     []byte(`{"service":"service","region":"","resource":"","resource_owner":"","permission":null}`),
				ResponseBody:    []byte(`{"verify_result":{"id":"id","effect":"","eid":""},"token":{"id":"subjectToken","expires_at":"0001-01-01T00:00:00Z","issued_at":"0001-01-01T00:00:00Z","methods":null,"domain":null,"project":null,"user":{"name":""},"roles":null,"catalog":null}}`),
				ResponseHeaders: map[string]string{"Content-Type": "application/json"},
			},
		}, {
			name:    "bce-error",
			request: &SinglePermissionVerifyRequest{},
			err: &csm.ResponseError{
				GenericError: csm.GenericError{RequestID: "request-id-srk"},
				StatusCode:   http.StatusBadRequest,
			},
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
				ResponseHeaders:    map[string]string{"Content-Type": "application/json"},
				ResponseBody:       []byte(`{"requestId":"request-id-srk","code":"","message":""}`),
			},
		}, {
			name:    "non-bce-error",
			request: &SinglePermissionVerifyRequest{},
			err:     errors.New("cannot get bce.Error from response"),
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			svr := util.NewTestServer(t, test.config)
			defer svr.Close()
			client := NewMockClient(svr.URL)
			actualResult, actualErr := client.PermissionVerifyWithUserID(test.userid, test.request)
			ass.Equal(test.result, actualResult, "result mismatch")
			if test.err != nil {
				ass.NotNil(actualErr)
			}
		})
	}
}

func TestClient_PermissionVerify(t *testing.T) {
	ass := assert.New(t)
	tests := []struct {
		name         string
		subjectToken string
		request      *SinglePermissionVerifyRequest
		resultToken  *Token
		resultResult *SinglePermissionVerifyResult
		err          error
		config       *util.TestServerConfig
	}{
		{
			name:         "normal",
			subjectToken: "subjectToken",
			request:      &SinglePermissionVerifyRequest{Service: "service"},
			resultToken:  &Token{ID: "subjectToken"},
			resultResult: &SinglePermissionVerifyResult{Id: "id"},
			config: &util.TestServerConfig{
				RequestMethod:   http.MethodPost,
				RequestURLPath:  "/v3/BCE-CRED/permissions",
				RequestBody:     []byte(`{"service":"service","region":"","resource":"","resource_owner":"","permission":null}`),
				ResponseBody:    []byte(`{"verify_result":{"id":"id","effect":"","eid":""},"token":{"id":"subjectToken","expires_at":"0001-01-01T00:00:00Z","issued_at":"0001-01-01T00:00:00Z","methods":null,"domain":null,"project":null,"user":{"name":""},"roles":null,"catalog":null}}`),
				ResponseHeaders: map[string]string{"Content-Type": "application/json"},
			},
		}, {
			name:    "bce-error",
			request: &SinglePermissionVerifyRequest{},
			err: &csm.ResponseError{
				GenericError: csm.GenericError{RequestID: "request-id-srk"},
				StatusCode:   http.StatusBadRequest,
			},
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
				ResponseHeaders:    map[string]string{"Content-Type": "application/json"},
				ResponseBody:       []byte(`{"requestId":"request-id-srk","code":"","message":""}`),
			},
		}, {
			name:    "non-bce-error",
			request: &SinglePermissionVerifyRequest{},
			err:     errors.New("cannot get bce.Error from response"),
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			svr := util.NewTestServer(t, test.config)
			defer svr.Close()
			client := NewMockClient(svr.URL)
			actualToken, actualResult, actualErr := client.PermissionVerify(test.subjectToken, test.request)
			ass.Equal(test.resultToken, actualToken, "token mismatch")
			ass.Equal(test.resultResult, actualResult, "result mismatch")
			if test.err != nil {
				ass.NotNil(actualErr)
			}
		})
	}
}

func TestClient_BatchPermissionVerify(t *testing.T) {
	ass := assert.New(t)
	tests := []struct {
		name      string
		userid    string
		requestId string
		request   []*MultiplePermissionVerifyRequest
		result    []*MultiplePermissionVerifyResult
		err       error
		config    *util.TestServerConfig
	}{
		{
			name:      "normal",
			userid:    "userid",
			requestId: "request-id",
			request: []*MultiplePermissionVerifyRequest{
				&MultiplePermissionVerifyRequest{
					Service: "service1",
				},
				&MultiplePermissionVerifyRequest{
					Service: "service2",
				},
			},
			result: []*MultiplePermissionVerifyResult{
				&MultiplePermissionVerifyResult{
					Results: []SinglePermissionVerifyResult{
						SinglePermissionVerifyResult{
							Id: "service1",
						},
						SinglePermissionVerifyResult{
							Id: "service2",
						},
					},
				},
			},
			config: &util.TestServerConfig{
				RequestMethod:   http.MethodPost,
				RequestURLPath:  "/v3/users/userid/batch_permissions",
				RequestBody:     []byte(`{"verify_list":[{"service":"service1","region":"","resource":null,"resource_owner":"","permission":null},{"service":"service2","region":"","resource":null,"resource_owner":"","permission":null}]}`),
				ResponseBody:    []byte(`{"verify_results":[{"result":[{"id":"service1","effect":"","eid":""},{"id":"service2","effect":"","eid":""}]}]}`),
				ResponseHeaders: map[string]string{"Content-Type": "application/json"},
			},
		}, {
			name:    "bce-error",
			request: []*MultiplePermissionVerifyRequest{},
			err: &csm.ResponseError{
				GenericError: csm.GenericError{RequestID: "request-id-srk"},
				StatusCode:   http.StatusBadRequest,
			},
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
				ResponseHeaders:    map[string]string{"Content-Type": "application/json"},
				ResponseBody:       []byte(`{"requestId":"request-id-srk","code":"","message":""}`),
			},
		}, {
			name:    "non-bce-error",
			request: []*MultiplePermissionVerifyRequest{},
			err:     errors.New("cannot get bce.Error from response"),
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			svr := util.NewTestServer(t, test.config)
			defer svr.Close()
			client := NewMockClient(svr.URL)
			actualResult, actualErr := client.BatchPermissionVerify(test.userid, test.requestId, test.request)
			ass.Equal(test.result, actualResult, "result mismatch")
			if test.err != nil {
				ass.NotNil(actualErr)
			}
		})
	}
}

func TestClient_AssumeRole(t *testing.T) {
	ass := assert.New(t)
	tests := []struct {
		name      string
		userid    string
		roleName  string
		requestId string
		duration  int
		result    *sts_credential.StsCredential
		err       error
		config    *util.TestServerConfig
	}{
		{
			name:      "normal",
			userid:    "userid",
			roleName:  "rolename",
			requestId: "request-id",
			duration:  42,
			result: &sts_credential.StsCredential{
				AccessKeyId: "accessKeyId",
			},
			config: &util.TestServerConfig{
				RequestMethod:  http.MethodPost,
				RequestURLPath: "/v1/credential",
				RequestQueryParams: map[string]string{
					"assumeRole":      "",
					"accountId":       "userid",
					"durationSeconds": "42",
					"roleName":        "rolename",
				},
				ResponseHeaders: map[string]string{"Content-Type": "application/json"},
				ResponseBody:    []byte(`{"accessKeyId":"accessKeyId","secretAccessKey":"","sessionToken":"","expiration":"0001-01-01T00:00:00Z","userId":"","roleId":""}`),
			},
		}, {
			name: "bce-error",
			err: &csm.ResponseError{
				GenericError: csm.GenericError{RequestID: "request-id-srk"},
				StatusCode:   http.StatusBadRequest,
			},
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
				ResponseHeaders:    map[string]string{"Content-Type": "application/json"},
				ResponseBody:       []byte(`{"requestId":"request-id-srk","code":"","message":""}`),
			},
		}, {
			name: "non-bce-error",
			err:  errors.New("cannot get bce.Error from response"),
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			svr := util.NewTestServer(t, test.config)
			defer svr.Close()
			client := NewMockClient(svr.URL)
			actualResult, actualErr := client.AssumeRole(test.userid, test.roleName, test.requestId, test.duration)
			ass.Equal(test.result, actualResult, "result mismatch")
			if test.err != nil {
				ass.NotNil(actualErr)
			}
		})
	}
}

func TestClient_GetAccount(t *testing.T) {
	ass := assert.New(t)
	tests := []struct {
		name      string
		username  string
		requestId string
		result    *UserAccount
		err       error
		config    *util.TestServerConfig
	}{
		{
			name:      "normal",
			username:  "username",
			requestId: "request-id",
			result: &UserAccount{
				Account: &Account{
					Name: "username",
				},
			},
			config: &util.TestServerConfig{
				RequestMethod:   http.MethodGet,
				RequestURLPath:  "/v3/accounts/username",
				ResponseHeaders: map[string]string{"Content-Type": "application/json"},
				ResponseBody:    []byte(`{"account":{"name":"username","domain_id":"","domain_name":"","email":"","phone":"","mobile_phone":"","company":"","industry":"","register_time":"","activate_time":""}}`),
			},
		}, {
			name: "bce-error",
			err: &csm.ResponseError{
				GenericError: csm.GenericError{RequestID: "request-id-srk"},
				StatusCode:   http.StatusBadRequest,
			},
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
				ResponseHeaders:    map[string]string{"Content-Type": "application/json"},
				ResponseBody:       []byte(`{"requestId":"request-id-srk","code":"","message":""}`),
			},
		}, {
			name: "non-bce-error",
			err:  errors.New("cannot get bce.Error from response"),
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			svr := util.NewTestServer(t, test.config)
			defer svr.Close()
			client := NewMockClient(svr.URL)
			actualResult, actualErr := client.GetAccount(test.username, test.requestId)
			ass.Equal(test.result, actualResult, "result mismatch")
			if test.err != nil {
				ass.NotNil(actualErr)
			}
		})
	}
}

func TestClient_authServiceToken(t *testing.T) {
	ass := assert.New(t)
	tests := []struct {
		name   string
		err    error
		config *util.TestServerConfig
	}{
		{
			name: "normal",
			err:  nil,
			config: &util.TestServerConfig{
				RequestMethod:  http.MethodPost,
				RequestURLPath: "/v3/auth/tokens",
				RequestBody:    []byte(`{"auth":{"identity":{"methods":["password"],"password":{"user":{"name":"spiderman","domain":{"name":"default"},"password":"catwoman"}}},"scope":{"domain":{"id":"default"}}}}`),
				ResponseHeaders: map[string]string{
					"Content-Type":    "application/json",
					"X-Subject-Token": "subjectToken",
				},
				ResponseBody:       []byte(`{"token":{"id":"subjectToken","expires_at":"0001-01-01T00:00:00Z","issued_at":"0001-01-01T00:00:00Z","methods":null,"domain":null,"project":null,"user":{"name":""},"roles":null,"catalog":null}}`),
				ResponseStatusCode: http.StatusCreated,
			},
		}, {
			name: "status-code-error",
			err:  errors.New("400 " + http.StatusText(http.StatusBadRequest)),
			config: &util.TestServerConfig{
				ResponseStatusCode: http.StatusBadRequest,
			},
		},
	}
	for i, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			svr := util.NewTestServer(t, test.config)
			defer svr.Close()
			client := NewMockClient(svr.URL)
			client.config.Username = "spiderman"
			client.config.Password = "catwoman"
			actual := client.authServiceToken()
			ass.Equal(test.err, actual, fmt.Sprintf("err mismatch for test %d", i))
			subjectToken, ok := test.config.ResponseHeaders["X-Subject-Token"]
			if ok {
				ass.Equal("subjectToken", subjectToken, fmt.Sprintf("subjectToken mismatch for test %d", i))
			}
		})
	}
}

func TestClient_AccessKey(t *testing.T) {
	want := "access key"
	client := new(Client)
	client.config = &Config{}

	client.accesskeys = []*Accesskey{
		&Accesskey{
			AccessKey: want,
		},
	}
	actual := client.AccessKey()
	assert.Equal(t, want, actual, "access key mismatch")

	client.config.AccessKey = want
	actual = client.AccessKey()
	assert.Equal(t, want, actual, "access key mismatch")
}

func TestClient_SecretKey(t *testing.T) {
	want := "secret key"
	client := new(Client)
	client.config = &Config{}

	client.accesskeys = []*Accesskey{
		&Accesskey{
			SecretKey: want,
		},
	}
	actual := client.SecretKey()
	assert.Equal(t, want, actual, "secret key mismatch")

	client.config.SecretKey = want
	actual = client.SecretKey()
	assert.Equal(t, want, actual, "secret key mismatch")
}

func TestClient_serviceToken(t *testing.T) {
	c := NewMockClient("")
	wantErr := errors.New("service token not exist")
	_, actualErr := c.ServiceToken()
	assert.Equal(t, wantErr, actualErr, "err mismatch")

	c.clientToken = &Token{ID: "id"}
	actualResult, _ := c.ServiceToken()
	assert.Equal(t, "id", actualResult, "result mismatch")
}

func TestClient_serviceUserID(t *testing.T) {
	c := NewMockClient("")
	wantErr := errors.New("service token not exist")
	_, actualErr := c.serviceUserID()
	assert.Equal(t, wantErr, actualErr, "err mismatch")

	c.clientToken = &Token{User: User{ID: "id"}}
	actualResult, _ := c.serviceUserID()
	assert.Equal(t, "id", actualResult, "result mismatch")
}

func TestClient_authMethod(t *testing.T) {
	c := NewMockClient("")
	c.config.AuthMethod = "authmethod"
	assert.Equal(t, "authmethod", c.authMethod(), "authMethod mismatch")
}

func TestClient_region(t *testing.T) {
	c := NewMockClient("")
	c.config.Region = "Region"
	assert.Equal(t, "Region", c.region(), "region mismatch")
}

func TestClient_loadconfig(t *testing.T) {
	ass := assert.New(t)
	tests := []struct {
		name string
		err  error
	}{
		{
			name: "test default",
			err:  nil,
		},
	}
	for i, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			viper.SetConfigType("yaml")
			if err := viper.ReadConfig(strings.NewReader(utIamTemp)); err != nil {
				panic(err.Error())
			}
			_, err := loadConfig("iam.profile.online")
			ass.Equal(err, test.err, fmt.Sprintf("err mismatch for test %d", i))
		})
	}
}

var utIamTemp = `iam:
  default: online
  profile:
    online:
      username : bap
      password : bap
      default_domain : default
      subuser_support : true
      auth_method : password
      access_key:
      secret_key:
      token_cache_size: 1000
      secrect_cache_size: 1000
      active_cache_size: 1000
      region: gz
      host: 127.0.0.1
      port: 80
      stshost: 127.0.0.1
      stsport: 80
  user_id_map:
    online:
      u-123: u-456`

func TestClient_ServiceAuth(t *testing.T) {
	c := NewMockClient("")
	c.config.Region = "Region"
	c.accesskeys = []*Accesskey{
		{},
	}
	c.accesskeys[0] = &Accesskey{
		AccessKey: "1",
		SecretKey: "2",
	}
	c.accesskeys[0] = &Accesskey{
		AccessKey: "1",
		SecretKey: "2",
	}
	_, err := c.ServiceAuth()
	assert.Equal(t, err, nil, "region mismatch")
}
