package iam

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	cache "github.com/hashicorp/golang-lru"
	"github.com/spf13/viper"
	"gopkg.in/resty.v1"
	"k8s.io/apimachinery/pkg/util/uuid"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/auth"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam/sts_credential"
	CsmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	log "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/restclient"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

const (
	AuthMethodPassword  = "password"
	AuthMethodSignature = "signature"

	PermissionAllow       = "ALLOW"
	PermissionDeny        = "DENY"
	PermissionDefaultDeny = "DEFAULT_DENY"
)

// Client IAM Client Reference: http://gollum.baidu.com/IamApiServiceGuide、 http://wiki.baidu.com/pages/viewpage.action?pageId=********
type Client struct {
	endpoint      string
	stsEndpoint   string
	trivialClient *resty.Client
	bceClient     *resty.Client
	tokenCache    *cache.Cache // ak => token
	secretCache   *cache.Cache // ak => sk
	activeCache   *cache.Cache // accountid => active true or false
	clientToken   *Token
	refreshCtrl   chan string
	config        *Config
	accesskeys    []*Accesskey
	// IAM前置机接入
	IamCleint *sdkIAM.BceClient
}

func New(iamType string) (*Client, error) {
	config, err := loadConfig(iamType)
	if err != nil {
		return nil, err
	}
	tc, err := cache.New(config.TokenCacheSize)
	if err != nil {
		return nil, errors.New("create token cache failed")
	}
	sc, err := cache.New(config.SecretCacheSize)
	if err != nil {
		return nil, errors.New("create secret cache failed")
	}
	ac, err := cache.New(config.ActiveCacheSize)
	if err != nil {
		return nil, errors.New("create active cache failed")
	}

	iamLogger := log.NewLogger().Named("iam")
	trivialClient := resty.New().SetLogger(iamLogger)
	bceClient := restclient.NewBCERestClient("iam")

	client := &Client{
		endpoint:      fmt.Sprintf("http://%s:%d", config.Host, config.Port),
		stsEndpoint:   fmt.Sprintf("http://%s:%d", config.StsHost, config.StsPort),
		trivialClient: trivialClient,
		bceClient:     bceClient,
		refreshCtrl:   make(chan string, 2),
		config:        config,
		tokenCache:    tc,
		secretCache:   sc,
		activeCache:   ac,
	}

	// IAM前置机接入
	iamClient := sdkIAM.NewBceClientWithAgent(&sdkIAM.BceClientConfiguration{
		Endpoint: fmt.Sprintf("http://%s:%d", config.Host, config.Port),
		UserName: config.Username,
		Password: config.Password,
		Retry:    sdkIAM.DEFAULT_RETRY_POLICY,
		Domain:   "Default",
		Version:  "/v3",
	}, &sdkIAM.AgentConfiguration{
		Endpoint: config.IamAgent,
		IsEnable: true,
	})
	client.IamCleint = iamClient

	client.bceClient.OnBeforeRequest(func(c *resty.Client, r *resty.Request) error {
		if client.supportSubuser() {
			r.SetHeader("X-Subuser-Support", "true")
		}
		if client.authMethod() == AuthMethodPassword {
			token, tokenErr := client.ServiceToken()
			if tokenErr != nil {
				return tokenErr
			}
			r.SetHeader("X-Auth-Token", token)
		}
		return nil
	})

	err = client.authServiceToken()
	if err != nil {
		log.Warnf("iam service auth failed, err=%s", err.Error())
	}
	userid, err := client.serviceUserID()
	if err == nil {
		keys, err := client.getAccessKeys(userid)
		if err != nil {
			log.Errorf("fetch service accesskeys failed: %s", err.Error())
		} else {
			client.accesskeys = keys
		}
	}

	go client.refreshRoutine()
	return client, nil
}

func NewAuthRequestByHttp(r *http.Request) *AuthenticationRequestAuth {
	authReq := new(AuthenticationRequestAuth)

	query := r.URL.Query()
	signature := query.Get("authorization")
	if signature == "" {
		signature = r.Header.Get("Authorization")
	}
	query.Del("authorization")

	authReq.Authorization = signature
	authReq.Request.
		WithMethod(r.Method).
		WithUri(r.URL.Path).
		WithParams(query).
		WithHeader(r.Header).
		WithHost(r.Host)
	authReq.SecurityToken = r.Header.Get("X-Bce-Security-Token")
	return authReq
}

// /v3/auth/tokens
func (c *Client) AuthenticateWithToken(subjectToken, requestId string) (*Token, error) {
	ctx := c.requestContext(requestId)
	uri := fmt.Sprintf("%s/v3/auth/tokens", c.endpoint)

	headers := map[string]string{
		"X-Subject-Token": subjectToken,
	}
	if c.supportSubuser() {
		headers["X-Subuser-Support"] = "true"
	}

	resp, err := c.bceClient.R().
		SetContext(ctx).
		SetResult(&tokenResp{}).
		SetHeaders(headers).
		Get(uri)

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		if resp.StatusCode() == http.StatusUnauthorized { // need refresh token
			c.refreshToken()
		}
		bceError, e := restclient.BceResponseError(resp)
		if e != nil {
			return nil, e
		}
		return nil, bceError
	}

	token := resp.Result().(*tokenResp)
	if token.Token != nil {
		token.Token.ID = subjectToken
	}
	return token.Token, nil
}

func (c *Client) AuthenticateAKSKSignature(cc CsmContext.CsmContext) (*Token, error) {
	authReq := NewAuthRequestByHttp(cc.Request())
	request := &AuthenticationRequest{
		Auth:      *authReq,
		RequestId: cc.RequestID(),
	}
	return c.AuthenticateWithAKSK(request)
}

func (c *Client) ServiceAuth() (*auth.BceAuthKey, error) {
	if len(c.accesskeys) == 0 {
		return nil, errors.New("service authinfo invalid")
	}
	key := c.accesskeys[0]
	return &auth.BceAuthKey{
		AccessKey: key.AccessKey,
		SecretKey: key.SecretKey,
	}, nil
}

// /v3/BCE-CRED/accesskeys
func (c *Client) AuthenticateWithAKSK(request *AuthenticationRequest) (*Token, error) {
	ctx := c.requestContext(request.RequestId)
	uri := fmt.Sprintf("%s/v3/BCE-CRED/accesskeys", c.endpoint)

	resp, err := c.bceClient.R().
		SetContext(ctx).
		SetResult(&tokenResp{}).
		SetBody(request).
		Post(uri)

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		if resp.StatusCode() == http.StatusUnauthorized { // need refresh token
			c.refreshToken()
		}
		bceError, e := restclient.BceResponseError(resp)
		if e != nil {
			return nil, e
		}
		return nil, bceError
	}

	token := resp.Result().(*tokenResp)
	if token.Token != nil {
		token.Token.ID = resp.Header().Get("X-Subject-Token")
	}
	return token.Token, nil
}

func (c *Client) PermissionVerifyWithUserID(userid string, request *SinglePermissionVerifyRequest) (*SinglePermissionVerifyResult, error) {
	ctx := c.requestContext(request.RequestId)
	uri := fmt.Sprintf("%s/v3/users/%s/permissions", c.endpoint, userid)

	resp, err := c.bceClient.R().
		SetContext(ctx).
		SetResult(&VerifyResultWithToken{}).
		SetBody(request).
		Post(uri)

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		if resp.StatusCode() == http.StatusUnauthorized { // need refresh token
			c.refreshToken()
		}
		bceError, e := restclient.BceResponseError(resp)
		if e != nil {
			return nil, e
		}
		return nil, bceError
	}

	result := resp.Result().(*VerifyResultWithToken)
	return result.Result, nil
}

// PermissionVerify /v3/BCE-CRED/permissions
func (c *Client) PermissionVerify(subjectToken string, request *SinglePermissionVerifyRequest) (*Token, *SinglePermissionVerifyResult, error) {
	ctx := c.requestContext(request.RequestId)
	uri := fmt.Sprintf("%s/v3/BCE-CRED/permissions", c.endpoint)

	req := c.bceClient.R().
		SetContext(ctx).
		SetResult(&VerifyResultWithToken{}).
		SetBody(request)

	if len(subjectToken) > 0 {
		req.SetHeader("X-Subject-Token", subjectToken)
	}

	resp, err := req.Post(uri)

	if err != nil {
		return nil, nil, err
	}

	if resp.IsError() {
		if resp.StatusCode() == http.StatusUnauthorized { // need refresh token
			c.refreshToken()
		}
		bceError, e := restclient.BceResponseError(resp)
		if e != nil {
			return nil, nil, e
		}
		return nil, nil, bceError
	}

	result := resp.Result().(*VerifyResultWithToken)
	if result.Token != nil && len(result.Token.ID) == 0 {
		result.Token.ID = subjectToken
	}
	return result.Token, result.Result, nil
}

func (c *Client) BatchPermissionVerify(userid, requestId string, requests []*MultiplePermissionVerifyRequest) ([]*MultiplePermissionVerifyResult, error) {
	ctx := c.requestContext(requestId)
	uri := fmt.Sprintf("%s/v3/users/%s/batch_permissions", c.endpoint, userid)

	verifyReq := batchVerifyRequest{}
	verifyReq.Requests = requests

	resp, err := c.bceClient.R().
		SetContext(ctx).
		SetResult(&batchVerifyResults{}).
		SetBody(verifyReq).
		Post(uri)

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		if resp.StatusCode() == http.StatusUnauthorized { // need refresh token
			c.refreshToken()
		}
		bceError, e := restclient.BceResponseError(resp)
		if e != nil {
			return nil, e
		}
		return nil, bceError
	}

	result := resp.Result().(*batchVerifyResults)
	return result.Results, nil
}

func (c *Client) AssumeRole(userid, roleName, requestId string, duration int) (*sts_credential.StsCredential, error) {
	ctx := c.requestContext(requestId)
	uri := fmt.Sprintf("%s/v1/credential", c.stsEndpoint)

	resp, err := c.bceClient.R().
		SetContext(ctx).
		SetResult(&sts_credential.StsCredential{}).
		SetQueryParam("assumeRole", "").
		SetQueryParam("accountId", userid).
		SetQueryParam("durationSeconds", strconv.Itoa(duration)).
		SetQueryParam("roleName", roleName).
		Post(uri)

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		if resp.StatusCode() == http.StatusUnauthorized { // need refresh token
			c.refreshToken()
		}
		bceError, e := restclient.BceResponseError(resp)
		if e != nil {
			return nil, e
		}
		return nil, bceError
	}

	result := resp.Result().(*sts_credential.StsCredential)
	return result, nil
}

func (c *Client) GetAccount(name string, requestId string) (*UserAccount, error) {
	ctx := c.requestContext(requestId)
	uri := fmt.Sprintf("%s/v3/accounts/%s", c.endpoint, name)

	resp, err := c.bceClient.R().
		SetContext(ctx).
		SetResult(&UserAccount{}).
		Get(uri)

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		if resp.StatusCode() == http.StatusUnauthorized { // need refresh token
			c.refreshToken()
		}
		bceError, e := restclient.BceResponseError(resp)
		if e != nil {
			return nil, e
		}
		return nil, bceError
	}

	data := resp.Result().(*UserAccount)
	return data, nil
}

func (c *Client) AccessKey() string {
	if c.config.AccessKey != "" {
		return c.config.AccessKey
	}
	if len(c.accesskeys) > 0 {
		key := c.accesskeys[0]
		return key.AccessKey
	}
	return ""
}

func (c *Client) SecretKey() string {
	if c.config.SecretKey != "" {
		return c.config.SecretKey
	}
	if len(c.accesskeys) > 0 {
		key := c.accesskeys[0]
		return key.SecretKey
	}
	return ""
}

func loadConfig(profile string) (*Config, error) {
	config := Config{}
	err := viper.UnmarshalKey(profile, &config)
	if err != nil {
		return nil, err
	}
	if config.Username == "" ||
		config.Password == "" ||
		config.Host == "" {
		return nil, errors.New("invalid iam config")
	}
	if (config.AuthMethod != AuthMethodPassword) &&
		(config.AuthMethod != AuthMethodSignature) {
		config.AuthMethod = AuthMethodPassword
	}
	if config.ActiveCacheSize <= 0 {
		config.ActiveCacheSize = 1000
	}
	if config.SecretCacheSize <= 0 {
		config.SecretCacheSize = 1000
	}
	if config.ActiveCacheSize <= 0 {
		config.ActiveCacheSize = 1000
	}
	if config.Port == 0 {
		config.Port = 80
	}
	return &config, nil
}

func (c *Client) authServiceToken() error {
	info := newTokenAuth(c.config.Username, c.config.Password)
	rsp, err := c.trivialClient.R().
		SetBody(info).
		SetResult(&tokenResp{}).
		Post(c.endpoint + "/v3/auth/tokens")

	if err != nil {
		return err
	}
	if rsp.StatusCode() != http.StatusCreated {
		return errors.New(rsp.Status())
	}

	tokenid := rsp.Header().Get("X-Subject-Token")
	token := rsp.Result().(*tokenResp)
	token.Token.ID = tokenid
	c.clientToken = token.Token
	return nil
}

func (c *Client) getAccessKeys(userid string) ([]*Accesskey, error) {
	uri := fmt.Sprintf("%s/v3/users/%s/accesskeys", c.endpoint, userid)
	resp, err := c.bceClient.R().
		SetResult(&accessKeyResponse{}).
		Get(uri)
	if err != nil {
		return nil, err
	}
	data := resp.Result().(*accessKeyResponse)
	return data.AccessKeys, nil
}

func (c *Client) refreshRoutine() {
	for {
		duration := 10 * time.Second
		token := c.clientToken
		if token != nil {
			duration = token.ExpiresAt.Sub(token.IssuedAt)
			if duration > 10*time.Minute {
				duration = 10 * time.Minute
			} else if duration < 0 {
				duration = 0 * time.Second
			}
		}
		select {
		case s := <-c.refreshCtrl:
			if s == "exit" {
				close(c.refreshCtrl)
				return
			}
		case <-time.After(duration):
		}
		err := c.authServiceToken()
		if err != nil {
			log.Errorf("iam service auth failed, err=%s", err.Error())
			continue
		}
		userid, _ := c.serviceUserID()
		keys, err := c.getAccessKeys(userid)
		if err != nil {
			log.Error("fetch service accesskeys failed.")
		}
		c.accesskeys = keys
	}
}

func (c *Client) refreshToken() {
	select {
	case c.refreshCtrl <- "refresh":
	default: // channel full
	}
}

func (c *Client) ServiceToken() (string, error) {
	t := c.clientToken
	if t == nil {
		return "", errors.New("service token not exist")
	}
	return t.ID, nil
}

func (c *Client) serviceUserID() (string, error) {
	t := c.clientToken
	if t == nil {
		return "", errors.New("service token not exist")
	}
	return t.User.ID, nil
}

func (c *Client) authMethod() string {
	return c.config.AuthMethod
}

func (c *Client) supportSubuser() bool {
	return c.config.SubUserSupport
}

func (c *Client) region() string {
	return c.config.Region
}

func (c *Client) requestContext(requestId string) context.Context {
	ctx := context.Background()
	requestId = ensuerRequestId(requestId)
	ctx = context.WithValue(ctx, restclient.BceRequestIdKey, requestId)
	authKey := auth.NewBceAuthKey(c.AccessKey(), c.SecretKey())
	ctx = context.WithValue(ctx, restclient.BceAuthContextKey, *authKey)
	return ctx
}

func ensuerRequestId(requestId string) string {
	if requestId == "" {
		requestId = string(uuid.NewUUID())
	}
	return requestId
}

func (c *Client) GetEndpoint() string {
	return c.endpoint
}
