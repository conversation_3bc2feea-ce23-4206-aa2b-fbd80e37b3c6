package iam

import "time"

type Domain struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type User struct {
	ID       string  `json:"id,omitempty"`
	Name     string  `json:"name"`
	Domain   *Domain `json:"domain,omitempty"` //主用户ID，数据库存住用户ID
	Password string  `json:"password,omitempty"`
}

type Role struct {
	ID   string `json:"id"`
	Name string `json:"name,omitempty"`
}

type Project struct {
	ID     string  `json:"id"`
	Name   string  `json:"name"`
	Domain *Domain `json:"domain,omitempty"`
}

type EndPoint struct {
	ID        string `json:"id"`
	URL       string `json:"url"`
	Region    string `json:"region"`
	Interface string `json:"inner"`
}

type Service struct {
	ID        string      `json:"id"`
	Type      string      `json:"type"`
	EndPoints []*EndPoint `json:"endpoints"`
}

type Token struct {
	ID        string     `json:"id"`
	ExpiresAt time.Time  `json:"expires_at"`
	IssuedAt  time.Time  `json:"issued_at"`
	Methods   []string   `json:"methods"`
	Domain    *Domain    `json:"domain"`
	Project   *Project   `json:"project"`
	User      User       `json:"user"`
	Roles     []*Role    `json:"roles"`
	Catalog   []*Service `json:"catalog"`
}

type Accesskey struct {
	CredentialID string `json:"credential_id"`
	AccessKey    string `json:"access"`
	SecretKey    string `json:"secret"`
	ProjectID    string `json:"project_id"`
	UserID       string `json:"user_id"`
}
