package iam

import (
	"errors"

	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

type profile string

type userIdMap map[string]string

var (
	DefaultClient    *Client
	clients          = map[profile]*Client{}
	userIdMapProfile = map[profile]userIdMap{}
)

func Init() {
	profiles := viper.GetStringMap("iam.profile")
	for name := range profiles {
		c, err := New("iam.profile." + name)
		if err != nil {
			panic(err)
		}
		csmlog.Debugf("iam client %s created", name)
		clients[profile(name)] = c
		userIdMapProfile[profile(name)] = viper.GetStringMapString("iam.user_id_map." + name)
	}
	defaultProfile := viper.GetString("iam.default")
	if c, ok := clients[profile(defaultProfile)]; ok {
		DefaultClient = c
	} else {
		panic("no default iam client")
	}
}

func GetClient(names ...string) (*Client, error) {
	if len(names) == 0 {
		return DefaultClient, nil
	}
	name := profile(names[0])
	if client, ok := clients[name]; ok {
		return client, nil
	} else if name == "default" {
		return DefaultClient, nil
	}
	return nil, errors.New("iam client profile not found")
}
