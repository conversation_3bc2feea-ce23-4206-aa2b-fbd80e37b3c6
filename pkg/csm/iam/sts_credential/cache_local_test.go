package sts_credential

import (
	"testing"
	"time"
)

func TestCredentialCache(t *testing.T) {
	c := NewLocalCredentialCache()
	cache, _ := c.(*stsLocalCredentialCache)
	cache.ticker.Stop()

	now := time.Now()
	c1 := &StsCredential{
		Expiration: now.Add(time.Second * -1),
	}
	cache.Set("c", "1", c1)
	c2 := cache.Get("c", "1")
	if c2 != nil {
		t.<PERSON>rror("not expired")
	}

	c1 = &StsCredential{
		Expiration: now.Add(time.Minute*10 + time.Second*10),
	}
	cache.Set("c", "1", c1)
	cache.doClean()
	c2 = cache.Get("c", "1")
	if c2 == nil {
		t.<PERSON>rror("expired")
	}
}
