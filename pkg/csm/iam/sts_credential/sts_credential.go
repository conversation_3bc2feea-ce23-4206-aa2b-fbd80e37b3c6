package sts_credential

import (
	"encoding/json"
	"time"
)

type StsCredential struct {
	AccessKeyId     string    `json:"accessKeyId"`
	AccessKeySecret string    `json:"secretAccessKey"`
	SessionToken    string    `json:"sessionToken"`
	Expiration      time.Time `json:"expiration"`
	UserId          string    `json:"userId"`
	RoleId          string    `json:"roleId"`
}

func (p *StsCredential) MarshalBinary() (data []byte, err error) {
	data, err = json.Marshal(p)
	return
}
