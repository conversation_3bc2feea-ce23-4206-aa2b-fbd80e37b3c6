package iam

import (
	"gopkg.in/resty.v1"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
)

func NewMockClient(endpoint string) *Client {
	client := new(Client)
	client.endpoint = endpoint
	client.stsEndpoint = endpoint
	client.config = &Config{
		AccessKey: "access key",
		SecretKey: "secret key",
	}
	client.trivialClient = resty.New()
	client.bceClient = resty.New()
	client.bceClient.SetError(&csm.GenericError{})
	client.config.SubUserSupport = true // for test coverage
	return client
}
