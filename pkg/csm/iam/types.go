package iam

type Config struct {
	Username        string `mapstructure:"username"`
	Password        string `mapstructure:"password"`
	AccessKey       string `mapstructure:"access_key"`
	SecretKey       string `mapstructure:"secret_key"`
	DefaultDomain   string `mapstructure:"default_domain"`
	SubUserSupport  bool   `mapstructure:"subuser_support"`
	AuthMethod      string `mapstructure:"auth_method"`
	Region          string `mapstructure:"region"`
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	StsHost         string `mapstructure:"stshost"`
	StsPort         int    `mapstructure:"stsport"`
	TokenCacheSize  int    `mapstructure:"token_cache_size"`
	SecretCacheSize int    `mapstructure:"secret_cache_size"`
	ActiveCacheSize int    `mapstructure:"active_cache_size"`
	IamAgent        string `mapstructure:"iamAgent"`
}

type authScope struct {
	Domain Domain `json:"domain"`
}
type authPassword struct {
	User User `json:"user"`
}
type authIdentity struct {
	Methods  []string     `json:"methods"`
	Password authPassword `json:"password"`
}
type authInfo struct {
	Identity authIdentity `json:"identity"`
	Scope    authScope    `json:"scope"`
}
type tokenAuth struct {
	Auth authInfo `json:"auth"`
}

type tokenResp struct {
	Token *Token `json:"token"`
}

func newTokenAuth(username, password string) *tokenAuth {
	info := tokenAuth{}
	identity := &info.Auth.Identity
	identity.Methods = append(identity.Methods, "password")
	identity.Password.User.Domain = &Domain{
		Name: "default",
	}
	identity.Password.User.Name = username
	identity.Password.User.Password = password
	info.Auth.Scope.Domain.ID = "default"
	return &info
}
