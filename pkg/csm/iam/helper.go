package iam

import (
	"fmt"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam/sts_credential"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

const ContextIAMUser = "User"

// GetUser 获取Context中的用户信息
func GetUser(c context.CsmContext) (*sdkIAM.User, error) {
	r := c.Get(ContextIAMUser)
	if r == nil {
		return nil, errors.New("user not found")
	}
	//user, ok := r.(*User)
	//if !ok {
	//	return nil, errors.New("user type assertion failed")
	//}

	// IAM前置机接入
	user, ok := r.(*sdkIAM.User)
	if !ok {
		return nil, errors.New("user type assertion failed")
	}
	return user, nil
}

// GetAccountId 校验资源权限同时获取访问的账户ID
func GetAccountId(c context.CsmContext) (string, error) {
	user, err := GetUser(c)
	//if err != nil {
	//	return "", csmErr.NewUnrecognizedClientException("User is invalid", err)
	//} else if user == nil || user.Domain == nil || user.Domain.ID == "" {
	//	return "", csmErr.NewUnrecognizedClientException(fmt.Sprintf("User is invalid. User:%#v", user), err)
	//}

	// IAM前置机改造
	if err != nil {
		return "", csmErr.NewUnrecognizedClientException("User is invalid", err)
	} else if user == nil || user.Domain.ID == "" {
		return "", csmErr.NewUnrecognizedClientException(fmt.Sprintf("User is invalid. User:%#v", user), err)
	}
	return user.Domain.ID, nil
}

// GetAssumeRoleCredential Get the Credential according to userid
func GetAssumeRoleCredential(c context.CsmContext, clientProfile, roleName string, stsCache sts_credential.Cache) (*sts_credential.StsCredential, error) {
	user, err := GetUser(c)
	if err != nil {
		return nil, errors.Wrap(err, "get credential failed")
	}
	userId := MapUserId(user.Domain.ID, clientProfile)

	credential := stsCache.Get(userId, roleName)
	if credential == nil {
		client, err := GetClient(clientProfile)
		if err != nil {
			return nil, errors.Wrap(err, "assume role failed")
		}
		// 调用sts服务assumeRole
		credential, err = client.AssumeRole(userId, roleName, c.RequestID(), 3600)
		if err != nil {
			return nil, errors.Wrap(err, "assume role failed")
		}

		stsCache.Set(userId, roleName, credential)
	}
	return credential, nil
}

func MapUserId(userId, clientProfile string) string {
	m := userIdMapProfile[profile(clientProfile)]
	if newUserId, ok := m[userId]; ok {
		return newUserId
	}
	return userId
}
