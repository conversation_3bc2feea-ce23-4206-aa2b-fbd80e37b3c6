package iam

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"net/http"
	"net/url"
	"testing"
)

func TestSignRequest_WithHeader(t *testing.T) {
	header := http.Header{}
	header.Set("key", "value")
	r := (&SignRequest{}).WithHeader(header)
	require.Equal(t, len(header), len(r.<PERSON>), "header length mismatch")

	for k, actual := range r.Headers {
		want := header.Get(k)
		assert.Equal(t, want, actual,
			fmt.Sprintf("'%s' mismatch, expected '%s' got '%s'", k, want, actual))
	}
}

func TestSignRequest_WithHost(t *testing.T) {
	host := "host"
	r := (&SignRequest{}).WithHost(host)
	assert.Equal(t, host, r.<PERSON>["Host"], "host mismatch")
}

func TestSignRequest_WithMethod(t *testing.T) {
	method := "method"
	r := (&SignRequest{}).WithMethod(method)
	assert.Equal(t, method, r.Method, "method mismatch")
}

func TestSignRequest_WithParams(t *testing.T) {
	params := url.Values{}
	params.Set("key1", "val1")
	r := (&SignRequest{}).WithParams(params)
	require.Equal(t, len(params), len(r.Params), "params length mismatch")

	for k, actual := range r.Params {
		want := params.Get(k)
		assert.Equal(t, want, actual,
			fmt.Sprintf("'%s' mismatch, expected '%s' got '%s'", k, want, actual))
	}
}

func TestSignRequest_WithUri(t *testing.T) {
	uri := "uri"
	r := (&SignRequest{}).WithUri(uri)
	assert.Equal(t, uri, r.Uri, "uri mismatch")
}

func TestSinglePermissionVerifyRequest_AddPermission(t *testing.T) {
	permissions := []string{"p1", "p2", "p3"}
	r := &SinglePermissionVerifyRequest{}

	for _, perm := range permissions {
		r.AddPermission(perm)
	}
	for _, perm := range permissions {
		assert.NotEqual(t, -1, searchString(r.Permission, perm),
			fmt.Sprintf("cannot found '%s'", perm))
	}
}

func TestSinglePermissionVerifyRequest_WithAuth(t *testing.T) {
	auth := &AuthenticationRequestAuth{
		Authorization: "unique member for test",
	}
	r := (&SinglePermissionVerifyRequest{}).WithAuth(auth)
	assert.Equal(t, auth, r.Auth, "auth mismarch")
}

func TestSinglePermissionVerifyRequest_WithContext(t *testing.T) {
	context := &PermissionVerifyContext{
		IPAddr: "unique member for test",
	}
	r := (&SinglePermissionVerifyRequest{}).WithContext(context)
	assert.Equal(t, context, r.Context, "context mismarch")
}

func TestSinglePermissionVerifyRequest_WithOwner(t *testing.T) {
	owner := "owner"
	r := (&SinglePermissionVerifyRequest{}).WithOwner(owner)
	assert.Equal(t, owner, r.Owner, "owner mismarch")
}

func TestSinglePermissionVerifyRequest_WithRegion(t *testing.T) {
	region := "region"
	r := (&SinglePermissionVerifyRequest{}).WithRegion(region)
	assert.Equal(t, region, r.Region, "region mismarch")
}

func TestSinglePermissionVerifyRequest_WithResource(t *testing.T) {
	resource := "resource"
	r := (&SinglePermissionVerifyRequest{}).WithResource(resource)
	assert.Equal(t, resource, r.Resource, "resource mismarch")
}

func TestSinglePermissionVerifyRequest_WithService(t *testing.T) {
	service := "service"
	r := (&SinglePermissionVerifyRequest{}).WithService(service)
	assert.Equal(t, service, r.Service, "service mismarch")
}

func TestMultiplePermissionVerifyRequest_AddPermission(t *testing.T) {
	permissions := []string{"p1", "p2", "p3"}
	r := &MultiplePermissionVerifyRequest{}

	for _, perm := range permissions {
		r.AddPermission(perm)
	}
	for _, perm := range permissions {
		assert.NotEqual(t, -1, searchString(r.Permission, perm),
			fmt.Sprintf("cannot found '%s'", perm))
	}
}

func TestMultiplePermissionVerifyRequest_AddResource(t *testing.T) {
	resources := []string{"r1", "r2", "r3"}
	r := &MultiplePermissionVerifyRequest{}

	for _, resource := range resources {
		r.AddResource(resource)
	}
	for _, resource := range resources {
		assert.NotEqual(t, -1, searchString(r.Resources, resource),
			fmt.Sprintf("cannot found '%s'", resource))
	}
}

func TestMultiplePermissionVerifyRequest_WithAuth(t *testing.T) {
	auth := &AuthenticationRequest{
		RequestId: "unique member for test",
	}
	r := (&MultiplePermissionVerifyRequest{}).WithAuth(auth)
	assert.Equal(t, auth, r.Auth, "auth mismarch")
}

func TestMultiplePermissionVerifyRequest_WithContext(t *testing.T) {
	context := &PermissionVerifyContext{
		IPAddr: "unique member for test",
	}
	r := (&MultiplePermissionVerifyRequest{}).WithContext(context)
	assert.Equal(t, context, r.Context, "context mismarch")
}

func TestMultiplePermissionVerifyRequest_WithOwner(t *testing.T) {
	owner := "owner"
	r := (&MultiplePermissionVerifyRequest{}).WithOwner(owner)
	assert.Equal(t, owner, r.Owner, "owner mismarch")
}

func TestMultiplePermissionVerifyRequest_WithRegion(t *testing.T) {
	region := "region"
	r := (&MultiplePermissionVerifyRequest{}).WithRegion(region)
	assert.Equal(t, region, r.Region, "region mismarch")
}

func TestMultiplePermissionVerifyRequest_WithService(t *testing.T) {
	service := "service"
	r := (&MultiplePermissionVerifyRequest{}).WithService(service)
	assert.Equal(t, service, r.Service, "service mismarch")
}

// return index if found, or -1
func searchString(strs []string, target string) int {
	for i, str := range strs {
		if str == target {
			return i
		}
	}
	return -1
}
