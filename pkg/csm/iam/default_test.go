package iam

import (
	"errors"
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestGetClient(t *testing.T) {
	oldClients := clients
	oldDefaultClient := DefaultClient
	clients = map[profile]*Client{
		"test": &Client{endpoint: "random member value for test"},
	}
	DefaultClient = nil
	defer func() {
		clients = oldClients
		DefaultClient = oldDefaultClient
	}()

	ass := assert.New(t)

	tests := []struct {
		name   string
		result *Client
		err    error
	}{
		{
			result: DefaultClient,
		}, {
			name:   "default",
			result: DefaultClient,
		}, {
			name:   "test",
			result: clients["test"],
		}, {
			name: "none",
			err:  errors.New("iam client profile not found"),
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			var actualClient *Client
			var actualErr error
			if test.name == "" {
				actualClient, actualErr = GetClient()
			} else {
				actualClient, actualErr = GetClient(test.name)
			}
			ass.Equal(test.result, actualClient, fmt.Sprintf("result mismatch for '%s'", test.name))
			ass.Equal(test.err, actualErr, fmt.Sprintf("err mismatch for '%s'", test.name))
		})
	}
}
