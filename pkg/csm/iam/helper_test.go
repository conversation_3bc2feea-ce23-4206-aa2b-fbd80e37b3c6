package iam

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

func TestGetUser(t *testing.T) {
	ctx, _ := context.NewCsmContextMock()
	_, err := GetUser(ctx)
	assert.Error(t, err, "should error on no-user")

	ctx.Set(ContextIAMUser, 42)
	_, err = GetUser(ctx)
	assert.Error(t, err, "should error on wront type")

	want := new(sdkIAM.User)
	ctx.Set(ContextIAMUser, want)
	actual, _ := GetUser(ctx)
	assert.Equal(t, want, actual, "user mismatch")
}

func TestMapUserId(t *testing.T) {
	oldUserIdMapProfile := userIdMapProfile
	userIdMapProfile = map[profile]userIdMap{
		"test": map[string]string{
			"id": "user",
		},
	}
	defer func() { userIdMapProfile = oldUserIdMapProfile }()
	assert.Equal(t, "user", MapUserId("id", "test"), "userid mismatch 1")
	assert.Equal(t, "id", MapUserId("id", "none"), "userid mismatch 2")
}
