package auth

// validator houses a set of rule needed for validation of a
// string value
type rules []rule

// rule inner allows for more flexible rules and just simply
// checks whether or not a value adheres to that rule
type rule interface {
	IsValid(value string) bool
}

// IsValid will iterate through all rules and see if any rules
// apply to the value and supports nested rules
func (r rules) IsValid(value string) bool {
	for _, rule := range r {
		if rule.IsValid(value) {
			return true
		}
	}
	return false
}

// mapRule generic rule for maps
type mapRule map[string]struct{}

// IsValid for the map rule satisfies whether it exists in the map
func (m mapRule) IsValid(value string) bool {
	_, ok := m[value]
	return ok
}

// whitelist is a generic rule for whitelisting
type whitelist struct {
	rule
}

// IsValid for whitelist checks if the value is within the whitelist
func (w whitelist) IsValid(value string) bool {
	return w.rule.IsValid(value)
}

// blacklist is a generic rule for blacklistingw
type blacklist struct {
	rule
}

// IsValid for blacklist checks if the value is within the blacklist
func (b blacklist) Is<PERSON>alid(value string) bool {
	return !b.rule.IsValid(value)
}
