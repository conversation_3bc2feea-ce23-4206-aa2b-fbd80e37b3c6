package auth

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

// BCE 鉴权机制可以参考
// https://cloud.baidu.com/doc/Reference/AuthenticationMechanism.html

// Signer xxx
type Signer struct {
	accessKey               string
	secretKey               string
	now                     time.Time // for debug only
	method                  string
	path                    string
	params                  url.Values
	headers                 http.Header
	ignoredHeaders          rules //ignored Headers 一定不加签的 headers
	ignoredHeadersGenerator func() rules
	expire                  int64
	withSignedHeader        bool
}

func NewSigner() *Signer {
	return &Signer{
		//以下header一定不加签
		ignoredHeadersGenerator: defaultIgnoredHeaders,
		withSignedHeader:        false,
		expire:                  3600,
	}
}

func (s *Signer) AuthKey(v *BceAuthKey) *Signer {
	s.accessKey = v.AccessKey
	s.secretKey = v.SecretKey
	return s
}
func (s *Signer) Now(v time.Time) *Signer       { s.now = v; return s }
func (s *Signer) Method(v string) *Signer       { s.method = v; return s }
func (s *Signer) Path(v string) *Signer         { s.path = v; return s }
func (s *Signer) Params(v url.Values) *Signer   { s.params = v; return s }
func (s *Signer) Headers(v http.Header) *Signer { s.headers = v; return s }
func (s *Signer) Expire(v int64) *Signer        { s.expire = v; return s }
func (s *Signer) WithSignedHeader() *Signer     { s.withSignedHeader = true; return s }

func (s *Signer) AddIgnoredHeader(headerKeys ...string) *Signer {
	s.ignoredHeaders = append(s.ignoredHeaders, whitelist{
		func(keys []string) mapRule {
			m := make(mapRule)
			for _, key := range keys {
				m[key] = struct{}{}
			}
			return m
		}(headerKeys),
	})
	return s
}

// GetSign xxx
func (s *Signer) GetSign() string {
	if s.now.IsZero() {
		s.now = time.Now()
	}

	stringPrefix := fmt.Sprintf("bce-auth-v1/%s/%s/%d", s.accessKey, GetCanonicalTime(s.now), s.expire)
	signKey := HmacSha256Hex(s.secretKey, stringPrefix)

	canonicalURI := getNormalizedString(s.path, true)
	canonicalQueryString := getCanonicalQueryString(s.params)
	canonicalHeaders, signHeaders := getCanonicalHeaders(s.headers, s.ignoredHeaders)

	canonicalRequest := s.method + "\n" + canonicalURI + "\n" + canonicalQueryString + "\n" + canonicalHeaders
	csmlog.Debugf("signer canonicalRequest:\n%s", canonicalRequest)
	signature := HmacSha256Hex(signKey, canonicalRequest)

	var result string
	if s.withSignedHeader == true {
		result = fmt.Sprintf("%s/%s/%s", stringPrefix, strings.Join(signHeaders, ";"), signature)
	} else {
		result = fmt.Sprintf("%s//%s", stringPrefix, signature)
	}
	csmlog.Debugf("signer auth: %s", result)
	return result
}

func (s *Signer) Reset() *Signer {
	s.accessKey = ""
	s.secretKey = ""
	s.now = time.Now()
	s.method = ""
	s.path = ""
	s.params = nil
	s.headers = nil
	s.withSignedHeader = false
	s.expire = 3600
	s.ignoredHeaders = s.ignoredHeadersGenerator()

	return s
}

// HmacSha256Hex xxx
func HmacSha256Hex(key, message string) string {
	mac := hmac.New(sha256.New, []byte(key))
	mac.Write([]byte(message))
	return fmt.Sprintf("%x", mac.Sum(nil))
}

// Md5Base64Hex xxx
func Md5Base64Hex(data []byte) string {
	digest := md5.Sum(data)
	encoding := base64.StdEncoding.EncodeToString(digest[:])
	return encoding
}

// Sha256Hex xxx
func Sha256Hex(data []byte) string {
	return fmt.Sprintf("%x", sha256.Sum256(data))
}

func GetCanonicalTime(now time.Time) string {
	year, mon, day := now.UTC().Date()
	hour, min, sec := now.UTC().Clock()
	return fmt.Sprintf("%04d-%02d-%02dT%02d:%02d:%02dZ", year, mon, day, hour, min, sec)
}

func getNormalizedString(s string, skipSlash bool) string {
	r := url.QueryEscape(s)
	// Go的QueryEscape与百度云鉴权要求的编码格式不一致，会将空格编码为+，而非%20，需要特殊处理
	// https://github.com/aws/aws-sdk-go/blob/60293cacbc1ed27eb78374e1b24a3ea55a74d1b6/aws/signer/v4/v4.go#L635
	r = strings.Replace(r, "+", "%20", -1)
	if skipSlash {
		r = strings.Replace(r, "%2F", "/", -1)
	}
	return r
}

func getCanonicalQueryString(query url.Values) string {
	result := []string{}
	for k := range query {
		if k == "authorization" {
			continue
		}
		v := query.Get(k)
		if len(v) == 0 {
			result = append(result, getNormalizedString(k, false)+"=")
		} else {
			result = append(result, getNormalizedString(k, false)+"="+getNormalizedString(v, false))
		}
	}
	sort.Strings(result)
	return strings.Join(result, "&")
}

func getCanonicalHeaders(headers http.Header, r rule) (string, []string) {
	standardHeaders := []string{
		"host", "content-md5", "content-length", "content-type",
	}
	var result []string
	var signHeaders []string
	for key := range headers {
		keyLower := strings.ToLower(key)
		value := headers.Get(key)
		if !r.IsValid(keyLower) && (strings.HasPrefix(keyLower, "x-bce-") || contains(standardHeaders, keyLower)) {
			result = append(result, keyLower+":"+getNormalizedString(value, false))
			signHeaders = append(signHeaders, keyLower)
		}
	}
	sort.Strings(result)
	sort.Strings(signHeaders)
	return strings.Join(result, "\n"), signHeaders
}

func contains(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

func defaultIgnoredHeaders() rules {
	return rules{
		whitelist{
			mapRule{
				"authorization": struct{}{},
				"user-agent":    struct{}{},
				"x-auth-token":  struct{}{},
				"app":           struct{}{},
			},
		},
	}
}
