package auth_test

import (
	"net/http"
	"net/url"
	"testing"
	"time"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/auth"
)

var accessKey = "b5e478e040214973a2c44d49fba0adb4"
var secretKey = "49589fb2c3da4041b8fd9cd9bfbdeef3"
var prefix = []byte("\xA8\x33\x00\x00" + // uint32 data_len
	"\x02\x00\x00\x00\x00\x00\x00\x00" + // uint64 pic_id
	"\x00\x00\x00\x00\x00\x00\x00\x00" + // uint64 pic_sign
	"\x00\x00\x00\x00\x70\x43\xE4\x45\x00\x00\x00\x00") // char[12] reserved

func TestHmacSha256Hex(t *testing.T) {
	authStringPrefix := "bce-auth-v1/" + accessKey + "/2016-01-04T06:12:04Z/1800"
	result := auth.HmacSha256Hex(secretKey, authStringPrefix)
	expectResult := "512198988274fa9c66458ba83b0fd8a752102e8afd72c8d6d297ba7fe84ac858"
	if result != expectResult {
		t.Errorf("want '%s', get '%s'", expectResult, result)
	}
}

func TestGetSign(t *testing.T) {
	authKey := auth.NewBceAuthKey(accessKey, secretKey)
	headers := http.Header{}
	headers.Add("Content-Length", "13256")
	headers.Add("Content-MD5", "ujOLK9GE1xdbYdfKvfI1BA==")
	headers.Add("Host", "bos.qasandbox.bcetest.baidu.com")
	headers.Add("x-bce-content-sha256", "4604e6530e5a06dec3099e77977cab6c7c88eae21005f6edd66deffc203a5e6e")
	headers.Add("x-bce-date", "2016-01-04T06:12:04Z")
	headers.Add("x-bce-request-id", "f98023ac-1189-412b-93f5-859970585dce")

	params := url.Values{}
	params.Add("foo", "bar")
	params.Add("path", "/aaa/bbb/ccc")

	signer := auth.NewSigner()

	result := signer.
		AuthKey(authKey).
		Method("PUT").
		Path("/v1/hiphotos/2").
		Headers(headers).
		Params(params).
		Now(time.Unix(1451887924, 0)).
		Expire(1800).
		GetSign()

	expectResult := "bce-auth-v1/b5e478e040214973a2c44d49fba0adb4/2016-01-04T06:12:04Z/1800//516852c8fcbf354a0d813bd24d5f51712dfbfac7649bbbdcdc10304246e47fed"
	if result != expectResult {
		t.Errorf("want '%s', get '%s'", expectResult, result)
	}

	result = signer.Reset().
		AuthKey(authKey).
		Method("POST").
		Path("/v1/hiphotos/3").
		Headers(headers).
		Params(params).
		Now(time.Unix(1451887924, 0)).
		Expire(1800).
		GetSign()

	expectResult = "bce-auth-v1/b5e478e040214973a2c44d49fba0adb4/2016-01-04T06:12:04Z/1800//49ee651663b3d46d3a8205d511da892627547e0231a928c06eeaa2d2944fa8bd"
	if result != expectResult {
		t.Errorf("want '%s', get '%s'", expectResult, result)
	}
}
