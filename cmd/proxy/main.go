//go:build proxy
// +build proxy

package main

import (
	"bytes"
	"fmt"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/config"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
	"io"
	"net/http"
	"net/http/httputil"
	"net/url"
	"time"
)

func main() {
	// 监听 / 路径的请求，并将其反向代理到目标地址
	http.HandleFunc("/", proxyHandler)

	addr := ":8089"
	// 从配置文件读取配置
	config.InitConfig()
	// 初始化日志格式
	csmlog.InitLogs()

	csmlog.Infof("Proxy server is start，listening on %s port...", addr)
	if err := http.ListenAndServe(addr, nil); err != nil {
		csmlog.Errorf("ListenAndServe error: %v", err)
	}
}

// proxyHandler 是一个 HTTP 处理器函数，用于处理代理请求。
// 它将请求转发到指定的目标服务器，并记录请求的开始时间、处理耗时以及日志。
//
// 参数：
// w http.ResponseWriter：用于向客户端发送响应的 HTTP 响应写入器。
// r *http.Request：表示 HTTP 请求的对象。
func proxyHandler(w http.ResponseWriter, r *http.Request) {
	target := r.Header.Get("X-Target")
	// 检查目标地址是否为空
	if target == "" {
		csmlog.Errorf("The X-Target header is missing！")
		http.Error(w, "X-Target header is missing", http.StatusBadRequest)
		return
	}

	// 构造目标 URL，示例中默认使用 http 协议
	targetURL, err := url.Parse("http://" + target)
	if err != nil {
		http.Error(w, "Invalid target URL", http.StatusBadRequest)
		return
	}

	// 使用 NewSingleHostReverseProxy 创建反向代理
	proxy := httputil.NewSingleHostReverseProxy(targetURL)
	// 设置一个较短的 FlushInterval 以便在 WebSocket 场景下能够及时将数据推送给客户端
	proxy.FlushInterval = 50 * time.Millisecond

	// 记录请求开始时间
	startTime := time.Now()

	// 自定义错误处理逻辑
	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		csmlog.Infof("Proxy Error: %v", err)
		http.Error(w, "Proxy Error: "+err.Error(), http.StatusBadGateway)
	}

	// 处理请求体，确保正确转发
	// 如果请求体不为空，且使用了 Content-Length，确保正确传递。
	if r.ContentLength > 0 {
		// 读取请求体，并复制到目标请求中
		body, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read request body", http.StatusInternalServerError)
			return
		}
		// 将读取的 body 写回请求
		r.Body = io.NopCloser(bytes.NewReader(body))
		// 设置目标请求的 Content-Length（如果有的话）
		r.ContentLength = int64(len(body))
		// 保持 Content-Type 和其他必要的 header
		r.Header.Set("Content-Length", fmt.Sprintf("%d", len(body)))
	}

	requestMethod := r.Method
	requestURL := r.URL.String()

	proxy.ServeHTTP(w, r)

	// 记录请求的耗时
	duration := time.Since(startTime)

	// 记录日志：请求方法、URL、耗时、响应码
	csmlog.Infof("requestMethod: %s, requestURL: %s, targetURL: %s, duration: %v, Code: %d", requestMethod, requestURL, targetURL, duration, http.StatusOK)
}
