package options

import (
	utilerrors "k8s.io/apimachinery/pkg/util/errors"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/rpc/db"
	serverOptions "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmflag"
)

type CsmOptions struct {
	Region                      string
	ServiceName                 string
	BceServiceRole              string
	EksAccountIds               []string
	EksProfile                  string
	EksProfileGreyClusterId     string
	EksProfileGreyClusterName   string
	EksProfileOnlineClusterId   string
	EksProfileOnlineClusterName string

	UserNames map[string]string

	ServerOptions *serverOptions.ServerOptions

	DbConfig *db.DbConfig
}

func NewCsmServerOption() *CsmOptions {
	csmServerOption := &CsmOptions{
		ServerOptions: serverOptions.NewServerOptions(),
		DbConfig:      &db.DbConfig{},
	}
	// todo tracing
	return csmServerOption
}

func (csmServerOption *CsmOptions) AddFlags(s *csmflag.FlagSet) {
	s.StringVar(&csmServerOption.BceServiceRole, "bceServiceRole", csmServerOption.BceServiceRole, "bce service role")
	s.StringVar(&csmServerOption.Region, "region", csmServerOption.Region, "service region")
	s.StringVar(&csmServerOption.ServiceName, "serviceName", csmServerOption.ServiceName, "service name")

	// register db config
	csmServerOption.DbConfig.AddFlags(s)
}

func (csmServerOption *CsmOptions) Validate() error {
	var errors []error
	errors = append(errors, csmServerOption.ServerOptions.Validate()...)
	return utilerrors.NewAggregate(errors)
}
