package main

import (
	"math/rand"
	"time"

	"github.com/spf13/pflag"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/config"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmflag"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

func main() {
	defer csmlog.FlushLogs()

	// 配置 CSM 参数
	opt := options.NewCsmServerOption()
	opt.AddFlags(csmflag.CommandLine)

	// 初始化默认配置
	app.InitCsmServerConfig()

	// 从配置文件读取配置
	config.InitConfig()

	// 初始化日志格式
	csmlog.InitLogs()

	if viper.GetBool("server.print_flag") {
		pflag.VisitAll(func(flag *pflag.Flag) {
			csmlog.Infof("FLAG: --%s=%q", flag.Name, flag.Value)
		})
	}

	// 随机种子数
	rand.Seed(time.Now().UTC().UnixNano())

	csmlog.Infof("Start CSM Server")
	if err := app.StartCsmServer(opt); err != nil {
		csmlog.Fatalf("start CSM server error", err)
	}
}
