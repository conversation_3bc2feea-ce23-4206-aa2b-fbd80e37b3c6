package app

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/registercenter"
	utilerrors "k8s.io/apimachinery/pkg/util/errors"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/rpc/db"
	serverOptions "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/options"
)

type ServerOptions struct {
	BCEServiceRole             string
	BCRServiceRole             string
	Region                     string
	CceEndpoints               map[string]string
	AuthEndpoint               string
	ApplicationEndpoint        string
	GateExternalEndpoints      map[string]string
	CceAeskeyStr               string
	IamProfile                 string
	CcrIamProfile              string
	ProxyEnableCheckPermission bool
}

type CsmServerOptions struct {
	ServerOptions *serverOptions.ServerOptions

	csmOptions *ServerOptions
}

func InitCsmServerConfig() {
	serverOptions.InitServerConfig()
	registercenter.InitServingOptions()
	db.InitMysqlConfig()
}

func (s *CsmServerOptions) Validate() error {
	var errors []error
	errors = append(errors, s.ServerOptions.Validate()...)
	return utilerrors.NewAggregate(errors)
}
