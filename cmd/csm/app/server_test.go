package app

import (
	"reflect"
	"testing"

	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/rpc/db"
	serverOptions "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/options"
)

func Test_getUserInfo(t *testing.T) {
	type args struct {
		csmServerOption *options.CsmOptions
	}
	tests := []struct {
		name    string
		args    args
		want    *options.CsmOptions
		wantErr bool
	}{
		{
			name: "test-getUserInfo",
			args: args{
				csmServerOption: options.NewCsmServerOption(),
			},
			want: &options.CsmOptions{
				UserNames:     map[string]string{},
				ServerOptions: serverOptions.NewServerOptions(),
				DbConfig:      &db.DbConfig{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			viper.Set("users.userInfo", "")
			got := getUserInfo(tt.args.csmServerOption)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getUserInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}
