package app

import (
	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	serverOptions "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/signal"
)

func StartCsmServer(options *options.CsmOptions) error {
	iam.Init()

	options.ServerOptions = serverOptions.NewServerOptions()
	options = getUserInfo(options)

	c, err := core.NewCsmCore(options)
	if err != nil {
		return errors.Wrap(err, "start APIServer core failed")
	}

	serverSetupConfig := server.NewSetupConfig(registerHandler(c))
	if err = options.Validate(); err != nil {
		return errors.Wrap(err, "validate options failed")
	}

	if err = options.ServerOptions.ApplyTo(serverSetupConfig); err != nil {
		return errors.Wrap(err, "apply server setup config failed")
	}

	webServer, err := serverSetupConfig.Setup()
	if err != nil {
		return errors.Wrap(err, "setup generic server failed")
	}
	stopCh := signal.SetupSignalHandler()
	webServer.Run(stopCh)
	return nil
}

func registerHandler(c *core.APIServerCore) server.HandlerRegister {
	return func(e *echo.Echo) {
		router.RegisterRouter(c)(e)
	}
}

// 获取配置文件中user信息
func getUserInfo(csmServerOption *options.CsmOptions) *options.CsmOptions {
	userInfo := viper.GetStringMapStringSlice("users.userInfo")
	userNames := make(map[string]string)
	for user := range userInfo {
		accountIDs := userInfo[user]
		for _, accountID := range accountIDs {
			userNames[accountID] = user
		}
	}
	csmServerOption.UserNames = userNames
	return csmServerOption
}
