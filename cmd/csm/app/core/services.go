package core

import (
	"net/http"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// GetMeshInstanceServices 获取一个网格实例下的所有服务
func (core *APIServerCore) GetMeshInstanceServices(ctx csmContext.CsmContext) (error error) {
	// 支持指定cluster和namespace筛选服务
	mrp := meta.NewLaneServiceParams()
	if err := ctx.Bind(mrp); err != nil {
		return err
	}
	res, err := core.ServicesSerivce.GetMeshInstanceServices(ctx, mrp)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, res)
}

// GetServiceDetails 获取服务详情
func (core *APIServerCore) GetServiceDetails(ctx csmContext.CsmContext) (error error) {
	mrp := meta.NewCsmMeshRequestParams()
	if err := ctx.Bind(mrp); err != nil {
		return err
	}
	res, err := core.ServicesSerivce.GetServiceDetails(ctx, mrp)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, res)
}
