package core

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"istio.io/client-go/pkg/apis/networking/v1alpha3"
	istionetworkingv1alpha3 "istio.io/api/networking/v1alpha3"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// TestExtractServiceNamesFromVS 测试从VirtualService中提取服务名称
func TestExtractServiceNamesFromVS(t *testing.T) {
	core := &APIServerCore{}

	t.Run("单服务模式", func(t *testing.T) {
		vs := &v1alpha3.VirtualService{
			ObjectMeta: metav1.ObjectMeta{
				Name: "test-route",
			},
			Spec: istionetworkingv1alpha3.VirtualService{
				Http: []*istionetworkingv1alpha3.HTTPRoute{
					{
						Route: []*istionetworkingv1alpha3.HTTPRouteDestination{
							{
								Destination: &istionetworkingv1alpha3.Destination{
									Host: "service1.namespace1.svc.cluster.local",
								},
							},
						},
					},
				},
			},
		}

		serviceNames := core.extractServiceNamesFromVS(vs)
		assert.Equal(t, 1, len(serviceNames))
		assert.Equal(t, "service1", serviceNames[0])
	})

	t.Run("多服务模式-按比例分发", func(t *testing.T) {
		vs := &v1alpha3.VirtualService{
			ObjectMeta: metav1.ObjectMeta{
				Name: "test-route-multi",
			},
			Spec: istionetworkingv1alpha3.VirtualService{
				Http: []*istionetworkingv1alpha3.HTTPRoute{
					{
						Route: []*istionetworkingv1alpha3.HTTPRouteDestination{
							{
								Destination: &istionetworkingv1alpha3.Destination{
									Host: "service1.namespace1.svc.cluster.local",
								},
								Weight: 50,
							},
							{
								Destination: &istionetworkingv1alpha3.Destination{
									Host: "service2.namespace2.svc.cluster.local",
								},
								Weight: 50,
							},
						},
					},
				},
			},
		}

		serviceNames := core.extractServiceNamesFromVS(vs)
		assert.Equal(t, 2, len(serviceNames))
		assert.Contains(t, serviceNames, "service1")
		assert.Contains(t, serviceNames, "service2")
	})

	t.Run("多服务模式-按模型名称分发", func(t *testing.T) {
		vs := &v1alpha3.VirtualService{
			ObjectMeta: metav1.ObjectMeta{
				Name: "test-route-model",
			},
			Spec: istionetworkingv1alpha3.VirtualService{
				Http: []*istionetworkingv1alpha3.HTTPRoute{
					{
						Match: []*istionetworkingv1alpha3.HTTPMatchRequest{
							{
								Headers: map[string]*istionetworkingv1alpha3.StringMatch{
									"x-model-header": {
										MatchType: &istionetworkingv1alpha3.StringMatch_Prefix{
											Prefix: "model1",
										},
									},
								},
							},
						},
						Route: []*istionetworkingv1alpha3.HTTPRouteDestination{
							{
								Destination: &istionetworkingv1alpha3.Destination{
									Host: "ollama-service-v1.ollama.svc.cluster.local",
								},
							},
						},
					},
					{
						Match: []*istionetworkingv1alpha3.HTTPMatchRequest{
							{
								Headers: map[string]*istionetworkingv1alpha3.StringMatch{
									"x-model-header": {
										MatchType: &istionetworkingv1alpha3.StringMatch_Prefix{
											Prefix: "model2",
										},
									},
								},
							},
						},
						Route: []*istionetworkingv1alpha3.HTTPRouteDestination{
							{
								Destination: &istionetworkingv1alpha3.Destination{
									Host: "ollama-service-v2.ollama.svc.cluster.local",
								},
							},
						},
					},
				},
			},
		}

		serviceNames := core.extractServiceNamesFromVS(vs)
		assert.Equal(t, 2, len(serviceNames))
		assert.Contains(t, serviceNames, "ollama-service-v1")
		assert.Contains(t, serviceNames, "ollama-service-v2")
	})

	t.Run("去重测试", func(t *testing.T) {
		vs := &v1alpha3.VirtualService{
			ObjectMeta: metav1.ObjectMeta{
				Name: "test-route-duplicate",
			},
			Spec: istionetworkingv1alpha3.VirtualService{
				Http: []*istionetworkingv1alpha3.HTTPRoute{
					{
						Route: []*istionetworkingv1alpha3.HTTPRouteDestination{
							{
								Destination: &istionetworkingv1alpha3.Destination{
									Host: "service1.namespace1.svc.cluster.local",
								},
							},
						},
					},
					{
						Route: []*istionetworkingv1alpha3.HTTPRouteDestination{
							{
								Destination: &istionetworkingv1alpha3.Destination{
									Host: "service1.namespace1.svc.cluster.local", // 重复的服务
								},
							},
						},
					},
				},
			},
		}

		serviceNames := core.extractServiceNamesFromVS(vs)
		assert.Equal(t, 1, len(serviceNames)) // 应该去重
		assert.Equal(t, "service1", serviceNames[0])
	})

	t.Run("空路由测试", func(t *testing.T) {
		vs := &v1alpha3.VirtualService{
			ObjectMeta: metav1.ObjectMeta{
				Name: "test-route-empty",
			},
			Spec: istionetworkingv1alpha3.VirtualService{
				Http: []*istionetworkingv1alpha3.HTTPRoute{},
			},
		}

		serviceNames := core.extractServiceNamesFromVS(vs)
		assert.Equal(t, 0, len(serviceNames))
	})
}
