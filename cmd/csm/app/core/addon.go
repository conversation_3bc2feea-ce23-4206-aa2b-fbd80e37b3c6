package core

import (
	"net/http"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/addon"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

func (core *APIServerCore) ListAddons(ctx context.CsmContext) (err error) {
	_, aiErr := iam.GetAccountId(ctx)
	if aiErr != nil {
		return aiErr
	}

	// 请求参数校验
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException("'" + constants.InstanceIDPathParam + "'")
	}

	args := &addon.ListConfig{}
	args.InstanceUUID = instanceUUID
	addons, err := core.addonService.ListAllAddons(ctx, args)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, addons.AddOnInfo)
}

func (core *APIServerCore) Install(ctx context.CsmContext) (err error) {
	_, aiErr := iam.GetAccountId(ctx)
	if aiErr != nil {
		return aiErr
	}
	args := &addon.InstallConfig{}
	if err = ctx.Bind(args); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 请求参数校验
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" || args.Name == "" {
		return csmErr.NewMissingParametersException("addons install parameter invalid")
	}

	args.InstanceUUID = instanceUUID
	err = core.addonService.InstallAddons(ctx, args)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

func (core *APIServerCore) UnInstall(ctx context.CsmContext) (err error) {
	_, aiErr := iam.GetAccountId(ctx)
	if aiErr != nil {
		return aiErr
	}
	args := &addon.UninstallConfig{}
	if err = ctx.Bind(args); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 请求参数校验
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" || args.Name == "" {
		return csmErr.NewMissingParametersException("addons uninstall parameter invalid")
	}

	args.InstanceUUID = instanceUUID
	err = core.addonService.UnInstallAddons(ctx, args)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}
