package core

import (
	"net/http"
	"strings"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/api/v1/cnap"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/page"
)

func (core *APIServerCore) GetCrd(ctx context.CsmContext) (err error) {
	// TODO 排期修改url，namespace挪到query中
	// 适配cluster级别的crd
	namespace := ctx.Param("namespace")
	if namespace == "*" {
		namespace = ""
	}

	param := &meta.CrdParam{
		InstanceUUID: ctx.Param(constants.InstanceIDPathParam),
		CrdKey: meta.CrdKey{
			Namespace: namespace,
			Kind:      ctx.QueryParam("kind"),
			Name:      ctx.QueryParam("name"),
		},
	}

	crd, err := core.CrdService.GetCrd(ctx, param)
	if err != nil {
		return err
	}
	crdView := &internal.CrdView{}
	err = crdView.FromModel(crd)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, crdView)
}

func (core *APIServerCore) GetCrds(ctx context.CsmContext) (err error) {
	p := page.GetPageParam(ctx, map[string]string{})
	params := &meta.CrdParams{
		CsmMeshRequestParams: &meta.CsmMeshRequestParams{
			InstanceUUID: ctx.Param(constants.InstanceIDPathParam),
			PageSize:     p.PageSize,
			PageNo:       p.PageNo,
			Keyword:      ctx.QueryParam("keyword"),
			KeywordType:  ctx.QueryParam("keywordType"),
			Order:        p.Order,
			OrderBy:      p.OrderBy,
		},
	}
	crds, pr, err := core.CrdService.GetCrds(ctx, params)
	if err != nil {
		return err
	}

	crdViews := make([]internal.CrdView, 0)
	for _, crd := range crds {
		crdView := internal.CrdView{}
		err := crdView.FromModel(&crd)
		if err != nil {
			return err
		}
		crdView.UpdatedAt = crd.UpdatedAt.Format("2006-01-02 15:04:05")
		crdViews = append(crdViews, crdView)
	}

	response := internal.CrdPageView{
		PageSize:   pr.PageSize,
		PageNo:     pr.PageNo,
		TotalCount: pr.TotalCount,
		Result:     crdViews,
	}

	return ctx.JSON(http.StatusOK, response)
}

func (core *APIServerCore) GetCrdsForOpenAPI(ctx context.CsmContext) (err error) {
	p := page.GetPageParam(ctx, map[string]string{})

	labels := make(map[string]string)
	labelStr := ctx.QueryParam("labels")
	if len(strings.TrimSpace(labelStr)) > 0 {
		labelSlice := strings.Split(labelStr, ",")
		for _, l := range labelSlice {
			kv := strings.Split(l, "=")
			labels[kv[0]] = kv[1]
		}
	}
	params := &meta.CrdParams{
		CsmMeshRequestParams: &meta.CsmMeshRequestParams{
			InstanceUUID: ctx.Param(cnap.MeshInstanceIdPathParam),
			PageSize:     100, // TODO: 待优化
			PageNo:       p.PageNo,
			Keyword:      ctx.QueryParam("keyword"),
			KeywordType:  ctx.QueryParam("keywordType"),
			Order:        p.Order,
			OrderBy:      p.OrderBy,
		},
		Namespace: ctx.QueryParam("namespace"),
		Kind:      ctx.QueryParam("kind"),
		Name:      ctx.QueryParam("name"),
		Labels:    labels,
	}
	crds, pr, err := core.CrdService.GetCrds(ctx, params)
	if err != nil {
		return err
	}

	crdViews := make([]internal.CrdView, 0)
	for _, crd := range crds {
		crdView := internal.CrdView{}
		err := crdView.FromModel(&crd)
		if err != nil {
			return err
		}
		crdView.UpdatedAt = crd.UpdatedAt.Format("2006-01-02 15:04:05")
		crdViews = append(crdViews, crdView)
	}

	response := internal.CrdPageView{
		PageSize:   pr.PageSize,
		PageNo:     pr.PageNo,
		TotalCount: pr.TotalCount,
		Result:     crdViews,
	}

	return ctx.JSON(http.StatusOK, response)
}

func (core *APIServerCore) BatchCrd(ctx context.CsmContext) (err error) {
	reqBody := &internal.CrdView{}

	if err = ctx.Bind(reqBody); err != nil {
		return errors.Errorf("Binding content which is in body failed.")
	}

	param := &meta.CrdParam{
		InstanceUUID: ctx.Param(constants.InstanceIDPathParam),
		Content:      reqBody.Content,
	}

	createCrdsInfo, err := core.CrdService.BatchCrd(ctx, param)
	if err != nil {
		return err
	}

	response := &internal.CsmMeshResponse{
		Result: createCrdsInfo,
	}
	// 全失败时，报错
	if len(createCrdsInfo.SuccessCrdList) == 0 && createCrdsInfo.FailedCrdMessage != "" {
		return ctx.JSON(http.StatusInternalServerError, response)
	}

	// 只要有成功的CRD就不算失败，返回创建成功的实例信息和错误信息。
	return ctx.JSON(http.StatusOK, response)
}

// TODO 一站式交互接口，修改需同步一站式一同修改接口
func (core *APIServerCore) CreateCrdsForOpenAPI(ctx context.CsmContext) (err error) {
	reqBody := &internal.CrdView{}

	if err = ctx.Bind(reqBody); err != nil {
		return errors.Errorf("Binding content which is in body failed.")
	}

	param := &meta.CrdParam{
		InstanceUUID: ctx.Param(cnap.MeshInstanceIdPathParam),
		Content:      reqBody.Content,
	}

	crd, err := core.CrdService.CreateCrd(ctx, param)
	if err != nil {
		return err
	}
	crdView := &internal.CrdView{}
	err = crdView.FromModel(crd)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, cnap.SuccessResponse{
		Success: "true",
	})
}

func (core *APIServerCore) UpdateCrd(ctx context.CsmContext) (err error) {
	reqBody := &internal.CrdView{}

	if err = ctx.Bind(reqBody); err != nil {
		return errors.Errorf("Binding content which is in body failed.")
	}
	// TODO 排期修改url，namespace挪到query中
	// 适配cluster级别的crd
	namespace := ctx.Param("namespace")
	if namespace == "*" {
		namespace = ""
	}

	param := &meta.CrdParam{
		InstanceUUID: ctx.Param(constants.InstanceIDPathParam),
		CrdKey: meta.CrdKey{
			Namespace: namespace,
			Kind:      ctx.QueryParam("kind"),
			Name:      ctx.QueryParam("name"),
		},
		Content: reqBody.Content,
	}

	crd, err := core.CrdService.UpdateCrd(ctx, param)
	if err != nil {
		return err
	}
	crdView := &internal.CrdView{}
	err = crdView.FromModel(crd)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, crdView)
}

// TODO: 判断是否有权限
func (core *APIServerCore) UpdateCrdsForOpenAPI(ctx context.CsmContext) (err error) {
	reqBody := &internal.CrdView{}

	if err = ctx.Bind(reqBody); err != nil {
		return errors.Errorf("Binding content which is in body failed.")
	}

	namespace := ctx.QueryParam("namespace")
	kind := ctx.QueryParam("kind")
	name := ctx.QueryParam("name")
	if len(namespace) == 0 || len(kind) == 0 || len(name) == 0 {
		return errors.Errorf("namespace, kind, name must not be empty")
	}

	param := &meta.CrdParam{
		InstanceUUID: ctx.Param(cnap.MeshInstanceIdPathParam),
		CrdKey: meta.CrdKey{
			Namespace: namespace,
			Kind:      kind,
			Name:      name,
		},
		Content: reqBody.Content,
	}

	crd, err := core.CrdService.UpdateCrd(ctx, param)
	if err != nil {
		return err
	}
	crdView := &internal.CrdView{}
	err = crdView.FromModel(crd)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, cnap.SuccessResponse{
		Success: "true",
	})
}

func (core *APIServerCore) DeleteCrd(ctx context.CsmContext) (err error) {
	reqBody := &internal.CrdListView{}

	if err = ctx.Bind(reqBody); err != nil {
		return errors.Errorf("Binding content which is in body failed.")
	}

	crdKeys := make([]meta.CrdKey, 0)
	for _, view := range reqBody.CrdList {
		// TODO 排期修改url，namespace挪到query中
		// 适配cluster级别的crd
		namespace := view.Namespace
		if namespace == "*" {
			namespace = ""
		}
		ck := meta.CrdKey{
			Namespace: namespace,
			Kind:      view.Kind,
			Name:      view.Name,
		}
		crdKeys = append(crdKeys, ck)
	}
	params := &meta.CrdParams{
		CsmMeshRequestParams: &meta.CsmMeshRequestParams{
			InstanceUUID: ctx.Param(constants.InstanceIDPathParam),
		},
		Keys: crdKeys,
	}
	err = core.CrdService.DeleteCrds(ctx, params)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, "")
}

// TODO: 校验是否有权限删除
func (core *APIServerCore) DeleteCrdsForOpenAPI(ctx context.CsmContext) (err error) {
	namespace := ctx.QueryParam("namespace")
	kind := ctx.QueryParam("kind")
	name := ctx.QueryParam("name")
	if len(namespace) == 0 || len(kind) == 0 || len(name) == 0 {
		return errors.Errorf("namespace, kind, name must not be empty")
	}

	crdKeys := make([]meta.CrdKey, 0)
	ck := meta.CrdKey{
		Namespace: namespace,
		Kind:      kind,
		Name:      name,
	}
	crdKeys = append(crdKeys, ck)
	params := &meta.CrdParams{
		CsmMeshRequestParams: &meta.CsmMeshRequestParams{
			InstanceUUID: ctx.Param(cnap.MeshInstanceIdPathParam),
		},
		Keys: crdKeys,
	}
	err = core.CrdService.DeleteCrds(ctx, params)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, cnap.SuccessResponse{
		Success: "true",
	})
}
