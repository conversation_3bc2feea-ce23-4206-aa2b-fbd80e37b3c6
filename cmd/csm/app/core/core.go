package core

import (
	"github.com/spf13/viper"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/aigateway"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/eip"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/api/v1/cnap"
	instancesModel "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/rpc/db"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/addon"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/aiingress"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/aiservices"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/blb"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/bls"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/crd"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/diagnosis"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/gateway"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/lane"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/monitor"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/multiprotocol"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/namespace"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/overview"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/registercenter"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/services"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/sidecar"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/sugar"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/trace"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/vpc"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/whitelist"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/validate"
)

type APIServerCore struct {
	*options.CsmOptions

	eipService            eip.ServiceInterface
	aigatewayModel        aigateway.ServiceInterface
	OverviewService       overview.ServiceInterface
	CrdService            crd.ServiceInterface
	ClusterService        cluster.ServiceInterface
	InstancesService      instances.ServiceInterface
	ServicesSerivce       services.ServiceInterface
	VersionService        version.ServiceInterface
	SidecarService        sidecar.ServiceInterface
	WhiteListService      whitelist.ServiceInterface
	NamespaceService      namespace.ServiceInterface
	MonitorService        monitor.ServiceInterface
	VpcService            vpc.ServiceInterface
	BlbService            blb.ServiceInterface
	GatewayService        gateway.ServiceInterface
	sugarService          sugar.ServiceInterface
	InstancesModel        instancesModel.ServiceInterface
	MultiProtocolService  multiprotocol.ServiceInterface
	cceService            cce.ClientInterface
	addonService          addon.AddonsService
	diagnosisService      diagnosis.ServiceInterface
	laneService           lane.ServiceInterface
	blsService            bls.ServiceInterface
	RegisterCenterService registercenter.Interface
	traceService          trace.ServiceInterface
	aiIngressService      aiingress.ServiceInterface
	aiServiceService      aiservices.ServiceInterface
}

// NewCsmCore NewCsmCore 创建一个 APIServerCore 实例，并返回该实例和错误信息。
// 参数 opt 是指向 options.CsmOptions 类型的指针，用于传入配置选项。
// 返回值：*APIServerCore，表示 APIServerCore 实例；error，表示可能发生的错误。
func NewCsmCore(opt *options.CsmOptions) (*APIServerCore, error) {
	d, err := db.NewMysqlEngine(opt.DbConfig)
	if err != nil {
		return nil, err
	}

	// 注册自定义 validate
	registerValidations()

	core := &APIServerCore{
		CsmOptions:            opt,
		OverviewService:       overview.NewOverviewService(overview.NewOption(d)),
		CrdService:            crd.NewCrdService(crd.NewOption(d)),
		ClusterService:        cluster.NewClusterService(cluster.NewOption(d)),
		InstancesService:      instances.NewInstanceService(instances.NewOption(d)),
		ServicesSerivce:       services.NewServicesService(services.NewOption(d)),
		aigatewayModel:        aigateway.NewAIGatewayService(aigateway.NewOption(d)),
		VersionService:        version.NewVersionService(version.NewOption()),
		SidecarService:        sidecar.NewInstanceService(sidecar.NewOption(d)),
		WhiteListService:      whitelist.NewWhiteListService(),
		NamespaceService:      namespace.NewNamespaceService(namespace.NewOption(d)),
		MonitorService:        monitor.NewMonitorService(monitor.NewOption(d)),
		InstancesModel:        instancesModel.NewInstancesService(instancesModel.NewOption(d)),
		VpcService:            vpc.NewVPCService(),
		eipService:            eip.NewEIPService(),
		BlbService:            blb.NewBlbService(blb.NewOption(d)),
		GatewayService:        gateway.NewGatewayService(gateway.NewOption(d)),
		sugarService:          sugar.NewSugarService(sugar.NewOption(d)),
		MultiProtocolService:  multiprotocol.NewService(),
		cceService:            cce.NewClientService(),
		addonService:          addon.NewAddonService(addon.NewOption(d, nil)),
		diagnosisService:      diagnosis.NewDiagnosisService(diagnosis.NewOption(d)),
		laneService:           lane.NewService(lane.NewOption(d)),
		blsService:            bls.NewBlsService(bls.NewOption(d)),
		RegisterCenterService: registercenter.NewRegisterCenterService(registercenter.NewOption(d)),
		traceService:          trace.NewTraceService(trace.NewOption(d)),
		aiIngressService:      aiingress.NewAiIngressService(aiingress.NewOption(d)),
		aiServiceService:      aiservices.NewAIServiceService(aiservices.NewOption(d)),
	}

	// initial region、service、bceServiceRole
	core.Region = viper.GetString("region")
	core.ServiceName = viper.GetString("serviceName")
	core.BceServiceRole = viper.GetString("bceServiceRole")

	core.EksAccountIds = viper.GetStringSlice(meta.EksAccountIds)
	core.EksProfile = viper.GetString(meta.EksProfile)
	core.EksProfileGreyClusterId = viper.GetString(meta.EksProfileGreyClusterId)
	core.EksProfileGreyClusterName = viper.GetString(meta.EksProfileGreyClusterName)
	core.EksProfileOnlineClusterId = viper.GetString(meta.EksProfileOnlineClusterId)
	core.EksProfileOnlineClusterName = viper.GetString(meta.EksProfileOnlineClusterName)

	return core, nil
}

func registerValidations() {
	validate.Validator.RegisterStructValidation(cnap.InstanceStructLevelValidation, cnap.Instance{})
	validate.Validator.RegisterStructValidation(cnap.ClustersStructLevelValidation, cnap.Clusters{})
}
