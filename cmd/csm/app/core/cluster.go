package core

import (
	"fmt"
	"net/http"
	"strings"
	"sync"

	"github.com/jinzhu/copier"
	"github.com/pkg/errors"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/internal/pkg/request"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/api/v1/cnap"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

const (
	MaxManagedClustersCount = 10
)

func (core *APIServerCore) GetManagedClusters(ctx context.CsmContext) (err error) {
	instanceId := ctx.Param(constants.InstanceIDPathParam)

	// 模糊查询
	keywordType := ctx.QueryParam(internal.QueryKeywordType)
	keyword := ctx.QueryParam(internal.QueryKeyword)

	// 筛选
	clusterStatus := ctx.QueryParam(vo.QueryClusterStatus)
	clusterRegion := ctx.QueryParam(vo.QueryClusterRegion)
	clusterConnectionState := ctx.QueryParam(vo.QueryClusterConnectionState)

	// 转换成业务模型
	listFilter := &meta.ListFilter{}
	if len(keywordType) != 0 && len(keyword) != 0 {
		listFilter.KeywordType = keywordType
		listFilter.Keyword = keyword
	}

	filters := make(map[string][]string)
	if len(clusterStatus) != 0 {
		filters[vo.QueryClusterStatus] = strings.Split(clusterStatus, internal.QueryListToken)
	}
	if len(clusterRegion) != 0 {
		filters[vo.QueryClusterRegion] = strings.Split(clusterRegion, internal.QueryListToken)
	}
	if len(clusterConnectionState) != 0 {
		filters[vo.QueryClusterConnectionState] = strings.Split(clusterConnectionState, internal.QueryListToken)
	}
	listFilter.Filters = filters

	pageParam := internal.GetPageParam(ctx, map[string]string{"addedTime": "create_time"})

	clusters, pr, err := core.ClusterService.GetManagedClusters(ctx, instanceId, pageParam, listFilter)
	if err != nil {
		return err
	}

	clusterViews := make([]interface{}, 0)
	for _, c := range clusters {
		mcv := vo.ManagedClusterView{
			ClusterId:      c.ClusterId,
			ClusterName:    c.ClusterName,
			Status:         string(c.Status),
			Version:        c.Version,
			NetworkSegment: c.ContainerNet,
			Region:         c.Region,
			AddedTime:      c.AddedTime.Format("2006-01-02 15:04:05"),
			Vpc: vo.Vpc{
				VpcId:   c.VpcId,
				VpcName: c.VpcName,
			},
			IsPrimary:       c.ClusterType == meta.ClusterTypePrimary,
			IsConfig:        c.ClusterType == meta.ClusterTypeConfig,
			ConnectionState: string(*c.ConnectionState),
		}
		clusterViews = append(clusterViews, mcv)
	}

	response := internal.PageView{
		PageSize:   pr.PageSize,
		PageNo:     pr.PageNo,
		Order:      pr.Order,
		OrderBy:    pr.OrderBy,
		TotalCount: pr.TotalCount,
		Result:     clusterViews,
	}
	return ctx.JSON(http.StatusOK, response)
}

func (core *APIServerCore) GetManagedClustersForOpenAPI(ctx context.CsmContext) (err error) {
	instanceId := ctx.Param(cnap.MeshInstanceIdPathParam)

	// TODO: 分页接口待优化
	clusters, _, err := core.ClusterService.GetManagedClusters(ctx, instanceId, nil, nil)
	if err != nil {
		return err
	}

	clusterViews := make([]cnap.Cluster, 0)
	for _, c := range clusters {
		cluster := &cnap.Cluster{}
		copier.Copy(cluster, c)
		clusterViews = append(clusterViews, *cluster)
	}

	return ctx.JSON(http.StatusOK, clusterViews)
}

// GetCandidateClusters
// 筛选：运行状态、模式、地域
// 模糊查询：集群名称、VPC 名称
// 查询 VPC 信息
func (core *APIServerCore) GetCandidateClusters(ctx context.CsmContext) (err error) {
	instanceId := ctx.Param(constants.InstanceIDPathParam)

	// 模糊查询
	// CCE 仅支持  clusterName, clusterID
	keywordType := ctx.QueryParam(internal.QueryKeywordType)
	keyword := ctx.QueryParam(internal.QueryKeyword)

	// CCE 排序仅支持 clusterName, clusterID, createdAt
	pageParam := internal.GetPageParam(ctx, nil)

	// 筛选
	clusterStatus := ctx.QueryParam(vo.QueryClusterStatus)
	clusterMasterMode := ctx.QueryParam(vo.QueryClusterMasterMode)
	clusterRegion := ctx.QueryParam(vo.QueryClusterRegion)

	listFilter := &meta.ListFilter{}
	if len(keywordType) != 0 && len(keyword) != 0 {
		listFilter.KeywordType = keywordType
		listFilter.Keyword = keyword
	}

	filters := make(map[string][]string)
	if len(clusterStatus) != 0 {
		filters[vo.QueryClusterStatus] = strings.Split(clusterStatus, internal.QueryListToken)
	}
	if len(clusterRegion) != 0 {
		filters[vo.QueryClusterRegion] = strings.Split(clusterRegion, internal.QueryListToken)
	}
	if len(clusterMasterMode) != 0 {
		filters[vo.QueryClusterMasterMode] = strings.Split(clusterMasterMode, internal.QueryListToken)
	}
	listFilter.Filters = filters

	clusters, pr, err := core.ClusterService.GetCandidateClusters(ctx, instanceId, pageParam, listFilter)
	if err != nil {
		return err
	}

	clusterViews := make([]interface{}, 0)
	for _, c := range clusters {
		mcv := vo.CandidateClusterView{
			ClusterId:      c.ClusterId,
			ClusterName:    c.ClusterName,
			Status:         string(c.Status),
			Version:        c.Version,
			NetworkSegment: c.NetworkSegment,
			MasterMode:     string(c.MasterMode),
			Region:         c.Region,
			Vpc: vo.Vpc{
				VpcId:   c.VpcId,
				VpcName: c.VpcName,
			},
			// TODO: 添加权限识别
			Available: true,
		}
		clusterViews = append(clusterViews, mcv)
	}

	response := internal.PageView{
		PageSize:   pr.PageSize,
		PageNo:     pr.PageNo,
		Order:      pr.Order,
		OrderBy:    pr.OrderBy,
		TotalCount: pr.TotalCount,
		Result:     clusterViews,
	}
	return ctx.JSON(http.StatusOK, response)
}

// AddClusters 纳管集群不能超过 10 个, 主集群或者托管集群 LB ready 后才可以添加集群
func (core *APIServerCore) AddClusters(ctx context.CsmContext) (err error) {
	instanceId := ctx.Param(constants.InstanceIDPathParam)
	reqBody := &vo.ClustersRequest{}
	if err = ctx.Bind(reqBody); err != nil {
		return errors.Errorf("Binding content which is in body failed.")
	}

	if len(reqBody.Clusters) == 0 {
		return errors.Errorf("There is no clusters to add.")
	}

	// 获取当前纳管集群数量
	_, pr, err := core.ClusterService.GetManagedClusters(ctx, instanceId, nil, nil)
	if err != nil {
		return err
	}

	if len(reqBody.Clusters)+int(pr.TotalCount) <= MaxManagedClustersCount {
		cc := make([]meta.CandidateCluster, 0)
		instances, err := core.InstancesModel.GetInstanceByInstanceUUID(ctx, instanceId)
		if err != nil {
			return err
		}
		for _, c := range reqBody.Clusters {
			candidateCluster := &meta.CandidateCluster{}
			// 校验k8s版本
			cceCluster, err := core.cceService.GetCCECluster(ctx, instances.Region, c.ClusterId)
			if err != nil {
				return err
			}
			ok, err := core.VersionService.CheckK8sVersion(ctx, instances.IstioVersion, cceCluster.Version)
			if err != nil || !ok {
				return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("The k8s cluster version does not match the istio version."+
					"<a target='_blank' href='https://istio.io/latest/docs/releases/supported-releases/#support-status-of-istio-releases'>Read the document.</a>"), err)
			}

			// 关联监控 检验要添加的 cce 集群是否安装 agent
			// 仅作用于独立网格实例
			if instances.InstanceType != string(meta.HostingMeshType) &&
				instances.MonitorEnabled != nil && *instances.MonitorEnabled {
				pc, _, err := core.InstancesModel.GetInstanceIstiodCluster(ctx, instanceId)
				if err != nil {
					return err
				}
				cpromInstanceID := pc.MonitorInstanceId
				region := pc.Region
				res := core.MonitorService.ClusterCPromAgentCheck(ctx, region, cpromInstanceID, c.ClusterId)
				ctx.CsmLogger().Infof("AddClusters ClusterCPromAgentCheck with %v", res)
				if res != nil && !res.IsExit {
					info := "CProm Agent not installed"
					ctx.CsmLogger().Warnf(info)
					return csmErr.NewCPromAgentNotInstallException(errors.Errorf(info))
				}
				ctx.CsmLogger().Infof("region=%s, cpromInstanceID=%s, ClusterId=%s "+
					"agent installed in cce cluster, AddClusters ClusterCPromAgentCheck successful",
					region, cpromInstanceID, c.ClusterId)
			}

			copier.Copy(candidateCluster, c)
			cc = append(cc, *candidateCluster)
		}
		err = core.ClusterService.AddClusters(ctx, instanceId, cc)
		if err != nil {
			return err
		}
	} else {
		return errors.Errorf("Cannot manage more than %d clusters.", MaxManagedClustersCount)
	}
	return ctx.JSON(http.StatusOK, "ok")
}

func (core *APIServerCore) AddClustersForOpenAPI(ctx context.CsmContext) (err error) {
	instanceId := ctx.Param(cnap.MeshInstanceIdPathParam)
	clusters := &cnap.Clusters{}
	err = request.ConvertAndCheck(ctx, clusters)
	if err != nil {
		return err
	}

	// eks region adapt. just for eks
	for index := range clusters.Clusters {
		region, regionErr := internal.AdaptClusterRegionForCNAP(ctx, clusters.Clusters[index].Region)
		if regionErr != nil {
			return regionErr
		}
		clusters.Clusters[index].Region = region
	}

	// 获取当前纳管集群
	mcs, pr, err := core.ClusterService.GetManagedClusters(ctx, instanceId, nil, nil)
	if err != nil {
		return err
	}

	// 去除重复集群
	cs := clusters.Clusters
	clusterIdMap := make(map[string]struct{})
	for _, mc := range mcs {
		clusterIdMap[mc.ClusterId] = struct{}{}
	}
	cc := make([]meta.CandidateCluster, 0)
	for _, c := range cs {
		if _, ok := clusterIdMap[c.ClusterId]; !ok {
			candidateCluster := &meta.CandidateCluster{}
			copier.Copy(candidateCluster, c)
			cc = append(cc, *candidateCluster)
		}
	}

	if len(cc)+int(pr.TotalCount) > MaxManagedClustersCount {
		return errors.Errorf("Cannot manage more than %d clusters.", MaxManagedClustersCount)
	}

	err = core.ClusterService.AddClusters(ctx, instanceId, cc)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, &cnap.SuccessResponse{
		Success: "true",
	})
}

// RemoveClusters 不能删除主集群 和 托管网格数据面提交的第一个remote集群
func (core *APIServerCore) RemoveClusters(ctx context.CsmContext) (err error) {
	instanceId := ctx.Param(constants.InstanceIDPathParam)

	reqBody := &vo.ClustersRequest{}
	if err = ctx.Bind(reqBody); err != nil {
		return errors.Errorf("Binding content which is in body failed.")
	}

	if len(reqBody.Clusters) != 0 {
		cc := make([]meta.ManagedCluster, 0)
		instance, err := core.InstancesModel.GetInstanceByInstanceUUID(ctx, instanceId)
		if err != nil {
			return err
		}
		for _, c := range reqBody.Clusters {
			managedCluster := &meta.ManagedCluster{}
			copier.Copy(managedCluster, c)
			cc = append(cc, *managedCluster)
			// 托管网格校验移出纳管集群不能移出config集群
			if instance.InstanceType == string(version.HostingVersionType) {
				isConfig, cErr := core.ClusterService.IsRemoteConfigClusters(ctx, instanceId,
					managedCluster.ClusterId, managedCluster.Region)
				if cErr != nil {
					return cErr
				}
				// 包含托管网格的config集群
				if isConfig {
					return csmErr.NewInvalidParameterInputValueException(
						fmt.Sprintf("could not remove remote config cluster %s", managedCluster.ClusterName))
				}
			}

			// 仅适用独立网格检查逻辑
			// 关联监控 检验要添加的 cce 集群是否安装 agent
			if instance.InstanceType == string(version.StandaloneVersionType) &&
				instance.MonitorEnabled != nil && *instance.MonitorEnabled {
				pc, _, err := core.InstancesModel.GetInstanceIstiodCluster(ctx, instanceId)
				if err != nil {
					return err
				}
				cpromInstanceID := pc.MonitorInstanceId
				region := pc.Region
				res := core.MonitorService.ClusterCPromAgentCheck(ctx, region, cpromInstanceID, c.ClusterId)
				if res != nil && !res.IsExit {
					info := "CProm Agent not installed"
					ctx.CsmLogger().Errorf(info)
					continue
				}
				ctx.CsmLogger().Infof("region=%s, cpromInstanceID=%s, ClusterId=%s "+
					"agent installed in cce cluster, RemoveClusters ClusterCPromAgentCheck successful",
					region, cpromInstanceID, c.ClusterId)
			}
		}
		err = core.ClusterService.RemoveClusters(ctx, instanceId, cc)
		if err != nil {
			return err
		}
	}
	return ctx.JSON(http.StatusOK, "ok")
}

// CheckClusterList 校验 node 节点类型
func (core *APIServerCore) CheckClusterList(ctx context.CsmContext) (err error) {
	reqBody := &vo.ClustersRequest{}
	if err = ctx.Bind(reqBody); err != nil {
		return errors.Errorf("Binding content which is in body failed.")
	}

	if len(reqBody.Clusters) == 0 {
		return errors.Errorf("There is no clusters to add.")
	}

	var wg sync.WaitGroup
	var mu sync.RWMutex
	cc := make([]interface{}, 0)

	for _, c := range reqBody.Clusters {
		wg.Add(1)
		go func(c vo.ClustersRequestItem) {
			defer wg.Done()
			cceClusterInstances, err := core.cceService.GetCCEClusterInstances(ctx, c.Region, c.ClusterId)
			if err != nil {
				ctx.CsmLogger().Errorf("GetCCEClusterInstances error. %v", err)
				return
			}
			hasInvalidInstance := false
			for _, v := range cceClusterInstances.InstancePage.InstanceList {
				ok := core.VersionService.CheckNodeOS(ctx, v)
				if !ok {
					hasInvalidInstance = true
					break
				}
			}
			mu.Lock()
			defer mu.Unlock()
			if hasInvalidInstance {
				cc = append(cc, c)
			}
		}(c)
	}
	wg.Wait()
	response := internal.PageView{
		TotalCount: int64(len(cc)),
		Result:     cc,
	}
	return ctx.JSON(http.StatusOK, response)
}
