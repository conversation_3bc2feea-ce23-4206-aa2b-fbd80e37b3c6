package core

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/blsv3"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	bls_mock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/bls/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

type testCase struct {
	clusterID      string
	instanceID     string
	mr             *mockResult
	expectedErrMsg string
}
type mockResult struct {
	result    *meta.AgentCheckResult
	logResult *meta.LogStoreListResult
	mockErr   error
}

// TestCSMAgentCheck 测试CSMAgentCheck函数，包括正确和错误的情况。
// 参数：t *testing.T - 用于记录断言结果的测试对象指针
func TestCSMAgentCheck(t *testing.T) {
	paramNames := []string{
		constants.InstanceIDPathParam,
	}
	paramValues := []string{}

	testCases := []testCase{
		{instanceID: "csm-123123", mr: &mockResult{result: &meta.AgentCheckResult{IsExist: true}, mockErr: nil}},
		{instanceID: "csm-123123", mr: &mockResult{result: &meta.AgentCheckResult{IsExist: false}, mockErr: nil}},
	}
	for _, tc := range testCases {
		paramValues = append(paramValues, tc.instanceID)
		rec := httptest.NewRecorder()
		ctx := getMockCtx(paramNames, paramValues, rec)
		core := getCSMMockCore(t, ctx, tc)
		err := core.CSMAgentCheck(ctx)
		if err != nil && tc.mr.mockErr == nil {
			assert.Containsf(t, err.Error(), tc.expectedErrMsg,
				"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
		} else {
			assert.Equal(t, http.StatusOK, rec.Code)
			pv := &internal.PageView{}
			if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), pv)) {
				result := pv.Result
				reflect.DeepEqual(result, tc.mr.result)
			}
		}
	}
}

// TestClusterAgentCheck 测试函数，用于检查集群代理是否退出。
// 参数：
//   - t *testing.T (必需) - 指向 testing.T 类型的指针，表示当前测试用例。
//
// 返回值：
//   - (无)
func TestClusterAgentCheck(t *testing.T) {
	paramNames := []string{
		constants.ClusterId,
	}
	paramValues := []string{}

	testCases := []testCase{
		{clusterID: "cce-123123", mr: &mockResult{result: &meta.AgentCheckResult{IsExist: true}, mockErr: nil}},
		{clusterID: "cce-123123", mr: &mockResult{result: &meta.AgentCheckResult{IsExist: false}, mockErr: nil}},
	}
	for _, tc := range testCases {
		paramValues = append(paramValues, tc.clusterID)
		rec := httptest.NewRecorder()
		ctx := getMockCtx(paramNames, paramValues, rec)

		core := getClusterMockCore(t, ctx, tc)
		err := core.ClusterAgentCheck(ctx)
		if err != nil && tc.mr.mockErr == nil {
			assert.Containsf(t, err.Error(), tc.expectedErrMsg,
				"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
		} else {
			assert.Equal(t, http.StatusOK, rec.Code)
			pv := &internal.PageView{}
			if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), pv)) {
				result := pv.Result
				reflect.DeepEqual(result, tc.mr.result)
			}
		}
	}
}

// getMockCtx 获取一个包含参数名和参数值的mock ctx，以及一个http.ResponseWriter类型的rec
// paramNames []string 参数名列表
// paramValues []string 参数值列表
// rec http.ResponseWriter 用于接收返回结果的http ResponseWriter
// 返回值 csmContext.CsmContext 包含ctx信息的csmContext.CsmContext
func getMockCtx(paramNames, paramValues []string, rec http.ResponseWriter) csmContext.CsmContext {
	user1 := iam.User{
		Domain: &iam.Domain{
			ID: "account-id-1",
		},
	}
	e := echo.New()
	q := make(url.Values)
	req := httptest.NewRequest(http.MethodGet, "/?"+q.Encode(), nil)
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

	c := e.NewContext(req, rec)
	c.Set(iam.ContextIAMUser, user1)
	c.SetParamNames(paramNames...)
	c.SetParamValues(paramValues...)
	return csmContext.NewCsmContext(c)
}

// getCSMMockCore 获取CSM Mock Core，用于测试API Server Core的功能
// 参数：
//   - t *testing.T: 指向当前正在执行的单元测试对象
//   - ctx csmContext.CsmContext: CSM上下文对象
//   - tc testCase: 包含了测试用例信息的结构体
//
// 返回值：
//   - *APIServerCore: API Server Core对象的指针
func getCSMMockCore(t *testing.T, ctx csmContext.CsmContext, tc testCase) *APIServerCore {

	ctrl := gomock.NewController(t)
	blsService := bls_mock.NewMockServiceInterface(ctrl)
	blsService.EXPECT().CSMAgentCheck(ctx, gomock.Any()).
		Return(tc.mr.result, tc.mr.mockErr)

	return &APIServerCore{
		CsmOptions: &options.CsmOptions{
			EksAccountIds: []string{"account-id-1"},
		},
		blsService: blsService,
	}
}

// getClusterMockCore 获取一个 APIServerCore 的 mock 对象，用于测试。
// 参数 t *testing.T - 测试用例所在的测试函数指针，用于控制 mock 对象的生成和断言。
// 参数 ctx csmContext.CsmContext - 上下文信息，包含请求相关的信息。
// 参数 tc testCase - 测试用例结构体，包含 mock 返回值、错误等信息。
// 返回值 *APIServerCore - 返回一个 APIServerCore 的 mock 对象，用于模拟 APIServerCore 的行为。
func getClusterMockCore(t *testing.T, ctx csmContext.CsmContext, tc testCase) *APIServerCore {

	ctrl := gomock.NewController(t)
	blsService := bls_mock.NewMockServiceInterface(ctrl)
	blsService.EXPECT().ClusterAgentCheck(ctx, gomock.Any(), gomock.Any()).
		Return(tc.mr.result, tc.mr.mockErr)

	return &APIServerCore{
		CsmOptions: &options.CsmOptions{
			EksAccountIds: []string{"account-id-1"},
		},
		blsService: blsService,
	}
}

func TestLogStoreList(t *testing.T) {
	paramNames := []string{
		constants.RegionHeaderKey,
	}
	paramValues := []string{"bj"}
	// paramValues := []string{""}
	// fmt.Println(paramValues)
	rec := httptest.NewRecorder()
	ctx := getMockCtx(paramNames, paramValues, rec)
	ctx.Request().Header.Set(constants.RegionHeaderKey, "bj")
	testCases := []testCase{
		{clusterID: "cce-123123", mr: &mockResult{logResult: &meta.LogStoreListResult{TotalCount: 10}, mockErr: nil}},
		{clusterID: "cce-123123", mr: &mockResult{logResult: &meta.LogStoreListResult{TotalCount: 10}, mockErr: nil}},
	}
	for i, test := range testCases {
		t.Run(fmt.Sprintf("test%d", i), func(t *testing.T) {
			core := getLogStoreMockCore(t, ctx, test)
			err := core.LogStoreList(ctx)
			if err != nil && test.mr.mockErr != nil {
				fmt.Println("err", err)
				assert.Containsf(t, err.Error(), test.expectedErrMsg,
					"expected error: %v, got %v", test.expectedErrMsg, err.Error())
			} else {
				// fmt.Println(rec)
				// assert.Equal(t, http.StatusOK, rec.Code)
				assert.Nil(t, err)
			}
		})
	}
}

// getLogStoreMockCore 函数用于生成一个带有模拟BLS服务的APIServerCore实例
//
// 参数：
// t: 测试实例指针
// ctx: CsmContext上下文
// tc: testCase测试用例
//
// 返回值：
// *APIServerCore: 带有模拟BLS服务的APIServerCore实例指针
func getLogStoreMockCore(t *testing.T, ctx csmContext.CsmContext, tc testCase) *APIServerCore {

	ctrl := gomock.NewController(t)
	blsService := bls_mock.NewMockServiceInterface(ctrl)
	blsService.EXPECT().LogStoreList(ctx, gomock.Any()).
		Return(tc.mr.logResult, tc.mr.mockErr).AnyTimes()

	return &APIServerCore{
		CsmOptions: &options.CsmOptions{
			EksAccountIds: []string{"account-id-1"},
		},
		blsService: blsService,
	}
}

// TestBlsList 是一个测试函数，用于测试 BlsList 函数
func TestBlsList(t *testing.T) {
	ctrl := gomock.NewController(t)
	blsService := bls_mock.NewMockServiceInterface(ctrl)
	paramNames := []string{
		constants.RegionHeaderKey,
		constants.InstanceIDPathParam,
	}
	paramValues := []string{"bj", instanceUUID}
	rec := httptest.NewRecorder()
	ctx := getMockCtx(paramNames, paramValues, rec)
	ctx.Request().Header.Set(constants.RegionHeaderKey, "bj")
	type Cases struct {
		clusterID string
		mr        *meta.BlsListResult
		mockErr   error
	}

	testCases := []Cases{
		{clusterID: "cce-123123", mr: &meta.BlsListResult{Status: blsv3.Abnormal}, mockErr: nil},
		{clusterID: "cce-123123", mr: &meta.BlsListResult{Status: blsv3.Open}, mockErr: nil},
	}
	for i, test := range testCases {
		t.Run(fmt.Sprintf("test%d", i), func(t *testing.T) {

			blsService.EXPECT().BlsList(ctx, gomock.Any(), gomock.Any()).
				Return(test.mr, nil).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				blsService: blsService,
			}

			err := core.BlsList(ctx)

			assert.Equal(t, http.StatusOK, rec.Code)
			assert.Nil(t, err)

		})
	}
}

// TestBlsClose 测试BlsClose函数，该函数用于关闭指定的BLS实例
// 参数t：*testing.T类型，表示当前测试用例
// 返回值：无
func TestBlsClose(t *testing.T) {
	ctrl := gomock.NewController(t)
	blsService := bls_mock.NewMockServiceInterface(ctrl)
	paramNames := []string{
		constants.RegionHeaderKey,
		constants.InstanceIDPathParam,
	}
	paramValues := []string{"bj", instanceUUID}
	rec := httptest.NewRecorder()
	ctx := getMockCtx(paramNames, paramValues, rec)
	ctx.Request().Header.Set(constants.RegionHeaderKey, "bj")
	type Cases struct {
		clusterID string
		mr        *meta.BlsCloseResult
		mockErr   error
	}

	testCases := []Cases{
		{clusterID: "cce-123123", mr: &meta.BlsCloseResult{
			BlsError: meta.BlsCloseError{ErrorMsg: "404 not found"},
		}, mockErr: nil},
		{clusterID: "cce-123123", mr: &meta.BlsCloseResult{}, mockErr: nil},
	}
	for i, test := range testCases {
		t.Run(fmt.Sprintf("test%d", i), func(t *testing.T) {

			blsService.EXPECT().BlsClose(ctx, gomock.Any(), gomock.Any()).
				Return(test.mr, nil).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				blsService: blsService,
			}

			err := core.BlsClose(ctx)

			assert.Equal(t, http.StatusOK, rec.Code)
			assert.Equal(t, err, fmt.Errorf("404 not found"))

		})
	}
}
