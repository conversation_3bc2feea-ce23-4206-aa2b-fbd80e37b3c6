package core

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/diagnosis/configdump"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

func (core *APIServerCore) NamespaceList(ctx csmContext.CsmContext) (error error) {
	// 请求参数校验
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException(constants.InstanceIDPathParam)
	}

	ns, error := core.diagnosisService.NamespaceList(ctx, instanceUUID)
	if error != nil {
		return error
	}

	return ctx.JSON(http.StatusOK, ns)
}

func (core *APIServerCore) ProxyStatusList(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException(constants.InstanceIDPathParam)
	}

	namespace := ctx.Param(constants.Namespace)
	if namespace == "" {
		return csmErr.NewMissingParametersException(constants.Namespace)
	}

	dlf := &vo.DiagnosisListFilter{
		Keyword:     ctx.QueryParam("keyword"),
		KeywordType: ctx.QueryParam("keywordType"),
		Namespace:   namespace,
	}

	pageParam := vo.GetPageParam(ctx, map[string]string{})
	proxyStatus, pageResult, err := core.diagnosisService.ProxyStatusList(ctx, instanceUUID, pageParam, dlf)
	if err != nil {
		ctx.CsmLogger().Errorf("ProxyStatusList failed", err)
		pageResult = &vo.PageResult{}
	}
	proxyStatusViews := make([]interface{}, 0)
	for _, ps := range proxyStatus {
		psv := internal.ProxyStatusView{
			PilotName:   ps.PilotName,
			ClusterName: ps.ClusterName,
			ProxyName:   ps.Proxy,
			Version:     ps.Version,
			CDS:         ps.CDS,
			LDS:         ps.LDS,
			EDS:         ps.EDS,
			RDS:         ps.RDS,
			ECDS:        ps.ECDS,
		}
		proxyStatusViews = append(proxyStatusViews, psv)
	}

	response := internal.PageView{
		PageSize:   pageResult.PageSize,
		PageNo:     pageResult.PageNo,
		Order:      pageResult.Order,
		OrderBy:    pageResult.OrderBy,
		TotalCount: pageResult.TotalCount,
		Result:     proxyStatusViews,
	}
	return ctx.JSON(http.StatusOK, response)
}

func (core *APIServerCore) ProxyConfigList(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException(constants.InstanceIDPathParam)
	}

	pageParam := vo.GetPageParam(ctx, map[string]string{})
	proxyName := ctx.QueryParam("proxyName")
	typeName := ctx.QueryParam("type")
	clusterName := ctx.QueryParam("clusterName")

	if proxyName == "" || typeName == "" || clusterName == "" {
		return csmErr.NewMissingParametersException("proxyName or type or clusterName is nil")
	}

	if !configdump.IsValidProxyConfig(configdump.ConvertToProxyConfig(typeName)) {
		return csmErr.NewMissingParametersException(fmt.Sprintf("type %s is not supported", typeName))
	}
	name := strings.Split(proxyName, ".")
	if len(name) != 2 {
		return csmErr.NewMissingParametersException("proxyName is not valid")
	}

	regionClusterID := strings.SplitN(clusterName, "-", 2)
	if len(regionClusterID) != 2 {
		return csmErr.NewMissingParametersException("clusterName is not valid, refer to bj-cce-xxxxxxxx")
	}

	pcr := &meta.ProxyConfigRequest{
		Region:    regionClusterID[0],
		ClusterID: regionClusterID[1],
		PodName:   name[0],
		Namespace: name[1],
		TypeName:  typeName,
	}

	pcView, pageResult, error := core.diagnosisService.ProxyConfigList(ctx, instanceUUID, pcr, pageParam)
	if error != nil {
		ctx.CsmLogger().Errorf("ProxyConfigList failed %v", error)
		pageResult = &vo.PageResult{}
	}

	var pcViews []interface{}
	if len(pcView) > 0 {
		err := json.Unmarshal(pcView, &pcViews)
		if err != nil {
			ctx.CsmLogger().Errorf("ProxyConfigList failed %v", err)
			pageResult = &vo.PageResult{}
		}
	}
	response := internal.PageView{
		PageSize:   pageResult.PageSize,
		PageNo:     pageResult.PageNo,
		Order:      pageResult.Order,
		OrderBy:    pageResult.OrderBy,
		TotalCount: pageResult.TotalCount,
		Result:     pcViews,
	}
	return ctx.JSON(http.StatusOK, response)
}

// ExceptionList 查询当前实例异常列表
func (core *APIServerCore) ExceptionList(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException(constants.InstanceIDPathParam)
	}
	namespace := ctx.Param(constants.Namespace)
	if namespace == "" {
		return csmErr.NewMissingParametersException(constants.Namespace)
	}
	pageParam := vo.GetPageParam(ctx, map[string]string{})
	exceptions, pageResult, error := core.diagnosisService.ExceptionList(ctx, instanceUUID, namespace, pageParam)
	if error != nil {
		ctx.CsmLogger().Errorf("ExceptionList failed %v", error)
		pageResult = &vo.PageResult{}
	}

	exceptionViews := make([]interface{}, 0)
	for _, e := range exceptions {
		ev := meta.Exception{
			Name:        e.Name,
			Level:       e.Level,
			Code:        e.Code,
			Description: e.Description,
			Version:     e.Version,
		}
		exceptionViews = append(exceptionViews, ev)
	}
	response := internal.PageView{
		PageSize:   pageResult.PageSize,
		PageNo:     pageResult.PageNo,
		Order:      pageResult.Order,
		OrderBy:    pageResult.OrderBy,
		TotalCount: pageResult.TotalCount,
		Result:     exceptionViews,
	}
	return ctx.JSON(http.StatusOK, response)
}

// ConfigDump 函数用于获取指定Pod的配置信息
func (core *APIServerCore) ConfigDump(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException(constants.InstanceIDPathParam)
	}

	namespace := ""
	podName := ctx.QueryParam("proxyName")
	clusterName := ctx.QueryParam("clusterName")
	strs := strings.Split(podName, ".") //podname格式为: istio-ingressgateway-698745d9f6-qv2zj.istio-system 后面的namespace不能加上
	if len(strs) > 1 {
		podName = strs[0]
		namespace = strs[1]
	} else {
		ctx.CsmLogger().Errorf("ConfigDump failed, proxyName is invaild")
		return csmErr.NewInvalidParameterInputValueException("proxyName is invalide")
	}
	res, error := core.diagnosisService.ConfigDump(ctx, instanceUUID, namespace, podName, clusterName)
	if error != nil {
		ctx.CsmLogger().Errorf("ConfigDump failed %v", error)
		return error
	}
	resStr := `{ "content": [` + string(res) + `]}`
	return ctx.JSONBlob(http.StatusOK, []byte(resStr))
}
