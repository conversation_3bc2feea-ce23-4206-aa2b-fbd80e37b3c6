package core

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/internal/csm/web"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	modelMeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	blbMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/blb/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

func TestBindBlb(t *testing.T) {
	type MockResult struct {
		mockErr error
	}
	testCases := []struct {
		name           string
		body           *web.BindBlb
		mr             *MockResult
		expected       string
		expectedErrMsg *string
		user           *sdkIAM.User
	}{
		{
			name: "bind success",
			body: &web.BindBlb{
				GatewayID:      "gateway-id-1",
				MeshInstanceID: "mesh-instance-id-1",
				NetworkConfig: web.NetworkConfig{
					BlbID: "blb-id-1",
					NetworkType: meta.NetworkType{
						VpcNetworkId: "",
						SubnetId:     "",
					},
					ElasticPublicNetwork: meta.ElasticPublicNetwork{
						Enabled:           false,
						PublicNetworkType: "",
					},
				},
			},
			mr: &MockResult{
				mockErr: nil,
			},
			expected:       "",
			expectedErrMsg: nil,
			user:           &user1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jsonBody, err := json.Marshal(tc.body)
			if err != nil {
				t.FailNow()
			}

			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mb := blbMock.NewMockServiceInterface(ctrl)
			mb.EXPECT().
				BindBlb(gomock.Any(), gomock.Any()).
				Return(tc.mr.mockErr)

			core := &APIServerCore{
				BlbService: mb,
			}

			err = core.BindBlb(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), *tc.expectedErrMsg,
					"expected error: %v, got %v", *tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestUnBindBlb(t *testing.T) {
	type MockResult struct {
		mockErr error
	}
	testCases := []struct {
		name           string
		body           *web.UnBindBlb
		mr             *MockResult
		expected       string
		expectedErrMsg *string
		user           *sdkIAM.User
	}{
		{
			name: "unbind success",
			body: &web.UnBindBlb{
				IsReleaseBlb:   true,
				IsReleseEip:    true,
				BlbID:          "blb-id-1",
				EipID:          "eip-id-1",
				GatewayID:      "gateway-id-1",
				MeshInstanceID: "mesh-instance-id-1",
			},
			mr: &MockResult{
				mockErr: nil,
			},
			expected:       "",
			expectedErrMsg: nil,
			user:           &user1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jsonBody, err := json.Marshal(tc.body)
			if err != nil {
				t.FailNow()
			}

			e := echo.New()
			req := httptest.NewRequest(http.MethodDelete, "/", strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mb := blbMock.NewMockServiceInterface(ctrl)
			mb.EXPECT().
				UnBindBlb(gomock.Any(), gomock.Any(), tc.body.IsReleseEip, tc.body.IsReleaseBlb).
				Return(tc.mr.mockErr)

			core := &APIServerCore{
				BlbService: mb,
			}

			err = core.UnBindBlb(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), *tc.expectedErrMsg,
					"expected error: %v, got %v", *tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestListAvailableBlb(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectRes *modelMeta.AvailableBlbListResponse
		expectErr error
	}{
		{
			name:      "correct-GetGatewayDetail",
			expectRes: &modelMeta.AvailableBlbListResponse{},
			expectErr: nil,
		},
	}
	mockCtx := csmContext.MockNewCsmContext()
	for _, testInfo := range testInfos {
		mockBlbService := blbMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions: options.NewCsmServerOption(),
			BlbService: mockBlbService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockBlbService.EXPECT().ListAvailableBlb(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.expectRes, testInfo.expectErr)
			err := core.ListAvailableBlb(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}
