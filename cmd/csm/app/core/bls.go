package core

import (
	"fmt"
	"net/http"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

// CSMAgentCheck 函数功能：检查指定实例是否可以被CSM Agent监控。
// 参数：
//   - ctx csmContext.CsmContext类型，包含请求的上下文信息，包括请求路径中的参数等。
//
// 返回值：
//   - error类型，表示发生错误时返回，包含错误码和错误信息；当错误为nil时表示操作成功。
func (core *APIServerCore) CSMAgentCheck(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException(constants.InstanceIDPathParam)
	}
	ctx.CsmLogger().Infof("CSM logAgent check start instanceUUID: %s", instanceUUID)
	res, error := core.blsService.CSMAgentCheck(ctx, instanceUUID)
	if error != nil {
		return error
	}

	return ctx.JSON(http.StatusOK, res)
}

// ClusterAgentCheck 该函数用于检查集群代理是否正常工作，参数为csmContext.CsmContext类型的上下文对象，返回值为error类型，表示可能出现的错误
func (core *APIServerCore) ClusterAgentCheck(ctx csmContext.CsmContext) (error error) {
	clusterID := ctx.Param(constants.ClusterId)
	if clusterID == "" {
		return csmErr.NewMissingParametersException(constants.ClusterId)
	}
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	ctx.CsmLogger().Infof("Cluster logAgent check start clusterID: %s, region: %s", clusterID, region)
	res, error := core.blsService.ClusterAgentCheck(ctx, clusterID, region)
	if error != nil {
		return error
	}

	return ctx.JSON(http.StatusOK, res)
}

// LogStoreList 返回给定区域的日志存储列表
//
// 参数：
//
//	core *APIServerCore - APIServerCore实例指针
//	ctx csmContext.CsmContext - CsmContext实例
//
// 返回值：
//
//	error - 错误信息，如果成功则为nil
func (core *APIServerCore) LogStoreList(ctx csmContext.CsmContext) (error error) {
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	if region == "" {
		return csmErr.NewMissingParametersException(constants.RegionHeaderKey)
	}
	res, error := core.blsService.LogStoreList(ctx, region)
	if error != nil {
		return error
	}

	return ctx.JSON(http.StatusOK, res)

}

// GetAllBlsTasksByInstanceUUID 根据实例UUID获取所有BLS任务
//
// 参数：
// ctx context.CsmContext - 上下文对象，包含请求相关信息
// instanceUUID string - 实例UUID
//
// 返回值：
// *[]meta.Bls - BLS任务列表
// error - 错误信息，若操作成功则为nil
func (core *APIServerCore) BlsList(ctx csmContext.CsmContext) (error error) {
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	if region == "" {
		return csmErr.NewMissingParametersException(constants.RegionHeaderKey)
	}
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException(constants.InstanceIDPathParam)
	}
	res, error := core.blsService.BlsList(ctx, region, instanceUUID)
	if error != nil {
		return error
	}

	return ctx.JSON(http.StatusOK, res)

}

// BlsClose 关闭指定区域的BLS实例，返回一个bool值表示是否成功关闭。如果关闭失败，则返回错误信息。
//
// 参数：
//   - ctx csmContext.CsmContext  上下文对象，包含请求相关信息和函数调用结果
//
// 返回值：
//   - error                      关闭BLS实例时可能出现的错误，如果没有错误则为nil
func (core *APIServerCore) BlsClose(ctx csmContext.CsmContext) (error error) {
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	if region == "" {
		return csmErr.NewMissingParametersException(constants.RegionHeaderKey)
	}
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException(constants.InstanceIDPathParam)
	}
	res, error := core.blsService.BlsClose(ctx, region, instanceUUID)
	if error != nil {
		return error
	}
	if res == nil || res.BlsError.ErrorMsg == "" {
		return ctx.JSON(http.StatusOK, true)
	}
	return fmt.Errorf(res.BlsError.ErrorMsg)
}

// BlsOpen 开启指定实例的日志服务，并返回操作结果。
//
// 参数：
//
//	ctx csmContext.CsmContext - 上下文对象，包含请求信息和响应处理函数等。
//
// 返回值：
//
//	error error - 如果操作成功，则返回nil；否则返回错误信息。
func (core *APIServerCore) BlsOpen(ctx csmContext.CsmContext) (error error) {
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	if region == "" {
		return csmErr.NewMissingParametersException(constants.RegionHeaderKey)
	}
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException(constants.InstanceIDPathParam)
	}
	name := &meta.BlsLogStoreName{}
	if err := ctx.Bind(name); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	res, error := core.blsService.BlsOpen(ctx, region, instanceUUID, name.BlsLogStoreName)
	if error != nil {
		return error
	}
	return ctx.JSON(http.StatusOK, res)

}
