package core

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/command"
	"net/http"
	"os"
	"path"
	"strings"

	"github.com/pkg/errors"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/object"
)

func (core *APIServerCore) DeleteHigressInstance(ctx csmContext.CsmContext) (error error) {
	clusterId := ctx.Param("clusterID")
	err := core.aiIngressService.DeleteHigress(ctx, clusterId)
	if err != nil {
		ctx.CsmLogger().Errorf("uninstall istio ingress error %v", err)
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

func (core *APIServerCore) CreateHigressInstance(ctx csmContext.CsmContext) (error error) {

	// 获取 accountId
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}
	clusterId := ctx.Param("clusterID")

	ingressParam := &meta.IngressGatewayParams{}
	if err = ctx.Bind(ingressParam); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	// 生成创建网关配置实例
	ingress, err := ingressParam.ToIngressModel()
	if err != nil {
		return err
	}
	clusterName := ingress.ClusterName
	deployMode := ingress.DeployMode
	gatewayType := ingress.GatewayType
	// 请求体参数校验
	if len(clusterName) == 0 || len(deployMode) == 0 || len(gatewayType) == 0 {
		return errors.New("clusterName, deployMode, gatewayType and ingressType can not be empty")
	}
	// 设置参数值
	ingress.Region = ctx.Get(reg.ContextRegion).(string)
	ingress.ClusterUUID = clusterId
	ingress.AccountId = accountId
	ingress.GatewayName = constants.IstiodGatewayName
	if ingress.IstioVersion == "" {
		ingress.IstioVersion = HigressVersion
	}

	// 生成网关ID
	gatewayUUID, err := core.GatewayService.GenerateGatewayID(ctx)
	if err != nil {
		return err
	}
	// 网关ID和实例ID一致
	ingress.GatewayUUID = gatewayUUID
	ingress.InstanceUUID = gatewayUUID
	// 网关实体默认参数补齐
	ingress.Namespace = constants.HigressNamespace
	ingress.Deleted = csm.Int(0)
	err = core.aiIngressService.CreateHigress(ctx, ingress, ingressParam)
	if err != nil {
		ctx.CsmLogger().Errorf("create istio ingress error %v", err)
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

// CreateHigressHostingInstance creates a Higress gateway in a hosting cluster that manages a remote user cluster
func (core *APIServerCore) CreateHigressHostingInstance(ctx csmContext.CsmContext) (error error) {
	clusterId := ctx.Param("clusterID")
	if clusterId == "" {
		return csmErr.NewMissingParametersException("clusterID is required")
	}

	region := ctx.Get(reg.ContextRegion).(string)
	if region == "" {
		return csmErr.NewMissingParametersException("region is required")
	}

	// Step 1: Get hosting cluster's k8s client
	hostingClient, err := core.cceService.NewClient(ctx, region, "cce-nikbywcm", meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create user cluster client, region=%s, clusterId=%s, err=%v", region, clusterId, err)
		return errors.Wrap(err, "failed to create user cluster client")
	}

	// Step 2: Create namespace in the user cluster
	userNamespace := fmt.Sprintf("istio-system-cce-user2")

	// Create namespace
	namespace := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: userNamespace,
		},
	}

	_, err = hostingClient.Kube().CoreV1().Namespaces().Create(context.TODO(), namespace, metav1.CreateOptions{})
	if err != nil && !strings.Contains(err.Error(), "already exists") {
		ctx.CsmLogger().Errorf("failed to create namespace %s in user cluster, err=%v", userNamespace, err)
		return errors.Wrap(err, "failed to create namespace in user cluster")
	}

	// Step 3: Read Higress installation template
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get current directory, err=%v", err)
		return errors.Wrap(err, "failed to get current directory")
	}

	higressTemplatePath := path.Join(pwd, "templates/higress/hosting/higress.yaml")
	higressTemplateData, err := os.ReadFile(higressTemplatePath)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to read Higress template file, err=%v", err)
		return errors.Wrap(err, "failed to read Higress template file")
	}

	// Step 4: Install Higress in the user cluster
	higressObjects, err := object.ManifestK8sObject(ctx, string(higressTemplateData))
	if err != nil {
		ctx.CsmLogger().Errorf("failed to parse Higress template manifest, err=%v", err)
		return errors.Wrap(err, "failed to parse Higress template manifest")
	}

	err = kube.CreateResources(ctx, hostingClient, higressObjects)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create Higress resources in user cluster, err=%v", err)
		return errors.Wrap(err, "failed to create Higress resources in user cluster")
	}

	// Step 5: Generate remote-secret for the user cluster
	// Create temporary kubeconfig file for the user cluster
	kubeConfigName := fmt.Sprintf("%s-%s-temp.yaml", region, clusterId)
	kubeConfigPath := path.Join(pwd, "templates/higress/hosting", kubeConfigName)

	kubeConfig, err := core.cceService.GetCCEClusterKubeConfigByClusterUUID(ctx, region, clusterId, "public", meta.StandaloneMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get kubeconfig for user cluster, err=%v", err)
		return errors.Wrap(err, "failed to get kubeconfig for user cluster")
	}

	err = os.WriteFile(kubeConfigPath, []byte(kubeConfig), 0644)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to write kubeconfig file, err=%v", err)
		return errors.Wrap(err, "failed to write kubeconfig file")
	}

	// Step 6: Create remote secret using istioctl
	// First, look for istioctl in the system or use a packaged one
	remoteSecretCmd := fmt.Sprintf("istioctl --kubeconfig=%s create-remote-secret --name=cce-user-1 --namespace=%s > %s",
		kubeConfigPath, userNamespace, path.Join(pwd, "templates/higress/hosting", "remote-secret.yaml"))

	stdout, stderr, err := command.ExecCmdOut(ctx, remoteSecretCmd)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create remote secret, err=%v, stderr=%s", err, string(stderr))
		return errors.Wrap(err, "failed to create remote secret")
	}

	ctx.CsmLogger().Infof("Remote secret creation output: %s", string(stdout))

	// Step 7: Apply the remote secret to the hosting cluster
	// Since we have the remote secret in a file, read it and apply
	remoteSecretPath := path.Join(pwd, "templates/higress/hosting", "remote-secret.yaml")
	remoteSecretData, err := os.ReadFile(remoteSecretPath)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to read remote secret file, err=%v", err)
		return errors.Wrap(err, "failed to read remote secret file")
	}

	// First, need to get the hosting cluster client
	// For now, we'll use the same client since we don't have a separate hosting cluster in this example
	// In a real implementation, you would get the client for the hosting cluster

	// Apply the remote secret
	remoteSecretObjects, err := object.ManifestK8sObject(ctx, string(remoteSecretData))
	if err != nil {
		ctx.CsmLogger().Errorf("failed to parse remote secret manifest, err=%v", err)
		return errors.Wrap(err, "failed to parse remote secret manifest")
	}

	err = kube.CreateResources(ctx, hostingClient, remoteSecretObjects)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to apply remote secret to hosting cluster, err=%v", err)
		return errors.Wrap(err, "failed to apply remote secret to hosting cluster")
	}

	// Step 8: Clean up temporary files
	os.Remove(kubeConfigPath)
	//os.Remove(remoteSecretPath)

	ctx.CsmLogger().Infof("Successfully created Higress hosting instance for cluster %s in region %s", clusterId, region)

	return ctx.JSON(http.StatusOK, map[string]interface{}{
		"success":   true,
		"message":   "Higress hosting instance created successfully",
		"clusterID": clusterId,
		"namespace": userNamespace,
	})
}
