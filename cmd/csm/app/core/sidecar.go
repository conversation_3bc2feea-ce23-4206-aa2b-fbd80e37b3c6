package core

import (
	"fmt"
	"net/http"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/sliceutil"
)

// GetSidecarQuota 获取Sidecar配额信息
func (core *APIServerCore) GetSidecarQuota(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	// 通过instanceUUID查询对应的primary cluster中的sidecar资源配置.
	sidecarQuota, err := core.SidecarService.GetSidecarQuota(ctx, instanceUUID)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, *sidecarQuota)
}

// SidecarInjection sidecar自动注入
func (core *APIServerCore) SidecarInjection(ctx csmContext.CsmContext) (err error) {
	injectionParam := &meta.InjectionParam{}
	if err = ctx.Bind(injectionParam); err != nil {
		return err
	}
	injectionParam = &meta.InjectionParam{
		InstanceUUID: ctx.Param(constants.InstanceIDPathParam),
		Namespace:    ctx.Param(constants.Namespace),
		ClusterUUID:  ctx.QueryParam(constants.ClusterUuid),
		Enabled:      ctx.QueryParam(constants.Enabled),
	}
	err = core.SidecarService.SidecarInjection(ctx, injectionParam)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

// UpdateSidecarQuota sidecar配额编辑
func (core *APIServerCore) UpdateSidecarQuota(ctx csmContext.CsmContext) (error error) {
	// 请求参数校验
	sidecar := &meta.SidecarQuota{}
	if error = ctx.Bind(sidecar); error != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(error)
	}
	quota := &meta.QuotaParam{
		InstanceUUID: ctx.Param("instanceUUID"),
		SidecarQuota: meta.SidecarQuota{
			CpuQuota: meta.CpuQuota{
				Request: sidecar.CpuQuota.Request,
				Limit:   sidecar.CpuQuota.Limit,
			},
			MemoryQuota: meta.MemoryQuota{
				Request: sidecar.MemoryQuota.Request,
				Limit:   sidecar.MemoryQuota.Limit,
			},
		},
	}
	updateType := constants.Cpu
	if quota.SidecarQuota.CpuQuota.Limit == 0 && quota.SidecarQuota.CpuQuota.Request == 0 {
		updateType = constants.Mem
	}
	if error, ok := checkSidecarQuota(updateType, quota); !ok {
		return error
	}
	paasType, quota, err := core.checkPaaSType(ctx, quota, updateType)
	if err != nil {
		return err
	}
	sidecarQuota, err := core.SidecarService.UpdateSidecarQuota(ctx, quota, updateType, paasType)
	if err != nil {
		ctx.CsmLogger().Errorf("edit sidecarQuota error %v", err)
		return ctx.JSON(http.StatusOK, meta.DefaultSidecarQuota())
	}
	return ctx.JSON(http.StatusOK, sidecarQuota)

}

// checkSidecarQuota 校验输入的sidecar配额值
func checkSidecarQuota(updateType string, quota *meta.QuotaParam) (error, bool) {
	//输入cpuQuota
	//配额值必须为大于0的正整数且requests<=limits
	if updateType == constants.Cpu {
		if (quota.SidecarQuota.CpuQuota.Limit <= 0) || (quota.SidecarQuota.CpuQuota.Request <= 0) {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("The parameter must be greater than zero")), false
		}
		if quota.SidecarQuota.CpuQuota.Limit < quota.SidecarQuota.CpuQuota.Request {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("Limit must be greater than Request")), false
		}
	} else {
		//输入memQuota
		//配额值必须为大于0的正整数且requests<=limits
		if (quota.SidecarQuota.MemoryQuota.Limit <= 0) || (quota.SidecarQuota.MemoryQuota.Request <= 0) {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("The parameter must be greater than zero")), false
		}
		if quota.SidecarQuota.MemoryQuota.Limit < quota.SidecarQuota.MemoryQuota.Request {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("Limit must be greater than Request")), false
		}
	}
	return nil, true
}

// checkPaaSType 校验PaaSType
func (core *APIServerCore) checkPaaSType(ctx csmContext.CsmContext, quota *meta.QuotaParam, updateType string) (paasType meta.PaaSType,
	newQuota *meta.QuotaParam, err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return "", nil, err
	}

	if sliceutil.StringContains(core.CsmOptions.EksAccountIds, accountId) {
		if updateType == constants.Cpu {
			if quota.SidecarQuota.CpuQuota.Limit != quota.SidecarQuota.CpuQuota.Request {
				return "", nil, csmErr.NewInvalidParameterInputValueException(
					fmt.Sprintf("The CPU limit and request must be equal"))
			}
		} else {
			if quota.SidecarQuota.MemoryQuota.Limit != quota.SidecarQuota.MemoryQuota.Request {
				return "", nil, csmErr.NewInvalidParameterInputValueException(
					fmt.Sprintf("The Memory limit and request must be equal"))
			}
		}
		ctx.CsmLogger().Infof("edit sidecarQuota on ==>eks<==")
		paasType = meta.PaaSTypeEKS
	} else {
		ctx.CsmLogger().Infof("edit sidecarQuota on ==>cce<==")
		paasType = meta.PaaSTypeCCE
	}
	return paasType, quota, nil
}
