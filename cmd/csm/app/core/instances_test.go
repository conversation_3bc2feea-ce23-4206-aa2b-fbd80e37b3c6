package core

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/baidubce/bce-sdk-go/services/cce"
	"github.com/golang/mock/gomock"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/api/v1/cnap"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	cceServiceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	clusterServiceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cluster/mock"
	gatewayServiceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/gateway/mock"
	instanceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/instances/mock"
	instancesMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/instances/mock"
	servicemeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	multiProtocolServiceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/multiprotocol/mock"
	versionMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	versionUtil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/testdata"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

var (
	accountId = "123"
	user      = &sdkIAM.User{
		ID:   "1",
		Name: "test-user",
		Domain: sdkIAM.UserDomain{
			ID:   accountId,
			Name: "aaaa",
		},
	}
	uuid       = "test-uuid"
	testRegion = "bj"
	meshOption = &options.CsmOptions{
		EksAccountIds: []string{accountId},
	}

	contextRegion = "Region"

	instancesWithDiscoveryLabel = []meta.Instances{{
		InstanceUUID:             "mesh-instance-id",
		IstioVersion:             testdata.Version1146,
		DiscoverySelectorEnabled: csm.Bool(true),
		DiscoverySelectorLabels:  `{"eks.baidu-int.com/workspaceID":"business-id-1"}`,
	}}

	user1 = sdkIAM.User{
		Domain: sdkIAM.UserDomain{
			ID: "account-id-1",
		},
	}
	user2 = sdkIAM.User{
		Domain: sdkIAM.UserDomain{
			ID: "account-id-2",
		},
	}
	user3 = sdkIAM.User{
		Domain: sdkIAM.UserDomain{
			ID: "123",
		},
	}

	instanceUUID = "csm-nxzqosxx"

	clusterID      = "cce-123456"
	clusterName    = "test-123456"
	testK8sVersion = "1.20.2"
)

func buildClusterDetail() *cce.GetClusterResult {
	return &cce.GetClusterResult{
		ClusterUuid: clusterID,
		ClusterName: clusterName,
		Version:     testK8sVersion,
		Region:      testRegion,
	}
}

func TestNewServiceMeshInstance(t *testing.T) {
	testInfos := []struct {
		name                              string
		body                              string
		mockCheckSupportedVersion         bool
		mockCheckSupportedVersionError    error
		mockGenerateInstancesID           bool
		mockGetHostingCluster             bool
		mockAddHostingServiceMeshInstance bool
		mockMultiProtocol                 bool
		mockEks                           bool
		expectErr                         error
		user                              *sdkIAM.User
	}{
		{
			name: "NewServiceMeshInstance-success",
			// nolint
			body:                              "{\"type\":\"hosting\",\"region\":\"bd\",\"serviceMeshInstanceName\":\"chentanjun-test\",\"istioVersion\":\"1.14.6\",\"elasticPublicNetwork\":{\"enabled\":true,\"type\":\"buy\",\"id\":\"xxx\"},\"networkType\":{\"vpcNetworkId\":\"vpc-xrrr4z0qgajc\",\"subnetId\":\"sbn-tk3y1vpvpmr5\"},\"traceInfo\":{\"traceEnabled\":true,\"samplingRate\":100,\"address\":\"jaeger-collector.istio-system:9411\",\"service\":\"xxx\"}}",
			mockCheckSupportedVersion:         true,
			mockGenerateInstancesID:           true,
			mockGetHostingCluster:             true,
			mockAddHostingServiceMeshInstance: true,
			expectErr:                         nil,
			user:                              user,
		},
		{
			name: "NewServiceMeshInstance-valid-type",
			// nolint
			body:                           "{\"region\":\"bd\",\"serviceMeshInstanceName\":\"chentanjun-test\",\"istioVersion\":\"1.14.6\",\"elasticPublicNetwork\":{\"enabled\":true,\"type\":\"buy\",\"id\":\"xxx\"},\"networkType\":{\"vpcNetworkId\":\"vpc-xrrr4z0qgajc\",\"subnetId\":\"sbn-tk3y1vpvpmr5\"},\"traceInfo\":{\"traceEnabled\":true,\"samplingRate\":100,\"address\":\"jaeger-collector.istio-system:9411\",\"service\":\"xxx\"}}",
			mockCheckSupportedVersion:      true,
			mockCheckSupportedVersionError: fmt.Errorf("type is null"),
			expectErr:                      fmt.Errorf("type is null"),
			user:                           user,
		},
		{
			name: "NewServiceMeshInstance-eks",
			body: "{\"region\":\"bd\",\"serviceMeshInstanceName\":\"chentanjun-test\",\"istioVersion\":\"1.14.6\"," +
				"\"elasticPublicNetwork\":{\"enabled\":true,\"type\":\"buy\",\"id\":\"xxx\"}," +
				"\"networkType\":{\"vpcNetworkId\":\"vpc-xrrr4z0qgajc\",\"subnetId\":\"sbn-tk3y1vpvpmr5\"}," +
				"\"discoverySelector\":{\"enabled\":true,\"matchLabels\":{\"foo\":\"bar\"}}," +
				"\"traceInfo\":{\"traceEnabled\":true,\"samplingRate\":100,\"address\":\"jaeger-collector.istio-system:9411\",\"service\":\"xxx\"}}",
			mockCheckSupportedVersion: true,
			mockGenerateInstancesID:   true,
			expectErr:                 nil,
			mockEks:                   true,
			user:                      &user3,
		},
		{
			name: "NewServiceMeshInstance-multiProtocol",
			// nolint
			body:                              "{\"type\":\"standalone\",\"region\":\"bd\",\"serviceMeshInstanceName\":\"chentanjun-test\",\"istioVersion\":\"1.14.6\",\"multiProtocol\":true,\"discoverySelector\":{\"enabled\":true,\"matchLabels\":{\"foo\":\"bar\"}},\"traceInfo\":{\"traceEnabled\":true,\"samplingRate\":100,\"address\":\"jaeger-collector.istio-system:9411\",\"service\":\"xxx\"}}",
			mockCheckSupportedVersion:         true,
			mockGenerateInstancesID:           true,
			mockMultiProtocol:                 true,
			mockGetHostingCluster:             false,
			mockAddHostingServiceMeshInstance: false,
			expectErr:                         nil,
			user:                              user,
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
			mockInstancesService := instancesMock.NewMockServiceInterface(ctrl)
			mockVersionService := versionMock.NewMockServiceInterface(ctrl)
			mockMultiProtocolService := multiProtocolServiceMock.NewMockServiceInterface(ctrl)
			if testInfo.mockCheckSupportedVersion {
				mockVersionService.EXPECT().CheckSupportedVersion(gomock.Any(), gomock.Any(),
					gomock.Any()).Return(true, testInfo.mockCheckSupportedVersionError)
			}
			mockCceService.EXPECT().GetCCECluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(buildClusterDetail(), nil).AnyTimes()
			mockVersionService.EXPECT().CheckK8sVersion(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
			if testInfo.mockGenerateInstancesID {
				mockInstancesService.EXPECT().GenerateInstancesID(gomock.Any()).Return(uuid, nil)
			}
			if testInfo.mockGetHostingCluster {
				mockInstancesService.EXPECT().GetHostingCluster(gomock.Any(), gomock.Any()).Return("test-cluster-id", "test-cluster-name")
			}
			if testInfo.mockAddHostingServiceMeshInstance {
				mockInstancesService.EXPECT().AddHostingServiceMeshInstance(gomock.Any(), gomock.Any()).Return(nil)
			}
			mockInstancesService.EXPECT().NewServiceMeshInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			serviceCore := &APIServerCore{
				InstancesService: mockInstancesService,
				VersionService:   mockVersionService,
				CsmOptions:       meshOption,
				cceService:       mockCceService,
			}

			if testInfo.mockMultiProtocol {
				mockMultiProtocolService.EXPECT().CheckVersionSupport(gomock.Any(), gomock.Any()).Return(true)
				serviceCore.MultiProtocolService = mockMultiProtocolService
			}

			ctx := context.MockNewCsmContext()
			ctx.Set("User", testInfo.user)
			ctx.Set(region.ContextRegion, testRegion)
			body := struct{ io.Reader }{strings.NewReader(testInfo.body)}
			testRequest := httptest.NewRequest("", "/v1/instance", body)
			testRequest.Header = http.Header{
				constants.RegionHeaderKey: []string{"bj"},
				"Content-Type":            []string{"application/json"},
			}
			ctx.SetRequest(testRequest)
			err := serviceCore.NewServiceMeshInstance(ctx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestCreateInstance(t *testing.T) {
	type MockResult struct {
		generatedInstanceId string
		checkVersionResult  bool
	}

	testCases := []struct {
		name           string
		body           *cnap.Instance
		mr             *MockResult
		expected       string
		expectedErrMsg string
		user           *sdkIAM.User
	}{
		{
			name: "create instance return mesh exists",
			body: &cnap.Instance{
				Type:         string(cnap.TypeHosting),
				Scope:        string(cnap.ScopeNamespace),
				Name:         testdata.Version1146,
				IstioVersion: "mesh-instance-test",
				ClusterId:    "",
				DiscoverySelector: &cnap.DiscoverySelector{Enabled: true, MatchLabels: map[string]string{
					cnap.BusinessKey: "business-id-1",
				}},
				TraceInfo: &cnap.TraceInfo{
					TraceEnabled: false,
				},
			},
			mr: &MockResult{
				generatedInstanceId: "mesh-instance-id",
				checkVersionResult:  true,
			},
			expected:       "mesh-instance-id",
			expectedErrMsg: "",
			user:           &user1,
		},
		{
			name: "create new instance success",
			body: &cnap.Instance{
				Type:         string(cnap.TypeHosting),
				Scope:        string(cnap.ScopeNamespace),
				Name:         testdata.Version1146,
				IstioVersion: "mesh-instance-test",
				ClusterId:    "",
				DiscoverySelector: &cnap.DiscoverySelector{Enabled: true, MatchLabels: map[string]string{
					cnap.BusinessKey: "business-id-2",
				}},
				TraceInfo: &cnap.TraceInfo{
					TraceEnabled: false,
				},
			},
			mr: &MockResult{
				generatedInstanceId: "mesh-instance-id",
				checkVersionResult:  true,
			},
			expected:       "mesh-instance-id",
			expectedErrMsg: "",
			user:           &user1,
		},
		{
			name: "create new instance with validation error",
			body: &cnap.Instance{
				Type:         string(cnap.TypeStandalone),
				Scope:        string(cnap.ScopeNamespace),
				Name:         testdata.Version1146,
				IstioVersion: "mesh-instance-test",
				ClusterId:    "",
				DiscoverySelector: &cnap.DiscoverySelector{Enabled: true, MatchLabels: map[string]string{
					cnap.BusinessKey: "business-id-2",
				}},
				TraceInfo: &cnap.TraceInfo{
					TraceEnabled: false,
				},
			},
			mr: &MockResult{
				generatedInstanceId: "mesh-instance-id",
				checkVersionResult:  true,
			},
			expected:       "mesh-instance-id",
			expectedErrMsg: "InvalidParameterValueException",
			user:           &user1,
		},
		{
			name: "create new instance with invalid user",
			body: &cnap.Instance{
				Type:         string(cnap.TypeHosting),
				Scope:        string(cnap.ScopeNamespace),
				Name:         testdata.Version1146,
				IstioVersion: "mesh-instance-test",
				ClusterId:    "",
				DiscoverySelector: &cnap.DiscoverySelector{Enabled: true, MatchLabels: map[string]string{
					cnap.BusinessKey: "business-id-2",
				}},
				TraceInfo: &cnap.TraceInfo{
					TraceEnabled: false,
				},
			},
			mr: &MockResult{
				generatedInstanceId: "mesh-instance-id",
				checkVersionResult:  true,
			},
			expected:       "mesh-instance-id",
			expectedErrMsg: "UnrecognizedClientException",
			user:           nil,
		},
		{
			name: "create new instance with unsupported version",
			body: &cnap.Instance{
				Type:         string(cnap.TypeHosting),
				Scope:        string(cnap.ScopeNamespace),
				Name:         "1.14.6",
				IstioVersion: "mesh-instance-test",
				ClusterId:    "",
				DiscoverySelector: &cnap.DiscoverySelector{Enabled: true, MatchLabels: map[string]string{
					cnap.BusinessKey: "business-id-2",
				}},
				TraceInfo: &cnap.TraceInfo{
					TraceEnabled: false,
				},
			},
			mr: &MockResult{
				generatedInstanceId: "mesh-instance-id",
				checkVersionResult:  false,
			},
			expected:       "mesh-instance-id",
			expectedErrMsg: "unsupported istio version",
			user:           &user1,
		},
		{
			name: "create new instance with conflict mesh instance id",
			body: &cnap.Instance{
				Type:         string(cnap.TypeHosting),
				Scope:        string(cnap.ScopeNamespace),
				Name:         testdata.Version1146,
				IstioVersion: "mesh-instance-test",
				ClusterId:    "",
				DiscoverySelector: &cnap.DiscoverySelector{Enabled: true, MatchLabels: map[string]string{
					cnap.BusinessKey: "business-id-2",
				}},
				TraceInfo: &cnap.TraceInfo{
					TraceEnabled: false,
				},
			},
			mr: &MockResult{
				generatedInstanceId: "mesh-instance-id",
				checkVersionResult:  true,
			},
			expected:       "mesh-instance-id",
			expectedErrMsg: "ConflictException",
			user:           &user1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jsonBody, err := json.Marshal(tc.body)
			if err != nil {
				t.FailNow()
			}

			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.Set(reg.ContextRegion, "bj")

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mi := instanceMock.NewMockServiceInterface(ctrl)
			mi.EXPECT().GetAllInstances(ctx).Return(instancesWithDiscoveryLabel, nil).AnyTimes()
			mi.EXPECT().GenerateInstancesID(ctx).Return(tc.expected, nil).AnyTimes()
			mi.EXPECT().NewServiceMeshInstance(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			mv := versionMock.NewMockServiceInterface(ctrl)
			mv.EXPECT().CheckSupportedVersion(ctx, tc.body.IstioVersion, gomock.Any()).Return(tc.mr.checkVersionResult, nil).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				InstancesService: mi,
				VersionService:   mv,
			}

			err = core.CreateInstance(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
				cir := &cnap.CreateInstanceResponse{}
				if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), cir)) {
					assert.Equal(t, cir.MeshInstanceId, tc.expected)
				}
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestListCceCluster(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name    string
		resErr  error
		wantErr bool
	}{
		{
			name:    "test_list_cce_cluster_succeed",
			resErr:  nil,
			wantErr: false,
		},
		{
			name:    "test_list_cce_cluster_failed",
			resErr:  fmt.Errorf(""),
			wantErr: true,
		},
	}
	mockCtx := context.MockNewCsmContext()
	for _, tt := range tests {
		mockInstancesService := instancesMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:       options.NewCsmServerOption(),
			InstancesService: mockInstancesService,
		}

		t.Run(tt.name, func(t *testing.T) {
			mockCtx.Request().Header.Set(constants.RegionHeaderKey, testRegion)

			mockInstancesService.EXPECT().GetAllCceClusterByRegion(mockCtx, gomock.Any()).Return([]servicemeta.MeshCluster{}, tt.resErr)

			res := core.ListCceCluster(mockCtx)
			assert.Equal(t, tt.wantErr, res != nil)
		})
	}
}

func TestCreateNamespace(t *testing.T) {
	type MockResult struct {
		createNamespaceResult error
	}
	type PathParam struct {
		meshInstanceId string
		namespace      string
	}

	testCases := []struct {
		name           string
		params         *PathParam
		mr             *MockResult
		expected       string
		expectedErrMsg string
		user           *sdkIAM.User
	}{
		{
			name: "create namespace success",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
				namespace:      "ns1",
			},
			mr: &MockResult{
				createNamespaceResult: nil,
			},
			expected:       "true",
			expectedErrMsg: "",
			user:           &user1,
		},
		{
			name: "create namespace with invalid user",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
				namespace:      "ns1",
			},
			mr: &MockResult{
				createNamespaceResult: nil,
			},
			expected:       "true",
			expectedErrMsg: "UnrecognizedClientException",
			user:           nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("meshInstanceId", "namespace")
			c.SetParamValues(tc.params.meshInstanceId, tc.params.namespace)

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mi := instanceMock.NewMockServiceInterface(ctrl)
			mi.EXPECT().CreateNamespace(ctx, tc.params.meshInstanceId, tc.params.namespace).Return(nil).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				InstancesService: mi,
			}

			err := core.CreateNamespace(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
				sr := &cnap.SuccessResponse{}
				if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), sr)) {
					assert.Equal(t, sr.Success, tc.expected)
				}
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestDeleteServiceMeshInstance(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name               string
		remoteCluster      bool
		mockRemoteCluster  bool
		hostingGateway     bool
		mockHostingGateway bool
		resultError        error
		expectError        error
	}{
		{
			name:               "TestDeleteServiceMeshInstance_succeed",
			remoteCluster:      false,
			mockRemoteCluster:  true,
			hostingGateway:     false,
			mockHostingGateway: true,
			resultError:        nil,
			expectError:        nil,
		},
		{
			name:               "TestDeleteServiceMeshInstance_ExistRemoteClusters",
			remoteCluster:      true,
			mockRemoteCluster:  true,
			hostingGateway:     true,
			mockHostingGateway: false,
			resultError:        nil,
			expectError:        fmt.Errorf("associated with remote cluster"),
		},
		{
			name:               "TestDeleteServiceMeshInstance_ExistGatewayWithInstanceUUID",
			remoteCluster:      false,
			mockRemoteCluster:  true,
			hostingGateway:     true,
			mockHostingGateway: true,
			resultError:        nil,
			expectError:        fmt.Errorf("associated with gateways"),
		},
	}
	for _, tt := range tests {
		mockCtx := context.MockNewCsmContext()
		mockInstancesService := instancesMock.NewMockServiceInterface(ctrl)
		mockGatewayService := gatewayServiceMock.NewMockServiceInterface(ctrl)
		mockClusterService := clusterServiceMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:       options.NewCsmServerOption(),
			InstancesService: mockInstancesService,
			GatewayService:   mockGatewayService,
			ClusterService:   mockClusterService,
		}
		t.Run(tt.name, func(t *testing.T) {
			mockCtx.Set("User", user)
			mockCtx.Set(contextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam)
			mockCtx.SetParamValues("aa")
			mockCtx.Request().Header.Set(constants.RegionHeaderKey, testRegion)

			if tt.mockRemoteCluster {
				mockClusterService.EXPECT().ExistRemoteClusters(gomock.Any(), gomock.Any()).Return(tt.remoteCluster, tt.resultError)
			}

			if tt.mockHostingGateway {
				mockGatewayService.EXPECT().ExistGatewayWithInstanceUUID(gomock.Any(), gomock.Any()).Return(tt.hostingGateway, tt.resultError)
			}

			if !tt.remoteCluster && !tt.hostingGateway {
				mockInstancesService.EXPECT().DeleteServiceMeshInstance(mockCtx, gomock.Any()).Return(tt.resultError)
			}

			res := core.DeleteServiceMeshInstance(mockCtx)

			if tt.expectError == nil {
				assert.Nil(t, res)
			} else {
				assert.Contains(t, res.Error(), tt.expectError.Error())
			}
		})
	}
}

func TestDeleteNamespace(t *testing.T) {
	type MockResult struct {
		deleteNamespaceResult error
	}
	type PathParam struct {
		meshInstanceId string
		namespace      string
	}

	testCases := []struct {
		name           string
		params         *PathParam
		mr             *MockResult
		expected       string
		expectedErrMsg string
		user           *sdkIAM.User
	}{
		{
			name: "create namespace success",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
				namespace:      "ns1",
			},
			mr: &MockResult{
				deleteNamespaceResult: nil,
			},
			expected:       "true",
			expectedErrMsg: "",
			user:           &user1,
		},
		{
			name: "create namespace with invalid user",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
				namespace:      "ns1",
			},
			mr: &MockResult{
				deleteNamespaceResult: nil,
			},
			expected:       "true",
			expectedErrMsg: "UnrecognizedClientException",
			user:           nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("meshInstanceId", "namespace")
			c.SetParamValues(tc.params.meshInstanceId, tc.params.namespace)

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mi := instanceMock.NewMockServiceInterface(ctrl)
			mi.EXPECT().DeleteNamespace(ctx, tc.params.meshInstanceId, tc.params.namespace).Return(nil).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				InstancesService: mi,
			}

			err := core.DeleteNamespace(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
				sr := &cnap.SuccessResponse{}
				if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), sr)) {
					assert.Equal(t, sr.Success, tc.expected)
				}
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestGetServiceMeshInstances(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name    string
		resErr  error
		wantErr bool
	}{
		{
			name:    "test_get_instance_list_succeed",
			resErr:  nil,
			wantErr: false,
		},
		{
			name:    "test_get_instance_list_failed",
			resErr:  fmt.Errorf(""),
			wantErr: true,
		},
	}
	mockCtx := context.MockNewCsmContext()
	for _, tt := range tests {
		mockInstancesService := instancesMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:       options.NewCsmServerOption(),
			InstancesService: mockInstancesService,
		}

		t.Run(tt.name, func(t *testing.T) {
			mockCtx.Set("User", user)
			mockCtx.Set(contextRegion, testRegion)

			mockInstancesService.EXPECT().GetServiceMeshInstances(mockCtx, gomock.Any()).Return(&meta.MeshInstanceListResponse{}, tt.resErr).Times(2)

			res := core.GetServiceMeshInstances(mockCtx)
			assert.Equal(t, tt.wantErr, res != nil)

			resIAM := core.GetServiceMeshInstancesIAM(mockCtx)
			assert.Equal(t, tt.wantErr, resIAM != nil)
		})
	}
}

func TestGetInstanceStatus(t *testing.T) {
	type MockResult struct {
		status    string
		statusErr error
	}
	type PathParam struct {
		meshInstanceId string
	}

	testCases := []struct {
		name           string
		query          string
		params         *PathParam
		mr             *MockResult
		expected       string
		expectedErrMsg string
		user           *sdkIAM.User
	}{
		{
			name: "get instance status success",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			mr: &MockResult{
				status:    constants.SmiRunning,
				statusErr: nil,
			},
			expected:       "running",
			expectedErrMsg: "",
			user:           &user1,
		},
		{
			name: "get instance unknown status",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			mr: &MockResult{
				status:    constants.SmiRunning,
				statusErr: nil,
			},
			expected:       "unknown",
			expectedErrMsg: "",
			user:           &user2,
		},
		{
			name:  "invalid eksRegion",
			query: "region=xx",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			mr: &MockResult{
				status:    constants.SmiRunning,
				statusErr: nil,
			},
			expected:       "unknown",
			expectedErrMsg: "",
			user:           &user2,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			target := fmt.Sprintf("/?%s", tc.query)
			req := httptest.NewRequest(http.MethodPost, target, nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("meshInstanceId")
			c.SetParamValues(tc.params.meshInstanceId)

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mi := instanceMock.NewMockServiceInterface(ctrl)
			mi.EXPECT().GetInstanceStatus(ctx, tc.params.meshInstanceId, gomock.Any(), gomock.Any()).Return(tc.mr.status,
				tc.mr.statusErr).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				InstancesService: mi,
			}

			err := core.GetInstanceStatus(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
				sr := &cnap.InstanceStatusResponse{}
				if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), sr)) {
					assert.Equal(t, sr.Status, tc.expected)
				}
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestAPIServerCore_GetIstioSupportK8sVersion(t *testing.T) {
	tests := []struct {
		name                string
		versionPairingLists []versionUtil.IstioSupportK8sVersion
		wantErr             bool
	}{
		{
			name: "test-getIstioSupportK8sVersion",
			versionPairingLists: []versionUtil.IstioSupportK8sVersion{
				{
					IstioVersion: constants.IstioVersion13,
					SupportedClusterVersionList: []string{
						constants.K8sVersion20,
						constants.K8sVersion22,
					},
				},
				{
					IstioVersion: constants.IstioVersion14,
					SupportedClusterVersionList: []string{
						constants.K8sVersion22,
						constants.K8sVersion24,
					},
				},
				{
					IstioVersion: constants.IstioVersion16,
					SupportedClusterVersionList: []string{
						constants.K8sVersion22,
						constants.K8sVersion24,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		mockCtx := context.MockNewCsmContext()
		ctrl := gomock.NewController(t)
		mockInstancesService := instancesMock.NewMockServiceInterface(ctrl)

		t.Run(tt.name, func(t *testing.T) {
			core := &APIServerCore{
				CsmOptions:       options.NewCsmServerOption(),
				InstancesService: mockInstancesService,
			}
			mockInstancesService.EXPECT().GetIstioSupportK8sVersion(mockCtx).Return(tt.versionPairingLists)
			if err := core.GetIstioSupportK8sVersion(mockCtx); (err != nil) != tt.wantErr {
				t.Errorf("GetVersionPairingList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
