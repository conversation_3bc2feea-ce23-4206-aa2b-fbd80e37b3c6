package core

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"net/http/httptest"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	TraceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/trace/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

var (
	traceInfo = &meta.TraceInfo{
		TraceEnabled: true,
		SamplingRate: 100,
	}
	paramNames = []string{
		constants.RegionHeaderKey,
		constants.InstanceIDPathParam,
	}
)

func TestAPIServerCore_UpdateTrace(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockTrace := TraceMock.NewMockServiceInterface(ctl)

	paramValues := []string{"bj", instanceUUID}
	rec := httptest.NewRecorder()
	ctx := getMockCtx(paramNames, paramValues, rec)
	ctx.Request().Header.Set(constants.RegionHeaderKey, "bj")

	tests := []struct {
		name    string
		wantErr error
	}{
		{
			name:    "test-UpdateTrace",
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			core := &APIServerCore{
				CsmOptions:   options.NewCsmServerOption(),
				traceService: mockTrace,
			}
			mockTrace.EXPECT().UpdateTrace(ctx, gomock.Any(), gomock.Any()).Return(traceInfo, tt.wantErr)
			err := core.UpdateTrace(ctx)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
		})
	}
}

// TestAPIServerCore_GetTrace 测试APIServerCore_GetTrace函数的功能
// 参数：t *testing.T - 单元测试的对象，用于记录结果并报错
// 返回值：nil<E> - 没有返回值，只是用于执行单元测试和断言
func TestAPIServerCore_GetTrace(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockTrace := TraceMock.NewMockServiceInterface(ctl)
	paramValues := []string{"bj", instanceUUID}
	rec := httptest.NewRecorder()
	ctx := getMockCtx(paramNames, paramValues, rec)
	ctx.Request().Header.Set(constants.RegionHeaderKey, "bj")
	tests := []struct {
		name    string
		wantErr error
	}{
		{
			name:    "test-GetTrace",
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			core := &APIServerCore{
				CsmOptions:   options.NewCsmServerOption(),
				traceService: mockTrace,
			}
			mockTrace.EXPECT().GetTrace(ctx, gomock.Any()).Return(traceInfo, tt.wantErr)
			err := core.GetTrace(ctx)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
		})
	}
}
