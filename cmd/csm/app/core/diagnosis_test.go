package core

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	diagnosis_mock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/diagnosis/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	service_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

var (
	ProxyName   = "helloworld-v1-5f7f86678c-6qffx.default"
	ClusterName = "gz-cce-hh4z2ea4"
	PilotName   = "istiod-66c5ff9789-vflhj"
	Version     = "1.16.5"
	CDS         = "SYNCED"
	LDS         = "SYNCED"
	EDS         = "SYNCED"
	RDS         = "SYNCED"
	ECDS        = "SYNCED"
)

func buildPss() []*service_meta.ProxyStatus {
	res := make([]*service_meta.ProxyStatus, 0)
	ps := service_meta.ProxyStatus{
		Proxy:       ProxyName,
		ClusterName: ClusterName,
		PilotName:   PilotName,
		Version:     Version,
		CDS:         CDS,
		LDS:         LDS,
		EDS:         EDS,
		RDS:         RDS,
		ECDS:        ECDS,
	}
	res = append(res, &ps)
	return res
}

func buildExpectResult() []interface{} {
	expectProxyStatus := make([]interface{}, 0)
	psv := internal.ProxyStatusView{
		ProxyName:   ProxyName,
		ClusterName: clusterName,
		PilotName:   PilotName,
		Version:     Version,
		CDS:         CDS,
		LDS:         LDS,
		EDS:         EDS,
		RDS:         RDS,
		ECDS:        ECDS,
	}
	expectProxyStatus = append(expectProxyStatus, psv)
	return expectProxyStatus
}

func buildConfigDumpResult() []interface{} {
	expectProxyStatus := make([]interface{}, 0)
	res := make([]byte, 0)
	resStr := `{ "content": [` + string(res) + `]}`
	expectProxyStatus = append(expectProxyStatus, resStr)
	return expectProxyStatus
}

func buildExceptionResult() []interface{} {
	exceptionViews := make([]interface{}, 0)
	exceptions := make([]meta.Exception, 0)
	for _, e := range exceptions {
		ev := meta.Exception{
			Name:        e.Name,
			Level:       e.Level,
			Code:        e.Code,
			Description: e.Description,
		}
		exceptionViews = append(exceptionViews, ev)
	}
	return exceptionViews
}

func TestProxyStatusList(t *testing.T) {
	type MockResult struct {
		ps      []*service_meta.ProxyStatus
		pr      *vo.PageResult
		mockErr error
	}
	type PathParam struct {
		instanceUUID string
		namespace    string
	}

	testCases := []struct {
		name           string
		pathParams     *PathParam
		mr             *MockResult
		expected       []interface{}
		expectedErrMsg *string
		user           *sdkIAM.User
	}{
		{
			name: "ProxyStatusList-success",
			pathParams: &PathParam{
				instanceUUID: "mesh-instance-id",
				namespace:    "default",
			},
			mr: &MockResult{
				ps:      buildPss(),
				pr:      &vo.PageResult{},
				mockErr: nil,
			},
			expected:       buildExpectResult(),
			expectedErrMsg: nil,
			user:           &user1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			q := make(url.Values)
			req := httptest.NewRequest(http.MethodGet, "/?"+q.Encode(), nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("instanceUUID", "namespace")
			c.SetParamValues(tc.pathParams.instanceUUID, tc.pathParams.namespace)

			ctx := csmContext.NewCsmContext(c)
			ctrl := gomock.NewController(t)
			diagnosisService := diagnosis_mock.NewMockServiceInterface(ctrl)
			diagnosisService.EXPECT().ProxyStatusList(ctx, gomock.Any(), gomock.Any(), gomock.Any()).
				Return(tc.mr.ps, tc.mr.pr, tc.mr.mockErr)

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				diagnosisService: diagnosisService,
			}

			err := core.ProxyStatusList(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
				pv := &internal.PageView{}
				if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), pv)) {
					result := pv.Result
					reflect.DeepEqual(result, tc.expected)
				}
			} else {
				assert.Containsf(t, err.Error(), *tc.expectedErrMsg,
					"expected error: %v, got %v", *tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestProxyConfigList(t *testing.T) {
	type MockResult struct {
		result  []byte
		pr      *vo.PageResult
		mockErr error
	}
	type PathParam struct {
		instanceUUID string
		namespace    string
	}
	type QueryParam struct {
		proxyName   string
		typePC      string
		clusterName string
	}

	testCases := []struct {
		name           string
		pathParams     *PathParam
		queryParams    *QueryParam
		mr             *MockResult
		expected       []interface{}
		expectedErrMsg *string
		user           *sdkIAM.User
	}{
		{
			name: "ProxyConfigList-success",
			pathParams: &PathParam{
				instanceUUID: "mesh-instance-id",
				namespace:    "default",
			},
			queryParams: &QueryParam{
				proxyName:   "helloworld-v2-779cd8fd6c-7kf6n.default",
				typePC:      "cluster",
				clusterName: "gz-cce-hh4z2e11",
			},
			mr: &MockResult{
				result:  nil,
				pr:      &vo.PageResult{},
				mockErr: nil,
			},
			expected:       buildExpectResult(),
			expectedErrMsg: nil,
			user:           &user1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			q := make(url.Values)
			q.Set("proxyName", tc.queryParams.proxyName)
			q.Set("type", tc.queryParams.typePC)
			q.Set("clusterName", tc.queryParams.clusterName)

			req := httptest.NewRequest(http.MethodGet, "/?"+q.Encode(), nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("instanceUUID", "namespace")
			c.SetParamValues(tc.pathParams.instanceUUID, tc.pathParams.namespace)

			ctx := csmContext.NewCsmContext(c)
			ctrl := gomock.NewController(t)
			diagnosisService := diagnosis_mock.NewMockServiceInterface(ctrl)
			diagnosisService.EXPECT().ProxyConfigList(ctx, gomock.Any(), gomock.Any(), gomock.Any()).
				Return(tc.mr.result, tc.mr.pr, tc.mr.mockErr)

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				diagnosisService: diagnosisService,
			}

			err := core.ProxyConfigList(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
				pv := &internal.PageView{}
				if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), pv)) {
					result := pv.Result
					reflect.DeepEqual(result, tc.expected)
				}
			} else {
				assert.Containsf(t, err.Error(), *tc.expectedErrMsg,
					"expected error: %v, got %v", *tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestConfigDump(t *testing.T) {
	type MockResult struct {
		result  []byte
		mockErr error
	}
	type PathParam struct {
		instanceUUID string
		namespace    string
	}
	type QueryParam struct {
		proxyName   string
		clusterName string
	}

	testCases := []struct {
		name           string
		pathParams     *PathParam
		queryParams    *QueryParam
		mr             *MockResult
		expected       []interface{}
		expectedErrMsg *string
		user           *sdkIAM.User
	}{
		{
			name: "ConfigDump",
			pathParams: &PathParam{
				instanceUUID: "mesh-instance-id",
				namespace:    "default",
			},
			queryParams: &QueryParam{
				proxyName:   "helloworld-v2-779cd8fd6c-7kf6n.default",
				clusterName: "gz-cce-hh4z2e11",
			},
			mr: &MockResult{
				result:  nil,
				mockErr: nil,
			},
			expected: buildConfigDumpResult(),
			user:     &user1,
		},
	}
	for _, tc := range testCases {
		e := echo.New()
		q := make(url.Values)
		q.Set("proxyName", tc.queryParams.proxyName)
		q.Set("clusterName", tc.queryParams.clusterName)

		req := httptest.NewRequest(http.MethodGet, "/?"+q.Encode(), nil)
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.Set(iam.ContextIAMUser, tc.user)
		c.SetParamNames("instanceUUID", "namespace")
		c.SetParamValues(tc.pathParams.instanceUUID, tc.pathParams.namespace)

		ctx := csmContext.NewCsmContext(c)
		ctrl := gomock.NewController(t)
		diagnosisService := diagnosis_mock.NewMockServiceInterface(ctrl)
		diagnosisService.EXPECT().ConfigDump(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(tc.mr.result, tc.mr.mockErr)
		core := &APIServerCore{
			CsmOptions: &options.CsmOptions{
				EksAccountIds: []string{"account-id-1"},
			},
			diagnosisService: diagnosisService,
		}
		err := core.ConfigDump(ctx)
		if err == nil {
			assert.Equal(t, http.StatusOK, rec.Code)
			pv := &internal.PageView{}
			if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), pv)) {
				result := pv.Result
				reflect.DeepEqual(result, tc.expected)
			}
		} else {
			assert.Containsf(t, err.Error(), *tc.expectedErrMsg,
				"expected error: %v, got %v", *tc.expectedErrMsg, err.Error())
		}
	}
}
func TestExceptionList(t *testing.T) {
	type MockResult struct {
		result  []*meta.Exception
		pr      *vo.PageResult
		mockErr error
	}
	type PathParam struct {
		instanceUUID string
		namespace    string
	}
	type QueryParam struct {
		namespace string
	}

	testCases := []struct {
		name           string
		pathParams     *PathParam
		queryParams    *QueryParam
		mr             *MockResult
		expected       []interface{}
		expectedErrMsg *string
		user           *sdkIAM.User
	}{
		{
			name: "ExceptionList",
			pathParams: &PathParam{
				instanceUUID: "mesh-instance-id",
				namespace:    "default",
			},
			queryParams: &QueryParam{
				namespace: "default",
			},
			mr: &MockResult{
				result:  nil,
				pr:      &vo.PageResult{},
				mockErr: nil,
			},
			expected: buildExceptionResult(),
			user:     &user1,
		},
	}
	for _, tc := range testCases {
		e := echo.New()
		q := make(url.Values)
		q.Set("namespace", tc.queryParams.namespace)

		req := httptest.NewRequest(http.MethodGet, "/?"+q.Encode(), nil)
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.Set(iam.ContextIAMUser, tc.user)
		c.SetParamNames("instanceUUID", "namespace")
		c.SetParamValues(tc.pathParams.instanceUUID, tc.pathParams.namespace)

		ctx := csmContext.NewCsmContext(c)
		ctrl := gomock.NewController(t)
		diagnosisService := diagnosis_mock.NewMockServiceInterface(ctrl)
		diagnosisService.EXPECT().ExceptionList(ctx, gomock.Any(), gomock.Any(), gomock.Any()).
			Return(tc.mr.result, tc.mr.pr, tc.mr.mockErr)
		core := &APIServerCore{
			CsmOptions: &options.CsmOptions{
				EksAccountIds: []string{"account-id-1"},
			},
			diagnosisService: diagnosisService,
		}
		err := core.ExceptionList(ctx)
		if err == nil {
			assert.Equal(t, http.StatusOK, rec.Code)
			pv := &internal.PageView{}
			if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), pv)) {
				result := pv.Result
				reflect.DeepEqual(result, tc.expected)
			}
		} else {
			assert.Containsf(t, err.Error(), *tc.expectedErrMsg,
				"expected error: %v, got %v", *tc.expectedErrMsg, err.Error())
		}
	}
}
