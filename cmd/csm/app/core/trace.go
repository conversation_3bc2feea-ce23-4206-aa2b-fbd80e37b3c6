package core

import (
	"fmt"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"net/http"
)

// UpdateTrace 更新链路追踪
func (core *APIServerCore) UpdateTrace(ctx csmContext.CsmContext) (error error) {
	//从param中获取InstanceUuid
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException(constants.InstanceIDPathParam)
	}
	traceInfo := &meta.TraceInfo{}
	if err := ctx.Bind(traceInfo); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	ctx.CsmLogger().Infof("traceInfo.Address is %s", traceInfo.Address)
	// 开启链路追踪，请求参数不对
	if traceInfo.TraceEnabled {
		if traceInfo.Address == "" {
			return csmErr.NewInvalidParameterValueExceptionWithoutMsg(fmt.Errorf("trace address is nil"))
		}
		if !validateTraceAddress(traceInfo.Address) {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("Trace address %s is valid", traceInfo.Address))
		}
	}

	updated, err := core.traceService.UpdateTrace(ctx, instanceUUID, traceInfo)
	if err != nil {
		return csmErr.NewUnknownError(err)
	}
	return ctx.JSON(http.StatusOK, updated)
}

// GetTrace 查看链路追踪
func (core *APIServerCore) GetTrace(ctx csmContext.CsmContext) (error error) {
	//从param中获取InstanceUuid
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException(constants.InstanceIDPathParam)
	}

	traceInfo, err := core.traceService.GetTrace(ctx, instanceUUID)
	if err != nil {
		return csmErr.NewUnknownError(err)
	}
	return ctx.JSON(http.StatusOK, traceInfo)
}
