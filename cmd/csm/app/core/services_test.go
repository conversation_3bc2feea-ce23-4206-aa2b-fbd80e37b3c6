package core

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	servicesMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/services/mock"
)

func TestGetMeshInstanceServices(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectRes *meta.MeshServiceListResponse
		expectErr error
	}{
		{
			name:      "correct-GetMeshInstanceServices",
			expectRes: nil,
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockServicesService := servicesMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:      options.NewCsmServerOption(),
			ServicesSerivce: mockServicesService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockServicesService.EXPECT().GetMeshInstanceServices(mockCtx, gomock.Any()).Return(testInfo.expectRes, testInfo.expectErr)
			err := core.GetMeshInstanceServices(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestGetServiceDetails(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectRes *meta.MeshServiceDetail
		expectErr error
	}{
		{
			name:      "correct-GetServiceDetails",
			expectRes: nil,
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockServicesService := servicesMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:      options.NewCsmServerOption(),
			ServicesSerivce: mockServicesService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockServicesService.EXPECT().GetServiceDetails(mockCtx, gomock.Any()).Return(testInfo.expectRes, testInfo.expectErr)
			err := core.GetServiceDetails(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}
