package core

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	overviewMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/overview/mock"
)

func buildMockInstancesOverview() *meta.InstancesOverview {
	return &meta.InstancesOverview{
		GroupByRegion: nil,
		GroupByStatus: nil,
	}
}

func TestGetOverviewOfInstances(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		region    string
		expectRes *meta.InstancesOverview
		expectErr error
	}{
		{
			name:      "GetOverviewOfInstances-success",
			region:    testRegion,
			expectRes: buildMockInstancesOverview(),
			expectErr: nil,
		},
		{
			name:      "GetOverviewOfInstances-region-success",
			expectRes: buildMockInstancesOverview(),
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockOverviewService := overviewMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:      options.NewCsmServerOption(),
			OverviewService: mockOverviewService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			e := echo.New()
			req := new(http.Request)
			rec := httptest.NewRecorder()

			q := make(url.Values)
			q.Set(reg.QueryRegion, testInfo.region)
			req = httptest.NewRequest(http.MethodGet, "/?"+q.Encode(), nil)
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, user)
			c.Set(reg.ContextRegion, testRegion)

			ctx := csmContext.NewCsmContext(c)

			mockOverviewService.EXPECT().GetInstancesOverview(ctx, gomock.Any()).Return(testInfo.expectRes, testInfo.expectErr)
			err := core.GetOverviewOfInstances(ctx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}
