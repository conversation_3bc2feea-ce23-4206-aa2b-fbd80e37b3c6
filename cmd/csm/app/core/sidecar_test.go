package core

import (
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	SidecarMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/sidecar/mock"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

const ContextIAMUser = "User"

func TestUpdateSidecarQuota(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockSidecar := SidecarMock.NewMockServiceInterface(ctl)
	ctx := context.MockNewCsmContext()
	bodyString := "{\"cpuQuota\":{\"request\":400,\"limit\":4000}}"
	body := struct{ io.Reader }{strings.NewReader(bodyString)}
	testRequest := httptest.NewRequest("", "/", body)
	testRequest.Header = http.Header{
		"Content-Type": []string{"application/json"},
	}
	ctx.SetRequest(testRequest)

	ctx.Set(ContextIAMUser, &sdkIAM.User{
		ID:   "",
		Name: "",
		Domain: sdkIAM.UserDomain{
			ID:   "123",
			Name: "",
		},
	})
	tests := []struct {
		name             string
		context          context.CsmContext
		wantResult       string
		wantSidecarQuota *meta.SidecarQuota
		wantErr          bool
	}{
		// TODO: Add test cases.
		{
			name:       "CpuQuota edit",
			context:    ctx,
			wantResult: "The parameter must be greater than zero",
			wantSidecarQuota: &meta.SidecarQuota{
				CpuQuota: meta.CpuQuota{
					Request: 400,
					Limit:   4000,
					Unit:    "m",
				},
				MemoryQuota: meta.MemoryQuota{
					Request: 128,
					Limit:   1024,
					Unit:    "Mi",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			apiServiceCore := &APIServerCore{
				CsmOptions:     options.NewCsmServerOption(),
				SidecarService: mockSidecar,
			}
			mockSidecar.EXPECT().UpdateSidecarQuota(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.wantSidecarQuota, nil)

			err := apiServiceCore.UpdateSidecarQuota(ctx)
			if err != nil {
				t.Errorf("UpdateSidecarQuota() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equal(t, tt.wantErr, err != nil)
		})
	}
}
