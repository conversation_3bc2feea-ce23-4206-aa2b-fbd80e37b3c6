package core

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

func TestRouteVirtualConsumerLogic(t *testing.T) {
	core := &APIServerCore{}

	t.Run("开启认证且有消费者时应该不添加虚拟消费者", func(t *testing.T) {
		// 模拟开启认证且有消费者的情况
		authEnabled := true
		allowedConsumers := []string{"consumer1", "consumer2"}

		// 模拟实际的逻辑
		allowList := []interface{}{}
		if authEnabled && len(allowedConsumers) > 0 {
			// 开启认证且有消费者时，添加所有消费者
			for _, consumerName := range allowedConsumers {
				allowList = append(allowList, consumerName)
			}
		} else {
			// 未开启认证 或 开启认证但没有消费者时，都添加虚拟消费者
			allowList = core.addVirtualConsumerToEmptyAllowArray(allowList)
		}

		assert.Len(t, allowList, 2)
		assert.Equal(t, "consumer1", allowList[0])
		assert.Equal(t, "consumer2", allowList[1])
	})

	t.Run("开启认证但没有消费者时应该添加虚拟消费者", func(t *testing.T) {
		// 模拟开启认证但没有消费者的情况
		authEnabled := true
		allowedConsumers := []string{}

		// 模拟实际的逻辑
		allowList := []interface{}{}
		if authEnabled && len(allowedConsumers) > 0 {
			// 开启认证且有消费者时，添加所有消费者
			for _, consumerName := range allowedConsumers {
				allowList = append(allowList, consumerName)
			}
		} else {
			// 未开启认证 或 开启认证但没有消费者时，都添加虚拟消费者
			allowList = core.addVirtualConsumerToEmptyAllowArray(allowList)
		}

		assert.Len(t, allowList, 1)
		assert.Equal(t, constants.GetVirtualDenyAllConsumerName(), allowList[0])
	})

	t.Run("未开启认证时应该添加虚拟消费者", func(t *testing.T) {
		// 模拟未开启认证的情况
		authEnabled := false
		allowedConsumers := []string{"consumer1"} // 即使有消费者，未开启认证时也应该使用虚拟消费者

		// 模拟实际的逻辑
		allowList := []interface{}{}
		if authEnabled && len(allowedConsumers) > 0 {
			// 开启认证且有消费者时，添加所有消费者
			for _, consumerName := range allowedConsumers {
				allowList = append(allowList, consumerName)
			}
		} else {
			// 未开启认证 或 开启认证但没有消费者时，都添加虚拟消费者
			allowList = core.addVirtualConsumerToEmptyAllowArray(allowList)
		}

		assert.Len(t, allowList, 1)
		assert.Equal(t, constants.GetVirtualDenyAllConsumerName(), allowList[0])
	})

	t.Run("未开启认证且没有消费者时也应该添加虚拟消费者", func(t *testing.T) {
		// 模拟未开启认证且没有消费者的情况
		authEnabled := false
		allowedConsumers := []string{}

		// 模拟实际的逻辑
		allowList := []interface{}{}
		if authEnabled && len(allowedConsumers) > 0 {
			// 开启认证且有消费者时，添加所有消费者
			for _, consumerName := range allowedConsumers {
				allowList = append(allowList, consumerName)
			}
		} else {
			// 未开启认证 或 开启认证但没有消费者时，都添加虚拟消费者
			allowList = core.addVirtualConsumerToEmptyAllowArray(allowList)
		}

		assert.Len(t, allowList, 1)
		assert.Equal(t, constants.GetVirtualDenyAllConsumerName(), allowList[0])
	})
}
