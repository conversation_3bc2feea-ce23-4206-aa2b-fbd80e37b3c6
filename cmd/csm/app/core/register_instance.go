package core

import (
	"fmt"
	"net/http"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/page"
	utilUuid "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/uuid"
)

// NewRegisterCenterInstance 创建托管注册中心实例
func (core *APIServerCore) NewRegisterCenterInstance(ctx csmContext.CsmContext) (err error) {

	// 请求参数校验
	instances := &internal.RegisterInstance{}
	if err = ctx.Bind(instances); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return csmErr.NewUnauthorizedException("user is nil", err)
	}

	if instances.Region == "" {
		return csmErr.NewInvalidParameterValueException("instance region is empty")
	}

	instances.AccountId = accountId
	instances.InstanceId = utilUuid.GetRegisterInstanceUUID()
	model, err := instances.ToRegisterInstancesModel(ctx)
	if err != nil {
		return err
	}

	err = core.RegisterCenterService.NewRegisterInstance(ctx, model, instances.MonitorToken, instances.EsgId)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, map[string]string{"instanceId": instances.InstanceId})
}

// UpdateRegisterCenterInstance 更新托管注册中心实例
func (core *APIServerCore) UpdateRegisterCenterInstance(ctx csmContext.CsmContext) (err error) {
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}
	req := &meta.UpdateRegistryReq{}
	if err = ctx.Bind(req); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	for _, ep := range req.Endpoints {
		err = core.RegisterCenterService.UpdateRegisterInstance(ctx, instanceId, ep.Id, ep.EsgId)
		if err != nil {
			return err
		}
	}
	return nil
}

// GetRegisterCenterInstanceList 获取托管注册中心实例列表
func (core *APIServerCore) GetRegisterCenterInstanceList(ctx csmContext.CsmContext) (err error) {
	accountId, err := iam.GetAccountId(ctx)
	p := page.GetPageParam(ctx, map[string]string{})
	queryArgs := &meta.QueryRegisterInstance{
		AccountID: accountId,
		PageNo:    p.PageNo,
		PageSize:  p.PageSize,
		Order:     p.Order,
		OrderBy:   p.OrderBy,
	}

	pageResponse, err := core.RegisterCenterService.GetRegisterInstances(ctx, queryArgs)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, pageResponse)
}

// DeleteRegisterCenterInstance 删除托管注册中心实例
func (core *APIServerCore) DeleteRegisterCenterInstance(ctx csmContext.CsmContext) (err error) {
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	err = core.RegisterCenterService.DeleteRegisterInstanceByInstanceId(ctx, instanceId)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, true)

}

func (core *APIServerCore) GetRegisterInstanceById(ctx csmContext.CsmContext) (err error) {
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	ctx.CsmLogger().Infof("GetRegisterInstanceById: %s", instanceId)

	registerInsDetail, err := core.RegisterCenterService.GetRegisterInstancesByInstanceId(ctx, instanceId)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, registerInsDetail)
}

// CheckWhiteList 查看用户是否在白名单中
func (core *APIServerCore) CheckWhiteList(ctx csmContext.CsmContext) (error error) {
	// 校验资源权限
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}
	mrp := meta.NewCsmMeshRequestParams()
	mrp.AccountID = accountId
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	if len(region) == 0 {
		return csmErr.NewMissingParametersException(fmt.Sprintf("Missing Header %s", constants.RegionHeaderKey))
	}
	mrp.Region = region

	isExist, err := core.WhiteListService.CheckCseWhiteList(ctx, mrp)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, isExist)
}
