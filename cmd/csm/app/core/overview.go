package core

import (
	"net/http"

	"github.com/jinzhu/copier"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

func (core *APIServerCore) GetOverviewOfInstances(ctx context.CsmContext) (err error) {
	region, err := internal.GetRequestRegion(ctx)
	if err != nil {
		return err
	}
	io, err := core.OverviewService.GetInstancesOverview(ctx, region)
	if err != nil {
		return err
	}

	overview := &internal.InstancesView{}
	err = copier.Copy(overview, io)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, overview)
}

func (core *APIServerCore) GetOverviewOfSidecars(ctx context.CsmContext) (err error) {
	region, err := internal.GetRequestRegion(ctx)
	if err != nil {
		return err
	}

	so, err := core.OverviewService.GetSidecarsOverview(ctx, region)
	if err != nil {
		return err
	}

	overview := make([]internal.SidecarsView, 0, len(so))
	for _, s := range so {
		o := &internal.SidecarsView{}
		err = copier.Copy(o, &s)
		if err != nil {
			return err
		}
		overview = append(overview, *o)
	}
	return ctx.JSON(http.StatusOK, overview)
}

func (core *APIServerCore) GetOverviewOfInstancesDetail(ctx context.CsmContext) (err error) {
	region, err := internal.GetRequestRegion(ctx)
	if err != nil {
		return err
	}

	ido, err := core.OverviewService.GetInstancesDetailOverview(ctx, region)
	if err != nil {
		return err
	}

	overview := make([]internal.InstanceDetailView, 0, len(ido))
	for _, s := range ido {
		o := &internal.InstanceDetailView{}
		err = copier.Copy(o, s)
		if err != nil {
			return err
		}
		overview = append(overview, *o)
	}
	return ctx.JSON(http.StatusOK, overview)
}
