package core

import (
	"net/http"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// GetMeshVersionList 获取支持的 istio 版本列表
func (core *APIServerCore) GetMeshVersionList(ctx csmContext.CsmContext) error {
	versionType := ctx.Param("type")
	err := core.VersionService.CheckVersionType(ctx, versionType)
	if err != nil {
		return err
	}
	versionList, err := core.VersionService.GetMeshVersionList(ctx, versionType)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, versionList)
}
