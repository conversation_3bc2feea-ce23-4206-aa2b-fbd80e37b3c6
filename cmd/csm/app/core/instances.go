package core

import (
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"

	"github.com/jinzhu/copier"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/internal/pkg/request"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/api/v1/cnap"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/sliceutil"
)

// AddHostingServiceMeshInstance 创建托管服务网格
func (core *APIServerCore) AddHostingServiceMeshInstance(ctx csmContext.CsmContext, instances *internal.Instances) (error error) {
	instances.Scope = string(meta.InstanceManageNamespaceScope)
	clusterId, clusterName := core.InstancesService.GetHostingCluster(ctx, instances.Region)
	if len(clusterId) == 0 || len(clusterName) == 0 {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(fmt.Errorf("clusterId or clusterName is nil"))
	}
	instances.InstallationClusterId = clusterId
	instances.InstallationClusterName = clusterName
	createMeshRequest, err := instances.ToCreateMeshRequest(ctx)
	if err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	paasType := meta.PaaSTypeCCE
	if sliceutil.StringContains(core.CsmOptions.EksAccountIds, instances.AccountId) {
		paasType = meta.PaaSTypeEKS
	}
	createMeshRequest.PaaSType = paasType
	err = core.InstancesService.AddHostingServiceMeshInstance(ctx, createMeshRequest)
	if err != nil {
		ctx.CsmLogger().Errorf("create istio cluster error %v", err)
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

// NewServiceMeshInstance 创建服务网格实例
func (core *APIServerCore) NewServiceMeshInstance(ctx csmContext.CsmContext) (error error) {
	// 校验资源权限
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	// TODO: 统一校验行为，包括 monitor/discoverySelector 等表现
	// 1. 监控打开时，必需选监控实例方能创建网格实例
	// 2. 选择监控实例时，只能选择本区域实例（临时逻辑）

	// 请求参数校验
	instances := &internal.Instances{}
	if err = ctx.Bind(instances); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	meshType := instances.Type

	// 校验 istio 版本
	ok, err := core.VersionService.CheckSupportedVersion(ctx, instances.IstioVersion, meshType)
	if err != nil || !ok {
		return csmErr.NewInvalidParameterInputValueException(
			fmt.Sprintf("unsupported istio version %s", instances.IstioVersion), err)
	}

	// 校验k8s版本, 只对独立网格 istio 使用的 k8s 集群进行校验
	if strings.EqualFold(string(version.StandaloneVersionType), meshType) {
		cceCluster, err := core.cceService.GetCCECluster(ctx, ctx.Request().Header.Get(constants.RegionHeaderKey), instances.InstallationClusterId)
		if err != nil {
			return err
		}
		ok, err = core.VersionService.CheckK8sVersion(ctx, instances.IstioVersion, cceCluster.Version)
		if err != nil || !ok {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("The k8s cluster version does not match the istio version."+
				"<a target='_blank' href='https://istio.io/latest/docs/releases/supported-releases/#support-status-of-istio-releases'>Read the document.</a>"), err)
		}
	}

	// 校验是否支持第三方私有协议
	if instances.MultiProtocol {
		// TODO 目前仅独立网格支持第三方私有协议，后续在拓展
		if !strings.EqualFold(string(version.StandaloneVersionType), meshType) {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("unsupported other protocol in %s", meshType))
		}
		ok = core.MultiProtocolService.CheckVersionSupport(ctx, instances.IstioVersion)
		if !ok {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("unsupported other protocol in version %s", instances.IstioVersion))
		}
	}

	// 校验 TraceInfo
	if instances.TraceInfo != nil {
		if instances.TraceInfo.TraceEnabled {
			if instances.TraceInfo.Address == "" {
				return csmErr.NewInvalidParameterValueExceptionWithoutMsg(fmt.Errorf("trace address is nil"))
			}
			if !validateTraceAddress(instances.TraceInfo.Address) {
				return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("Trace address %s is valid", instances.TraceInfo.Address))
			}
		}
	} else {
		instances.TraceInfo = &internal.TraceInfo{
			TraceEnabled: false,
		}
	}

	instances.Region = ctx.Get(reg.ContextRegion).(string)
	instances.AccountId = accountId
	uuid, err := core.InstancesService.GenerateInstancesID(ctx)
	if err != nil {
		return csmErr.NewDBOperationException(err)
	}
	instances.InstancesUUID = uuid
	if strings.EqualFold(string(version.HostingVersionType), meshType) {
		// 校验托管服务网格开启公网访问参数
		elasticPublicNetwork := instances.ElasticPublicNetwork
		if elasticPublicNetwork == nil {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("elasticPublicNetwork is nil"))
		}

		if elasticPublicNetwork.Enabled && elasticPublicNetwork.PublicNetworkType == internal.BIND && len(elasticPublicNetwork.Id) == 0 {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("elasticPublicNetwork id is nil for hosting istio when public type is BIND"))
		}

		networkType := instances.NetworkType
		if networkType == nil {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("networkType is nil"))
		}
		if len(networkType.VpcNetworkId) == 0 || len(networkType.SubnetId) == 0 {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("networkType vpcNetworkId or subnetId is nil"))
		}
		return core.AddHostingServiceMeshInstance(ctx, instances)
	}

	// TODO: 后续集群级别或命名空间级别参数由前端传
	// eks 只能用命名空间粒度
	if sliceutil.StringContains(core.CsmOptions.EksAccountIds, accountId) {
		instances.Scope = string(meta.InstanceManageNamespaceScope)
		instances.Type = string(meta.CnapMeshType)
		// 对于内部 EKS 必须使用选择性服务发现
		if !instances.CheckDiscoverySelector() {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("we must be use discoverySelectors on eks"))
		}
	} else {
		instances.Scope = string(meta.InstanceManageClusterScope)
	}

	// 模型转换
	newInstances, err := instances.ToInstancesModel(ctx)
	if err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	clusters, err := instances.ToClusterModel()
	if err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	clusters.ClusterType = string(meta.ClusterTypePrimary)

	traceInfo := &meta.TraceInfo{
		TraceEnabled: instances.TraceInfo.TraceEnabled,
		SamplingRate: instances.TraceInfo.SamplingRate,
		Service:      instances.TraceInfo.Service,
		Address:      instances.TraceInfo.Address,
	}
	// 创建服务网格实例
	err = core.InstancesService.NewServiceMeshInstance(ctx, newInstances, clusters, traceInfo)
	if err != nil {
		ctx.CsmLogger().Errorf("create istio cluster error %v", err)
		return err
	}

	if instances.Bls == nil {
		return ctx.JSON(http.StatusOK, true)
	}
	// TODO:后续可以优化到标准创建流程中
	if instances.Bls.Enabled {
		res, err := core.blsService.BlsOpen(ctx, instances.Bls.Instances.Region, uuid, instances.Bls.Instances.Name)
		if !res || err != nil {
			ctx.CsmLogger().Errorf("create Bls Task error %v", err)
		}
	}

	return ctx.JSON(http.StatusOK, true)
}

// validateTraceAddress 验证 traceAddress 是否符合格式，必须包含主机名或 IP 地址和端口号，端口号在 1~65535 之间
// 参数：
//
//	traceAddress (string) - 需要验证的字符串，格式为 "主机名或IP地址:端口号"
//
// 返回值：
//
//	bool - 如果 traceAddress 符合格式，则返回 true；否则返回 false
func validateTraceAddress(traceAddress string) bool {
	hostPortPattern := `^([a-zA-Z0-9.-]*|\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{1,5})$`
	match, _ := regexp.MatchString(hostPortPattern, traceAddress)
	if !match {
		return false
	}
	parts := regexp.MustCompile(":").Split(traceAddress, -1)
	port, err := strconv.Atoi(parts[1])
	if err != nil || port < 1 || port > 65535 {
		return false
	}
	return true
}

// CreateInstance 创建服务网格实例(OpenAPI）
// 当前仅支持一站式，后续将所有创建逻辑迁移到此方法
// TODO: 兼容线上和灰度环境: 线上托管集群为 CCE，灰度托管集群为 EKS
// 目前不支持在创建的时候开始BLS服务
func (core *APIServerCore) CreateInstance(ctx csmContext.CsmContext) error {
	// 绑定数据
	instance := &cnap.Instance{}
	err := request.ConvertAndCheck(ctx, instance)
	if err != nil {
		return err
	}

	// 提取参数
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	// 校验 istio 版本
	ok, err := core.VersionService.CheckSupportedVersion(ctx, instance.IstioVersion, string(meta.StandaloneMeshType))
	if err != nil || !ok {
		return csmErr.NewInvalidParameterInputValueException(
			fmt.Sprintf("unsupported istio version %s", instance.IstioVersion), err)
	}

	// 校验业务逻辑
	// 判断是否为一站式环境, eks 只能用命名空间粒度
	var isCNAP bool
	if sliceutil.StringContains(core.CsmOptions.EksAccountIds, accountId) {
		isCNAP = true
	}

	if isCNAP {
		if !strings.EqualFold(instance.Type, string(cnap.TypeHosting)) {
			return csmErr.NewInvalidParameterInputValueException("mesh instance must be hosting type on eks")
		}
		if !strings.EqualFold(instance.Scope, string(cnap.ScopeNamespace)) {
			return csmErr.NewInvalidParameterInputValueException("mesh instance must be namespace scope on eks")
		}
		if instance.DiscoverySelector == nil || !instance.DiscoverySelector.Enabled ||
			instance.DiscoverySelector.MatchLabels == nil || len(instance.DiscoverySelector.MatchLabels) == 0 {
			return csmErr.NewInvalidParameterInputValueException("need discovery selector on eks")
		}

		// 校验业务是否创建过网格实例，若已创建，返回 mesh instance id
		matchLabels := instance.DiscoverySelector.MatchLabels
		if workspaceId, ok := matchLabels[cnap.BusinessKey]; ok && len(workspaceId) != 0 {
			// 获取该账户已创建网格实例
			instances, err := core.InstancesService.GetAllInstances(ctx)
			if err != nil {
				return err
			}

			for _, i := range instances {
				if len(i.DiscoverySelectorLabels) == 0 {
					continue
				}
				jsonMap := make(map[string]interface{})
				err := json.Unmarshal([]byte(i.DiscoverySelectorLabels), &jsonMap)
				if err != nil {
					return err
				}

				if v, ok := jsonMap[cnap.BusinessKey]; ok && strings.EqualFold(v.(string), workspaceId) {
					return ctx.JSON(http.StatusOK, &cnap.CreateInstanceResponse{
						MeshInstanceId: i.InstanceUUID,
					})
				}
			}
		}

	}

	// 模型转换
	// TODO: 若非 CNAP 请求，该逻辑需要适配
	i := &internal.Instances{}
	i.FromRequest(instance)
	if strings.EqualFold(core.CsmOptions.EksProfile, meta.EksProfileOnline) {
		i.InstallationClusterId = core.CsmOptions.EksProfileOnlineClusterId
		i.InstallationClusterName = core.CsmOptions.EksProfileOnlineClusterName
	} else {
		i.InstallationClusterId = core.CsmOptions.EksProfileGreyClusterId
		i.InstallationClusterName = core.CsmOptions.EksProfileGreyClusterName
	}
	i.Region = ctx.Get(reg.ContextRegion).(string)
	i.AccountId = accountId
	uuid, err := core.InstancesService.GenerateInstancesID(ctx)
	if err != nil {
		return csmErr.NewResourceConflictException("generate mesh instance id conflict", err)
	}
	i.InstancesUUID = uuid
	i.NetworkType = &internal.NetworkType{
		VpcNetworkId: "",
		SubnetId:     "",
	}

	// 一站式实例类型固定为 cnap-hosting
	i.Type = string(meta.CnapMeshType)

	// TODO: 这个转换其实没必要
	newInstances, err := i.ToInstancesModel(ctx)
	if err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// TODO: 这个转换也没必要
	cluster, err := i.ToClusterModel()
	if err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	cluster.ClusterType = string(meta.ClusterTypePrimary)

	traceInfo := meta.TraceInfo{
		TraceEnabled: i.TraceInfo.TraceEnabled,
		SamplingRate: i.TraceInfo.SamplingRate,
		Service:      i.TraceInfo.Service,
		Address:      i.TraceInfo.Address,
	}
	// 创建服务网格实例
	// TODO: 优化创建网格逻辑
	err = core.InstancesService.NewServiceMeshInstance(ctx, newInstances, cluster, &traceInfo)
	if err != nil {
		ctx.CsmLogger().Errorf("create istio cluster error %v", err)
		return err
	}

	return ctx.JSON(http.StatusOK, &cnap.CreateInstanceResponse{
		MeshInstanceId: uuid,
	})
}

// DeleteServiceMeshInstance 删除服务网格实例
func (core *APIServerCore) DeleteServiceMeshInstance(ctx csmContext.CsmContext) (error error) {
	// 校验资源权限
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	// 请求参数校验
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException("'" + constants.InstanceIDPathParam + "'")
	}

	// 平台类型
	paasType := meta.PaaSTypeCCE
	if sliceutil.StringContains(core.CsmOptions.EksAccountIds, accountId) {
		paasType = meta.PaaSTypeEKS
	}

	// 校验托管网格实例下是否存在用户集群
	existRemoteCluster, err := core.ClusterService.ExistRemoteClusters(ctx, instanceUUID)
	if err != nil {
		return err
	}
	if existRemoteCluster {
		return csmErr.NewHostingInstanceAssociationRemoteClusters()
	}
	ctx.CsmLogger().Infof("ExistRemoteClusters check success")

	// 校验托管网格实例下是否存在托管网关
	existGateway, err := core.GatewayService.ExistGatewayWithInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return err
	}
	if existGateway {
		return csmErr.NewHostingInstanceAssociationGateways()
	}
	ctx.CsmLogger().Infof("ExistGatewayWithInstanceUUID check success")

	deleteMeshRequest := internal.ToDeleteMeshRequest(instanceUUID)
	deleteMeshRequest.PaaSType = paasType

	// 删除服务网格实例以及关联的集群
	err = core.InstancesService.DeleteServiceMeshInstance(ctx, deleteMeshRequest)
	if err != nil {
		ctx.CsmLogger().Errorf("remove MeshInstance error %v", err)
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

// ListCceCluster 获取 cce 集群列表中的数据
func (core *APIServerCore) ListCceCluster(ctx csmContext.CsmContext) error {
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	cceCluster, err := core.InstancesService.GetAllCceClusterByRegion(ctx, region)
	if err != nil {
		ctx.CsmLogger().Errorf("ListCceCluster error %v", err)
		return err
	}
	res := make([]*internal.CceCluster, 0)
	for _, cluster := range cceCluster {
		cceClusterView, cceErr := internal.ToMeshCluster(&cluster)
		if cceErr != nil {
			return cceErr
		}
		res = append(res, cceClusterView)
	}
	return ctx.JSON(http.StatusOK, res)
}

// GetServiceMeshInstances 获取所有服务网格实例
func (core *APIServerCore) GetServiceMeshInstances(ctx csmContext.CsmContext) (error error) {
	// 校验资源权限
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}
	mrp := meta.NewCsmMeshRequestParams()
	// 获取请求参数
	if err = ctx.Bind(mrp); err != nil {
		return err
	}
	mrp.AccountID = accountId
	mrp.Region = ctx.Get(reg.ContextRegion).(string)

	response, err := core.InstancesService.GetServiceMeshInstances(ctx, mrp)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, response)
}

// GetServiceMeshInstancesIAM 获取所有服务网格实例，仅供IAM鉴权调用
func (core *APIServerCore) GetServiceMeshInstancesIAM(ctx csmContext.CsmContext) (error error) {
	// 校验资源权限
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}
	mrp := meta.NewCsmMeshRequestParams()
	// 获取请求参数
	if err = ctx.Bind(mrp); err != nil {
		return err
	}
	// iam请求实例接口只传了region和keyword两个参数，并没有筛选效果。并且iam侧逻辑默认拉取都是全量拉取，筛选是在iam界面或者iam侧做的。
	// 示例：region=bj&keyword=111
	// 这里强制将pageSize变为9999
	mrp.PageSize = 9999
	mrp.AccountID = accountId

	response, err := core.InstancesService.GetServiceMeshInstances(ctx, mrp)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, response)
}

// InstanceDetail 查询服务网格实例详情
func (core *APIServerCore) InstanceDetail(ctx csmContext.CsmContext) (error error) {
	//从param中获取InstanceUuid
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	ctx.CsmLogger().Infof("instanceId is %s", instanceUUID)

	//通过instanceUUid获取instanceDetail
	instanceDetail, err := core.InstancesService.GetInstanceDetail(ctx, instanceUUID)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, instanceDetail)
}

// UpdateDiscoverySelector 更新 istiod 选择性标签
func (core *APIServerCore) UpdateDiscoverySelector(ctx csmContext.CsmContext) (err error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if len(instanceUUID) == 0 {
		csmErr.NewInvalidParameterValueExceptionWithoutMsg(fmt.Errorf("instanceUUID is nil"))
	}
	ctx.CsmLogger().Infof("instanceId is %s", instanceUUID)

	// 请求参数校验
	discoverySelector := &internal.DiscoverySelector{}
	if err := ctx.Bind(discoverySelector); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	// 判断 discoverySelector 是否合法
	if discoverySelector.Enabled && len(discoverySelector.MatchLabels) == 0 {
		return fmt.Errorf("matchLabels is nil due to discovery enabled")
	}

	ctx.CsmLogger().Infof("discoverySelector is %v", discoverySelector)
	serviceMetaDiscoverySelector := discoverySelector.ToServiceDiscoverySelector()

	updateError := core.InstancesService.UpdateDiscoverySelector(ctx, instanceUUID, serviceMetaDiscoverySelector)
	if updateError != nil {
		ctx.CsmLogger().Errorf("updateDiscoverySelector error %v", updateError)
		return csmErr.NewUnknownError(updateError)
	}
	return ctx.JSON(http.StatusOK, discoverySelector)
}

// GetDiscoverySelector 获取 istiod 选择性标签
func (core *APIServerCore) GetDiscoverySelector(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if len(instanceUUID) == 0 {
		csmErr.NewInvalidParameterValueExceptionWithoutMsg(fmt.Errorf("instanceUUID is nil"))
	}
	ctx.CsmLogger().Infof("instanceId is %s", instanceUUID)

	// 请求参数校验
	discoverySelector := &internal.DiscoverySelector{}

	selector, err := core.InstancesService.GetDiscoverySelector(ctx, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("getDiscoverySelector error %v", err)
		return csmErr.NewUnknownError(err)
	}
	copier.Copy(discoverySelector, selector)
	return ctx.JSON(http.StatusOK, discoverySelector)
}

func (core *APIServerCore) CreateNamespace(ctx csmContext.CsmContext) error {
	instanceId := ctx.Param(cnap.MeshInstanceIdPathParam)
	namespace := ctx.Param("namespace")

	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	// 校验业务逻辑
	// 判断是否为一站式环境, eks 只能用命名空间粒度
	var isCNAP bool
	if sliceutil.StringContains(core.CsmOptions.EksAccountIds, accountId) {
		isCNAP = true
	}

	if isCNAP {
		err = core.InstancesService.CreateNamespace(ctx, instanceId, namespace)
		if err != nil {
			return err
		}
	}

	return ctx.JSON(http.StatusOK, &cnap.SuccessResponse{
		Success: "true",
	})
}

func (core *APIServerCore) DeleteNamespace(ctx csmContext.CsmContext) error {
	instanceId := ctx.Param(cnap.MeshInstanceIdPathParam)
	namespace := ctx.Param("namespace")

	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	// 校验业务逻辑
	// 判断是否为一站式环境, eks 只能用命名空间粒度
	var isCNAP bool
	if sliceutil.StringContains(core.CsmOptions.EksAccountIds, accountId) {
		isCNAP = true
	}

	if isCNAP {
		err = core.InstancesService.DeleteNamespace(ctx, instanceId, namespace)
		if err != nil {
			return err
		}
	}

	return ctx.JSON(http.StatusOK, &cnap.SuccessResponse{
		Success: "true",
	})
}

// func (core *APIServerCore) GetNamespaceKubeconfig(ctx csmContext.CsmContext) error {
// 	instanceId := ctx.Param(cnap.MeshInstanceIdPathParam)
// 	namespace := ctx.Param("namespace")

// 	// 临时逻辑
// 	user := iam.User{
// 		ID:   "",
// 		Name: "",
// 		Domain: &iam.Domain{
// 			ID:   "0c0b3c9dbb6e41308d3bfd587d908922",
// 			Name: "",
// 		},
// 		Password: "",
// 	}
// 	ctx.Set(iam.ContextIAMUser, &user)

// 	accountId, err := iam.GetAccountId(ctx)
// 	if err != nil {
// 		return err
// 	}

// 	// 校验业务逻辑
// 	// 判断是否为一站式环境, eks 只能用命名空间粒度
// 	var isCNAP bool
// 	if sliceutil.StringContains(core.CsmOptions.EksAccountIds, accountId) {
// 		isCNAP = true
// 	}

// 	if isCNAP {
// 		core.InstancesService.GetNamespaceKubeconfig(ctx, instanceId, namespace)
// 	}

// 	return ctx.JSON(http.StatusOK, &cnap.SuccessResponse{
// 		Success: "true",
// 	})
// }

func (core *APIServerCore) GetInstanceStatus(ctx csmContext.CsmContext) (err error) {
	instanceId := ctx.Param(cnap.MeshInstanceIdPathParam)

	// 适配一站式新需求，只查primary跟指定的remote集群istiod状态
	remoteClusterRegion := ctx.QueryParam(cnap.MeshClusterRegionInQueryParam)
	remoteClusterID := ctx.QueryParam(cnap.MeshClusterIDInQueryParam)
	// 兼容全集群查询
	if remoteClusterRegion != "" {
		remoteClusterRegion, err = internal.AdaptClusterRegionForCNAP(ctx, remoteClusterRegion)
		if err != nil {
			return err
		}
	}

	// TODO: 对于判断 CNAP 的逻辑抽出单独函数进行复用
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	// 校验业务逻辑
	// 判断是否为一站式环境, eks 只能用命名空间粒度
	var isCNAP bool
	if sliceutil.StringContains(core.CsmOptions.EksAccountIds, accountId) {
		isCNAP = true
	}

	status := constants.SmiUnknown
	if isCNAP {
		status, err = core.InstancesService.GetInstanceStatus(ctx, instanceId, remoteClusterID, remoteClusterRegion)
		if err != nil {
			return err
		}
	}

	return ctx.JSON(http.StatusOK, &cnap.InstanceStatusResponse{
		Status: status,
	})
}

func (core *APIServerCore) GetIstioSupportK8sVersion(ctx csmContext.CsmContext) error {
	result := core.InstancesService.GetIstioSupportK8sVersion(ctx)
	return ctx.JSON(http.StatusOK, result)
}
