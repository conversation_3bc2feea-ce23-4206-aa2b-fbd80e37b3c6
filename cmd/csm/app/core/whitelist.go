package core

import (
	"fmt"
	"net/http"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

// IsExistInWhiteList 查看申请用户是否在白名单中
func (core *APIServerCore) IsExistInWhiteList(ctx csmContext.CsmContext) (error error) {
	// 校验资源权限
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}
	mrp := meta.NewCsmMeshRequestParams()
	mrp.AccountID = accountId
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	if len(region) == 0 {
		return csmErr.NewMissingParametersException(fmt.Sprintf("Missing Header %s", constants.RegionHeaderKey))
	}
	mrp.Region = region

	isExist, err := core.WhiteListService.CheckCsmWhiteList(ctx, mrp)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, isExist)
}
