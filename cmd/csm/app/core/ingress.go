package core

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

const (
	IngressIstioVersion = "1.14.6"
	HigressVersion      = "2.0.7"
)

type SuccessResponse struct {
	Success   string `json:"success"`
	ClusterId string `json:"clusterId"`
	Region    string `json:"region"`
	Result    string `json:"result"`
}

// ConsumerInfo 存储从JWT生成的消费者信息
type ConsumerInfo struct {
	Subject    string
	RouteNames []string // 修改为数组以支持多个路由
	JWTToken   string
}

// CreateIngressInstance 函数用于在APIServerCore结构体上创建一个Ingress实例
//
// 参数：
//
//	ctx: 上下文信息，类型为csmContext.CsmContext
//
// 返回值：
//
//	返回error类型，如果操作成功，则返回true；如果操作失败，则返回相应的错误信息
func (core *APIServerCore) CreateIngressInstance(ctx csmContext.CsmContext) (error error) {
	// 获取 accountId
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}
	clusterId := ctx.Param("clusterID")

	ingressParam := &meta.IngressGatewayParams{}
	if err = ctx.Bind(ingressParam); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	// 生成创建网关配置实例
	ingress, err := ingressParam.ToIngressModel()
	if err != nil {
		return err
	}
	clusterName := ingress.ClusterName
	deployMode := ingress.DeployMode
	gatewayType := ingress.GatewayType
	// 请求体参数校验
	if len(clusterName) == 0 || len(deployMode) == 0 || len(gatewayType) == 0 {
		return fmt.Errorf("clusterName, deployMode, gatewayType and ingressType can not be empty")
	}
	// 设置参数值
	ingress.Region = ctx.Get(reg.ContextRegion).(string)
	ingress.ClusterUUID = clusterId
	ingress.AccountId = accountId
	ingress.GatewayName = constants.IstiodGatewayName
	if ingress.IstioVersion == "" {
		ingress.IstioVersion = IngressIstioVersion
	}

	// 生成网关ID
	gatewayUUID, err := core.GatewayService.GenerateGatewayID(ctx)
	if err != nil {
		return err
	}
	// 网关ID和实例ID一致
	ingress.GatewayUUID = gatewayUUID
	ingress.InstanceUUID = gatewayUUID
	// 网关实体默认参数补齐
	ingress.Namespace = constants.IstioNamespace
	ingress.Deleted = csm.Int(0)

	err = core.aiIngressService.CreateIngress(ctx, ingress, ingressParam)
	if err != nil {
		ctx.CsmLogger().Errorf("create istio ingress error %v", err)
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

// DeleteIngressInstance 从APIServerCore中删除Ingress实例
//
// 参数：
//
//	ctx: 上下文信息，类型为csmContext.CsmContext
//
// 返回值：
//
//	返回error类型，如果删除成功，则返回true；如果删除失败，则返回相应的错误信息
func (core *APIServerCore) DeleteIngressInstance(ctx csmContext.CsmContext) (error error) {
	clusterId := ctx.Param("clusterID")
	err := core.aiIngressService.DeleteIngress(ctx, clusterId)
	if err != nil {
		ctx.CsmLogger().Errorf("uninstall istio ingress error %v", err)
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

// GetIngressDetail 函数用于从APIServerCore中获取Ingress的详细信息
//
// 参数：
//
//	ctx: 上下文信息，类型为csmContext.CsmContext
//
// 返回值：
//
//	返回error类型，如果获取成功，则返回详细信息；如果获取失败，则返回相应的错误信息
func (core *APIServerCore) GetIngressDetail(ctx csmContext.CsmContext) (error error) {
	// 获取clusterID
	clusterID := ctx.Param("clusterID")
	ctx.CsmLogger().Infof("clusterID is %s", clusterID)

	//通过clusterID获取instanceDetail
	ingressDetail, err := core.aiIngressService.GetIngressDetail(ctx, clusterID)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, ingressDetail)
}

// GenerateJWTToken
func (core *APIServerCore) GenerateJWTToken(ctx csmContext.CsmContext) (err error) {
	// 获取instanceId和clusterID
	instanceId := ctx.Param("instanceId")
	clusterID := ctx.Param("clusterID")
	ctx.CsmLogger().Infof("开始生成JWT Token，instanceId: %s, clusterID: %s", instanceId, clusterID)

	// 获取 accountId
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("获取accountId失败: %v", err)
		return err
	}
	region := ctx.Get(reg.ContextRegion).(string)

	// 尝试绑定单个请求格式
	reqBody := &internal.JWTGenerateRequest{}
	if err = ctx.Bind(reqBody); err != nil {
		ctx.CsmLogger().Errorf("解析JWT请求失败，尝试解析批量请求: %v", err)
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	ctx.CsmLogger().Infof("接收到JWT生成请求: subject=%s, routeNames=%v, kid=%s, issuer=%s",
		reqBody.Subject, reqBody.RouteNames, reqBody.KID, reqBody.Issuer)

	// 验证参数
	if len(reqBody.KID) == 0 || len(reqBody.Issuer) == 0 || len(reqBody.Subject) == 0 {
		ctx.CsmLogger().Errorf("KID、issuer或subject不能为空")
		return fmt.Errorf("subject, kid and issuer can not be empty")
	}

	if len(reqBody.RouteNames) == 0 {
		ctx.CsmLogger().Errorf("route_names不能为空")
		return fmt.Errorf("route_names can not be empty")
	}

	tokenMap := make(map[string]string)

	param := &meta.TokenParam{
		KID:       reqBody.KID,
		Issuer:    reqBody.Issuer,
		Subject:   reqBody.Subject,
		ClusterID: clusterID,
		AccountID: accountId,
		Region:    region,
	}

	// 生成JWT token
	jwtToken, err := core.aiIngressService.GetJwtToken(ctx, param, tokenMap)
	if err != nil {
		ctx.CsmLogger().Errorf("生成JWT token失败: %v", err)
		return err
	}

	// 获取生成的token
	tokenKey := fmt.Sprintf("%s/%s/%s", param.KID, param.Issuer, param.Subject)
	generatedToken, exists := jwtToken.Token[tokenKey]
	if !exists {
		ctx.CsmLogger().Errorf("未找到生成的token，key: %s", tokenKey)
		return fmt.Errorf("failed to get generated JWT token")
	}

	ctx.CsmLogger().Infof("成功生成JWT token，key: %s", tokenKey)

	// 准备消费者信息
	consumersToCreate := []ConsumerInfo{
		{
			Subject:    reqBody.Subject,
			RouteNames: reqBody.RouteNames,
			JWTToken:   generatedToken,
		},
	}

	ctx.CsmLogger().Infof("准备创建消费者: subject=%s, routeNames=%v", reqBody.Subject, reqBody.RouteNames)

	// 创建消费者
	ctx.CsmLogger().Infof("开始根据JWT信息创建消费者")
	err = core.createConsumersFromJWT(ctx, instanceId, consumersToCreate)
	if err != nil {
		ctx.CsmLogger().Errorf("创建消费者失败: %v", err)
		// 消费者创建失败不影响JWT生成的结果返回
	} else {
		ctx.CsmLogger().Infof("成功创建消费者")
	}

	ctx.CsmLogger().Infof("JWT生成和消费者创建流程完成，返回token结果")
	return ctx.JSON(http.StatusOK, jwtToken)
}

// createConsumersFromJWT 根据JWT信息创建消费者
func (core *APIServerCore) createConsumersFromJWT(ctx csmContext.CsmContext, instanceId string, consumers []ConsumerInfo) error {
	ctx.CsmLogger().Infof("开始批量创建消费者，instanceId: %s, 消费者数量: %d", instanceId, len(consumers))

	if len(consumers) == 0 {
		ctx.CsmLogger().Infof("没有消费者需要创建，直接返回")
		return nil
	}

	// 获取基础信息
	region := ctx.Get(reg.ContextRegion).(string)
	namespace := fmt.Sprintf("istio-system-%s", instanceId)

	// 创建k8s客户端
	hostingClient, err := core.getHostingClient(ctx, region)
	if err != nil {
		return err
	}

	// 获取key-auth插件
	keyAuthPlugin, err := core.getKeyAuthPlugin(ctx, hostingClient, namespace)
	if err != nil {
		return err
	}

	// 解析现有配置
	consumersData, matchRules, err := core.parseKeyAuthConfig(keyAuthPlugin)
	if err != nil {
		return err
	}

	// 处理消费者和路由规则
	for _, consumerInfo := range consumers {
		ctx.CsmLogger().Infof("处理消费者: subject=%s, routeNames=%v", consumerInfo.Subject, consumerInfo.RouteNames)

		// 创建或更新消费者
		if err := core.upsertConsumer(ctx, &consumersData, consumerInfo); err != nil {
			ctx.CsmLogger().Errorf("处理消费者 %s 失败: %v", consumerInfo.Subject, err)
			continue
		}

		// 更新路由规则
		core.updateRouteRules(ctx, &matchRules, consumerInfo)

		// 设置配额信息
		err := core.setConsumerQuota(ctx, instanceId, consumerInfo.Subject, true, nil)
		if err != nil {
			ctx.CsmLogger().Errorf("设置消费者配额失败: %v", err)
			return err
		}
	}

	// 更新插件配置
	return core.updateKeyAuthPlugin(ctx, hostingClient, namespace, keyAuthPlugin, consumersData, matchRules)
}

// getHostingClient 获取hosting集群客户端
func (core *APIServerCore) getHostingClient(ctx csmContext.CsmContext, region string) (kube.Client, error) {
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return nil, csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return nil, err
	}

	return hostingClient, nil
}

// getKeyAuthPlugin 获取key-auth插件
func (core *APIServerCore) getKeyAuthPlugin(ctx csmContext.CsmContext, client kube.Client, namespace string) (*unstructured.Unstructured, error) {
	ctx.CsmLogger().Infof("获取key-auth插件，命名空间: %s", namespace)

	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	dynamicClient := client.Dynamic()
	unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: fmt.Sprintf("metadata.name=%s", constants.GetKeyAuthPluginName()),
	})
	if err != nil {
		ctx.CsmLogger().Errorf("查询WasmPlugins失败: %v", err)
	}
	ctx.CsmLogger().Infof("找到key-auth插件")
	return &unstructuredList.Items[0], nil
}

// parseKeyAuthConfig 解析key-auth插件配置
func (core *APIServerCore) parseKeyAuthConfig(keyAuthPlugin *unstructured.Unstructured) ([]interface{}, []interface{}, error) {
	spec, exists, _ := unstructured.NestedMap(keyAuthPlugin.Object, "spec")
	if !exists {
		return nil, nil, fmt.Errorf("key-auth插件缺少spec字段")
	}

	defaultConfig, exists, _ := unstructured.NestedMap(spec, "defaultConfig")
	if !exists {
		return nil, nil, fmt.Errorf("key-auth插件缺少defaultConfig字段")
	}

	// 获取现有consumers
	consumersData, exists, _ := unstructured.NestedSlice(defaultConfig, "consumers")
	if !exists {
		consumersData = []interface{}{}
	}

	// 确保虚拟消费者存在
	consumersData = core.ensureVirtualConsumerExists(consumersData)

	// 获取现有matchRules，保持原始结构
	matchRules, exists, _ := unstructured.NestedSlice(spec, "matchRules")
	if !exists {
		matchRules = []interface{}{}
	}

	return consumersData, matchRules, nil
}

// upsertConsumer 创建或更新消费者
func (core *APIServerCore) upsertConsumer(ctx csmContext.CsmContext, consumersData *[]interface{}, consumerInfo ConsumerInfo) error {
	// 查找现有消费者
	for j, consumerItem := range *consumersData {
		consumer, ok := consumerItem.(map[string]interface{})
		if !ok {
			continue
		}

		consumerName, _, _ := unstructured.NestedString(consumer, "name")
		if consumerName == consumerInfo.Subject {
			ctx.CsmLogger().Infof("消费者 %s 已存在，更新credential", consumerInfo.Subject)
			consumer["credential"] = consumerInfo.JWTToken
			(*consumersData)[j] = consumer
			return nil
		}
	}

	// 创建新消费者
	ctx.CsmLogger().Infof("创建新消费者: %s", consumerInfo.Subject)

	currentTime := time.Now().In(time.FixedZone("CST", 8*60*60))
	createTime := currentTime.Format("2006-01-02 15:04:05")

	// 生成消费者ID
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	randomId := fmt.Sprintf("%06d", time.Now().UnixNano()%1000000)
	consumerId := fmt.Sprintf("consumer-%s-%s", timestamp, randomId)

	newConsumer := map[string]interface{}{
		"credential":     consumerInfo.JWTToken,
		"name":           consumerInfo.Subject,
		"id":             consumerId,
		"description":    fmt.Sprintf("由JWT生成自动创建的消费者"),
		"authType":       "JWT",
		"createTime":     createTime,
		"unlimitedQuota": true,
	}

	*consumersData = append(*consumersData, newConsumer)
	ctx.CsmLogger().Infof("成功创建消费者 %s", consumerInfo.Subject)
	return nil
}

// updateRouteRules 更新路由规则
func (core *APIServerCore) updateRouteRules(ctx csmContext.CsmContext, matchRules *[]interface{}, consumerInfo ConsumerInfo) {
	for _, routeName := range consumerInfo.RouteNames {
		ctx.CsmLogger().Infof("处理路由规则: 消费者=%s, 路由=%s", consumerInfo.Subject, routeName)

		// 查找包含该routeName的现有规则
		ruleFound := false
		for i, ruleItem := range *matchRules {
			rule, ok := ruleItem.(map[string]interface{})
			if !ok {
				continue
			}

			// 检查该规则是否包含目标routeName
			if core.ruleContainsRoute(rule, routeName) {
				ctx.CsmLogger().Infof("找到包含路由 %s 的现有规则，更新allow列表", routeName)
				core.addConsumerToRule(ctx, rule, consumerInfo.Subject, routeName)
				(*matchRules)[i] = rule // 更新规则
				ruleFound = true
				break
			}
		}

		// 如果没有找到包含该routeName的规则，创建新规则
		if !ruleFound {
			newRule := map[string]interface{}{
				"config": map[string]interface{}{
					"allow": []interface{}{consumerInfo.Subject},
				},
				"configDisable": false,
				"ingress":       []interface{}{routeName},
			}
			*matchRules = append(*matchRules, newRule)
			ctx.CsmLogger().Infof("为路由 %s 创建新规则，添加消费者 %s", routeName, consumerInfo.Subject)
		}
	}
}

// ruleContainsRoute 检查规则是否包含指定的路由
func (core *APIServerCore) ruleContainsRoute(rule map[string]interface{}, targetRoute string) bool {
	ingressList, exists, _ := unstructured.NestedSlice(rule, "ingress")
	if !exists {
		return false
	}

	for _, ingressItem := range ingressList {
		if routeName, ok := ingressItem.(string); ok && routeName == targetRoute {
			return true
		}
	}
	return false
}

// addConsumerToRule 将消费者添加到规则的allow列表
func (core *APIServerCore) addConsumerToRule(ctx csmContext.CsmContext, rule map[string]interface{}, consumerName, routeName string) {
	configMap, exists, _ := unstructured.NestedMap(rule, "config")
	if !exists {
		configMap = map[string]interface{}{}
	}

	allowList, exists, _ := unstructured.NestedSlice(configMap, "allow")
	if !exists {
		allowList = []interface{}{}
	}

	// 检查消费者是否已在allow列表中
	for _, allowItem := range allowList {
		if existingConsumer, ok := allowItem.(string); ok && existingConsumer == consumerName {
			ctx.CsmLogger().Infof("消费者 %s 已在路由 %s 的allow列表中", consumerName, routeName)
			return
		}
	}

	// 添加消费者到allow列表
	allowList = append(allowList, consumerName)
	_ = unstructured.SetNestedSlice(configMap, allowList, "allow")
	_ = unstructured.SetNestedMap(rule, configMap, "config")
	ctx.CsmLogger().Infof("成功将消费者 %s 添加到路由 %s 的allow列表", consumerName, routeName)
}

// updateKeyAuthPlugin 更新key-auth插件配置
func (core *APIServerCore) updateKeyAuthPlugin(ctx csmContext.CsmContext,
	client kube.Client, namespace string,
	keyAuthPlugin *unstructured.Unstructured,
	consumersData, matchRules []interface{}) error {
	ctx.CsmLogger().Infof("开始更新key-auth插件配置")

	// 更新consumers列表
	if err := unstructured.SetNestedSlice(keyAuthPlugin.Object, consumersData, "spec", "defaultConfig", "consumers"); err != nil {
		ctx.CsmLogger().Errorf("设置consumers失败: %v", err)
		return err
	}

	// 更新matchRules
	if err := unstructured.SetNestedSlice(keyAuthPlugin.Object, matchRules, "spec", "matchRules"); err != nil {
		ctx.CsmLogger().Errorf("设置matchRules失败: %v", err)
		return err
	}

	// 更新插件
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	dynamicClient := client.Dynamic()
	_, err := dynamicClient.Resource(groupVersionResource).Namespace(namespace).Update(context.TODO(), keyAuthPlugin, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("更新key-auth插件失败: %v", err)
		return err
	}

	ctx.CsmLogger().Infof("成功更新key-auth插件配置")
	return nil
}

// CreateCrdsForBG 创建CRD
// 参数:
//
//	ctx: 上下文对象，包含请求信息和参数
//
// 返回值:
//
//	若操作成功，返回response；否则返回错误信息
func (core *APIServerCore) CreateCrdsForBG(ctx csmContext.CsmContext) (err error) {
	// 获取clusterID
	clusterID := ctx.Param("clusterId")
	region := ctx.Get(reg.ContextRegion).(string)
	// 获取请求参数
	ctx.CsmLogger().Infof("ctx is %s", ctx)
	reqBody := &internal.CrdView{}
	if err = ctx.Bind(reqBody); err != nil {
		ctx.CsmLogger().Errorf("Binding content err is %v", err)
		return errors.New("Binding content which is in body failed.")
	}
	param := &meta.CrdParam{
		ClusterID: clusterID,
		Region:    region,
		Content:   reqBody.Content,
	}
	ctx.CsmLogger().Infof("param is %v", param)
	// 创建CRD资源
	crd, err := core.aiIngressService.CreateCrdForBG(ctx, param)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, SuccessResponse{
		Success:   "true",
		ClusterId: clusterID,
		Region:    region,
		Result:    crd.Content,
	})
}

// DeleteCrdsForBG 删除CRD
//
// ctx: 上下文对象，包含请求信息和参数
//
// 返回值:
// 如果操作成功，返回response；否则返回错误信息
func (core *APIServerCore) DeleteCrdsForBG(ctx csmContext.CsmContext) (err error) {
	clusterId := ctx.Param("clusterId")
	region := ctx.Get(reg.ContextRegion).(string)
	namespace := ctx.QueryParam("namespace")
	kind := ctx.QueryParam("kind")
	name := ctx.QueryParam("name")
	if len(namespace) == 0 || len(kind) == 0 || len(name) == 0 {
		return errors.New("namespace, kind, name must not be empty")
	}
	crdKey := meta.CrdKey{
		Namespace: namespace,
		Kind:      kind,
		Name:      name,
	}
	param := &meta.CrdParam{
		Region:    region,
		CrdKey:    crdKey,
		ClusterID: clusterId,
	}
	err = core.aiIngressService.DeleteCrdForBG(ctx, param)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, SuccessResponse{
		Success:   "true",
		ClusterId: clusterId,
		Region:    region,
		Result:    "delete crd success",
	})
}

// GetCrdsForBG
// 参数:
//
//	ctx: 上下文对象，包含请求信息和参数
//
// 返回值:
//
//	若操作成功，返回response；否则返回错误信息
func (core *APIServerCore) GetCrdsForBG(ctx csmContext.CsmContext) (err error) {
	// 获取参数
	region := ctx.Get(reg.ContextRegion).(string)
	clusterId := ctx.Param("clusterId")

	param := &meta.CrdParam{
		ClusterID: clusterId,
		Region:    region,
		CrdKey: meta.CrdKey{
			Namespace: ctx.QueryParam("namespace"),
			Kind:      ctx.QueryParam("kind"),
			Name:      ctx.QueryParam("name"),
		},
	}
	ctx.CsmLogger().Infof("=========param is %v", param)
	crd, err := core.aiIngressService.GetCrd(ctx, param)
	if err != nil {
		csmlog.Warnf("GetCrd error %v", err)
		return err
	}
	ctx.CsmLogger().Infof("crd is %v", crd)
	crdView := &internal.CrdView{}
	err = crdView.FromModel(crd)
	if err != nil {
		csmlog.Warnf("FromModel error %v", err)
		return err
	}
	ctx.CsmLogger().Infof("crdView is %v", crdView)
	return ctx.JSON(http.StatusOK, crdView)
}

// UpdateCrdsForBG
// 参数:
//
//	ctx: 上下文对象，包含请求信息和参数
//
// 返回值:
//
//	若操作成功，返回response；否则返回错误信息
func (core *APIServerCore) UpdateCrdsForBG(ctx csmContext.CsmContext) (err error) {
	// 获取参数
	region := ctx.Get(reg.ContextRegion).(string)
	clusterId := ctx.Param("clusterId")

	reqBody := &internal.CrdView{}
	if err = ctx.Bind(reqBody); err != nil {
		return errors.New("Binding content which is in body failed.")
	}

	param := &meta.CrdParam{
		ClusterID: clusterId,
		Region:    region,
		CrdKey: meta.CrdKey{
			Namespace: ctx.QueryParam("namespace"),
			Kind:      ctx.QueryParam("kind"),
			Name:      ctx.QueryParam("name"),
		},
		Content: reqBody.Content,
	}
	crd, err := core.aiIngressService.UpdateCrd(ctx, param)
	if err != nil {
		return err
	}
	crdView := &internal.CrdView{}
	err = crdView.FromModel(crd)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, crdView)
}
