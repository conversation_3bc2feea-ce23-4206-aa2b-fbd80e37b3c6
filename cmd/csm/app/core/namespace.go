package core

import (
	"fmt"
	"net/http"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

// GetNamespaceList 获取namespace列表
func (core *APIServerCore) GetNamespaceList(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	namespaceListRequest := meta.NewRequestParams()
	if err := ctx.Bind(namespaceListRequest); err != nil {
		return err
	}
	namespaceList, error := core.NamespaceService.GetNamespaceList(ctx, instanceUUID, namespaceListRequest)
	return ctx.JSON(http.StatusOK, namespaceList)
}

// GetKubeConfig 获取托管网格实例对应的命名空间
func (core *APIServerCore) GetKubeConfig(ctx csmContext.CsmContext) error {
	instanceId := ctx.Param(constants.InstanceIDPathParam)
	if len(instanceId) == 0 {
		return csmErr.NewInvalidParameterValueException(fmt.Sprintf("invalid instanceId %s", instanceId))
	}
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)

	kubeConfigType := ctx.QueryParam(constants.KubeConfigType)

	kubeConfigResult, err := core.NamespaceService.GetKubeConfig(ctx, instanceId, region, kubeConfigType)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, kubeConfigResult)
}
