package core

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	namespaceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/namespace/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

func TestGetKubeConfig(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name string
	}{
		{
			name: "success",
		},
	}
	for _, testInfo := range testInfos {

		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set(User, iamUser)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam)
			mockCtx.SetParamValues("inst-1")
			mockOverviewService := namespaceMock.NewMockServiceInterface(ctrl)
			core := &APIServerCore{
				CsmOptions:       options.NewCsmServerOption(),
				NamespaceService: mockOverviewService,
			}
			mockOverviewService.EXPECT().GetKubeConfig(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil, nil)

			err := core.GetKubeConfig(mockCtx)

			assert.Nil(t, err)

		})
	}
}
