package core

import (
	"net/http"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

func (core *APIServerCore) GetScale(ctx csmContext.CsmContext) error {
	region := ctx.QueryParam(constants.InstanceRegionField)
	names := core.UserNames
	response, err := core.sugarService.GetScale(ctx, region, names)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, response)
}

func (core *APIServerCore) GetUserList(ctx csmContext.CsmContext) error {
	response, err := core.sugarService.GetUserList(ctx)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, response)
}
