package core

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	laneMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/lane/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

func TestNewLaneGroup(t *testing.T) {
	testCases := []struct {
		name           string
		body           meta.LaneGroupParams
		success        bool
		expectedErrMsg string
	}{
		{
			name: "success",
			body: meta.LaneGroupParams{
				GroupName: "groupName",
			},
			success: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jsonBody, err := json.Marshal(tc.body)
			if err != nil {
				t.FailNow()
			}

			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().NewLaneGroup(ctx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err = core.NewLaneGroup(ctx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestNewLane(t *testing.T) {
	testCases := []struct {
		name           string
		body           meta.LaneParams
		success        bool
		expectedErrMsg string
	}{
		{
			name: "success",
			body: meta.LaneParams{
				LaneName: "name",
			},
			success: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jsonBody, err := json.Marshal(tc.body)
			if err != nil {
				t.FailNow()
			}

			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().NewLane(ctx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err = core.NewLane(ctx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestModifyLane(t *testing.T) {
	testCases := []struct {
		name           string
		body           meta.LaneParams
		success        bool
		expectedErrMsg string
	}{
		{
			name: "success",
			body: meta.LaneParams{
				LaneName: "name",
			},
			success: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jsonBody, err := json.Marshal(tc.body)
			if err != nil {
				t.FailNow()
			}

			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")
			c.SetParamNames("laneID")
			c.SetParamValues("test-laneID")
			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().ModifyLane(ctx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err = core.NewLane(ctx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestGetLaneGroups(t *testing.T) {
	testCases := []struct {
		name            string
		laneGroupParams []meta.LaneGroupParams
		success         bool
		expectedErrMsg  string
	}{
		{
			name:            "success",
			laneGroupParams: []meta.LaneGroupParams{*buildLaneGroupParams()},
			success:         true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().GetLaneGroups(ctx, gomock.Any()).Return(nil, nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err := core.GetLaneGroups(ctx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func buildLaneGroupParams() *meta.LaneGroupParams {
	serviceList := []meta.ServiceListParams{
		{
			ClusterRegion: "region1",
			ClusterName:   "cluster1",
			ClusterID:     "id1",
			Namespace:     "namespace1",
			ServiceName:   "service1",
		},
		// 添加更多服务，如果需要
	}

	laneParams := meta.LaneParams{
		InstanceUUID:       "instance-uuid",
		GroupID:            "group-id",
		LaneID:             "lane-id",
		LaneName:           "lane-name",
		LabelSelectorKey:   "selector-key",
		LabelSelectorValue: "selector-value",
		ServiceList:        serviceList,
		IsBase:             true,
	}

	return &meta.LaneGroupParams{
		InstanceUUID: "instance-uuid",
		GroupID:      "group-id",
		GroupName:    "group-name",
		TraceHeader:  "x-trace-header",
		RouteHeader:  "x-route-header",
		ServiceList:  serviceList,
		BaseLane:     laneParams,
	}
}

func TestDeleteLaneGroup(t *testing.T) {
	testCases := []struct {
		name           string
		success        bool
		expectedErrMsg string
	}{
		{
			name:    "success",
			success: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			mockCtx := csmContext.NewCsmContext(c)
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.LaneGroupIDParam)
			mockCtx.SetParamValues("inst-1", "group-1")

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().DeleteLaneGroupByID(mockCtx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err := core.DeleteLaneGroup(mockCtx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestDeleteLane(t *testing.T) {
	testCases := []struct {
		name           string
		success        bool
		expectedErrMsg string
	}{
		{
			name:    "success",
			success: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			mockCtx := csmContext.NewCsmContext(c)
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.LaneGroupIDParam, constants.LaneIDParam)
			mockCtx.SetParamValues("inst-1", "group-1", "lane-1")

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().DeleteLane(mockCtx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err := core.DeleteLane(mockCtx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestModifyBaseLane(t *testing.T) {
	testCases := []struct {
		name           string
		success        bool
		expectedErrMsg string
	}{
		{
			name:    "success",
			success: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			mockCtx := csmContext.NewCsmContext(c)
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.LaneGroupIDParam, constants.LaneIDParam)
			mockCtx.SetParamValues("inst-1", "group-1", "lane-1")

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().ModifyBaseLane(mockCtx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err := core.ModifyBaseLane(mockCtx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestGetLanes(t *testing.T) {
	testCases := []struct {
		name           string
		success        bool
		expectedErrMsg string
	}{
		{
			name:    "success",
			success: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			mockCtx := csmContext.NewCsmContext(c)
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.LaneGroupIDParam, constants.LaneIDParam)
			mockCtx.SetParamValues("inst-1", "group-1", "lane-1")

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().GetLanes(mockCtx, gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err := core.GetLanes(mockCtx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestNewRouteRule(t *testing.T) {
	testCases := []struct {
		name           string
		success        bool
		expectedErrMsg string
	}{
		{
			name:    "success",
			success: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			mockCtx := csmContext.NewCsmContext(c)
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.LaneGroupIDParam, constants.LaneIDParam)
			mockCtx.SetParamValues("inst-1", "group-1", "lane-1")

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().NewRoute(mockCtx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err := core.NewRouteRule(mockCtx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestModifyRouteRule(t *testing.T) {
	testCases := []struct {
		name           string
		success        bool
		expectedErrMsg string
	}{
		{
			name:    "success",
			success: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			mockCtx := csmContext.NewCsmContext(c)
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.LaneGroupIDParam, constants.LaneIDParam)
			mockCtx.SetParamValues("inst-1", "group-1", "lane-1")

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().ModifyRoute(mockCtx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err := core.ModifyRouteRule(mockCtx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestGetRouteRule(t *testing.T) {
	testCases := []struct {
		name           string
		success        bool
		expectedErrMsg string
	}{
		{
			name:    "success",
			success: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			mockCtx := csmContext.NewCsmContext(c)
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.LaneGroupIDParam, constants.LaneIDParam)
			mockCtx.SetParamValues("inst-1", "group-1", "lane-1")

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().GetRouteRules(mockCtx, gomock.Any()).Return(nil, nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err := core.GetRouteRule(mockCtx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestDeleteRouteRule(t *testing.T) {
	testCases := []struct {
		name           string
		success        bool
		expectedErrMsg string
	}{
		{
			name:    "success",
			success: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			mockCtx := csmContext.NewCsmContext(c)
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.LaneGroupIDParam, constants.LaneIDParam)
			mockCtx.SetParamValues("inst-1", "group-1", "lane-1")

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().DeleteRoute(mockCtx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err := core.DeleteRouteRule(mockCtx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestGetLabelSelectorSet(t *testing.T) {
	testCases := []struct {
		name           string
		labelSet       map[string][]string
		success        bool
		expectedErrMsg string
	}{
		{
			name: "success",
			labelSet: map[string][]string{
				"key1": []string{"value"},
			},
			success: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			mockCtx := csmContext.NewCsmContext(c)
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.LaneGroupIDParam)
			mockCtx.SetParamValues("inst-1", "group-1")

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().GetLabelSelectorSet(mockCtx, gomock.Any()).Return(tc.labelSet, nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err := core.GetLabelSelectorSet(mockCtx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestGetLaneServiceList(t *testing.T) {
	testCases := []struct {
		name           string
		success        bool
		expectedErrMsg string
	}{
		{
			name:    "success",
			success: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			mockCtx := csmContext.NewCsmContext(c)
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.LaneGroupIDParam, constants.LaneIDParam)
			mockCtx.SetParamValues("inst-1", "group-1", "lane-1")

			ctrl := gomock.NewController(t)
			mockLaneService := laneMock.NewMockServiceInterface(ctrl)
			mockLaneService.EXPECT().GetServiceList(mockCtx, gomock.Any()).Return(nil, nil).AnyTimes()

			core := &APIServerCore{
				laneService: mockLaneService,
			}

			err := core.GetLaneServiceList(mockCtx)
			if tc.success {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}
