package core

import (
	"fmt"
	"net/http"
	"strings"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

func (core *APIServerCore) GetMonitorInstances(ctx context.CsmContext) (err error) {
	clusterID := ctx.QueryParam(constants.ClusterId)
	// 兼容托管网格
	instanceID := ctx.QueryParam(constants.InstanceId)

	monitorInstances, err := core.MonitorService.GetMonitorInstances(ctx, clusterID, instanceID)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, monitorInstances)
}

func (core *APIServerCore) GetMonitorInstanceDetail(ctx context.CsmContext) (err error) {
	// 请求参数校验
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewMissingParametersException(constants.InstanceIDPathParam)
	}

	// 获取网格实例关联的监控详情
	monitorInstances, err := core.MonitorService.GetMonitorInstanceDetail(ctx, instanceUUID)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, monitorInstances)
}

func (core *APIServerCore) UpdateMonitor(ctx context.CsmContext) error {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if len(instanceUUID) == 0 {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(fmt.Errorf("instanceUUID is nil"))
	}
	ctx.CsmLogger().Infof("instanceId is %s", instanceUUID)

	// 请求参数校验
	monitor := &vo.MonitorInstances{}
	if err := ctx.Bind(monitor); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 开启监控但是没有实例，请求参数不对
	if monitor.Enabled && len(monitor.Instances) == 0 {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(fmt.Errorf("monitor instances is nil"))
	}

	updated, err := core.MonitorService.UpdateMonitor(ctx, instanceUUID, monitor)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			info := "CProm Agent not installed"
			ctx.CsmLogger().Warnf(info)
			return csmErr.NewCPromAgentNotInstallException(err)
		}
		ctx.CsmLogger().Errorf("updateMonitor error %v", err)
		return csmErr.NewUnknownError(err)
	}
	return ctx.JSON(http.StatusOK, updated)
}

func (core *APIServerCore) ClusterCPromAgentCheck(ctx context.CsmContext) (err error) {
	cpromInstanceID := ctx.QueryParam("cpromInstanceID")
	if cpromInstanceID == "" {
		return csmErr.NewMissingParametersException("cpromInstanceID")
	}

	clusterID := ctx.QueryParam(constants.ClusterId)
	if clusterID == "" {
		return csmErr.NewMissingParametersException(constants.ClusterId)
	}

	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	if region == "" {
		return csmErr.NewMissingParametersException(constants.InstanceRegionField)
	}

	res := core.MonitorService.ClusterCPromAgentCheck(ctx, region, cpromInstanceID, clusterID)

	return ctx.JSON(http.StatusOK, res)
}
