package core

import (
	"net/http"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

// NewLaneGroup 新建泳道组，并创建基准泳道
func (core *APIServerCore) NewLaneGroup(ctx context.CsmContext) (err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}
	laneGroup := &meta.LaneGroupParams{}
	if err = ctx.Bind(laneGroup); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	// 直接将前后端交互的结构传递给service层处理，后续需要判断泳道中的服务是否在泳道组中。
	err = core.laneService.NewLaneGroup(ctx, laneGroup, accountId)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

// GetLaneGroups 获取泳道组列表
func (core *APIServerCore) GetLaneGroups(ctx context.CsmContext) (err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	// 直接将前后端交互的结构传递给service层处理，后续需要判断泳道中的服务是否在泳道组中。
	laneGroupParams, err := core.laneService.GetLaneGroups(ctx, accountId)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, laneGroupParams)
}

// DeleteLaneGroup 删除泳道组，并删除其下所有泳道
func (core *APIServerCore) DeleteLaneGroup(ctx context.CsmContext) (err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return csmErr.NewInvalidParameterInputValueException("instanceUUID is invalid")
	}
	laneGroupID := ctx.Param(constants.LaneGroupIDParam)
	if laneGroupID == "" {
		return csmErr.NewInvalidParameterInputValueException("laneGroupID is invalid")
	}
	laneGroup := &meta.LaneGroupParams{
		InstanceUUID: instanceUUID,
		GroupID:      laneGroupID,
	}
	err = core.laneService.DeleteLaneGroupByID(ctx, laneGroup, accountId)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

// GetLabelSelectorSet 根据body中的服务列表获取可供选择的服务标签
func (core *APIServerCore) GetLabelSelectorSet(ctx context.CsmContext) (err error) {
	laneGroup := &meta.LaneGroupParams{}
	if err = ctx.Bind(laneGroup); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 直接将前后端交互的结构传递给service层处理，后续需要判断泳道中的服务是否在泳道组中。
	labelSet, err := core.laneService.GetLabelSelectorSet(ctx, laneGroup)
	if err != nil {
		return err
	}
	labels := make([]internal.Label, 0)

	for key, value := range labelSet {
		label := internal.Label{
			Key:       key,
			ValueList: value,
		}
		labels = append(labels, label)
	}
	result := internal.LabelSetResponseParam{
		Result: labels,
	}

	return ctx.JSON(http.StatusOK, result)
}

// NewLane 新建泳道
func (core *APIServerCore) NewLane(ctx context.CsmContext) (err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	laneParam := &meta.LaneParams{}
	if err = ctx.Bind(laneParam); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	err = core.laneService.NewLane(ctx, laneParam, accountId)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

// ModifyLane 修改泳道
func (core *APIServerCore) ModifyLane(ctx context.CsmContext) (err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	laneParam := &meta.LaneParams{}
	if err = ctx.Bind(laneParam); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	laneID := ctx.Param(constants.LaneIDParam)
	if laneID == "" {
		return csmErr.NewInvalidParameterInputValueException("laneID is empty")
	}
	laneParam.LaneID = laneID

	if laneParam.InstanceUUID == "" || laneParam.GroupID == "" {
		return csmErr.NewInvalidParameterInputValueException("InstanceUUID or GroupID is empty")
	}

	err = core.laneService.ModifyLane(ctx, laneParam, accountId)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

// ModifyBaseLane 修改泳道
func (core *APIServerCore) ModifyBaseLane(ctx context.CsmContext) (err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	laneParam := &meta.LaneParams{}
	if err = ctx.Bind(laneParam); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	laneID := ctx.Param(constants.LaneIDParam)
	if laneID == "" {
		return csmErr.NewInvalidParameterInputValueException("laneID is empty")
	}
	laneParam.LaneID = laneID

	if laneParam.InstanceUUID == "" || laneParam.GroupID == "" {
		return csmErr.NewInvalidParameterInputValueException("InstanceUUID or GroupID is empty")
	}

	err = core.laneService.ModifyBaseLane(ctx, laneParam, accountId)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

// DeleteLane 修改泳道
func (core *APIServerCore) DeleteLane(ctx context.CsmContext) (err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	laneParam := &meta.LaneParams{}
	if err = ctx.Bind(laneParam); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	laneID := ctx.Param(constants.LaneIDParam)
	if laneID == "" {
		return csmErr.NewInvalidParameterInputValueException("laneID is empty")
	}
	laneParam.LaneID = laneID
	if laneParam.InstanceUUID == "" || laneParam.GroupID == "" {
		return csmErr.NewInvalidParameterInputValueException("InstanceUUID or GroupID is empty")
	}

	err = core.laneService.DeleteLane(ctx, laneParam, accountId)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

func (core *APIServerCore) GetLanes(ctx context.CsmContext) (err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	laneParam := &meta.LaneParams{}
	if err = ctx.Bind(laneParam); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	if laneParam.InstanceUUID == "" || laneParam.GroupID == "" {
		return csmErr.NewInvalidParameterInputValueException("InstanceUUID or GroupID is empty")
	}
	result, err := core.laneService.GetLanes(ctx, laneParam, accountId)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, result)
}

// NewRouteRule 新建引流规则
func (core *APIServerCore) NewRouteRule(ctx context.CsmContext) (err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	routeParam := &meta.RouteParams{}
	if err = ctx.Bind(routeParam); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	if routeParam.InstanceUUID == "" || routeParam.GroupID == "" || routeParam.LaneID == "" {
		return csmErr.NewInvalidParameterInputValueException("InstanceUUID, LaneID or GroupID is empty")
	}

	err = core.laneService.NewRoute(ctx, routeParam, accountId)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

func (core *APIServerCore) ModifyRouteRule(ctx context.CsmContext) (err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	routeParam := &meta.RouteParams{}
	if err = ctx.Bind(routeParam); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	if routeParam.InstanceUUID == "" || routeParam.GroupID == "" || routeParam.LaneID == "" {
		return csmErr.NewInvalidParameterInputValueException("InstanceUUID, LaneID or GroupID is empty")
	}

	err = core.laneService.ModifyRoute(ctx, routeParam, accountId)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

func (core *APIServerCore) GetRouteRule(ctx context.CsmContext) (err error) {
	routeParam := &meta.RouteParams{}
	if err = ctx.Bind(routeParam); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	if routeParam.InstanceUUID == "" || routeParam.GroupID == "" || routeParam.LaneID == "" {
		return csmErr.NewInvalidParameterInputValueException("InstanceUUID, LaneID or GroupID is empty")
	}

	result, err := core.laneService.GetRouteRules(ctx, routeParam)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, result)
}

func (core *APIServerCore) DeleteRouteRule(ctx context.CsmContext) (err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	routeParam := &meta.RouteParams{}
	if err = ctx.Bind(routeParam); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	if routeParam.InstanceUUID == "" || routeParam.GroupID == "" || routeParam.LaneID == "" {
		return csmErr.NewInvalidParameterInputValueException("InstanceUUID, LaneID or GroupID is empty")
	}

	err = core.laneService.DeleteRoute(ctx, routeParam, accountId)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

func (core *APIServerCore) GetLaneServiceList(ctx context.CsmContext) (err error) {
	// 支持指定cluster和namespace筛选服务
	mrp := &meta.LaneGroupServiceListParams{}
	if err := ctx.Bind(mrp); err != nil {
		return err
	}
	res, err := core.laneService.GetServiceList(ctx, mrp)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, res)
}
