package core

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/api/v1/cnap"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	crdMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/crd/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/ptrutil"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

var (
	time1, _ = time.Parse("2006-01-02 15:04:05", "2022-09-29 14:24:00")

	crds = []meta.Crd{{
		Namespace: "ns1",
		Name:      "crd-name-1",
		Kind:      "VirtualService",
		UpdatedAt: time1,
		Content:   "test content",
	}}
)

func TestGetCrdsForOpenAPI(t *testing.T) {
	type MockResult struct {
		crds    []meta.Crd
		pr      *meta.PageResult
		mockErr error
	}
	type PathParam struct {
		meshInstanceId string
	}
	type QueryParam struct {
		namespace string
		kind      string
		name      string
		labels    string
	}

	testCases := []struct {
		name           string
		pathParams     *PathParam
		queryParams    *QueryParam
		mr             *MockResult
		expected       []internal.CrdView
		expectedErrMsg *string
		user           *sdkIAM.User
	}{
		{
			name: "get crds success",
			pathParams: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			queryParams: &QueryParam{
				namespace: "ns1",
				kind:      "VirtualService",
				name:      "crd-name-1",
				labels:    "",
			},
			mr: &MockResult{
				crds:    crds,
				pr:      &meta.PageResult{},
				mockErr: nil,
			},
			expected: []internal.CrdView{{
				Name:      "crd-name-1",
				Namespace: "ns1",
				Kind:      "VirtualService",
				UpdatedAt: "2022-09-29 14:24:00",
				Content:   "test content",
			}},
			expectedErrMsg: nil,
			user:           &user1,
		},
		{
			name: "get crds with labels",
			pathParams: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			queryParams: &QueryParam{
				namespace: "ns1",
				kind:      "VirtualService",
				name:      "crd-name-1",
				labels:    "foo=bar",
			},
			mr: &MockResult{
				crds:    crds,
				pr:      &meta.PageResult{},
				mockErr: nil,
			},
			expected: []internal.CrdView{{
				Name:      "crd-name-1",
				Namespace: "ns1",
				Kind:      "VirtualService",
				UpdatedAt: "2022-09-29 14:24:00",
				Content:   "test content",
			}},
			expectedErrMsg: nil,
			user:           &user1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			q := make(url.Values)
			q.Set("namespace", tc.queryParams.namespace)
			q.Set("kind", tc.queryParams.kind)
			q.Set("name", tc.queryParams.name)
			q.Set("labels", tc.queryParams.labels)
			req := httptest.NewRequest(http.MethodGet, "/?"+q.Encode(), nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("meshInstanceId")
			c.SetParamValues(tc.pathParams.meshInstanceId)

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mc := crdMock.NewMockServiceInterface(ctrl)
			mc.EXPECT().GetCrds(ctx, gomock.Any()).Return(tc.mr.crds, tc.mr.pr, tc.mr.mockErr).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				CrdService: mc,
			}

			err := core.GetCrdsForOpenAPI(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
				sr := &internal.CrdPageView{}
				if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), sr)) {
					assert.Equal(t, sr.Result, tc.expected)
				}
			} else {
				assert.Containsf(t, err.Error(), *tc.expectedErrMsg,
					"expected error: %v, got %v", *tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestBatchCrd(t *testing.T) {
	testCases := []struct {
		name           string
		meshInstanceId string
		body           *internal.CrdView
		batchCrd       *meta.BatchCreateCrdInfo
		expectedCrd    int

		user *sdkIAM.User
	}{
		{
			name: "batch crd success",

			meshInstanceId: "mesh-instance-id",
			body: &internal.CrdView{
				Content: "test content",
			},
			batchCrd:    buildBatchCreateCrdInfo(),
			expectedCrd: 200,
			user:        &user1,
		},
		{
			name:           "batch crd failed",
			meshInstanceId: "mesh-instance-id",
			body: &internal.CrdView{
				Content: "test content",
			},
			batchCrd: &meta.BatchCreateCrdInfo{
				FailedCrdMessage: "test",
			},
			expectedCrd: 500,
			user:        &user1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jsonBody, err := json.Marshal(tc.body)
			if err != nil {
				t.FailNow()
			}

			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("meshInstanceId")
			c.SetParamValues(tc.meshInstanceId)

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mc := crdMock.NewMockServiceInterface(ctrl)
			mc.EXPECT().BatchCrd(ctx, gomock.Any()).Return(tc.batchCrd, nil).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				CrdService: mc,
			}

			_ = core.BatchCrd(ctx)

			assert.Equal(t, tc.expectedCrd, rec.Code)

		})
	}
}
func buildBatchCreateCrdInfo() *meta.BatchCreateCrdInfo {
	return &meta.BatchCreateCrdInfo{
		FailedCrdMessage: "test",
		SuccessCrdList: []meta.Crd{
			{
				Namespace: "test-ns",
				Name:      "name",
			},
		},
	}
}

func TestCreateCrdsForOpenAPI(t *testing.T) {
	type MockResult struct {
		crd     *meta.Crd
		mockErr error
	}
	type PathParam struct {
		meshInstanceId string
	}

	testCases := []struct {
		name           string
		pathParams     *PathParam
		body           *internal.CrdView
		mr             *MockResult
		expected       string
		expectedErrMsg *string
		user           *sdkIAM.User
	}{
		{
			name: "create crds success",
			pathParams: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			body: &internal.CrdView{
				Content: "test content",
			},
			mr: &MockResult{
				crd:     &crds[0],
				mockErr: nil,
			},
			expected:       "true",
			expectedErrMsg: nil,
			user:           &user1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jsonBody, err := json.Marshal(tc.body)
			if err != nil {
				t.FailNow()
			}

			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("meshInstanceId")
			c.SetParamValues(tc.pathParams.meshInstanceId)

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mc := crdMock.NewMockServiceInterface(ctrl)
			mc.EXPECT().CreateCrd(ctx, gomock.Any()).Return(tc.mr.crd, tc.mr.mockErr).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				CrdService: mc,
			}

			err = core.CreateCrdsForOpenAPI(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
				sr := &cnap.SuccessResponse{}
				if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), sr)) {
					assert.Equal(t, sr.Success, tc.expected)
				}
			} else {
				assert.Containsf(t, err.Error(), *tc.expectedErrMsg,
					"expected error: %v, got %v", *tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestUpdateCrdsForOpenAPI(t *testing.T) {
	type MockResult struct {
		crd     *meta.Crd
		mockErr error
	}
	type PathParam struct {
		meshInstanceId string
	}
	type QueryParam struct {
		namespace string
		kind      string
		name      string
	}

	testCases := []struct {
		name           string
		pathParams     *PathParam
		queryParams    *QueryParam
		body           *internal.CrdView
		mr             *MockResult
		expected       string
		expectedErrMsg *string
		user           *sdkIAM.User
	}{
		{
			name: "update crds success",
			pathParams: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			queryParams: &QueryParam{
				namespace: "ns1",
				kind:      "VirtualService",
				name:      "crd-name-1",
			},
			body: &internal.CrdView{
				Content: "new test content",
			},
			mr: &MockResult{
				crd:     &crds[0],
				mockErr: nil,
			},
			expected:       "true",
			expectedErrMsg: nil,
			user:           &user1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jsonBody, err := json.Marshal(tc.body)
			if err != nil {
				t.FailNow()
			}

			e := echo.New()
			q := make(url.Values)
			q.Set("namespace", tc.queryParams.namespace)
			q.Set("kind", tc.queryParams.kind)
			q.Set("name", tc.queryParams.name)
			req := httptest.NewRequest(http.MethodPut, "/?"+q.Encode(), strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("meshInstanceId")
			c.SetParamValues(tc.pathParams.meshInstanceId)

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mc := crdMock.NewMockServiceInterface(ctrl)
			mc.EXPECT().UpdateCrd(ctx, gomock.Any()).Return(tc.mr.crd, tc.mr.mockErr).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				CrdService: mc,
			}

			err = core.UpdateCrdsForOpenAPI(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
				sr := &cnap.SuccessResponse{}
				if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), sr)) {
					assert.Equal(t, sr.Success, tc.expected)
				}
			} else {
				assert.Containsf(t, err.Error(), *tc.expectedErrMsg,
					"expected error: %v, got %v", *tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestUpdateCrd(t *testing.T) {
	type MockResult struct {
		crd     *meta.Crd
		mockErr error
	}
	type PathParam struct {
		meshInstanceId string
	}
	type QueryParam struct {
		namespace string
		kind      string
		name      string
	}

	testCases := []struct {
		name           string
		pathParams     *PathParam
		queryParams    *QueryParam
		body           *internal.CrdView
		mr             *MockResult
		expectedErrMsg *string
		user           *sdkIAM.User
	}{
		{
			name: "update crds success",
			pathParams: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			queryParams: &QueryParam{
				kind: "VirtualService",
				name: "crd-name-1",
			},
			body: &internal.CrdView{
				Content: "new test content",
			},
			mr: &MockResult{
				crd: &meta.Crd{
					Namespace: "",
					Name:      "crd-name-1",
					Kind:      "VirtualService",
					UpdatedAt: time1,
					Content:   "test content",
				},
				mockErr: nil,
			},
			expectedErrMsg: nil,
			user:           &user1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jsonBody, err := json.Marshal(tc.body)
			if err != nil {
				t.FailNow()
			}

			e := echo.New()
			q := make(url.Values)
			q.Set("kind", tc.queryParams.kind)
			q.Set("name", tc.queryParams.name)
			req := httptest.NewRequest(http.MethodPut, "/?"+q.Encode(), strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("meshInstanceId", "namespace")
			c.SetParamValues(tc.pathParams.meshInstanceId, "*")

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mc := crdMock.NewMockServiceInterface(ctrl)
			mc.EXPECT().UpdateCrd(ctx, gomock.Any()).Return(tc.mr.crd, tc.mr.mockErr).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				CrdService: mc,
			}

			err = core.UpdateCrd(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
			} else {
				assert.Containsf(t, err.Error(), *tc.expectedErrMsg,
					"expected error: %v, got %v", *tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestGetCrd(t *testing.T) {
	testInfos := []struct {
		name    string
		success bool
	}{
		{
			name:    "success",
			success: true,
		},
	}
	for _, tt := range testInfos {
		t.Run(tt.name, func(t *testing.T) {

			e := echo.New()
			q := make(url.Values)
			q.Set("kind", "MetaRoute")
			q.Set("name", "test-name")
			req := httptest.NewRequest(http.MethodDelete, "/?"+q.Encode(), nil)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			c.SetParamNames("meshInstanceId", "namespace")
			c.SetParamValues("instanceId", "*")

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mc := crdMock.NewMockServiceInterface(ctrl)
			mc.EXPECT().GetCrd(ctx, gomock.Any()).Return(buildMockCrd(), nil).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				CrdService: mc,
			}
			err := core.GetCrd(ctx)
			if tt.success {
				assert.Nil(t, err)
			}
		})
	}
}

func buildMockCrd() *meta.Crd {
	return &meta.Crd{
		InstanceUUID: "instanceId",
		Namespace:    "",
	}
}

func TestDeleteCrdsForOpenAPI(t *testing.T) {
	type MockResult struct {
		mockErr error
	}
	type PathParam struct {
		meshInstanceId string
	}
	type QueryParam struct {
		namespace string
		kind      string
		name      string
	}

	testCases := []struct {
		name           string
		pathParams     *PathParam
		queryParams    *QueryParam
		mr             *MockResult
		expected       string
		expectedErrMsg *string
		user           *sdkIAM.User
	}{
		{
			name: "delete crds success",
			pathParams: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			queryParams: &QueryParam{
				namespace: "ns1",
				kind:      "VirtualService",
				name:      "crd-name-1",
			},
			mr: &MockResult{
				mockErr: nil,
			},
			expected:       "true",
			expectedErrMsg: nil,
			user:           &user1,
		},
		{
			name: "delete crds witch invalid query",
			pathParams: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			queryParams: &QueryParam{
				namespace: "",
				kind:      "VirtualService",
				name:      "crd-name-1",
			},
			mr: &MockResult{
				mockErr: nil,
			},
			expected:       "true",
			expectedErrMsg: ptrutil.String("namespace, kind, name"),
			user:           &user1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			q := make(url.Values)
			q.Set("namespace", tc.queryParams.namespace)
			q.Set("kind", tc.queryParams.kind)
			q.Set("name", tc.queryParams.name)
			req := httptest.NewRequest(http.MethodDelete, "/?"+q.Encode(), nil)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("meshInstanceId")
			c.SetParamValues(tc.pathParams.meshInstanceId)

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mc := crdMock.NewMockServiceInterface(ctrl)
			mc.EXPECT().DeleteCrds(ctx, gomock.Any()).Return(tc.mr.mockErr).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				CrdService: mc,
			}

			err := core.DeleteCrdsForOpenAPI(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
				sr := &cnap.SuccessResponse{}
				if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), sr)) {
					assert.Equal(t, sr.Success, tc.expected)
				}
			} else {
				assert.Containsf(t, err.Error(), *tc.expectedErrMsg,
					"expected error: %v, got %v", *tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestDeleteCrd(t *testing.T) {
	type MockResult struct {
		mockErr error
	}

	testCases := []struct {
		name string
		mr   *MockResult
		user *sdkIAM.User
	}{
		{
			name: "delete crds success",

			mr: &MockResult{
				mockErr: nil,
			},
			user: &user1,
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			body := &internal.CrdListView{
				CrdList: []internal.CrdView{{
					Name:      "name",
					Namespace: "*",
					Kind:      "MetaRoute",
				}},
			}
			jsonBody, err := json.Marshal(body)
			if err != nil {
				t.FailNow()
			}
			req := httptest.NewRequest(http.MethodDelete, "/", strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("meshInstanceId")
			c.SetParamValues("mesh-instance-id")

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)

			mc := crdMock.NewMockServiceInterface(ctrl)
			mc.EXPECT().DeleteCrds(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			core := &APIServerCore{
				CrdService: mc,
			}

			err = core.DeleteCrd(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
			}
		})
	}
}
