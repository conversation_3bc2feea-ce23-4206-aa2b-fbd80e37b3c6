package core

import (
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	blbMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/blb/mock"
	gatewayMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/gateway/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

var (
	User         = "User"
	iamAccountId = "123"
	iamUser      = &sdkIAM.User{
		ID:   "1",
		Name: "test-user",
		Domain: sdkIAM.UserDomain{
			ID:   iamAccountId,
			Name: "aaaa",
		},
	}
)

func TestNewGateway(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		gatewayId string
		body      string
		expectErr error
	}{
		{
			name:      "correct-NewGateway",
			gatewayId: "test-123",
			body: "{\"basicConfig\":{\"gatewayName\":\"gw-1\",\"deployMode\":\"hosting\"," +
				"\"gatewayType\":\"ingress\",\"resourceQuota\":\"2C4G\",\"replicas\":3,\"hpa\":{\"enabled\":true," +
				"\"minReplicas\":1,\"maxReplicas\":3},\"log\":{\"enabled\":true,\"type\":\"BLS\",\"logFile\":\"a\"}," +
				"\"monitor\":{\"enabled\":true,\"instances\":[{\"region\":\"bj\",\"id\":\"123\"}]}," +
				"\"tlsAcc\":{\"enabled\":true}},\"networkConfig\":{\"blbId\":\"blb-1\",\"networkType\":{\"vpcNetworkId\":\"vpc-1\"," +
				"\"subnetId\":\"sub-1\"},\"securityGroupId\":\"sg-1\",\"elasticPublicNetwork\":{\"enabled\":true," +
				"\"type\":\"BIND\",\"id\":\"eip-1\",\"ip\":\"ip-1\"}}}",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		mockBlbService := blbMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
			BlbService:     mockBlbService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set(User, iamUser)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames("InstanceUUID")
			mockCtx.SetParamValues("csm-123")
			body := struct{ io.Reader }{strings.NewReader(testInfo.body)}
			testRequest := httptest.NewRequest("", "/v1/instance", body)
			testRequest.Header = http.Header{
				"Content-Type": []string{"application/json"},
			}
			mockCtx.SetRequest(testRequest)

			mockGatewayService.EXPECT().NewGateway(mockCtx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mockGatewayService.EXPECT().GenerateGatewayID(mockCtx).Return(testInfo.gatewayId, nil)

			err := core.NewGateway(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestDeleteGateway(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-DeleteGateway",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockGatewayService.EXPECT().DeleteGateway(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.expectErr)
			err := core.DeleteGateway(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestGetGatewayList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectRes *meta.GatewayListResponse
		expectErr error
	}{
		{
			name:      "correct-GetGatewayList",
			expectRes: &meta.GatewayListResponse{},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockGatewayService.EXPECT().GetGatewayList(mockCtx, gomock.Any()).Return(testInfo.expectRes, testInfo.expectErr)
			err := core.GetGatewayList(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestGetGatewayDetail(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectRes *meta.GatewayDetailResponse
		expectErr error
	}{
		{
			name:      "correct-GetGatewayDetail",
			expectRes: &meta.GatewayDetailResponse{},
			expectErr: nil,
		},
	}
	mockCtx := context.MockNewCsmContext()
	for _, testInfo := range testInfos {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockGatewayService.EXPECT().GetGatewayDetail(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.expectRes, testInfo.expectErr)
			err := core.GetGatewayDetail(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestGetGatewayBlbList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectRes *meta.GatewayBlbListResponse
		expectErr error
	}{
		{
			name:      "correct-GetGatewayDetail",
			expectRes: &meta.GatewayBlbListResponse{},
			expectErr: nil,
		},
	}
	mockCtx := context.MockNewCsmContext()
	for _, testInfo := range testInfos {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockGatewayService.EXPECT().GetGatewayBlbList(mockCtx, gomock.Any()).Return(testInfo.expectRes, testInfo.expectErr)
			err := core.GetGatewayBlbList(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestGetGatewayDomainList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectRes *meta.GatewayDomainListResponse
		expectErr error
	}{
		{
			name:      "correct-GetGatewayDomainList",
			expectRes: &meta.GatewayDomainListResponse{},
			expectErr: nil,
		},
	}
	mockCtx := context.MockNewCsmContext()
	for _, testInfo := range testInfos {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockGatewayService.EXPECT().GetGatewayDomainList(mockCtx, gomock.Any()).Return(testInfo.expectRes, testInfo.expectErr)
			err := core.GetGatewayDomainList(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestAddGatewayDomain(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-AddGatewayDomain",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockGatewayService.EXPECT().AddGatewayDomain(mockCtx, gomock.Any()).Return(testInfo.expectErr)
			err := core.AddGatewayDomain(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestDeleteGatewayDomain(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-DeleteGatewayDomain",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockGatewayService.EXPECT().DeleteGatewayDomain(mockCtx, gomock.Any()).Return(testInfo.expectErr)
			err := core.DeleteGatewayDomain(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestModifyGatewayDomain(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-ModifyGatewayDomain",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockGatewayService.EXPECT().ModifyGatewayDomain(mockCtx, gomock.Any()).Return(testInfo.expectErr)
			err := core.ModifyGatewayDomain(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestModifyGatewayBlsTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-ModifyGatewayBlsTask",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockGatewayService.EXPECT().ModifyGatewayBlsTask(mockCtx, gomock.Any()).Return(&meta.Log{}, testInfo.expectErr)
			err := core.ModifyGatewayBlsTask(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestModifyGatewayHPA(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-ModifyGatewayHPA",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockGatewayService.EXPECT().ModifyGatewayHPA(mockCtx, gomock.Any()).Return(&meta.HPA{}, testInfo.expectErr)
			err := core.ModifyGatewayHPA(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestModifyGatewayMonitor(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-ModifyGatewayMonitor",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockGatewayService.EXPECT().ModifyGatewayMonitor(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&meta.Monitor{}, testInfo.expectErr)
			err := core.ModifyGatewayMonitor(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestModifyGatewayTLSAcceleration(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-ModifyGatewayTLSAcc",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockGatewayService.EXPECT().ModifyGatewayTLSAcceleration(mockCtx, gomock.Any()).Return(&meta.TLSAcc{}, testInfo.expectErr)
			err := core.ModifyGatewayTLSAcceleration(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestAPIServerCore_ModifyGatewayResourceQuota(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "test-modifyGatewayResourceQuota",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			GatewayService: mockGatewayService,
		}
		t.Run(tt.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockGatewayService.EXPECT().ModifyGatewayResourceQuota(mockCtx, gomock.Any()).Return(nil, nil)
			if err := core.ModifyGatewayResourceQuota(mockCtx); (err != nil) != tt.wantErr {
				t.Errorf("ModifyGatewayResourceQuota() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestModifyGatewayIngress(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-ModifyGatewayIngress",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
			core := &APIServerCore{
				CsmOptions:     options.NewCsmServerOption(),
				GatewayService: mockGatewayService,
			}
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockGatewayService.EXPECT().ModifyGatewayIngress(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(testInfo.expectErr)

			err := core.ModifyGatewayIngress(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestGetGatewayIngress(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-GetGatewayIngress",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			mockGatewayService := gatewayMock.NewMockServiceInterface(ctrl)
			core := &APIServerCore{
				CsmOptions:     options.NewCsmServerOption(),
				GatewayService: mockGatewayService,
			}
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set("User", user)
			mockCtx.Set(region.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.GatewayIDPathParam)
			mockCtx.SetParamValues("inst-1", "gw-1")
			mockGatewayService.EXPECT().GetGatewayIngress(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, testInfo.expectErr)

			err := core.GetGatewayIngress(mockCtx)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}
