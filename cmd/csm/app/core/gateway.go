package core

import (
	"net/http"
	"strings"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

// NewGateway 创建网关
func (core *APIServerCore) NewGateway(ctx csmContext.CsmContext) (error error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}
	// 从前端获取创建网关相关配置
	gwp := &internal.GatewayParam{}
	if err = ctx.Bind(gwp); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	// 生成创建网关配置实例
	gatewayInst, err := gwp.ToGatewayModel()
	if err != nil {
		return err
	}
	gatewayUUID, err := core.GatewayService.GenerateGatewayID(ctx)
	if err != nil {
		return err
	}
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	gatewayInst.GatewayUUID = gatewayUUID
	gatewayInst.Region = region
	gatewayInst.AccountId = accountId
	// 生成部署HPA配置实例
	hpaConf, err := gwp.ToHpaConf(gatewayInst)
	if err != nil {
		return err
	}
	// 生成部署BLB配置实例
	blbConf, err := gwp.ToBlbConf()
	if err != nil {
		return err
	}
	// 生成部署Log配置实例
	logConf, err := gwp.ToLogConf(gatewayInst)
	if err != nil {
		return err
	}
	// 生成TLS加速配置实例
	tlsAccConf, err := gwp.ToTLSAccConf()
	if err != nil {
		return err
	}

	err = core.GatewayService.NewGateway(ctx, gatewayInst, hpaConf, blbConf, logConf, tlsAccConf)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

// DeleteGateway 删除网关
func (core *APIServerCore) DeleteGateway(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if len(strings.TrimSpace(instanceUUID)) == 0 {
		return csmErr.NewMissingParametersException("'" + constants.InstanceIDPathParam + "'")
	}
	gatewayUUID := ctx.Param(constants.GatewayIDPathParam)
	if len(strings.TrimSpace(gatewayUUID)) == 0 {
		return csmErr.NewMissingParametersException("'" + constants.GatewayIDPathParam + "'")
	}

	err := core.GatewayService.DeleteGateway(ctx, instanceUUID, gatewayUUID)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

// GetGatewayList 获取网关列表
func (core *APIServerCore) GetGatewayList(ctx csmContext.CsmContext) (error error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	mrp := meta.NewCsmMeshRequestParams()
	if err = ctx.Bind(mrp); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}
	mrp.AccountID = accountId
	mrp.Region = ctx.Get(reg.ContextRegion).(string)

	response, err := core.GatewayService.GetGatewayList(ctx, mrp)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, response)
}

// GetGatewayDetail 获取网关实例详情
func (core *APIServerCore) GetGatewayDetail(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if len(strings.TrimSpace(instanceUUID)) == 0 {
		return csmErr.NewMissingParametersException("'" + constants.InstanceIDPathParam + "'")
	}
	gatewayUUID := ctx.Param(constants.GatewayIDPathParam)
	if len(strings.TrimSpace(gatewayUUID)) == 0 {
		return csmErr.NewMissingParametersException("'" + constants.GatewayIDPathParam + "'")
	}

	res, err := core.GatewayService.GetGatewayDetail(ctx, instanceUUID, gatewayUUID)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, res)
}

// GetGatewayBlbList 获取网关绑定的BLB实例列表
func (core *APIServerCore) GetGatewayBlbList(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if len(strings.TrimSpace(instanceUUID)) == 0 {
		return csmErr.NewMissingParametersException("'" + constants.InstanceIDPathParam + "'")
	}
	gatewayUUID := ctx.Param(constants.GatewayIDPathParam)
	if len(strings.TrimSpace(gatewayUUID)) == 0 {
		return csmErr.NewMissingParametersException("'" + constants.GatewayIDPathParam + "'")
	}

	res, err := core.GatewayService.GetGatewayBlbList(ctx, instanceUUID)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, res)
}

// GetGatewayDomainList 获取网关监听的域名列表
func (core *APIServerCore) GetGatewayDomainList(ctx csmContext.CsmContext) (error error) {
	mrp := meta.NewCsmMeshRequestParams()
	// 获取请求参数
	if err := ctx.Bind(mrp); err != nil {
		return err
	}

	res, err := core.GatewayService.GetGatewayDomainList(ctx, mrp)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, res)
}

// AddGatewayDomain 添加网关监听的域名
func (core *APIServerCore) AddGatewayDomain(ctx csmContext.CsmContext) (error error) {
	domainConf := meta.NewDomainConf()
	// 获取请求参数
	if err := ctx.Bind(domainConf); err != nil {
		return err
	}
	err := core.GatewayService.AddGatewayDomain(ctx, domainConf)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

// DeleteGatewayDomain 删除网关监听的域名
func (core *APIServerCore) DeleteGatewayDomain(ctx csmContext.CsmContext) (error error) {
	domainConf := meta.NewDomainConf()
	// 获取请求参数
	if err := ctx.Bind(domainConf); err != nil {
		return err
	}
	err := core.GatewayService.DeleteGatewayDomain(ctx, domainConf)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

// ModifyGatewayDomain 编辑网关监听的域名
func (core *APIServerCore) ModifyGatewayDomain(ctx csmContext.CsmContext) (error error) {
	domainConf := meta.NewDomainConf()
	// 获取请求参数
	if err := ctx.Bind(domainConf); err != nil {
		return err
	}
	err := core.GatewayService.ModifyGatewayDomain(ctx, domainConf)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

// ModifyGatewayBlsTask 编辑网关的日志服务
func (core *APIServerCore) ModifyGatewayBlsTask(ctx csmContext.CsmContext) (error error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}
	logConf := meta.NewLogConf()
	// 获取请求参数
	if err = ctx.Bind(logConf); err != nil {
		return err
	}
	logConf.AccountID = accountId
	res, err := core.GatewayService.ModifyGatewayBlsTask(ctx, logConf)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, res)
}

// ModifyGatewayHPA 编辑网关的HPA扩缩容
func (core *APIServerCore) ModifyGatewayHPA(ctx csmContext.CsmContext) (error error) {
	hpaConf := meta.NewHpaConf()
	// 获取请求参数
	if err := ctx.Bind(hpaConf); err != nil {
		return err
	}
	res, err := core.GatewayService.ModifyGatewayHPA(ctx, hpaConf)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, res)
}

// ModifyGatewayMonitor 编辑网关的CProm监控
func (core *APIServerCore) ModifyGatewayMonitor(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	gatewayID := ctx.Param(constants.GatewayIDPathParam)
	monitorConf := &meta.Monitor{}
	// 获取请求参数
	if err := ctx.Bind(monitorConf); err != nil {
		return err
	}
	resMonitor, err := core.GatewayService.ModifyGatewayMonitor(ctx, instanceUUID, gatewayID, monitorConf)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, resMonitor)
}

// ModifyGatewayTLSAcceleration 编辑网关的TLS加速开关
func (core *APIServerCore) ModifyGatewayTLSAcceleration(ctx csmContext.CsmContext) (error error) {
	tlsAccConf := meta.NewTLSAccConf()
	// 获取请求参数
	if err := ctx.Bind(tlsAccConf); err != nil {
		return err
	}
	res, err := core.GatewayService.ModifyGatewayTLSAcceleration(ctx, tlsAccConf)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, res)
}

// ModifyGatewayResourceQuota 编辑网关的资源配额
func (core *APIServerCore) ModifyGatewayResourceQuota(ctx csmContext.CsmContext) (error error) {
	resourceQuotaConf := meta.NewResourceQuotaConf()
	// 获取请求参数
	if err := ctx.Bind(resourceQuotaConf); err != nil {
		return err
	}
	res, err := core.GatewayService.ModifyGatewayResourceQuota(ctx, resourceQuotaConf)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, res)
}

// ModifyGatewayIngress 修改remote集群监听ingress
func (core *APIServerCore) ModifyGatewayIngress(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	gatewayID := ctx.Param(constants.GatewayIDPathParam)
	ingressParam := &meta.IngressParam{}
	// 获取请求参数
	if err := ctx.Bind(ingressParam); err != nil {
		return err
	}
	// 兼容前端改动：前端去掉总开关，通过len(clustersList) 来判断是否开启higress部署。
	// 长度为0的时候，表示关闭；长度大于0的时候，表示开启
	ingressParam.Enabled = false
	if len(ingressParam.CLusterList) > 0 {
		ingressParam.Enabled = true
	}

	err := core.GatewayService.ModifyGatewayIngress(ctx, instanceUUID, gatewayID, ingressParam)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

// GetGatewayIngress 查看remote集群监听ingress
func (core *APIServerCore) GetGatewayIngress(ctx csmContext.CsmContext) (error error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	gatewayID := ctx.Param(constants.GatewayIDPathParam)

	ingressParam, err := core.GatewayService.GetGatewayIngress(ctx, instanceUUID, gatewayID)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, ingressParam)
}
