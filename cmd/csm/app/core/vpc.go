package core

import (
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	"net/http"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

func (core *APIServerCore) ListVPC(ctx context.CsmContext) (err error) {
	// 校验资源权限
	_, aiErr := iam.GetAccountId(ctx)
	if aiErr != nil {
		return aiErr
	}
	region := ctx.Get(reg.ContextRegion).(string)
	// 目前是查询所有，后续如有需求，可以根据前端参数过滤查询
	args := &meta.VPCArgs{}
	vpc, err := core.VpcService.ListVPC(ctx, args, region)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, vpc.VPCs)
}

func (core *APIServerCore) ListSubnet(ctx context.CsmContext) (err error) {
	// 校验资源权限
	_, aiErr := iam.GetAccountId(ctx)
	if aiErr != nil {
		return aiErr
	}

	// 请求参数校验
	vpcId := ctx.Param("vpcId")
	if len(vpcId) <= 0 {
		return csmErr.NewMissingParametersException("bad request due to without parameter vpcId")
	}

	region := ctx.Get(reg.ContextRegion).(string)
	args := &meta.SubnetArgs{}
	args.VpcID = vpcId

	listSubnet, err := core.VpcService.ListSubnet(ctx, args, region)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, listSubnet.Subnets)
}

func (core *APIServerCore) ListSecurityGroup(ctx context.CsmContext) (err error) {
	// 校验资源权限
	_, aiErr := iam.GetAccountId(ctx)
	if aiErr != nil {
		return aiErr
	}

	// 请求参数校验
	vpcId := ctx.Param("vpcId")
	if len(vpcId) <= 0 {
		return csmErr.NewMissingParametersException("bad request due to without parameter vpcId")
	}

	region := ctx.Get(reg.ContextRegion).(string)
	args := &meta.SecurityGroupArgs{}
	args.VpcID = vpcId

	listSecurityGroup, err := core.VpcService.ListSecurityGroup(ctx, args, region)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, listSecurityGroup.SecurityGroups)
}
