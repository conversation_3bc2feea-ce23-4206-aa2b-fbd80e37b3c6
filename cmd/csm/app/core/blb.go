package core

import (
	"net/http"
	"strings"

	"github.com/jinzhu/copier"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/internal/csm/domain"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/internal/csm/web"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/internal/pkg/request"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

func (core *APIServerCore) BindBlb(ctx context.CsmContext) (err error) {
	// 绑定数据
	bb := &web.BindBlb{}
	err = request.ConvertAndCheck(ctx, bb)
	if err != nil {
		return err
	}

	// 转换模型
	bi := &domain.BlbInfo{}
	err = copier.Copy(bi, bb)
	bi.BlbID = bb.NetworkConfig.BlbID
	if err != nil {
		return err
	}

	err = core.BlbService.BindBlb(ctx, bi)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, nil)
}

func (core *APIServerCore) UnBindBlb(ctx context.CsmContext) (err error) {
	ubb := &web.UnBindBlb{}
	err = request.ConvertAndCheck(ctx, ubb)
	if err != nil {
		return err
	}

	// 转换模型
	bi := &domain.BlbInfo{}
	err = copier.Copy(bi, ubb)
	if err != nil {
		return err
	}
	err = core.BlbService.UnBindBlb(ctx, bi, ubb.IsReleaseBlb, ubb.IsReleseEip)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, nil)
}

func (core *APIServerCore) ListAvailableBlb(ctx context.CsmContext) (err error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if len(strings.TrimSpace(instanceUUID)) == 0 {
		return csmErr.NewMissingParametersException("'" + constants.InstanceIDPathParam + "'")
	}

	region := ctx.Request().Header.Get(constants.RegionHeaderKey)

	response, err := core.BlbService.ListAvailableBlb(ctx, instanceUUID, region)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, response)
}
