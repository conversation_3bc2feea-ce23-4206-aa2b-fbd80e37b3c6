package core

import (
	"testing"

	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	instancesModelMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	sugarMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/sugar/mock"
)

func TestAPIServerCore_GetScale(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "test-getScale",
			wantErr: false,
		},
	}
	mockCtx := context.MockNewCsmContext()
	for _, tt := range tests {
		mockSugarService := sugarMock.NewMockServiceInterface(ctrl)
		mockInstancesModel := instancesModelMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			sugarService:   mockSugarService,
			InstancesModel: mockInstancesModel,
		}
		t.Run(tt.name, func(t *testing.T) {
			mockSugarService.EXPECT().GetScale(mockCtx, gomock.Any(), gomock.Any()).Return(nil, nil)

			if err := core.GetScale(mockCtx); (err != nil) != tt.wantErr {
				t.Errorf("GetScale() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestAPIServerCore_GetUserList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "test-getUserList",
			wantErr: false,
		},
	}
	mockCtx := context.MockNewCsmContext()
	for _, tt := range tests {
		mockSugarService := sugarMock.NewMockServiceInterface(ctrl)
		mockInstancesModel := instancesModelMock.NewMockServiceInterface(ctrl)
		core := &APIServerCore{
			CsmOptions:     options.NewCsmServerOption(),
			sugarService:   mockSugarService,
			InstancesModel: mockInstancesModel,
		}
		t.Run(tt.name, func(t *testing.T) {
			mockSugarService.EXPECT().GetUserList(mockCtx).Return(nil, nil)

			if err := core.GetUserList(mockCtx); (err != nil) != tt.wantErr {
				t.Errorf("GetUserList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
