package core

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/registercentersdk"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
)

// todo mock register instance args
var registerInstanceArgs = map[string]string{
	"maintain.deleteEmptyAutoCreatedService.timeout": "30",
	"maintain.deleteUnHealthyInstance.enable":        "false",
	"maintain.deleteUnHealthyInstance.timeout":       "60",
}

func (core *APIServerCore) GetRegisterInstanceArgs(ctx csmContext.CsmContext) (err error) {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	// todo invoke register server backend

	args, err := core.RegisterCenterService.GetRegisterInstanceArgs(ctx, instanceId)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, args)
}

func (core *APIServerCore) UpdateRegisterInstanceArgs(ctx csmContext.CsmContext) (err error) {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	args := map[string]interface{}{}
	if err = ctx.Bind(&args); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	if err = core.RegisterCenterService.UpdateRegisterInstanceArgs(ctx, instanceId, args); err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

func (core *APIServerCore) GetServiceList(ctx csmContext.CsmContext) (err error) {

	keyword := ctx.QueryParam("keyword")
	keywordType := ctx.QueryParam("keywordType")
	pageNoStr := ctx.QueryParam("pageNo")
	pageSizeStr := ctx.QueryParam("pageSize")

	pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
	pageNo = util.MaxInt64(1, pageNo)

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
	if err != nil {
		pageSize = 10
	}
	pageSize = util.MaxInt64(1, pageSize)
	pageSize = util.MinInt64(1000, pageSize)

	if keyword != "" {
		keyword = fmt.Sprintf("*%s*", keyword)
	}

	req := &registercentersdk.RegisterCenterServiceListRequest{
		Offset: int((pageNo - 1) * pageSize),
		Limit:  int(pageSize),
	}
	if keywordType == "serviceName" {
		req.Name = keyword
	} else if keywordType == "namespace" {
		req.Namespace = keyword
	}

	serviceList, err := core.RegisterCenterService.GetServiceList(ctx, req)
	if err != nil {
		return err
	}

	response := meta.PageResponse{
		TotalCount: int64(serviceList.Amount),
		PageNo:     pageNo,
		PageSize:   pageSize,
		Result:     []*internal.RegisterService{},
	}

	if serviceList.Size == 0 {
		return ctx.JSON(http.StatusOK, response)
	}

	// RegisterCenterServiceModel -> RegisterService
	services := make([]*internal.RegisterService, 0)
	for _, s := range serviceList.Services {
		services = append(services, &internal.RegisterService{
			Id:          s.Id,
			Name:        s.Name,
			Namespace:   s.Namespace,
			Business:    s.Business,
			Department:  s.Department,
			HealthCount: s.HealthyInstanceCount,
			TotalCount:  s.TotalInstanceCount,
			CreateTime:  s.Ctime,
			UpdateTime:  s.Mtime,
		})
	}
	response.Result = services

	return ctx.JSON(http.StatusOK, response)
}

func (core *APIServerCore) CreateServiceInstance(ctx csmContext.CsmContext) (err error) {
	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	serviceId := ctx.Param("serviceId")
	if serviceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	serviceInstance := &internal.ServiceInstance{}
	if err = ctx.Bind(serviceInstance); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	req := &registercentersdk.CreateServiceInstanceRequest{
		Service:           serviceInstance.ServiceName,
		Namespace:         serviceInstance.Namespace,
		Host:              serviceInstance.Host,
		Port:              serviceInstance.Port,
		Healthy:           serviceInstance.HealthStatus,
		Isolate:           serviceInstance.IsolateEnable,
		Metadata:          serviceInstance.Metadata,
		EnableHealthCheck: serviceInstance.HealthCheckEnable,
		Weight:            serviceInstance.Weight,
	}

	if req.EnableHealthCheck {
		req.HealthCheck = &registercentersdk.HealthCheck{
			Type: "HEARTBEAT",
			Heartbeat: registercentersdk.Heartbeat{
				Ttl: serviceInstance.TTL,
			},
		}
	}
	// append special metadata
	parseSpecialMetadata(req, serviceInstance.Metadata)

	err = core.RegisterCenterService.CreateServiceInstance(ctx, req)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

func (core *APIServerCore) UpdateServiceInstance(ctx csmContext.CsmContext) (err error) {
	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	serviceId := ctx.Param("serviceId")
	if serviceId == "" {
		return csmErr.NewInvalidParameterValueException("register serviceId could not be nil")
	}

	serviceInstanceId := ctx.Param("serviceInstanceId")
	if serviceInstanceId == "" {
		return csmErr.NewInvalidParameterValueException("register serviceInstanceId could not be nil")
	}

	serviceInstance := &internal.ServiceInstance{}
	if err = ctx.Bind(serviceInstance); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	req := &registercentersdk.CreateServiceInstanceRequest{
		Id:                serviceInstanceId,
		Host:              serviceInstance.Host,
		Port:              serviceInstance.Port,
		Metadata:          serviceInstance.Metadata,
		EnableHealthCheck: serviceInstance.HealthCheckEnable,
		Isolate:           serviceInstance.IsolateEnable,
		Service:           serviceInstance.ServiceName,
		Namespace:         serviceInstance.Namespace,
		Healthy:           serviceInstance.HealthStatus,
		Weight:            serviceInstance.Weight,
	}

	if req.EnableHealthCheck {
		req.HealthCheck = &registercentersdk.HealthCheck{
			Type: "HEARTBEAT",
			Heartbeat: registercentersdk.Heartbeat{
				Ttl: serviceInstance.TTL,
			},
		}
	}
	// append special metadata
	parseSpecialMetadata(req, serviceInstance.Metadata)

	requestByte, err := json.Marshal(req)
	ctx.CsmLogger().Infof("update service instance request: %s", string(requestByte))

	err = core.RegisterCenterService.UpdateServiceInstance(ctx, req)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

func (core *APIServerCore) DeleteServiceInstance(ctx csmContext.CsmContext) (err error) {
	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}
	serviceId := ctx.Param("serviceId")
	if serviceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	serviceInstanceId := ctx.Param("serviceInstanceId")
	if serviceInstanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	req := &registercentersdk.DeleteServiceInstanceRequest{
		Id: serviceInstanceId,
	}
	err = core.RegisterCenterService.DeleteServiceInstance(ctx, req)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

func (core *APIServerCore) GetServiceInstanceList(ctx csmContext.CsmContext) (err error) {
	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}
	serviceId := ctx.Param("serviceId")
	if serviceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	serviceName := ctx.QueryParam("serviceName")
	if serviceName == "" {
		return csmErr.NewInvalidParameterValueException("service name could not be nil")
	}
	namespace := ctx.QueryParam("namespace")

	pageNoStr := ctx.QueryParam("pageNo")
	pageSizeStr := ctx.QueryParam("pageSize")

	pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
	pageNo = util.MaxInt64(1, pageNo)

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
	if err != nil {
		pageSize = 10
	}
	pageSize = util.MaxInt64(1, pageSize)
	pageSize = util.MinInt64(1000, pageSize)

	queryInstanceId := ctx.QueryParam("queryInstanceId")
	host := ctx.QueryParam("host")
	healthStatus := ctx.QueryParam("healthStatus")
	isolateStatus := ctx.QueryParam("isolateStatus")

	if queryInstanceId != "" {
		queryInstanceId = fmt.Sprintf("*%s*", queryInstanceId)
	}

	req := &registercentersdk.ServiceInstanceListRequest{
		Offset:    int((pageNo - 1) * pageSize),
		Limit:     int(pageSize),
		Service:   serviceName,
		Id:        queryInstanceId,
		Host:      host,
		Healthy:   healthStatus,
		Isolate:   isolateStatus,
		Namespace: namespace,
	}

	serviceInstanceList, err := core.RegisterCenterService.GetServiceInstanceList(ctx, req)
	if err != nil {
		return err
	}

	response := meta.PageResponse{
		TotalCount: int64(serviceInstanceList.Amount),
		PageNo:     pageNo,
		PageSize:   pageSize,
		Result:     []*internal.ServiceInstance{},
	}

	if serviceInstanceList.Size == 0 {
		return ctx.JSON(http.StatusOK, response)
	}

	// ServiceInstanceListResponse -> ServiceInstance
	instances := make([]*internal.ServiceInstance, 0)
	for _, i := range serviceInstanceList.Instances {
		healthy := i.Healthy
		ins := &internal.ServiceInstance{
			ServiceName:       i.Service,
			ServiceInstanceID: i.Id,
			Namespace:         i.Namespace,
			Host:              i.Host,
			Port:              i.Port,
			Weight:            i.Weight,
			HealthStatus:      &healthy,
			IsolateEnable:     i.Isolate,
			CreateTime:        i.Ctime,
			UpdateTime:        i.Mtime,
			HealthCheckEnable: i.EnableHealthCheck,
			Metadata:          i.Metadata,
		}
		if ins.HealthCheckEnable {
			ins.TTL = i.HealthCheck.Heartbeat.Ttl
		}

		// set last heart beat time
		if heartbeatTime, ok := ins.Metadata["last-heartbeat-time"]; ok {
			ins.LastHeartbeatTime = heartbeatTime
		}
		appendInstanceMetadata(i, ins)
		for key, value := range ins.Metadata {
			if value == "<nil>" {
				ins.Metadata[key] = ""
			}
		}

		instances = append(instances, ins)
	}
	response.Result = instances
	return ctx.JSON(http.StatusOK, response)
}

func (core *APIServerCore) BatchOperateServiceInstance(ctx csmContext.CsmContext) (err error) {
	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}
	serviceId := ctx.Param("serviceId")
	if serviceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	batch := &registercentersdk.BatchOptRequest{}
	if err = ctx.Bind(batch); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	action := ctx.QueryParam("Action")
	if action == "" {
		return csmErr.NewInvalidParameterValueException("action could not be nil")
	}

	if action == "BatchDelete" {
		err = core.RegisterCenterService.BatchDeleteServiceInstance(ctx, batch.ServiceInstanceList)
	} else if action == "BatchIsolate" {
		err = core.RegisterCenterService.BatchIsolateServiceInstance(ctx, batch.ServiceInstanceList)
	} else {
		return csmErr.NewInvalidParameterValueException("action is invalid")
	}

	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, true)
}

func (core *APIServerCore) GetRegisterNamespaces(ctx csmContext.CsmContext) (err error) {
	keyword := ctx.QueryParam("name")
	pageNoStr := ctx.QueryParam("pageNo")
	pageSizeStr := ctx.QueryParam("pageSize")

	pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
	pageNo = util.MaxInt64(1, pageNo)

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
	if err != nil {
		pageSize = 10
	}
	pageSize = util.MaxInt64(1, pageSize)
	pageSize = util.MinInt64(1000, pageSize)

	if keyword != "" {
		keyword = fmt.Sprintf("*%s*", keyword)
	}

	req := &registercentersdk.RegisterCenterNamespaceListRequest{
		Offset: int((pageNo - 1) * pageSize),
		Limit:  int(pageSize),
		Name:   keyword,
	}

	namespaceList, err := core.RegisterCenterService.GetNamespaceList(ctx, req)
	if err != nil {
		return err
	}

	response := meta.PageResponse{
		TotalCount: int64(namespaceList.GetAmount().GetValue()),
		PageNo:     pageNo,
		PageSize:   pageSize,
		Result:     []*internal.RegisterNamespace{},
	}

	if namespaceList.GetSize().GetValue() == 0 {
		return ctx.JSON(http.StatusOK, response)
	}

	// RegisterCenterServiceModel -> RegisterService
	namespaces := make([]*internal.RegisterNamespace, 0)
	for _, s := range namespaceList.Namespaces {
		namespaces = append(namespaces, &internal.RegisterNamespace{
			Name:                s.GetName().GetValue(),
			Comment:             s.GetComment().GetValue(),
			CreateTime:          s.GetCtime().GetValue(),
			UpdateTime:          s.GetMtime().GetValue(),
			ServiceCount:        int(s.GetTotalServiceCount().GetValue()),
			InstanceCount:       int(s.GetTotalInstanceCount().GetValue()),
			HealthInstanceCount: int(s.GetTotalHealthInstanceCount().GetValue()),
		})
	}
	response.Result = namespaces

	return ctx.JSON(http.StatusOK, response)
}

func (core *APIServerCore) CreateRegisterNamespaces(ctx csmContext.CsmContext) (err error) {
	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	req := &internal.RegisterNamespaceReq{}
	if err = ctx.Bind(req); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	namespaces := make([]registercentersdk.CreateNamespaceRequest, 0)
	for _, ns := range req.Namespaces {
		namespaces = append(namespaces, registercentersdk.CreateNamespaceRequest{
			Name:    ns.Name,
			Comment: ns.Comment,
		})
	}

	err = core.RegisterCenterService.CreateNamespaces(ctx, namespaces)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

func (core *APIServerCore) UpdateRegisterNamespaces(ctx csmContext.CsmContext) (err error) {
	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	req := &internal.RegisterNamespaceReq{}
	if err = ctx.Bind(req); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	namespaces := make([]registercentersdk.CreateNamespaceRequest, 0)
	for _, ns := range req.Namespaces {
		namespaces = append(namespaces, registercentersdk.CreateNamespaceRequest{
			Name:    ns.Name,
			Comment: ns.Comment,
		})
	}

	err = core.RegisterCenterService.UpdateNamespaces(ctx, namespaces)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}

func (core *APIServerCore) DeleteRegisterNamespaces(ctx csmContext.CsmContext) (err error) {
	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	namespaces := ctx.QueryParam("namespaces")
	if namespaces == "" {
		return csmErr.NewInvalidParameterValueException("namespaces could not be nil")
	}

	req := make([]registercentersdk.CreateNamespaceRequest, 0)
	for _, ns := range strings.Split(namespaces, ",") {
		req = append(req, registercentersdk.CreateNamespaceRequest{
			Name: ns,
		})
	}

	err = core.RegisterCenterService.DeleteNamespaces(ctx, req)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, true)
}
