package core

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"github.com/golang/mock/gomock"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/options"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/api/v1/cnap"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	instancesModelMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	cceServiceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	clusterMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cluster/mock"
	versionMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/ptrutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/validate"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

var (
	managedClusters = []*meta.ManagedCluster{{
		ClusterId:   "cluster-id-1",
		ClusterName: "cluster-name-1",
		Region:      "bj",
	}}
	candidateClusters = []meta.CandidateCluster{
		{
			ClusterId:      "test-xxx",
			ClusterName:    "cce-xxx",
			Status:         meta.ClusterStatusRunning,
			Version:        "1.14.6",
			MasterMode:     "managedPro",
			NetworkSegment: "xxx",
			Region:         "bj",
			Available:      true,
		},
	}
	pr = meta.PageResult{
		PageSize:   10,
		PageNo:     1,
		Order:      "DESC",
		OrderBy:    "clusterId",
		TotalCount: 1,
	}
)

func TestGetManagedClustersForOpenAPI(t *testing.T) {
	type MockResult struct {
		clusters []*meta.ManagedCluster
		mockErr  error
	}
	type PathParam struct {
		meshInstanceId string
	}

	testCases := []struct {
		name           string
		params         *PathParam
		mr             *MockResult
		expected       []cnap.Cluster
		expectedErrMsg string
		user           *sdkIAM.User
	}{
		{
			name: "get managed clusters success",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			mr: &MockResult{
				clusters: managedClusters,
			},
			expected: []cnap.Cluster{{
				ClusterId:   "cluster-id-1",
				ClusterName: "cluster-name-1",
				Region:      "bj",
			}},
			expectedErrMsg: "",
			user:           &user1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("meshInstanceId")
			c.SetParamValues(tc.params.meshInstanceId)

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mc := clusterMock.NewMockServiceInterface(ctrl)
			mc.EXPECT().
				GetManagedClusters(ctx, tc.params.meshInstanceId, nil, nil).
				Return(tc.mr.clusters, nil, tc.mr.mockErr).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				ClusterService: mc,
			}

			err := core.GetManagedClustersForOpenAPI(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
				sr := make([]cnap.Cluster, 0)
				if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), &sr)) {
					assert.Equal(t, sr, tc.expected)
				}
			} else {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestAddClustersForOpenAPI(t *testing.T) {
	type MockResult struct {
		clusters []*meta.ManagedCluster
		pr       *meta.PageResult
		mockErr  error
	}
	type PathParam struct {
		meshInstanceId string
	}

	testCases := []struct {
		name           string
		params         *PathParam
		body           *cnap.Clusters
		mr             *MockResult
		expected       string
		expectedErrMsg *string
		user           *sdkIAM.User
	}{
		{
			name: "add single cluster success",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			body: &cnap.Clusters{
				Clusters: []cnap.Cluster{{
					ClusterId:   "cluster-id-2",
					ClusterName: "cluster-name-2",
					Region:      "bd",
				}},
			},
			mr: &MockResult{
				clusters: managedClusters,
				pr: &meta.PageResult{
					TotalCount: 1,
				},
				mockErr: nil,
			},
			expected:       "true",
			expectedErrMsg: nil,
			user:           &user1,
		},
		{
			name: "add sz cluster success",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			body: &cnap.Clusters{
				Clusters: []cnap.Cluster{
					{
						ClusterId:   "cluster-id-2",
						ClusterName: "cluster-name-2",
						Region:      "sz",
					},
					{
						ClusterId:   "cluster-id-2",
						ClusterName: "cluster-name-2",
						Region:      "bd",
					}},
			},
			mr: &MockResult{
				clusters: managedClusters,
				pr: &meta.PageResult{
					TotalCount: 2,
				},
				mockErr: nil,
			},
			expected:       "true",
			expectedErrMsg: nil,
			user:           &user1,
		},
		{
			name: "add multi clusters success",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			body: &cnap.Clusters{
				Clusters: []cnap.Cluster{
					{
						ClusterId:   "cluster-id-1",
						ClusterName: "cluster-name-1",
						Region:      "bj",
					},
					{
						ClusterId:   "cluster-id-2",
						ClusterName: "cluster-name-2",
						Region:      "bd",
					},
				},
			},
			mr: &MockResult{
				clusters: managedClusters,
				pr: &meta.PageResult{
					TotalCount: 1,
				},
				mockErr: nil,
			},
			expected:       "true",
			expectedErrMsg: nil,
			user:           &user1,
		},
		{
			name: "add cluster with invalid params",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			body: &cnap.Clusters{
				Clusters: make([]cnap.Cluster, 0),
			},
			mr: &MockResult{
				clusters: managedClusters,
				pr: &meta.PageResult{
					TotalCount: 1,
				},
				mockErr: nil,
			},
			expected:       "true",
			expectedErrMsg: ptrutil.String("Field validation"),
			user:           &user1,
		},
		{
			name: "add cluster exceed the threshold",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			body: &cnap.Clusters{
				Clusters: []cnap.Cluster{
					{
						ClusterId:   "cluster-id-1",
						ClusterName: "cluster-name-1",
						Region:      "bj",
					},
					{
						ClusterId:   "cluster-id-2",
						ClusterName: "cluster-name-2",
						Region:      "bd",
					},
				},
			},
			mr: &MockResult{
				clusters: managedClusters,
				pr: &meta.PageResult{
					TotalCount: 10,
				},
				mockErr: nil,
			},
			expected:       "true",
			expectedErrMsg: ptrutil.String("Cannot manage more than"),
			user:           &user1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jsonBody, err := json.Marshal(tc.body)
			if err != nil {
				t.FailNow()
			}

			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(iam.ContextIAMUser, tc.user)
			c.SetParamNames("meshInstanceId")
			c.SetParamValues(tc.params.meshInstanceId)

			ctx := csmContext.NewCsmContext(c)

			ctrl := gomock.NewController(t)
			mc := clusterMock.NewMockServiceInterface(ctrl)
			mc.EXPECT().
				GetManagedClusters(ctx, tc.params.meshInstanceId, nil, nil).
				Return(tc.mr.clusters, tc.mr.pr, tc.mr.mockErr).AnyTimes()
			mc.EXPECT().
				AddClusters(ctx, tc.params.meshInstanceId, gomock.Any()).
				Return(tc.mr.mockErr).AnyTimes()

			core := &APIServerCore{
				CsmOptions: &options.CsmOptions{
					EksAccountIds: []string{"account-id-1"},
				},
				ClusterService: mc,
			}

			err = core.AddClustersForOpenAPI(ctx)
			if err == nil {
				assert.Equal(t, http.StatusOK, rec.Code)
				sr := cnap.SuccessResponse{}
				if assert.NoError(t, json.Unmarshal(rec.Body.Bytes(), &sr)) {
					assert.Equal(t, sr.Success, tc.expected)
				}
			} else {
				assert.Containsf(t, err.Error(), *tc.expectedErrMsg,
					"expected error: %v, got %v", *tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestMain(m *testing.M) {
	validate.Validator.RegisterStructValidation(cnap.ClustersStructLevelValidation, cnap.Clusters{})
	os.Exit(m.Run())
}

func TestAPIServerCore_GetCandidateClusters(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "test-GetCandidateClusters",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockInstancesService := clusterMock.NewMockServiceInterface(ctrl)
			serviceCore := &APIServerCore{
				ClusterService: mockInstancesService,
				CsmOptions:     meshOption,
			}
			ctx := context.MockNewCsmContext()

			mockInstancesService.EXPECT().GetCandidateClusters(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(candidateClusters, &pr, nil)
			if err := serviceCore.GetCandidateClusters(ctx); (err != nil) != tt.wantErr {
				t.Errorf("GetCandidateClusters() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func buildMockManagedCluster() []*meta.ManagedCluster {
	var managedClusters []*meta.ManagedCluster
	managedClusters = append(managedClusters, &meta.ManagedCluster{
		InstanceId:  instanceUUID,
		ClusterId:   clusterID,
		ClusterName: clusterName,
		ClusterType: meta.ClusterTypePrimary,
		Region:      testRegion,
		AccountId:   accountId,
		Status:      meta.ClusterStatusRunning,
		Version:     testK8sVersion,
	})
	return managedClusters
}

func buildMockInstance() *meta.Instances {
	instances := &meta.Instances{
		InstanceUUID: instanceUUID,
		IstioVersion: "1.14.6",
		Region:       testRegion,
		AccountId:    accountId,
	}
	return instances
}

func TestAPIServerCore_AddClusters(t *testing.T) {
	type PathParam struct {
		meshInstanceId string
	}
	clusters := []vo.ClustersRequestItem{
		{
			ClusterId: clusterID,
			Region:    testRegion,
		},
	}
	tests := []struct {
		name               string
		params             *PathParam
		reqBody            *vo.ClustersRequest
		GetManagedClusters []*meta.ManagedCluster
		pageResult         *meta.PageResult
		wantErr            bool
	}{
		{
			name: "addCluster-success",
			params: &PathParam{
				meshInstanceId: "mesh-instance-id",
			},
			reqBody: &vo.ClustersRequest{
				Clusters: clusters,
			},
			GetManagedClusters: buildMockManagedCluster(),
			pageResult: &meta.PageResult{
				TotalCount: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonBody, err := json.Marshal(tt.reqBody)
			if err != nil {
				t.FailNow()
			}

			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("meshInstanceId")
			c.SetParamValues(tt.params.meshInstanceId)
			ctx := csmContext.NewCsmContext(c)
			ctrl := gomock.NewController(t)
			clusterServiceMock := clusterMock.NewMockServiceInterface(ctrl)
			instanceModelMock := instancesModelMock.NewMockServiceInterface(ctrl)
			mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
			mockVersionService := versionMock.NewMockServiceInterface(ctrl)
			core := &APIServerCore{
				ClusterService: clusterServiceMock,
				InstancesModel: instanceModelMock,
				cceService:     mockCceService,
				VersionService: mockVersionService,
			}
			clusterServiceMock.EXPECT().GetManagedClusters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(tt.GetManagedClusters, tt.pageResult, nil)
			instanceModelMock.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).Return(buildMockInstance(), nil)
			mockCceService.EXPECT().GetCCECluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(buildClusterDetail(), nil).AnyTimes()
			mockVersionService.EXPECT().CheckK8sVersion(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
			clusterServiceMock.EXPECT().AddClusters(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			if err := core.AddClusters(ctx); (err != nil) != tt.wantErr {
				t.Errorf("AddClusters() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func buildCceClusterInstances() *v2.ListInstancesResponse {
	cceClusterInstances := &v2.ListInstancesResponse{
		InstancePage: &v2.InstancePage{
			ClusterID: clusterID,
			InstanceList: []*v2.Instance{
				{
					Spec: &types.InstanceSpec{
						InstanceOS: types.InstanceOS{
							OSName:    "CentOS",
							OSVersion: "7.5",
						},
					},
				},
			},
		},
	}
	return cceClusterInstances
}

func TestAPIServerCore_CheckClusterList(t *testing.T) {
	clusters := []vo.ClustersRequestItem{
		{
			ClusterId: clusterID,
			Region:    testRegion,
		},
	}
	tests := []struct {
		name    string
		reqBody *vo.ClustersRequest
		wantErr bool
	}{
		{
			name: "checkClusterList-test",
			reqBody: &vo.ClustersRequest{
				Clusters: clusters,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonBody, err := json.Marshal(tt.reqBody)
			if err != nil {
				t.FailNow()
			}

			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", strings.NewReader(string(jsonBody)))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			ctx := csmContext.NewCsmContext(c)
			ctrl := gomock.NewController(t)
			mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
			mockVersionService := versionMock.NewMockServiceInterface(ctrl)
			core := &APIServerCore{
				cceService:     mockCceService,
				VersionService: mockVersionService,
			}
			mockCceService.EXPECT().GetCCEClusterInstances(gomock.Any(), gomock.Any(), gomock.Any()).Return(buildCceClusterInstances(), nil)
			mockVersionService.EXPECT().CheckNodeOS(gomock.Any(), gomock.Any()).Return(true)
			if err := core.CheckClusterList(ctx); (err != nil) != tt.wantErr {
				t.Errorf("CheckClusterList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
