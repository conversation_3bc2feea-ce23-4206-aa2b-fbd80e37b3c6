package core

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/registercentersdk"
)

func parseSpecialMetadata(ins *registercentersdk.CreateServiceInstanceRequest, metadata map[string]string) {
	// "version" "protocol" "region" "zone" "campus"
	if metadata == nil || ins == nil {
		return
	}
	if v, ok := metadata["version"]; ok && v != "" {
		ins.Version = v
	}
	if v, ok := metadata["protocol"]; ok && v != "" {
		ins.Protocol = v
	}
	if v, ok := metadata["region"]; ok && v != "" {
		ins.Location.Region = v
	}
	if v, ok := metadata["zone"]; ok && v != "" {
		ins.Location.Zone = v
	}
	if v, ok := metadata["campus"]; ok && v != "" {
		ins.Location.Campus = v
	}
}

func appendInstanceMetadata(insModel registercentersdk.RegisterServiceInstanceModel, insView *internal.ServiceInstance) {
	// "version" "protocol" "region" "zone" "campus"
	// 注册实例时上述 metadata 会被过滤，需要额外添加
	if insView == nil {
		return
	}
	if insView.Metadata == nil {
		insView.Metadata = make(map[string]string)
	}

	if insModel.Protocol != "" {
		insView.Metadata["protocol"] = insModel.Protocol
	}
	if insModel.Version != "" {
		insView.Metadata["version"] = insModel.Version
	}
	appendLocationMetadata(insModel, insView)
}

func appendLocationMetadata(insModel registercentersdk.RegisterServiceInstanceModel, insView *internal.ServiceInstance) {
	// "region" "zone" "campus"
	if insModel.Location.Zone != "" {
		insView.Metadata["zone"] = insModel.Location.Zone
	}
	if insModel.Location.Campus != "" {
		insView.Metadata["campus"] = insModel.Location.Campus
	}
	if insModel.Location.Region != "" {
		insView.Metadata["region"] = insModel.Location.Region
	}
}
