package router

import (
	"github.com/labstack/echo/v4"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type OverviewRouter struct{}

func (or *OverviewRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group = group.Group("/v1")

	group.GET("/overview/instances", server.<PERSON>sm<PERSON>and<PERSON>(c.GetOverviewOfInstances),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
	)

	group.GET("/overview/sidecars", server.CsmHand<PERSON>(c.GetOverviewOfSidecars),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
	)

	group.GET("/overview/instances/detail", server.<PERSON>sm<PERSON><PERSON><PERSON>(c.GetOverviewOfInstancesDetail),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
	)

	return group
}
