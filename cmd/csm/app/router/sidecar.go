package router

import (
	"github.com/labstack/echo/v4"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type SidecarRouter struct{}

func (sidecarRouter *SidecarRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.GET("/v1/instance/:instanceUUID/sidecarQuota", server.CsmHandler(c.GetSidecarQuota),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadInstance),
	)

	group.PUT("/v1/instance/:instanceUUID/namespace/:namespace/sidecarInjection", server.CsmHandler(c.SidecarInjection),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCrd),
	)

	group.PUT("/v1/instance/:instanceUUID/sidecarQuota", server.CsmHandler(c.UpdateSidecarQuota),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCrd),
	)

	return group
}
