package router

import (
	"github.com/labstack/echo/v4"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type InstancesRouter struct{}

func (instanceRouter *InstancesRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.GET("/v1/instance/:type/version", server.CsmHandler(c.GetMeshVersionList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.POST("/v1/instance", server.CsmHandler(c.NewServiceMeshInstance),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
		middleware.FilterEmptyQueryParam(),
	)

	group.DELETE("/v1/instance/:instanceUUID", server.CsmHandler(c.DeleteServiceMeshInstance),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyInstance),
	)

	// TODO: change path from /v1/cluster/list to /v1/instance/clusterList for resource management
	group.GET("/v1/cluster/list", server.CsmHandler(c.ListCceCluster),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	// TODO: remove above method once this interface ready
	group.GET("/v1/instance/clusterList", server.CsmHandler(c.ListCceCluster),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.GET("/v1/instance/list", server.CsmHandler(c.GetServiceMeshInstances),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
	)

	group.GET("/v1/instance/:instanceUUID", server.CsmHandler(c.InstanceDetail),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadInstance),
	)

	group.PUT("/v1/instance/:instanceUUID/discoverySelector", server.CsmHandler(c.UpdateDiscoverySelector),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
	)

	group.GET("/v1/instance/:instanceUUID/discoverySelector", server.CsmHandler(c.GetDiscoverySelector),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
	)

	group.GET("/v1/istioSupportK8sVersion", server.CsmHandler(c.GetIstioSupportK8sVersion),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
	)

	return group
}

func (instanceRouter *InstancesRouter) RegisterInnerAPI(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.GET("/iam/instances", server.CsmHandler(c.GetServiceMeshInstancesIAM),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)
	return group
}

func (instanceRouter *InstancesRouter) RegisterOpenAPI(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.POST("/instances", server.CsmHandler(c.CreateInstance),
		middleware.CheckIAMSignature(), // TODO: 此中间件与下面的有重叠
		middleware.CheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
		middleware.FilterEmptyQueryParam(),
	)

	group.POST("/instances/:meshInstanceId/namespaces/:namespace", server.CsmHandler(c.CreateNamespace),
		middleware.CheckIAMSignature(), // TODO: 此中间件与下面的有重叠
		middleware.OpenAPICheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
		middleware.FilterEmptyQueryParam(),
	)

	group.DELETE("/instances/:meshInstanceId/namespaces/:namespace", server.CsmHandler(c.DeleteNamespace),
		middleware.CheckIAMSignature(), // TODO: 此中间件与下面的有重叠
		middleware.OpenAPICheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
		middleware.FilterEmptyQueryParam(),
	)

	// group.GET("/instances/:meshInstanceId/namespaces/:namespace/kubeconfig", server.CsmHandler(c.GetKubeconfig),
	// middleware.CheckIAMSignature(), // TODO: 此中间件与下面的有重叠
	// middleware.CheckAndSetRegion(),
	// middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	// middleware.FilterEmptyQueryParam(),
	// )

	group.GET("/instances/:meshInstanceId/status", server.CsmHandler(c.GetInstanceStatus),
		middleware.CheckIAMSignature(), // TODO: 此中间件与下面的有重叠
		middleware.OpenAPICheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
		middleware.FilterEmptyQueryParam(),
	)
	return group
}
