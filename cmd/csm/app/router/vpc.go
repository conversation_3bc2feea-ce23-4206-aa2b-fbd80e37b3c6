package router

import (
	"github.com/labstack/echo/v4"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type VpcRouter struct{}

func (vr *VpcRouter) RegisterOpenAPI(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group = group.Group("/v1")

	group.GET("/vpc/vpcList", server.CsmHandler(c.ListVPC),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	group.GET("/vpc/:vpcId/subnetList", server.CsmHandler(c.ListSubnet),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	group.GET("/vpc/:vpcId/securityGroupList", server.CsmHandler(c.ListSecurityGroup),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	return group
}
