package permission

const (
	GlobalReadOnly = "GL<PERSON><PERSON>L_READONLY" // for overview and instances list pages
	GlobalModify   = "GLOBAL_MODIFY"   // only for instance creation

	InstanceReadOnly = "INSTANCE_READONLY"
	InstanceModify   = "INSTANCE_MODIFY"

	ClusterReadOnly = "CLUSTER_READONLY"
	ClusterModify   = "CLUSTER_MODIFY"

	CrdReadOnly = "CRD_READONLY"
	CrdModify   = "CRD_MODIFY"

	OtherReadOnly = "OTHER_READONLY" // for other interfaces after
	OtherModify   = "OTHER_MODIFY"
)

var (
	ReadGlobal = []string{
		GlobalReadOnly,
	}
	ModifyGlobal = []string{
		GlobalModify,
	}
	ReadInstance = []string{
		InstanceReadOnly,
	}
	ModifyInstance = []string{
		InstanceModify,
	}
	ReadCluster = []string{
		ClusterReadOnly,
	}
	ModifyCluster = []string{
		ClusterModify,
	}
	ReadCrd = []string{
		CrdReadOnly,
	}
	ModifyCrd = []string{
		CrdModify,
	}
	ReadOther = []string{
		OtherReadOnly,
	}
	ModifyOther = []string{
		OtherModify,
	}
)
