package router

import (
	"github.com/labstack/echo/v4"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type RegisterInsServiceRouter struct{}

func (registerInsServiceRouter *RegisterInsServiceRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.GET("/v1/registry/:instanceId/config", server.CsmHandler(c.GetRegisterInstanceArgs),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)

	group.PUT("/v1/registry/:instanceId/config", server.CsmHandler(c.UpdateRegisterInstanceArgs),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.GET("/v1/registry/:instanceId/services", server.CsmHandler(c.GetServiceList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)

	group.POST("/v1/registry/:instanceId/service/:serviceId/serviceInstance", server.CsmHandler(c.CreateServiceInstance),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.PUT("/v1/registry/:instanceId/service/:serviceId/serviceInstance/:serviceInstanceId", server.CsmHandler(c.UpdateServiceInstance),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.DELETE("/v1/registry/:instanceId/service/:serviceId/serviceInstance/:serviceInstanceId", server.CsmHandler(c.DeleteServiceInstance),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.GET("/v1/registry/:instanceId/service/:serviceId/serviceInstances", server.CsmHandler(c.GetServiceInstanceList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)

	group.POST("/v1/registry/:instanceId/service/:serviceId/serviceInstances", server.CsmHandler(c.BatchOperateServiceInstance),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.POST("/v1/registry/:instanceId/namespaces", server.CsmHandler(c.CreateRegisterNamespaces),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)

	group.DELETE("/v1/registry/:instanceId/namespaces", server.CsmHandler(c.DeleteRegisterNamespaces),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)

	group.PUT("/v1/registry/:instanceId/namespaces", server.CsmHandler(c.UpdateRegisterNamespaces),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)

	group.GET("/v1/registry/:instanceId/namespaces", server.CsmHandler(c.GetRegisterNamespaces),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)
	return group
}
