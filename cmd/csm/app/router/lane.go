package router

import (
	"github.com/labstack/echo/v4"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type LanesRouter struct{}

func (lanesRouter *LanesRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.POST("/v1/instance/:instanceUUID/laneGroup", server.CsmHandler(c.NewLaneGroup),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.<PERSON>ame, permission.ModifyGlobal),
	)

	group.POST("/v1/instance/:instanceUUID/laneGroup/:laneGroupID/lane", server.CsmHandler(c.NewLane),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.GET("/v1/instance/:instanceUUID/laneGroups", server.CsmHandler(c.GetLaneGroups),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)

	group.DELETE("/v1/instance/:instanceUUID/laneGroup/:laneGroupID", server.CsmHandler(c.DeleteLaneGroup),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	// 获取泳道标签值，这里用post原因是为了前端传递涉及的服务列表body
	group.POST("/v1/instance/:instanceUUID/laneGroup/label", server.CsmHandler(c.GetLabelSelectorSet),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.PUT("/v1/instance/:instanceUUID/laneGroup/:laneGroupID/lane/:laneID", server.CsmHandler(c.ModifyLane),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.DELETE("/v1/instance/:instanceUUID/laneGroup/:laneGroupID/lane/:laneID", server.CsmHandler(c.DeleteLane),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.PUT("/v1/instance/:instanceUUID/laneGroup/:laneGroupID/baseLane/:laneID", server.CsmHandler(c.ModifyBaseLane),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.GET("/v1/instance/:instanceUUID/laneGroup/:laneGroupID/lanes", server.CsmHandler(c.GetLanes),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)

	group.POST("/v1/instance/:instanceUUID/laneGroup/:laneGroupID/lane/:laneID/route", server.CsmHandler(c.NewRouteRule),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.PUT("/v1/instance/:instanceUUID/laneGroup/:laneGroupID/lane/:laneID/route", server.CsmHandler(c.ModifyRouteRule),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.GET("/v1/instance/:instanceUUID/laneGroup/:laneGroupID/lane/:laneID/route", server.CsmHandler(c.GetRouteRule),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)

	group.DELETE("/v1/instance/:instanceUUID/laneGroup/:laneGroupID/lane/:laneID/route", server.CsmHandler(c.DeleteRouteRule),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.GET("/v1/instance/:instanceUUID/lane/serviceList", server.CsmHandler(c.GetLaneServiceList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)
	return group
}
