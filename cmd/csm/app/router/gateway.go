package router

import (
	"github.com/labstack/echo/v4"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type GatewayRouter struct{}

func (gatewayRouter *GatewayRouter) RegisterOpenAPI(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.POST("/v1/instance/:instanceUUID/gateway", server.CsmHandler(c.NewGateway),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
		middleware.FilterEmptyQueryParam(),
	)

	group.DELETE("/v1/instance/:instanceUUID/gateway/:gatewayUUID", server.CsmHandler(c.DeleteGateway),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.GET("/v1/instance/:instanceUUID/gatewayList", server.CsmHandler(c.GetGatewayList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.GET("/v1/instance/:instanceUUID/gateway/:gatewayUUID", server.CsmHandler(c.GetGatewayDetail),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.GET("/v1/instance/:instanceUUID/gateway/:gatewayUUID/blbList", server.CsmHandler(c.GetGatewayBlbList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.GET("/v1/instance/:instanceUUID/gateway/:gatewayUUID/domainList", server.CsmHandler(c.GetGatewayDomainList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.POST("/v1/instance/:instanceUUID/gateway/:gatewayUUID/domain", server.CsmHandler(c.AddGatewayDomain),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.DELETE("/v1/instance/:instanceUUID/gateway/:gatewayUUID/domain", server.CsmHandler(c.DeleteGatewayDomain),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.PUT("/v1/instance/:instanceUUID/gateway/:gatewayUUID/domain", server.CsmHandler(c.ModifyGatewayDomain),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.PUT("/v1/instance/:instanceUUID/gateway/:gatewayUUID/log", server.CsmHandler(c.ModifyGatewayBlsTask),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.PUT("/v1/instance/:instanceUUID/gateway/:gatewayUUID/hpa", server.CsmHandler(c.ModifyGatewayHPA),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.PUT("/v1/instance/:instanceUUID/gateway/:gatewayUUID/monitor", server.CsmHandler(c.ModifyGatewayMonitor),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.PUT("/v1/instance/:instanceUUID/gateway/:gatewayUUID/tlsAcc", server.CsmHandler(c.ModifyGatewayTLSAcceleration),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.PUT("/v1/instance/:instanceUUID/gateway/:gatewayUUID/resourceQuota", server.CsmHandler(c.ModifyGatewayResourceQuota),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.PUT("/v1/instance/:instanceUUID/gateway/:gatewayUUID/ingress/sync", server.CsmHandler(c.ModifyGatewayIngress),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)

	group.GET("/v1/instance/:instanceUUID/gateway/:gatewayUUID/ingress/sync", server.CsmHandler(c.GetGatewayIngress),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)
	group.POST("/v1/ingress/:clusterID", server.CsmHandler(c.CreateIngressInstance),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		//middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
		middleware.FilterEmptyQueryParam(),
	)
	group.DELETE("/v1/ingress/:clusterID", server.CsmHandler(c.DeleteIngressInstance),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		//middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyInstance),
	)
	group.GET("/v1/ingress/:clusterID", server.CsmHandler(c.GetIngressDetail),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		//middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadInstance),
	)
	group.POST("/v1/higress/:clusterID", server.CsmHandler(c.CreateHigressInstance),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		//middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
		middleware.FilterEmptyQueryParam(),
	)
	group.DELETE("/v1/higress/:clusterID", server.CsmHandler(c.DeleteHigressInstance),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		//middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyInstance),
	)
	// Add new endpoint for hosting Higress gateway
	group.POST("/v1/higress/hosting/:clusterID", server.CsmHandler(c.CreateHigressHostingInstance),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
	)

	// Add new endpoint for AI Gateway
	group.POST("/v1/aigateway", server.CsmHandler(c.CreateAIGateway),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	group.GET("/v1/aigateway/:InstanceId", server.CsmHandler(c.GetAIGatewayDetail),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		//middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadInstance),
	)

	group.GET("/v1/aigateway/list", server.CsmHandler(c.GetAllIngressInstances),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		//middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadInstance),
	)
	// Add new endpoint for deleting AI Gateway
	group.DELETE("/v1/aigateway/:InstanceId", server.CsmHandler(c.DeleteAIGateway),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// Add new endpoint for associating clusters with AI Gateway
	group.POST("/v1/aigateway/instance/:InstanceId/clusterList", server.CsmHandler(c.AssociateClusterWithAIGateway),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	group.GET("/v1/aigateway/instance/clusterList", server.CsmHandler(c.ListCceCluster),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.GET("/v1/aigateway/vpc/vpcList", server.CsmHandler(c.ListVPC),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	group.GET("/v1/aigateway/vpc/:vpcId/subnetList", server.CsmHandler(c.ListSubnet),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	group.GET("/v1/aigateway/vpc/:vpcId/securityGroupList", server.CsmHandler(c.ListSecurityGroup),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	return group
}
