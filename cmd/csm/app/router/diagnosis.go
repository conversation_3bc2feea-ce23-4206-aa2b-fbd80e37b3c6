package router

import (
	"github.com/labstack/echo/v4"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type DiagnosisRouter struct{}

// RegisterConsole 将 DiagnosisRouter 注册为控制台路由
func (DiagnosisRouter *DiagnosisRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.GET("/v1/instance/:instanceUUID/diagnosis/namespaceList", server.CsmHandler(c.NamespaceList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
	)

	group.GET("/v1/instance/:instanceUUID/diagnosis/:namespace/proxyStatusList", server.<PERSON><PERSON><PERSON><PERSON><PERSON>(c.ProxyStatusList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
	)

	group.GET("/v1/instance/:instanceUUID/diagnosis/proxyConfigList", server.CsmHandler(c.ProxyConfigList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
	)
	group.GET("/v1/instance/:instanceUUID/diagnosis/:namespace/exceptionList", server.CsmHandler(c.ExceptionList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
	)
	group.GET("/v1/instance/:instanceUUID/configDump", server.CsmHandler(c.ConfigDump),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
	)
	return group
}
