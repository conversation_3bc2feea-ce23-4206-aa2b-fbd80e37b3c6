package router

import (
	"github.com/labstack/echo/v4"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type TraceRouter struct{}

func (traceRouter *TraceRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.PUT("/v1/instance/:instanceUUID/trace", server.CsmHandler(c.UpdateTrace),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.GET("/v1/instance/:instanceUUID/trace", server.<PERSON>sm<PERSON>andler(c.GetTrace),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	return group
}
