package router

import (
	"github.com/labstack/echo/v4"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type MonitorRouter struct{}

func (cr *MonitorRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group = group.Group("/v1")

	group.GET("/monitor/instanceList", server.CsmHandler(c.GetMonitorInstances),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
		middleware.FilterEmptyQueryParam(),
	)

	group.GET("/instance/:instanceUUID/monitor", server.CsmHandler(c.GetMonitorInstanceDetail),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
		middleware.FilterEmptyQueryParam(),
	)

	group.PUT("/instance/:instanceUUID/monitor", server.CsmHandler(c.UpdateMonitor),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
	)

	// 先检测 cce 集群 CProm 是否安装了 agent
	group.GET("/instance/cpromagentcheck/monitor", server.CsmHandler(c.ClusterCPromAgentCheck),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
	)

	return group
}
