package router

import (
	"github.com/labstack/echo/v4"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type BlbRouter struct {
}

func (br *BlbRouter) RegisterOpenAPI(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.GET("/v1/instance/:instanceUUID/availableBlbList", server.CsmHandler(c.ListAvailableBlb),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyOther),
	)
	return group
}
