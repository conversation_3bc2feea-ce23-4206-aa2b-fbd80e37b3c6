package router

import (
	"github.com/labstack/echo/v4"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type RegisterInstancesRouter struct{}

func (registerInstanceRouter *RegisterInstancesRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.POST("/v1/registry", server.CsmHandler(c.NewRegisterCenterInstance),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.PUT("/v1/registry/:instanceId", server.CsmHandler(c.UpdateRegisterCenterInstance),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.DELETE("/v1/registry/:instanceId", server.CsmHandler(c.DeleteRegisterCenterInstance),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
	)

	group.GET("/v1/registries", server.CsmHandler(c.GetRegisterCenterInstanceList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)

	group.GET("/v1/registry/:instanceId", server.CsmHandler(c.GetRegisterInstanceById),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)

	group.GET("/v1/whiteList/check", server.CsmHandler(c.CheckWhiteList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion4Registry(),
		middleware.VerifyCseResourceOwnership(c.RegisterCenterService),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadGlobal),
	)
	return group
}
