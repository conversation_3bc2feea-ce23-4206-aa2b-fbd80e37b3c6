package router

import (
	"github.com/labstack/echo/v4"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type ClusterRouter struct{}

func (cr *ClusterRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group = group.Group("/v1")

	group.GET("/instance/:instanceUUID/clusterList", server.CsmHandler(c.GetManagedClusters),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadCluster),
	)

	group.GET("/instance/:instanceUUID/clusterList/candidate", server.CsmHandler(c.GetCandidateClusters),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCluster),
	)

	group.POST("/instance/:instanceUUID/clusterList", server.CsmHandler(c.AddClusters),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCluster),
	)

	group.DELETE("/instance/:instanceUUID/clusterList", server.CsmHandler(c.RemoveClusters),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCluster),
	)

	group.POST("/instance/:instanceUUID/clusterList/check", server.CsmHandler(c.CheckClusterList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadCluster),
	)

	return group
}

func (cr *ClusterRouter) RegisterOpenAPI(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.GET("/instances/:meshInstanceId/clusters", server.CsmHandler(c.GetManagedClustersForOpenAPI),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadCluster),
	)

	group.POST("/instances/:meshInstanceId/clusters", server.CsmHandler(c.AddClustersForOpenAPI),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCluster),
	)

	return group
}
