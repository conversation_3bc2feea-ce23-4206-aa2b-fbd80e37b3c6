package router

import (
	"github.com/labstack/echo/v4"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type AddonRouter struct{}

func (or *AddonRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group = group.Group("/v1")

	group.GET("/instance/:instanceUUID/addon", server.CsmHandler(c.ListAddons),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadInstance),
	)

	group.POST("/instance/:instanceUUID/addon", server.CsmHandler(c.Install),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadInstance),
	)

	group.DELETE("/instance/:instanceUUID/addon", server.CsmHandler(c.UnInstall),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadInstance),
	)

	return group
}
