package router

import (
	"github.com/labstack/echo/v4"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type SugarRouter struct{}

func (sugarRouter *SugarRouter) RegisterOpenAPI(c *core.APIServerCore, group *echo.Group) *echo.Group {

	group.GET("/v1/sugar/scale", server.CsmHandler(c.GetScale),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
	)

	group.GET("/v1/sugar/userList", server.CsmHandler(c.GetUserList),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
	)

	return group
}
