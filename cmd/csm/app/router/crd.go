package router

import (
	"github.com/labstack/echo/v4"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type CrdRouter struct{}

func (cr *CrdRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	// TODO: group 待优化
	group.GET("/v1/instance/:instanceUUID/crdList", server.CsmHandler(c.GetCrds),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadCrd),
	)

	group.GET("/v1/instance/:instanceUUID/:namespace/crd", server.CsmHandler(c.GetCrd),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadCrd),
	)

	group.POST("/v1/instance/:instanceUUID/crd", server.CsmHandler(c.BatchCrd),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCrd),
	)

	group.PUT("/v1/instance/:instanceUUID/:namespace/crd", server.CsmHandler(c.UpdateCrd),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCrd),
	)

	group.DELETE("/v1/instance/:instanceUUID/crd", server.CsmHandler(c.DeleteCrd),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCrd),
	)

	return group
}

func (cr *CrdRouter) RegisterOpenAPI(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.GET("/instances/:meshInstanceId/crds", server.CsmHandler(c.GetCrdsForOpenAPI),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadCrd),
	)

	group.POST("/instances/:meshInstanceId/crds", server.CsmHandler(c.CreateCrdsForOpenAPI),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCrd),
	)

	group.PUT("/instances/:meshInstanceId/crds", server.CsmHandler(c.UpdateCrdsForOpenAPI),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCrd),
	)

	group.DELETE("/instances/:meshInstanceId/crds", server.CsmHandler(c.DeleteCrdsForOpenAPI),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCrd),
	)
	group.POST("/ingress/:clusterId/crd", server.CsmHandler(c.CreateCrdsForBG),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		//middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCrd),
	)
	group.DELETE("/ingress/:clusterId/crd", server.CsmHandler(c.DeleteCrdsForBG),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		//middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCrd),
	)
	group.GET("/ingress/:clusterId/crd", server.CsmHandler(c.GetCrdsForBG),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		//middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadCrd),
	)
	group.PUT("/ingress/:clusterId/crd", server.CsmHandler(c.UpdateCrdsForBG),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		//middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyCrd),
	)

	return group
}
