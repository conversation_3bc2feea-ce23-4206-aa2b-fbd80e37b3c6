package router

import (
	"github.com/labstack/echo/v4"

	echoSwagger "github.com/swaggo/echo-swagger/v2"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
)

func RegisterRouter(c *core.APIServerCore) func(e *echo.Echo) {
	return func(e *echo.Echo) {
		e.GET("/swagger/*", echoSwagger.WrapHandler)

		// 服务网格实例接口 Group
		// TODO: 加个 v1，代表版本，方便以后升级
		// csm := e.Group("/api/logic/csm/v1")
		csm := e.Group("/api/logic/csm")
		csmV1 := e.Group("/api/csm/v1")
		cse := e.Group("/api/cse")
		aigw := e.Group("/api/aigw")

		lanesRouter := &LanesRouter{}
		lanesRouter.RegisterConsole(c, csm)

		overviewRouter := &OverviewRouter{}
		overviewRouter.RegisterConsole(c, csm)

		instanceRouter := &InstancesRouter{}
		instanceRouter.RegisterConsole(c, csm)
		instanceRouter.RegisterOpenAPI(c, csmV1)
		instanceRouter.RegisterInnerAPI(c, csm)

		servicesRouter := &ServicesRouter{}
		servicesRouter.RegisterOpenAPI(c, csm)

		crdRouter := &CrdRouter{}
		crdRouter.RegisterConsole(c, csm)
		crdRouter.RegisterOpenAPI(c, csmV1)

		clusterRouter := &ClusterRouter{}
		clusterRouter.RegisterConsole(c, csm)
		clusterRouter.RegisterOpenAPI(c, csmV1)

		whiteListRouter := &WhiteListRouter{}
		whiteListRouter.RegisterConsole(c, csm)

		namespaceRouter := &NamespaceRouter{}
		namespaceRouter.RegisterConsole(c, csm)

		sidecarRouter := &SidecarRouter{}
		sidecarRouter.RegisterConsole(c, csm)

		monitorRouter := &MonitorRouter{}
		monitorRouter.RegisterConsole(c, csm)

		vpcRouter := &VpcRouter{}
		vpcRouter.RegisterOpenAPI(c, csm)

		blbRouter := &BlbRouter{}
		blbRouter.RegisterOpenAPI(c, csm)

		gatewayRouter := &GatewayRouter{}
		gatewayRouter.RegisterOpenAPI(c, csm)

		aigwRouter := &AIGatewayRouter{}
		aigwRouter.RegisterAIGWOpenAPI(c, aigw)

		sugarRouter := &SugarRouter{}
		sugarRouter.RegisterOpenAPI(c, csm)

		addonRouter := &AddonRouter{}
		addonRouter.RegisterConsole(c, csm)

		diagnosisRouter := &DiagnosisRouter{}
		diagnosisRouter.RegisterConsole(c, csm)

		blsRouter := &BlsRouter{}
		blsRouter.RegisterConsole(c, csm)

		registerInstancesRouter := &RegisterInstancesRouter{}
		registerInstancesRouter.RegisterConsole(c, cse)

		registerInsServiceRouter := &RegisterInsServiceRouter{}
		registerInsServiceRouter.RegisterConsole(c, cse)

		traceRouter := &TraceRouter{}
		traceRouter.RegisterConsole(c, csm)
	}
}
