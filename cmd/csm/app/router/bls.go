package router

import (
	"github.com/labstack/echo/v4"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/router/permission"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type BlsRouter struct{}

// RegisterConsole 将 DiagnosisRouter 注册为控制台路由
func (BlsRouter *BlsRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.GET("/v1/instance/:instanceUUID/bls/agentCheck", server.CsmHandler(c.CSMAgentCheck),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
	)
	group.GET("/v1/cluster/:clusterId/bls/agentCheck", server.CsmHandler(c.ClusterAgentCheck),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
	)

	group.GET("/v1/bls/logStoreList", server.CsmHandler(c.LogStoreList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
	)

	group.GET("/v1/instance/:instanceUUID/bls/blsList", server.CsmHandler(c.BlsList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
	)
	group.DELETE("/v1/instance/:instanceUUID/bls/log", server.CsmHandler(c.BlsClose),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
		middleware.FilterEmptyQueryParam(),
	)
	group.POST("/v1/instance/:instanceUUID/bls/logTask", server.CsmHandler(c.BlsOpen),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ModifyGlobal),
		middleware.FilterEmptyQueryParam(),
	)
	return group
}
