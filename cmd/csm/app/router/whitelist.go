package router

import (
	"github.com/labstack/echo/v4"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type WhiteListRouter struct{}

func (whiteListRouter *WhiteListRouter) RegisterConsole(c *core.APIServerCore, group *echo.Group) *echo.Group {
	group.GET("/v1/whiteList/check", server.CsmHandler(c.IsExistInWhiteList),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
	)

	return group
}
