package internal

type InstanceStatus string

const (
	Running      InstanceStatus = "running"
	Changing     InstanceStatus = "changing"
	Deploying    InstanceStatus = "deploying"
	Removing     InstanceStatus = "removing"
	DeployFailed InstanceStatus = "deployFailed"
	Exception    InstanceStatus = "exception"
)

type InstancesView struct {
	GroupByRegion map[string]int         `json:"groupByRegion"`
	GroupByStatus map[InstanceStatus]int `json:"groupByStatus"`
}

type SidecarsView struct {
	InstanceId   string `json:"instanceId"`
	InstanceName string `json:"instanceName"`
	Num          int    `json:"num"`
}

type ServicesOverview struct {
	AllOf  int `json:"allOf"`
	AnyOf  int `json:"anyOf"`
	NoneOf int `json:"noneOf"`
}

type ClustersOverview struct {
	RunningNum int `json:"runningNum"`
	Total      int `json:"total"`
}

type InstanceDetailView struct {
	InstanceId   string         `json:"instanceId"`
	InstanceName string         `json:"instanceName"`
	Status       InstanceStatus `json:"status"`
	Region       string         `json:"region"`
	SidecarNum   int            `json:"sidecarNum"`

	ServicesOverview ServicesOverview `json:"servicesOverview"`
	ClustersOverview ClustersOverview `json:"clustersOverview"`
}
