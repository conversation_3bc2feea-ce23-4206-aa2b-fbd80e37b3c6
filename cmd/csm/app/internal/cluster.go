package internal

import (
	"fmt"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

const (
	EksSZRegion  = "sz"
	EksGZRegion  = "gz"
	EksBJRegion  = "bj"
	EKsBDRegion  = "bd"
	EksFWHRegion = "fwh"
	EksHKGRegion = "hkg"

	CceSURegion  = "su"
	CceGZRegion  = "gz"
	CceBJRegion  = "bj"
	CceBDRegion  = "bd"
	CceFWHRegion = "fwh"
	CceHKGRegion = "hkg"
)

// CceCluster 表示 cce 集群
type CceCluster struct {
	// ClusterName 集群名称
	ClusterName string `json:"clusterName"`

	// ClusterId 集群 id
	ClusterId string `json:"clusterId"`

	Region string `json:"region"`
	// ClusterVersion 集群版本
	//ClusterVersion string `json:"clusterVersion"`

	// Admin 是否具有管理员权限
	//Admin bool `json:"admin"`

	// IstioInstalledStatus 是否已经安装 istio 集群
	//IstioInstalledSttus bool `json:"istioInstalledStatus"`
	Status  string `json:"status"`
	VpcId   string `json:"vpcId"`
	VpcCidr string `json:"vpcCidr"`
}

// ToMeshCluster 转换成 cluster view
func ToMeshCluster(meshCluster *meta.MeshCluster) (*CceCluster, error) {
	cceCluster := &CceCluster{}
	cceCluster.ClusterId = meshCluster.ClusterUuid
	cceCluster.ClusterName = meshCluster.ClusterName
	cceCluster.Status = "RUNNING"
	cceCluster.Region = meshCluster.Region
	//cceCluster.ClusterVersion = meshCluster.Version
	//cceCluster.Admin = meshCluster.Admin
	//cceCluster.IstioInstalledStatus = meshCluster.IstioInstalledStatus
	cceCluster.VpcId = meshCluster.VpcId
	cceCluster.VpcCidr = meshCluster.VpcCidr
	return cceCluster, nil
}

// AdaptClusterRegionForCNAP The region on the eks is inconsistent with the cce
func AdaptClusterRegionForCNAP(ctx context.CsmContext, eksRegion string) (string, error) {
	switch eksRegion {
	case EksHKGRegion:
		return CceHKGRegion, nil
	case EksFWHRegion:
		return CceFWHRegion, nil
	case EKsBDRegion:
		return CceBDRegion, nil
	case EksBJRegion:
		return CceBJRegion, nil
	case EksSZRegion:
		return CceSURegion, nil
	case EksGZRegion:
		return CceGZRegion, nil
	default:
		err := fmt.Errorf("invalid eksRegion: %s", eksRegion)
		ctx.CsmLogger().Errorf(err.Error())
		return "", err
	}
}
