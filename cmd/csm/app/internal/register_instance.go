package internal

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	ctxContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type RegisterInstance struct {
	InstanceId        string          `json:"instanceId"`
	Name              string          `json:"name"`
	Region            string          `json:"region"`
	NetworkConfig     RegisterNetwork `json:"networkConfig"`
	MonitorEnable     bool            `json:"monitorEnable"`
	AccountId         string          `json:"accountId"`
	MonitorInstanceId string          `json:"monitorInstanceId"`
	MonitorToken      string          `json:"monitorToken"`
	EsgId             string          `json:"enterpriseSecurityGroupId"`
}

type RegisterNetwork struct {
	VpcId    string `json:"vpcId"`
	SubnetId string `json:"subnetId"`
}

func (registerIns *RegisterInstance) ToRegisterInstancesModel(ctx ctxContext.CsmContext) (*meta.RegisterInstance, error) {

	return &meta.RegisterInstance{
		AccountId:         registerIns.AccountId,
		InstanceId:        registerIns.InstanceId,
		InstanceName:      registerIns.Name,
		Region:            registerIns.Region,
		VpcId:             registerIns.NetworkConfig.VpcId,
		SubnetId:          registerIns.NetworkConfig.SubnetId,
		MonitorEnabled:    BoolToInt(registerIns.MonitorEnable),
		MonitorInstanceId: registerIns.MonitorInstanceId,
	}, nil
}

func BoolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

type RegisterService struct {
	Id                  string            `json:"id"`
	RegisterInstanceID  string            `json:"instanceId"`
	Name                string            `json:"name"`
	Namespace           string            `json:"namespace"`
	Business            string            `json:"business"`
	Department          string            `json:"department"`
	CreateTime          string            `json:"createTime"`
	UpdateTime          string            `json:"updateTime"`
	HealthCount         int               `json:"healthCount"`
	TotalCount          int               `json:"totalCount"`
	ServiceInstanceList []ServiceInstance `json:"serviceInstances"`
}

type RegisterNamespace struct {
	Name                string `json:"name"`
	Comment             string `json:"comment"`
	CreateTime          string `json:"createTime"`
	UpdateTime          string `json:"updateTime"`
	ServiceCount        int    `json:"serviceCount"`
	InstanceCount       int    `json:"instanceCount"`
	HealthInstanceCount int    `json:"healthInstanceCount"`
}

type RegisterNamespaceReq struct {
	Namespaces []RegisterNamespace `json:"namespaces"`
}

type ServiceInstance struct {
	ServiceName       string            `json:"serviceName"`
	Namespace         string            `json:"namespace"`
	ServiceInstanceID string            `json:"serviceInstanceId"`
	Host              string            `json:"host"`
	Port              int               `json:"port"`
	Weight            int               `json:"weight"`
	HealthStatus      *bool             `json:"healthStatus,omitempty"`
	IsolateEnable     bool              `json:"isolateEnable"`
	HealthCheckEnable bool              `json:"healthCheckEnable"`
	TTL               int               `json:"ttl"`
	CreateTime        string            `json:"createTime"`
	UpdateTime        string            `json:"updateTime"`
	Metadata          map[string]string `json:"metadata"`
	LastHeartbeatTime string            `json:"lastHeartbeatTime"`
}
