package internal

import (
	"fmt"
	"strconv"
	"strings"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

func GetPageParam(ctx context.CsmContext, orderParam map[string]string) *meta.Page {
	// 排序初始化
	page := &meta.Page{}
	page.OrderBy = ctx.QueryParam("orderBy")
	page.Order = ctx.QueryParam("order")
	page.PageNoStr = ctx.QueryParam("pageNo")
	page.PageSizeStr = ctx.QueryParam("pageSize")
	orderBy := "created_at"
	// 排序字段映射
	for front, backend := range orderParam {
		if page.OrderBy == front {
			orderBy = backend
			break
		}
	}
	page.OrderBy = orderBy

	if strings.ToUpper(page.Order) == "ASC" {
		page.Order = strings.ToUpper(page.Order)
	} else {
		page.Order = "DESC"
	}

	// 分页
	pageNo, err := strconv.ParseInt(page.PageNoStr, 10, 64)
	if err != nil {
		pageNo = 1
	}
	pageNo = util.MaxInt64(1, pageNo)

	pageSize, err := strconv.ParseInt(page.PageSizeStr, 10, 64)
	if err != nil {
		pageSize = 10
	}
	pageSize = util.MaxInt64(1, pageSize)
	pageSize = util.MinInt64(10000, pageSize)

	page.PageNo = pageNo
	page.PageSize = pageSize
	page.PageNoStr = strconv.FormatInt(pageNo, 10)
	page.PageSizeStr = strconv.FormatInt(pageSize, 10)
	return page
}

func GetRequestRegion(ctx context.CsmContext) (string, error) {
	region := ctx.QueryParam(reg.QueryRegion)
	if len(region) == 0 {
		ctx.CsmLogger().Infof("will get region from Header %s", constants.RegionHeaderKey)
		region = ctx.Get(reg.ContextRegion).(string)
	}
	if len(region) == 0 {
		return "", csmErr.NewMissingParametersException(fmt.Sprintf("Missing Header %s", constants.RegionHeaderKey))
	}
	return region, nil
}
