package internal

import (
	"github.com/asaskevich/govalidator"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
)

// 创建网关请求参数
type GatewayParam struct {
	InstanceUUID  string         `param:"instanceUUID" valid:"required"`
	BasicConfig   *BasicConfig   `json:"basicConfig" valid:"required"`
	NetworkConfig *NetworkConfig `json:"networkConfig" valid:"required"`
}

type BasicConfig struct {
	GatewayName   string   `json:"gatewayName" valid:"required"`
	DeployMode    string   `json:"deployMode" valid:"required"`
	GatewayType   string   `json:"gatewayType" valid:"required"`
	ResourceQuota string   `json:"resourceQuota" valid:"required"`
	Replicas      int16    `json:"replicas" valid:"range(1|3),required"`
	HPA           *HPA     `json:"hpa"`
	Log           *Log     `json:"log"`
	Monitor       *Monitor `json:"monitor"`
	TLSAcc        *TLSAcc  `json:"tlsAcc"`
}

// HPA扩缩容
type HPA struct {
	Enabled     bool  `json:"enabled"`
	MinReplicas int32 `json:"minReplicas" valid:"range(1|3)"`
	MaxReplicas int32 `json:"maxReplicas" valid:"range(1|3)"`
}

// Log BLS日志服务
type Log struct {
	Enabled bool   `json:"enabled"`
	Type    string `json:"type"`
	LogFile string `json:"logFile"`
}

// TLSAcc TLS加速
type TLSAcc struct {
	Enabled bool `json:"enabled"`
}

type NetworkConfig struct {
	BlbID                string                `json:"blbId" valid:"required"`
	NetworkType          *NetworkType          `json:"networkType" valid:"required"`
	SecurityGroupID      string                `json:"securityGroupId"`
	ElasticPublicNetwork *ElasticPublicNetwork `json:"elasticPublicNetwork"`
}

// ToGatewayModel 生成Gateway配置对象
func (gw *GatewayParam) ToGatewayModel() (*meta.GatewayModel, error) {
	if _, validErr := govalidator.ValidateStruct(gw); validErr != nil {
		return nil, validErr
	}
	// 适配CProm监控
	enabled := false
	monitorInstanceID := ""
	monitorRegion := ""
	if gw.BasicConfig.Monitor != nil && gw.BasicConfig.Monitor.Enabled &&
		len(gw.BasicConfig.Monitor.Instances) == 1 {
		// TODO 后期统一修改monitor.Instances结构，监控实例只能有且仅有一个。
		enabled = true
		monitorInstanceID = gw.BasicConfig.Monitor.Instances[0].Id
		monitorRegion = gw.BasicConfig.Monitor.Instances[0].Region
	}

	gatewayModel := &meta.GatewayModel{
		InstanceUUID:      gw.InstanceUUID,
		IstioVersion:      "",
		ClusterName:       "",
		ClusterUUID:       "",
		GatewayUUID:       "",
		GatewayName:       gw.BasicConfig.GatewayName,
		Region:            "",
		AccountId:         "",
		Namespace:         "",
		ResourceQuota:     gw.BasicConfig.ResourceQuota,
		VpcNetworkId:      gw.NetworkConfig.NetworkType.VpcNetworkId,
		SubnetId:          gw.NetworkConfig.NetworkType.SubnetId,
		DeployMode:        gw.BasicConfig.DeployMode,
		GatewayType:       gw.BasicConfig.GatewayType,
		Replicas:          gw.BasicConfig.Replicas,
		MonitorEnabled:    &enabled,
		MonitorAgentID:    "",
		MonitorInstanceID: monitorInstanceID,
		MonitorJobID:      "",
		MonitorRegion:     monitorRegion,
		Deleted:           csm.Int(0),
	}

	return gatewayModel, nil
}

// ToHpaConf 生成HPA配置对象
func (gw *GatewayParam) ToHpaConf(gwModel *meta.GatewayModel) (*meta.HpaConf, error) {
	if _, validErr := govalidator.ValidateStruct(gw); validErr != nil {
		return nil, validErr
	}

	hpaConf := &meta.HpaConf{
		Enabled:     gw.BasicConfig.HPA.Enabled,
		MinReplicas: gw.BasicConfig.HPA.MinReplicas,
		MaxReplicas: gw.BasicConfig.HPA.MaxReplicas,
		Namespace:   gwModel.Namespace,
		GatewayUUID: gwModel.GatewayUUID,
	}
	return hpaConf, nil
}

// ToBlbConf 生成BLB配置对象
func (gw *GatewayParam) ToBlbConf() (*meta.BlbConf, error) {
	if _, validErr := govalidator.ValidateStruct(gw); validErr != nil {
		return nil, validErr
	}

	blbConf := &meta.BlbConf{
		BlbID: gw.NetworkConfig.BlbID,
		EipConf: &meta.EipConf{
			Enabled: gw.NetworkConfig.ElasticPublicNetwork.Enabled,
			Type:    meta.ElasticPublicNetworkType(gw.NetworkConfig.ElasticPublicNetwork.PublicNetworkType),
			ID:      gw.NetworkConfig.ElasticPublicNetwork.Id,
			IP:      gw.NetworkConfig.ElasticPublicNetwork.Ip,
		},
	}
	return blbConf, nil
}

// ToLogConf 生成BLS日志配置对象
func (gw *GatewayParam) ToLogConf(gwModel *meta.GatewayModel) (*meta.LogConf, error) {
	if _, validErr := govalidator.ValidateStruct(gw); validErr != nil {
		return nil, validErr
	}

	logConf := &meta.LogConf{
		Enabled:     gw.BasicConfig.Log.Enabled,
		Type:        gw.BasicConfig.Log.Type,
		LogFile:     gw.BasicConfig.Log.LogFile,
		Namespace:   gwModel.Namespace,
		GatewayUUID: gwModel.GatewayUUID,
		AccountID:   gwModel.AccountId,
	}
	return logConf, nil
}

// ToTLSAccConf 生成TLS加速配置对象
func (gw *GatewayParam) ToTLSAccConf() (*meta.TLSAccConf, error) {
	tlsAccConf := &meta.TLSAccConf{
		Enabled: gw.BasicConfig.TLSAcc.Enabled,
	}
	return tlsAccConf, nil
}
