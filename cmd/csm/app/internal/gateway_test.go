package internal

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
)

func buildGatewayParam() *GatewayParam {
	return &GatewayParam{
		InstanceUUID: "inst-1",
		BasicConfig: &BasicConfig{
			GatewayName:   "gw1",
			DeployMode:    "hosting",
			GatewayType:   "ingress",
			ResourceQuota: "2c4g",
			Replicas:      3,
			HPA: &HPA{
				Enabled:     true,
				MinReplicas: 1,
				MaxReplicas: 3,
			},
			Log: &Log{
				Enabled: true,
				Type:    "BLS",
				LogFile: "a",
			},
		},
		NetworkConfig: &NetworkConfig{
			BlbID: "blb-1",
			NetworkType: &NetworkType{
				VpcNetworkId: "vpc-1",
				SubnetId:     "sub-1",
			},
			SecurityGroupID: "aaa",
			ElasticPublicNetwork: &ElasticPublicNetwork{
				Enabled:           true,
				Id:                "abc",
				Ip:                "*******",
				PublicNetworkType: "BIND",
			},
		},
	}
}

func buildGatewayModel() *meta.GatewayModel {
	return &meta.GatewayModel{
		Namespace:   "ns-1",
		GatewayUUID: "gw-1",
		AccountId:   "acc-1",
	}
}

func TestToGatewayModel(t *testing.T) {
	expectGatewayDB := &meta.GatewayModel{
		InstanceUUID:   "inst-1",
		IstioVersion:   "",
		ClusterName:    "",
		ClusterUUID:    "",
		GatewayUUID:    "",
		GatewayName:    "gw1",
		MonitorEnabled: csm.Bool(false),
		Region:         "",
		AccountId:      "",
		Namespace:      "",
		ResourceQuota:  "2c4g",
		VpcNetworkId:   "vpc-1",
		SubnetId:       "sub-1",
		DeployMode:     "hosting",
		GatewayType:    "ingress",
		Replicas:       3,
		Deleted:        csm.Int(0),
	}
	gatewayParam := buildGatewayParam()
	testGatewayDB, err := gatewayParam.ToGatewayModel()
	assert.Nil(t, err)
	assert.Equal(t, *testGatewayDB, *expectGatewayDB)

	// with CProm
	monitor := &Monitor{
		Enabled: true,
		Instances: []MonitorInstance{
			{
				Id:     "cprom-id",
				Region: "gz",
			},
		},
	}
	gatewayParam.BasicConfig.Monitor = monitor
	expectGatewayDB.MonitorInstanceID = "cprom-id"
	expectGatewayDB.MonitorRegion = "gz"
	expectGatewayDB.MonitorEnabled = csm.Bool(true)
	testGatewayDB, err = gatewayParam.ToGatewayModel()
	assert.Nil(t, err)
	assert.Equal(t, *testGatewayDB, *expectGatewayDB)
}

func TestGatewayParam_ToHpaConf(t *testing.T) {
	expectHpaConf := &meta.HpaConf{
		Enabled:     true,
		MinReplicas: 1,
		MaxReplicas: 3,
		Namespace:   "ns-1",
		GatewayUUID: "gw-1",
	}
	gatewayParam := buildGatewayParam()
	testHpaConf, err := gatewayParam.ToHpaConf(buildGatewayModel())
	assert.Nil(t, err)
	assert.Equal(t, *testHpaConf, *expectHpaConf)
}

func TestGatewayParam_ToBlbConf(t *testing.T) {
	expectBlbConf := &meta.BlbConf{
		BlbID: "blb-1",
		EipConf: &meta.EipConf{
			Enabled: true,
			Type:    "BIND",
			ID:      "abc",
			IP:      "*******",
		},
	}
	gatewayParam := buildGatewayParam()
	testBlbConf, err := gatewayParam.ToBlbConf()
	assert.Nil(t, err)
	assert.Equal(t, *testBlbConf, *expectBlbConf)
}

func TestGatewayParam_ToLogConf(t *testing.T) {
	expectLogConf := &meta.LogConf{
		Enabled:     true,
		Type:        "BLS",
		LogFile:     "a",
		Namespace:   "ns-1",
		GatewayUUID: "gw-1",
		AccountID:   "acc-1",
	}
	gatewayParam := buildGatewayParam()
	testBlbConf, err := gatewayParam.ToLogConf(buildGatewayModel())
	assert.Nil(t, err)
	assert.Equal(t, *testBlbConf, *expectLogConf)
}
