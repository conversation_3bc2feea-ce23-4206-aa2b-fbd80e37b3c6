package internal

import (
	"testing"

	"github.com/magiconair/properties/assert"

	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	mockCtx, _ = ctxCsm.NewCsmContextMock()
)

func TestAdaptClusterRegionForCNAP(t *testing.T) {
	eksRegion := "sz"
	res, _ := AdaptClusterRegionForCNAP(mockCtx, eksRegion)
	assert.Equal(t, res, CceSURegion)

	eksRegion = "gz"
	res, _ = AdaptClusterRegionForCNAP(mockCtx, eksRegion)
	assert.Equal(t, res, CceGZRegion)

	eksRegion = "bj"
	res, _ = AdaptClusterRegionForCNAP(mockCtx, eksRegion)
	assert.Equal(t, res, CceBJRegion)

	eksRegion = "bd"
	res, _ = AdaptClusterRegionForCNAP(mockCtx, eksRegion)
	assert.Equal(t, res, CceBDRegion)

	eksRegion = "fwh"
	res, _ = AdaptClusterRegionForCNAP(mockCtx, eksRegion)
	assert.Equal(t, res, CceFWHRegion)

	eksRegion = "invalid"
	res, _ = AdaptClusterRegionForCNAP(mockCtx, eksRegion)
	assert.Equal(t, res, "")
}
