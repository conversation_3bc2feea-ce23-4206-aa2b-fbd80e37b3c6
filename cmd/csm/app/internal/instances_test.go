package internal

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/api/v1/cnap"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
)

var (
	instanceType                      = "standalone"
	instanceRegion                    = "bj"
	instanceServiceMeshInstanceName   = "istio-test-01"
	instanceIstioVersion              = "1.13.2"
	instanceInstallationClusterId     = "xxxx"
	instanceInstallationClusterName   = "xxxx-test"
	instanceInstallationAccountId     = "1"
	instanceInstallationInstancesUUID = "xxxxxxx"
	enabled                           = true
	matchLabels                       = map[string]string{"user": "test01"}
	labelsStr                         = "{'user': 'test01'}"
	istioInstallNamespace             = "istio-system"
	instanceManageScope               = "cluster"
)

func buildMetaInstance() *meta.Instances {
	return &meta.Instances{
		InstanceUUID:             instanceInstallationInstancesUUID,
		InstanceName:             instanceServiceMeshInstanceName,
		InstanceType:             instanceType,
		IstioVersion:             instanceIstioVersion,
		Region:                   instanceRegion,
		AccountId:                instanceInstallationAccountId,
		DiscoverySelectorEnabled: csm.Bool(enabled),
		DiscoverySelectorLabels:  labelsStr,
		IstioInstallNamespace:    istioInstallNamespace,
		InstanceManageScope:      meta.InstanceManageScopeToString(meta.InstanceManageClusterScope),
		TraceEnabled:             csm.Bool(enabled),
	}
}

func buildInstance() *Instances {
	return &Instances{
		Type:                    instanceType,
		Region:                  instanceRegion,
		ServiceMeshInstanceName: instanceServiceMeshInstanceName,
		IstioVersion:            instanceIstioVersion,
		InstallationClusterId:   instanceInstallationClusterId,
		InstallationClusterName: instanceInstallationClusterName,
		AccountId:               instanceInstallationAccountId,
		InstancesUUID:           instanceInstallationInstancesUUID,
		DiscoverySelector: &DiscoverySelector{
			Enabled:     enabled,
			MatchLabels: matchLabels,
		},
		NetworkType: &NetworkType{
			VpcNetworkId: "xxx",
			SubnetId:     "xxx",
		},
		ApiServerEip: true,
		TraceInfo: &TraceInfo{
			TraceEnabled: enabled,
			SamplingRate: 100,
			Service:      "Jaeger/Zipkin",
			Address:      "jaeger-collector.istio-system:9411",
		},
	}
}

func TestToInstancesModel(t *testing.T) {
	instances := buildInstance()
	instancesModel, err := instances.ToInstancesModel(mockCtx)
	metaInstance := buildMetaInstance()

	assert.Nil(t, err)
	assert.Equal(t, instancesModel.InstanceName, metaInstance.InstanceName)
}

func TestToClusterModel(t *testing.T) {
	instance := buildInstance()
	clusterModel, err := instance.ToClusterModel()
	assert.Nil(t, err)
	assert.Equal(t, clusterModel.ClusterName, instance.InstallationClusterName)
}

func TestFromRequest(t *testing.T) {
	instance := &Instances{}
	req := &cnap.Instance{
		Type:         instanceType,
		Scope:        instanceManageScope,
		Name:         instanceServiceMeshInstanceName,
		IstioVersion: instanceIstioVersion,
		ClusterId:    instanceInstallationClusterId,
		DiscoverySelector: &cnap.DiscoverySelector{
			Enabled:     enabled,
			MatchLabels: matchLabels,
		},
		TraceInfo: &cnap.TraceInfo{
			TraceEnabled: enabled,
			SamplingRate: 100,
			Service:      "Jaeger/Zipkin",
			Address:      "jaeger-collector.istio-system:9411",
		},
	}
	instance.FromRequest(req)
	assert.Equal(t, instance.Type, req.Type)
	assert.Equal(t, instance.Scope, req.Scope)
	assert.Equal(t, instance.ServiceMeshInstanceName, req.Name)
	assert.Equal(t, instance.IstioVersion, req.IstioVersion)
	assert.Equal(t, instance.InstallationClusterId, req.ClusterId)
	assert.Equal(t, instance.DiscoverySelector.Enabled, req.DiscoverySelector.Enabled)
	assert.Equal(t, instance.DiscoverySelector.MatchLabels, req.DiscoverySelector.MatchLabels)
}
