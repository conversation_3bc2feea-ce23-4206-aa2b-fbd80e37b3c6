package internal

import (
	"encoding/json"
	"strings"

	"github.com/asaskevich/govalidator"
	"github.com/jinzhu/copier"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/api/v1/cnap"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	ctxContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	service_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

// TODO: 后续把该文件内容平移到 internal/csm/instance/instance.go
type Instances struct {
	Type                    string             `json:"type"`
	Region                  string             `json:"region"`
	ServiceMeshInstanceName string             `json:"serviceMeshInstanceName"`
	IstioVersion            string             `json:"istioVersion"`
	InstallationClusterId   string             `json:"installationClusterId"`
	InstallationClusterName string             `json:"installationClusterName"`
	AccountId               string             `json:"accountId"`
	InstancesUUID           string             `json:"serviceMeshInstanceId"`
	DiscoverySelector       *DiscoverySelector `json:"discoverySelector"`
	Monitor                 *Monitor           `json:"monitor"`
	Scope                   string             `json:"scope"`
	// NetworkType 托管集群 vpc 网络
	NetworkType *NetworkType `json:"networkType"`
	// SecurityGroupId 托管集群安全组
	SecurityGroupId string `json:"securityGroupId"`
	// ElasticPublicNetwork 托管集群公网访问
	ElasticPublicNetwork *ElasticPublicNetwork `json:"elasticPublicNetwork"`
	// MultiProtocol 支持多协议
	MultiProtocol bool `json:"multiProtocol"`
	Bls           *Bls `json:"bls"`
	// 托管网格api server 是否绑定用户eip
	ApiServerEip bool `json:"ApiServerEip"`
	// ConfigCluster 托管网格部署模式，配置集群是external还是第一个remote集群
	ConfigCluster string `json:"configCluster"`
	// TraceInfo 链路追踪相关参数
	TraceInfo *TraceInfo `json:"traceInfo"`
}

// NetworkType contains the attribute of vpc
type NetworkType struct {
	VpcNetworkId string `json:"vpcNetworkId"`
	SubnetId     string `json:"subnetId"`
}

// ElasticPublicNetwork contains
type ElasticPublicNetwork struct {
	Enabled           bool                     `json:"enabled"`
	Id                string                   `json:"id"`
	Ip                string                   `json:"ip"`
	PublicNetworkType ElasticPublicNetworkType `json:"type"`
}

type ElasticPublicNetworkType string

const (
	BIND ElasticPublicNetworkType = "BIND"
	BUY  ElasticPublicNetworkType = "BUY"
)

// DiscoverySelector contains parameters for implementing selective service discovery
type DiscoverySelector struct {
	Enabled     bool              `json:"enabled"`
	MatchLabels map[string]string `json:"matchLabels"`
}

type Monitor struct {
	Enabled   bool              `json:"enabled"`
	Instances []MonitorInstance `json:"instances"`
}

type MonitorInstance struct {
	Region string `json:"region"`
	Id     string `json:"id"`
}

type Bls struct {
	Enabled   bool        `json:"enabled"`
	Instances BlsInstance `json:"instances"`
}
type BlsInstance struct {
	Region string `json:"region"`
	Name   string `json:"name"`
}

// TraceInfo 链路追踪信息
type TraceInfo struct {
	TraceEnabled bool    `json:"traceEnabled"`
	SamplingRate float64 `json:"samplingRate"`
	Service      string  `json:"service"`
	Address      string  `json:"address"`
}

// ToInstancesModel 将Instances实例转换为meta.Instances模型，并返回。如果发生错误则返回error
func (instances *Instances) ToInstancesModel(ctx ctxContext.CsmContext) (*meta.Instances, error) {
	if _, validErr := govalidator.ValidateStruct(instances); validErr != nil {
		return nil, validErr
	}
	var discoverySelectorLabels string
	var discoverySelectorEnabled bool
	istioInstallNamespace := constants.IstioNamespace
	if instances.CheckDiscoverySelector() {
		ctx.CsmLogger().Infof("discoverySelector enabled %q", instances.DiscoverySelector)
		labelsStr, marErr := json.Marshal(instances.DiscoverySelector.MatchLabels)
		if marErr != nil {
			return &meta.Instances{}, marErr
		}
		discoverySelectorEnabled = true
		discoverySelectorLabels = string(labelsStr)
	}
	if strings.EqualFold(instances.Scope, string(meta.InstanceManageClusterScope)) {
		istioInstallNamespace = constants.IstioNamespace
	} else {
		istioInstallNamespace = util.GetNamespaceWithCsmInstanceId(istioInstallNamespace, instances.InstancesUUID, "-")
	}
	ctx.CsmLogger().Infof("the install namespace=%s", istioInstallNamespace)
	var publicEnabled bool
	var vpcNetworkId, subnetId string
	if instances.Type == string(version.HostingVersionType) {
		if instances.ElasticPublicNetwork == nil {
			publicEnabled = false
		} else {
			publicEnabled = instances.ElasticPublicNetwork.Enabled
		}
		if instances.NetworkType == nil {
			vpcNetworkId = ""
			subnetId = ""
		} else {
			vpcNetworkId = instances.NetworkType.VpcNetworkId
			subnetId = instances.NetworkType.SubnetId
		}
	}

	multiProtocolEnabled := instances.MultiProtocol

	instance := &meta.Instances{
		InstanceName:             instances.ServiceMeshInstanceName,
		InstanceType:             instances.Type,
		IstioVersion:             instances.IstioVersion,
		Region:                   instances.Region,
		AccountId:                instances.AccountId,
		InstanceUUID:             instances.InstancesUUID,
		DiscoverySelectorLabels:  discoverySelectorLabels,
		DiscoverySelectorEnabled: &discoverySelectorEnabled,
		// 是否开启托管服务网格 istiod 公网访问
		PublicEnabled: &publicEnabled,
		MonitorEnabled: csm.Bool(func() bool {
			if instances.Monitor == nil {
				return false
			}
			return instances.Monitor.Enabled
		}()),
		VpcNetworkId:          vpcNetworkId,
		SubnetId:              subnetId,
		IstioInstallNamespace: istioInstallNamespace,
		InstanceManageScope:   instances.Scope,
		MultiProtocolEnabled:  csm.Bool(multiProtocolEnabled),
		Metadata:              "",
		Status:                "",
		Deleted:               csm.Int(0),
		BlsEnabled: csm.Bool(func() bool {
			if instances.Bls == nil {
				return false
			}
			return instances.Bls.Enabled
		}()),
		APIServerEip:  csm.Bool(instances.ApiServerEip),
		ConfigCluster: instances.ConfigCluster,
		TraceEnabled:  &instances.TraceInfo.TraceEnabled,
	}
	return instance, nil
}

func (instances *Instances) ToClusterModel() (*meta.Cluster, error) {
	istioInstallNamespace := constants.IstioNamespace
	if strings.EqualFold(instances.Scope, string(meta.InstanceManageNamespaceScope)) {
		istioInstallNamespace = util.GetNamespaceWithCsmInstanceId(istioInstallNamespace, instances.InstancesUUID, "-")
	}
	// 解析当前 region 的 CProm 实例
	// 注意：当前产品设计不完整，实现方案为临时方案
	var monitorInstanceId string
	var monitorRegion string
	monitor := instances.Monitor
	if monitor != nil && monitor.Enabled {
		// 检查 CProm 实例，当前同 region 只允许一个 CProm 实例
		var flag bool
		for _, mi := range monitor.Instances {
			if strings.EqualFold(mi.Region, instances.Region) {
				if flag {
					flag = false
					break
				}
				flag = true
				monitorInstanceId = mi.Id
				monitorRegion = mi.Region
			}
		}

		if !flag {
			return nil, csmErr.NewInvalidParameterInputValueException("Please specify monitor instance and not duplicate with same region.")
		}
	}

	cluster := &meta.Cluster{
		InstanceUUID:          instances.InstancesUUID,
		ClusterUUID:           instances.InstallationClusterId,
		ClusterName:           instances.InstallationClusterName,
		IstioInstallNamespace: istioInstallNamespace,
		MonitorInstanceId:     monitorInstanceId,
		MonitorRegion:         monitorRegion,
		MonitorJobIds:         "",
		Region:                instances.Region,
		AccountId:             instances.AccountId,
		Deleted:               csm.Int(0),
		ConnectionState:       "",
	}
	return cluster, nil
}

// CheckDiscoverySelector checks selective service discovery
func (instances *Instances) CheckDiscoverySelector() bool {
	discoverySelector := instances.DiscoverySelector
	if discoverySelector == nil {
		return false
	}
	return len(discoverySelector.MatchLabels) > 0 && discoverySelector.Enabled
}

func (discoverySelector *DiscoverySelector) ToServiceDiscoverySelector() *service_meta.DiscoverySelector {
	return &service_meta.DiscoverySelector{
		Enabled:     discoverySelector.Enabled,
		MatchLabels: discoverySelector.MatchLabels,
	}
}

func (instances *Instances) ToCreateMeshRequest(ctx ctxContext.CsmContext) (*service_meta.CreateMeshRequest, error) {
	instanceModel, err := instances.ToInstancesModel(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("ToInstancesModel failed: %v", err)
		return nil, err
	}
	clusterModel, err := instances.ToClusterModel()
	if err != nil {
		ctx.CsmLogger().Errorf("ToClusterModel failed: %v", err)
		return nil, err
	}
	clusterModel.ClusterType = string(meta.ClusterTypeExternal)
	createMeshRequest := &service_meta.CreateMeshRequest{
		InstancesModel: instanceModel,
		ClusterModel:   clusterModel,
	}
	serviceInstances := &service_meta.Instances{}
	err = copier.Copy(serviceInstances, instances)
	if err != nil {
		return nil, err
	}
	createMeshRequest.Instances = serviceInstances
	return createMeshRequest, nil
}

// ToDeleteMeshRequest 初始化删除参数
func ToDeleteMeshRequest(instanceUUID string) (request *service_meta.DeleteMeshRequest) {
	return &service_meta.DeleteMeshRequest{
		InstanceUUID:             instanceUUID,
		IsReleaseControlPlaneBlb: false,
		IsReleaseEip:             false,
	}
}

func (instances *Instances) FromRequest(req *cnap.Instance) {
	instances.Type = req.Type
	instances.ServiceMeshInstanceName = req.Name
	instances.IstioVersion = req.IstioVersion
	instances.InstallationClusterId = req.ClusterId
	instances.Scope = req.Scope
	instances.DiscoverySelector = &DiscoverySelector{
		Enabled:     req.DiscoverySelector.Enabled,
		MatchLabels: req.DiscoverySelector.MatchLabels,
	}
	instances.TraceInfo = &TraceInfo{
		TraceEnabled: req.TraceInfo.TraceEnabled,
		SamplingRate: req.TraceInfo.SamplingRate,
		Service:      req.TraceInfo.Service,
		Address:      req.TraceInfo.Address,
	}
}
