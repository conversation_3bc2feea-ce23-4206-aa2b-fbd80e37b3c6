package internal

import (
	"errors"
	"strings"

	"github.com/jinzhu/copier"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
)

type CrdView struct {
	Name      string `json:"name,omitempty"`
	Namespace string `json:"namespace,omitempty"`
	Kind      string `json:"kind,omitempty"`
	UpdatedAt string `json:"updatedAt,omitempty"`
	Content   string `json:"content,omitempty"`
}

type CrdListView struct {
	CrdList []CrdView `json:"crdList"`
}

type CrdInfo struct {
	Name      string `json:"name,omitempty"`
	Namespace string `json:"namespace,omitempty"`
	Kind      string `json:"kind,omitempty"`
	UpdatedAt string `json:"updatedAt,omitempty"`
	Content   string `json:"content,omitempty"`
}
type CrdPageView struct {
	PageSize   int64     `json:"pageSize"`
	PageNo     int64     `json:"pageNo"`
	Order      string    `json:"order"`
	OrderBy    string    `json:"orderBy"`
	TotalCount int64     `json:"totalCount"`
	Result     []CrdView `json:"result"`
}

func (crd *CrdView) FromModel(crdModel *meta.Crd) (err error) {
	if crdModel == nil {
		return errors.New("crd model is nil")
	}

	err = copier.Copy(crd, crdModel)
	if err != nil {
		return err
	}
	// 处理cluster级别的CRD. 目前只有ApplicationProtocol是cluster级别
	if strings.EqualFold(crdModel.Kind, meta.ApplicationProtocol) {
		crd.Namespace = "*"
	}

	return nil
}

func (crdPage *CrdPageView) FromModel(crdModels []meta.Crd) (err error) {
	if len(crdModels) == 0 {
		return errors.New("there is no crd model")
	}

	err = copier.Copy(crdPage, crdModels)
	if err != nil {
		return err
	}

	return nil
}
