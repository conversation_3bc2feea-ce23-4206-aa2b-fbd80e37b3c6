package internal

const (
	QueryListToken = ","

	QueryKeywordType = "keywordType"
	QueryKeyword     = "keyword"
)

type CsmMeshResponse struct {
	Result interface{} `json:"result"`
}

type PageView struct {
	PageSize   int64         `json:"pageSize"`
	PageNo     int64         `json:"pageNo"`
	Order      string        `json:"order"`
	OrderBy    string        `json:"orderBy"`
	TotalCount int64         `json:"totalCount"`
	Result     []interface{} `json:"result"`
}
