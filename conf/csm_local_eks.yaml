app: csm

cce:
  iamProfile: "staging"

server:
  listen: 0.0.0.0
  port: 9905
  write_timeout: 60s
  pprof_enabled: true
  metric_enabled: true
  # 开启访问 request 日志
  access_log: true
  # true 表示在控制台打印启动参数，false 表示不打印
  print_flag: false

region: "global"
serviceName: "bce:csm"
bceServiceRole: "BceServiceRole_csm"

# 托管集群信息
cloud:
  # ACG-云计算业务 > 16-云原生平台 > 服务网格 CSM
  # 访问托管集群所使用的 ak sk(属于智能云账户公有云服务网格 BCE:CSM 资源账号的 ak、sk)
  hosting:
    access_key: "ALTAKrl2vcxH633BZpOeuvxHC6"
    secret_key: "41bb7511667f461598b7d49c644f5b8d"

  iamProfile: "staging"
  hostingRegion:
    bj:
      clusterId: "cce-rbt8vuyb"
      clusterName: "bj-spec-vpc-test"
    bd:
      clusterId: "bd-xx"
      clusterName: "bd-xx"
    su:
      clusterId: "su-xx"
      clusterName: "su-xx"
    fwh:
      clusterId: "fwh-xx"
      clusterName: "fwh-xx"
    gz:
      clusterId: "cce-e3ca4ffj"
      clusterName: "gz-offline"
    hkg:
      clusterId: "hkg-xx"
      clusterName: "hkg-xx"
  vpc:
    endpoint: "http://bcc.%s.baidubce.com"
  cce:
    endpoint: "http://cce.%s.baidubce.com"
  blb:
    endpoint: "http://blb.%s.baidubce.com"
  eip:
    endpoint: "http://eip.%s.baidubce.com"
  cert:
    endpoint: "http://certificate.baidubce.com"
  bls:
    endpoint: "**************:8085" # 暂使用VIP，待OpenAPI上线后改为"http://bls.%s.baidubce.com:8085"
  regions:
    - bd
    - bj
    - fwh
    - gz
    - hkg
    - su

# 配置支持的服务网格版本
istio:
  standalone:
    aeraki:
      hub: "registry.baidubce.com/csm-offline/aeraki"
      imagePullPolicy: "Always"
      supportVersion: # istio 对应的 aeraki 控制平面版本
        "1.14.6": "1.2.3"
        "1.16.5": "1.3.0"
    iop:
      hub: "registry.baidubce.com/csm-offline"
    version:
      - "1.13.2"
      - "1.14.6"
      - "1.16.5"
  hosting:
    iop:
      hub: "registry.baidubce.com/csm-offline"
    version:
      - "1.14.6-baidu"
    higress:
      hub: "registry.baidubce.com/csm"
      image: "higress"
      tag: "dev"

# 数据库配置
mysql:
  dsnOptions: "charset=utf8&parseTime=true&loc=Local&timeout=3s&readTimeout=8s&writeTimeout=3s"
  driver: "mysql"
  user: "root"
  password: "vm531@Vrp"
  host: "bjhw-bce-cnap01.bjhw.baidu.com:8306"
  database: "bce_csm_logic_qasandbox"

eks:
  profile: "grey"
  grey:
    clusterId: "cce-v8rhd9sv"
    clusterName: "native-bce-trial-bjlsh"
  online:
    clusterId: "cce-6c2qtavn"
    clusterName: "csm-primary-bjdd"
  accountIds:
    - 0c0b3c9dbb6e41308d3bfd587d908922

local:
  # 本地开发的开关, true表示通过本地ak，sk调用cce
  dev: false
  # 是否表示跳过iam middleware, 沙盒环境需置为false
  mock: false
  mockUserID: 144581026ccf4c419c8b1f8cc7e135fa
  mockDomainID: 144581026ccf4c419c8b1f8cc7e135fa

  # 本地访问 cce 所使用的 ak sk(属于智能云账户 CCE_jpaas_测试账号 eca97e148cb74e9683d7b7240829d1ff (ACG内部测试账号))
  access_key: "ALTAKgEYq7AE0lP3wECucIC3Ba"
  secret_key: "48261c922bc94bbabf645398addc5355"

  # CProm 测试用
  # access_key: "e0c68be8495540f280b3e9ef03ec25d2"
  # secret_key: "b599d5f4f30e41bcb0e1482c9de65bd6"

iam:
  profile:
    staging:
      username: csm
      password: Zbg3QA69fJKfw8sAXNSWrcCuKicSe7g8
      default_domain: default
      subuser_support: true
      auth_method: password
      access_key:
      secret_key:
      token_cache_size: 1000
      secret_cache_size: 1000
      active_cache_size: 1000
      region: bj
      host: iam.bj.internal-qasandbox.baidu-int.com
      port: 80
      stshost: sts.bj.internal-qasandbox.baidu-int.com
      stsport: 8586
  default: staging
  user_id_map:
    online:

proxy:
  enableCheckPermission: true
lanFilter:
  domainWhiteList:
    - hub.baidubce.com
    - hub-auth.baidubce.com
    - index.docker.io
    - mirror.baidubce.com
  ipWhiteList:

userWhiteList:
  host: "**********"
  endpoint: "http://user-config.internal-qasandbox.baidu-int.com:8690"
  requestPath: "/v1/settings/acl/get"
  requestBody:
    featureType: "EnableCSM"
    aclType: "AccountId"

monitor:
  iamProfile: "staging"
  profile: "internal"
  internal:
    host:
      gz: "http://*************:28794"
      bj: "http://**************:8794"
      bd: "http://***********:8794"
      fwh: "http://***********:8794"
      hkg: "http://**********:8794"
      su: "http://**************:8794"
    instancesUrl: "/api/v1/cprom/instances"
    agentUrl: "/api/v1/cprom/instances/%s/binding_cluster"
    scrapeJobUrl: "/api/v1/cprom/scrape_jobs?instanceID=%s&agentID=%s"
    deleteScrapeJobUrl: "/api/v1/cprom/scrape_jobs/%s?instanceID=%s&agentID=%s"
  online:
    host: "http://cprom.%s.baidubce.com"
    instancesUrl: "/v1/instances"
    agentUrl: "/v1/instances/%s/binding_cluster"
    scrapeJobUrl: "/v1/scrape_jobs?instanceID=%s&agentID=%s"
    deleteScrapeJobUrl: "/v1/scrape_jobs/%s?instanceID=%s&agentID=%s"
  sandbox:
    host: "http://*************:8794"
    instancesUrl: "/api/v1/cprom/instances"
    agentUrl: "/api/v1/cprom/instances/%s/binding_cluster"
    scrapeJobUrl: "/api/v1/cprom/scrape_jobs?instanceID=%s&agentID=%s"
    deleteScrapeJobUrl: "/api/v1/cprom/scrape_jobs/%s?instanceID=%s&agentID=%s"
  scrape_job:
    template:
      istio: "istio_scrape_job.yaml"
      envoy: "envoy_scrape_job.yaml"
      gateway: "gateway_scrape_job.tmpl"

users:
  userInfo:
    数云科技:
      - 35d9ca8a69a243f0b2aa5864fd1eca29
    内部混合云账号EKS/集团云:
      - 0c0b3c9dbb6e41308d3bfd587d908922
    超境汽车:
      - 735f05c2245146019c18d2d88887fc5c
    QA测试账号:
      - 1a3ad1bcb67449ad992245e690e1a442
    集度汽车:
      - 5f5ac06141d44783a7e67491f3b0edc1
      - f7ac4b5b395846389a889f7b89e9f030
    RD测试账号:
      - 144581026ccf4c419c8b1f8cc7e135fa