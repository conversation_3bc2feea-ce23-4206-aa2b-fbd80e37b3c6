---
description: 
globs: 
alwaysApply: false
---
# Coding Standards and Best Practices

This project follows specific coding standards for maintainability and consistency.

## General Principles

### Code Modification Guidelines
- **Preserve existing code**: Avoid modifying existing code when possible
- **Comment preservation**: Keep original comments, add new ones for modifications
- **Logging**: Add comprehensive logging at key function points for debugging
- **Error handling**: Use established error handling patterns from [pkg/error](mdc:pkg/error)

### Function Design
- **Context passing**: Always pass `csmContext.CsmContext` as first parameter for handlers
- **Transaction support**: Implement `WithTx()` methods for database operations
- **Validation**: Create dedicated validation methods (e.g., `ValidateRewriteConfig()`)
- **Response formatting**: Use consistent response structures

## Data Structure Patterns

### Request/Response Models
- Define in [pkg/model/meta](mdc:pkg/model/meta) directory
- Use proper JSON tags and validation tags
- Include helpful comments for field purposes
- Example: `AIRouteRequest`, `AIRouteResponse` in [pkg/model/meta/airoute.go](mdc:pkg/model/meta/airoute.go)

### Database Models
- Include `dbutil.BaseModel` for common fields
- Use proper GORM tags for database mapping
- Implement `TableName()` method
- Follow naming convention: `t_模块_实体`

## API Handler Patterns

### Parameter Validation
```go
// Get path parameters
instanceId := ctx.Param("instanceId")
if instanceId == "" {
    return csmErr.NewMissingParametersException("instanceId")
}

// Bind request body
request := &meta.SomeRequest{}
if err := ctx.Bind(request); err != nil {
    return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
}
```

### Logging Standards
```go
ctx.CsmLogger().Infof("Starting operation with param: %s", param)
// ... operation logic ...
if err != nil {
    ctx.CsmLogger().Errorf("Operation failed: %v", err)
    return err
}
ctx.CsmLogger().Infof("Operation completed successfully")
```

### Response Formatting
```go
response := &meta.SomeResponse{
    Field1: value1,
    Field2: value2,
}
return ctx.JSON(http.StatusOK, response)
```

## Configuration Management

### Validation Methods
- Create dedicated validation methods for complex configurations
- Include parameter format validation
- Provide clear error messages
- Example: `ValidateRewriteConfig()`, `ValidateTrafficDistribution()`

### Default Values
- Use pointer types for optional fields with defaults
- Implement helper methods like `GetUnlimitedQuota()`
- Document default behaviors clearly

## Testing Guidelines

### File Organization
- Test files alongside implementation: `service_test.go`
- Use meaningful test names: `TestService_Create`
- Mock external dependencies using gomock

### Testing Patterns
```go
func TestFunction_Success(t *testing.T) {
    // Setup
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    
    // Test logic
    // Assertions
    assert.Nil(t, err)
}
```

## Hard-Coding Avoidance
- Use configuration-driven behavior instead of hard-coded values
- Implement feature flags and toggles
- Make algorithms and strategies configurable
- Example: Dynamic rewrite configuration instead of hard-coded `Uri: "/"`
