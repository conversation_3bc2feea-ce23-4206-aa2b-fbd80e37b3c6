---
description: 
globs: 
alwaysApply: true
---
当生成产品需求 PRD 文档时，请严格按照以下结构和格式：

### 1. 文档头部
[产品功能名称]文档

TODO：
1. [待完成事项1]
2. [待完成事项2]
3. [待完成事项3]

### 2. 背景章节 (第1章)
# 1 背景
---

[详细描述当前面临的问题、挑战和需求背景]

## 1.1 名词解释
* **术语1：** [详细解释]
* **术语2：** [详细解释]
* **术语3：** [详细解释]

### 3. 设计目标章节 (第2章)

# 2 设计目标
---

## 2.1 需求说明
[描述具体的需求场景]

### 情况一：[场景名称]
[详细描述第一种场景及其问题]

### 情况二：[场景名称]  
[详细描述第二种场景及其问题]

## 2.2 实现的功能
针对上述的问题，需要进行不同的解决方式。

### 针对[问题1]的解决方案
**简单来说，[核心解决思路]。**

[详细的解决方案描述，包括：]
1. **关键点1**
2. **关键点2**

### 针对[问题2]的解决方案
**简单来说，[核心解决思路]。**

[详细的解决方案描述]

## 总结
整个方案的改造点共计分为[X]个主要部分：
1. **功能点1**
2. **功能点2**
3. **功能点3**