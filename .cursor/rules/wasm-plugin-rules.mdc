---
description: 
globs: 
alwaysApply: true
---
# Higress Wasm-Go 插件开发指南

Higress 提供了强大的 WebAssembly (Wasm) 插件机制，允许使用 Go 语言开发高性能、安全的网关扩展。本指南详细介绍了使用 Go 语言开发 Wasm 插件的全过程。

## 插件目录结构

一个标准的 Wasm-Go 插件应当遵循以下目录结构：

```
extensions/
└── your-plugin-name/
    ├── main.go            # 插件主入口文件
    ├── go.mod             # Go 模块定义
    ├── go.sum             # Go 依赖校验和
    ├── config/            # 配置相关代码（可选）
    │   └── config.go
    ├── README.md          # 插件文档
    ├── VERSION            # 插件版本号
    └── .buildrc           # 构建配置（可选）
```

## 插件基本结构

最简单的插件看起来如下（参考 [hello-world](mdc:plugins/wasm-go/extensions/hello-world/main.go)）：

```go
package main

import (
        "github.com/alibaba/higress/plugins/wasm-go/pkg/wrapper"
        "github.com/tetratelabs/proxy-wasm-go-sdk/proxywasm"
        "github.com/tetratelabs/proxy-wasm-go-sdk/proxywasm/types"
        "github.com/tidwall/gjson"
)

func main() {
        wrapper.SetCtx(
                // 插件名称
                "my-plugin",
                // 为解析插件配置，设置自定义函数
                wrapper.ParseConfigBy(parseConfig),
                // 为处理请求头，设置自定义函数
                wrapper.ProcessRequestHeadersBy(onHttpRequestHeaders),
        )
}

// 自定义插件配置
type MyConfig struct {
        mockEnable bool
}

// 在控制台插件配置中填写的yaml配置会自动转换为json，此处直接从json这个参数里解析配置即可
func parseConfig(json gjson.Result, config *MyConfig, log wrapper.Log) error {
        // 解析出配置，更新到config中
    	config.mockEnable = json.Get("mockEnable").Bool()
        return nil
}

func onHttpRequestHeaders(ctx wrapper.HttpContext, config MyConfig, log wrapper.Log) types.Action {
        proxywasm.AddHttpRequestHeader("hello", "world")
        if config.mockEnable {
                proxywasm.SendHttpResponse(200, nil, []byte("hello world"), -1)
        }
        return types.ActionContinue
}
```

## 插件生命周期

Higress Wasm-Go 插件支持以下生命周期钩子：

1. **请求处理**
   - `ProcessRequestHeadersBy`: 处理请求头
   - `ProcessRequestBodyBy`: 处理请求体
   - `PostProcessRequestBy`: 请求处理后回调

2. **响应处理**
   - `ProcessResponseHeadersBy`: 处理响应头
   - `ProcessResponseBodyBy`: 处理响应体
   - `PostProcessResponseBy`: 响应处理后回调

3. **其他钩子**
   - `ProcessStreamingDataBy`: 处理流式数据
   - `PostProcessStreamingDataBy`: 流式数据处理后回调
   - `BuildContext`: 自定义上下文构建

## 插件配置

插件配置通常定义在 `config/config.go` 文件中，使用结构体定义配置项，并添加相应的注释以生成 UI 配置界面。配置项支持通过标签指定验证规则。

配置示例（参考 [ai-proxy](mdc:plugins/wasm-go/extensions/ai-proxy/config/config.go)）：

```go
// @Name ai-proxy
// @Category custom
// @Phase UNSPECIFIED_PHASE
// @Priority 0
// @Title zh-CN AI代理
// @Description zh-CN 通过AI助手提供智能对话服务
// @IconUrl https://img.alicdn.com/imgextra/i1/O1CN018iKKih1iVx287RltL_!!6000000004419-2-tps-42-42.png
// @Version 0.1.0
//
// @Contact.name CH3CHO
// @Contact.url https://github.com/CH3CHO
// @Contact.email <EMAIL>
//
// @Example
// { "provider": { "type": "qwen", "apiToken": "YOUR_DASHSCOPE_API_TOKEN", "modelMapping": { "*": "qwen-turbo" } } }
// @End
type PluginConfig struct {
    // @Title zh-CN AI服务提供商配置
    // @Description zh-CN AI服务提供商配置，包含API接口、模型和知识库文件等信息
    providerConfigs []provider.ProviderConfig `required:"true" yaml:"providers"`
    
    // ...更多配置项
}

// 从JSON解析配置
func (c *PluginConfig) FromJson(json gjson.Result) {
    // 配置解析逻辑
}

// 验证配置有效性
func (c *PluginConfig) Validate() error {
    // 配置验证逻辑
    return nil
}

// 完成配置初始化
func (c *PluginConfig) Complete() error {
    // 配置完成逻辑
    return nil
}
```

## 构建与发布

## 环境要求

- Go 版本: >= 1.18（需要支持泛型特性）
- TinyGo 版本: >= 0.28.1

## 插件部署与使用

创建 WasmPlugin 自定义资源以部署插件：

```yaml
apiVersion: extensions.higress.io/v1alpha1
kind: WasmPlugin
metadata:
  name: your-plugin-name
  namespace: higress-system
spec:
  defaultConfig:
    # 全局默认配置
    key: "value"
  matchRules:
    # 路由级生效配置
    - ingress:
      - default/foo
      config:
        key: "foo-value"
    # 域名级生效配置
    - domain:
      - "*.example.com"
      config:
        key: "domain-value"
  url: oci://your-registry/your-plugin-name:1.0.0
```

## 最佳实践

1. **配置验证**：实现 `Validate()` 方法检查配置有效性
2. **错误处理**：始终检查 API 调用返回的错误
3. **流式处理**：处理大型请求/响应时使用流式 API
4. **内存管理**：注意 Wasm 环境中的内存限制，避免过大的内存分配
5. **编写单元测试**：使用 E2E 测试验证插件功能
6. **完善文档**：提供详细的 README.md 说明插件功能和配置

## 高级功能

### 路由级/域名级匹配

插件可以配置为仅对特定路由或域名生效，详见部署示例。

### 流式数据处理

处理 SSE（Server-Sent Events）等流式协议：

```go
wrapper.ProcessStreamingDataBy(onStreamingData)

func onStreamingData(ctx wrapper.HttpContext, data []byte, config YourConfig, log wrapper.Log) ([]byte, error) {
    // 处理流式数据
    return processedData, nil
}
```

### HTTP 回调

在插件中发起 HTTP 调用：

```go
wrapper.MakeHttpCall("cluster", headers, body, trailers, timeout, callback)
```

## 插件开发工具

1. **本地调试**：`make local-all` 启动本地 Envoy 进行插件调试
2. **E2E 测试**：`PLUGIN_NAME=your-plugin-name make higress-wasmplugin-test`

## 常见问题

1. **编译错误**：确保使用兼容的 TinyGo 版本
2. **配置不生效**：检查 WasmPlugin 资源是否正确配置
3. **性能问题**：避免在插件中执行耗时操作，考虑使用异步回调
