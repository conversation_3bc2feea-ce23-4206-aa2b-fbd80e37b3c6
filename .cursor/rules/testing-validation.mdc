---
description: 
globs: 
alwaysApply: false
---
# Testing and Validation Patterns

This project follows specific testing patterns and validation strategies for reliable code delivery.

## Testing Environment Setup

### Test Configuration
- Test kubeconfig: [testdata/aigw-offline-bj](mdc:testdata/aigw-offline-bj)
- Environment variables: `TEST_KUBE=../../testdata/aigw-offline-bj`, `EXTERNAL_IP=*************`
- Test namespace: `istio-system-aigw-wdv5qgx7`

### Plugin Testing
- Build commands use `registry.baidubce.com/csm-offline/wasm-go` registry
- Image tags use current date format: `20250617`
- Reference [document/技术验证](mdc:document/技术验证) for validation guides

## Validation Patterns

### Configuration Validation
- Create dedicated validation methods for complex configs
- Example patterns from [pkg/model/meta/airoute.go](mdc:pkg/model/meta/airoute.go):
  - `ValidateRewriteConfig()` - Path rewrite validation
  - `ValidateTrafficDistribution()` - Multi-service configuration
  - `ValidateTokenRateLimitConfig()` - Rate limiting rules

### Parameter Validation
```go
// Required fields validation
if field == "" {
    return csmErr.NewMissingParametersException("fieldName")
}

// Format validation
if !strings.HasPrefix(path, "/") {
    return fmt.Errorf("path must start with '/'")
}

// Business logic validation
if enabled && requiredField == "" {
    return fmt.Errorf("requiredField is required when enabled")
}
```

## Test API Patterns

### Standard Test Route
```bash
curl -v http://$EXTERNAL_IP/test/v1/chat/completions \
-H "Content-Type: application/json" \
-d '{
  "model": "deepseek-r1:1.5b",
  "messages": [{"role": "user", "content": "hello!"}],
  "temperature": 0.7,
  "stream": false
}'
```

### Docker Build Commands
```bash
DOCKER_BUILDKIT=1 docker build --build-arg PLUGIN_NAME=plugin-name \
  --build-arg BUILDER=higress-registry.cn-hangzhou.cr.aliyuncs.com/plugins/wasm-go-builder:go1.20.14-tinygo0.29.0-oras1.0.0 \
  --build-arg GOPROXY=https://proxy.golang.org,direct \
  -t registry.baidubce.com/csm-offline/wasm-go/plugin-name:$(date +%Y%m%d) \
  ../../higress/plugins/wasm-go/
```

## Unit Testing Guidelines

### Test Structure
- Use `gomock` for mocking dependencies
- Follow `TestFunction_Scenario` naming convention
- Include both success and error scenarios
- Test with proper context and cleanup

### Test Example Pattern
```go
func TestService_Create(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    
    // Setup mocks
    mockDB, _ := gorm.Open("sqlite3", ":memory:")
    mockDao := daoMock.NewMockBaseInterface(ctrl)
    
    service := &Service{
        opt: NewOption(mockDB),
        dao: mockDao,
    }
    
    t.Run("success", func(t *testing.T) {
        mockCtx := context.MockNewCsmContext()
        mockDao.EXPECT().Save(mockCtx, gomock.Any()).Return(nil)
        
        err := service.Create(mockCtx, testModel)
        assert.Nil(t, err)
    })
    
    t.Run("error", func(t *testing.T) {
        // Error scenario testing
    })
}
```

## Integration Testing

### Kubernetes Integration
- Use real cluster environment for testing
- Verify VirtualService and DestinationRule creation
- Test with actual traffic routing
- Monitor logs for proper behavior

### Plugin Testing Steps
1. Build and push plugin image
2. Deploy WasmPlugin resource
3. Test configuration application
4. Verify traffic handling
5. Check monitoring and logs

## Validation Documentation

### Technical Validation
- Document test procedures in [document/技术验证](mdc:document/技术验证)
- Include environment setup steps
- Provide complete test scenarios
- Document expected results and troubleshooting

### Implementation Summaries
- Track implementation progress in [document/接口实现总结](mdc:document/接口实现总结)
- Include test coverage details
- Document any limitations or known issues
- Provide deployment and rollback procedures
