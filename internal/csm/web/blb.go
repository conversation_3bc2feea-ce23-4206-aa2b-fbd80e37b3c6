package web

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

type BindBlb struct {
	GatewayID      string        `json:"gatewayId" validate:"required_without=MeshInstanceId"`
	MeshInstanceID string        `json:"serviceMeshInstanceId" validate:"required_without=GatewayId"`
	NetworkConfig  NetworkConfig `json:"networkConfig" validate:"required"`
}

type UnBindBlb struct {
	IsReleaseBlb   bool   `json:"isReleaseBlb"`
	IsReleseEip    bool   `json:"isReleaseEip"`
	BlbID          string `json:"blbId"`
	EipID          string `json:"eipId"`
	GatewayID      string `json:"gatewayId"`
	MeshInstanceID string `json:"instanceId"`
}

type NetworkConfig struct {
	BlbID                string                    `json:"blbId" validate:"required"`
	NetworkType          meta.NetworkType          `json:"networkType" validate:"required"`
	ElasticPublicNetwork meta.ElasticPublicNetwork `json:"elasticPublicNetwork" validate:"required"`
}

func (bb *BindBlb) FillDefault()    {}
func (ubb *UnBindBlb) FillDefault() {}

type BindBlbResponse struct{}
