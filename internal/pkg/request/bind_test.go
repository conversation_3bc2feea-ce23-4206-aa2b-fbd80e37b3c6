package request

import (
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
)

type User struct {
	Name  string `json:"name" validate:"required"`
	Email string `json:"email"`
}

func (u *User) FillDefault() {}

func TestConvertAndCheck(t *testing.T) {
	testCases := []struct {
		name           string
		input          string
		expected       User
		expectedErrMsg string
	}{
		{
			name:  "simple",
			input: `{"name":"<PERSON>","email":"<EMAIL>"}`,
			expected: User{
				Name:  "<PERSON>",
				Email: "<EMAIL>",
			},
			expectedErrMsg: "",
		},
		{
			name:  "lack field",
			input: `{"name":"<PERSON>"}`,
			expected: User{
				Name:  "<PERSON>",
				Email: "",
			},
			expectedErrMsg: "",
		},
		{
			name:  "more field",
			input: `{"name":"<PERSON>","email":"<EMAIL>", "gender": "male"}`,
			expected: User{
				Name:  "<PERSON>",
				Email: "<EMAIL>",
			},
			expectedErrMsg: "",
		},
		{
			name:           "invalid json",
			input:          `{"name":"<PERSON>}`,
			expected:       User{},
			expectedErrMsg: "InvalidParameterValueException",
		},
		{
			name:  "validation error",
			input: `{"email":"<EMAIL>"}`,
			expected: User{
				Email: "<EMAIL>",
			},
			expectedErrMsg: "Field validation",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", strings.NewReader(tc.input))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			u := new(User)
			err := ConvertAndCheck(c, u)
			if err != nil {
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}

			if !reflect.DeepEqual(*u, tc.expected) {
				t.Errorf("expected: %v, got %v", tc.expected, *u)
			}
		})
	}
}
