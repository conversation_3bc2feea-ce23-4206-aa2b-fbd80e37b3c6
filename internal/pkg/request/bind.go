package request

import (
	"github.com/labstack/echo/v4"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/validate"
)

type BindData interface {
	FillDefault()
}

func ConvertAndCheck(ctx echo.Context, i BindData) error {
	// 绑定
	if err := ctx.Bind(i); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 填充默认值
	i.FillDefault()

	// 校验参数
	err := validate.Validator.Struct(i)
	if err != nil {
		return err
	}
	return nil
}
