# AI网关实例扩容功能测试文档

## 功能概述
本次实现为AI网关实例更新接口新增了网关扩容功能，允许用户通过API调整higress-gateway Deployment的副本数。

## 实现的功能

### 1. 接口文档更新
- 文件：`document/接口文档/实例管理/更新实例.md`
- 新增 `replicas` 参数，支持1-5的副本数设置
- 更新了请求示例、响应示例和参数说明

### 2. 数据模型修改
- 文件：`pkg/model/meta/aigateway.go`
- `UpdateAIGatewayResult` 结构体新增 `Replicas` 字段
- 使用指针类型支持可选参数

### 3. 接口实现
- 文件：`cmd/csm/app/core/aigateway.go`
- 更新接口请求结构体，新增 `replicas` 字段
- 添加参数验证逻辑（1-5范围检查）
- 完善日志记录

### 4. 服务层实现
- 文件：`pkg/service/aiingress/service.go`
- 修改 `UpdateAIGateway` 方法签名，新增 `replicas` 参数
- 实现 `scaleHigressGateway` 方法，负责实际的Kubernetes Deployment扩容
- 添加事务性操作，扩容失败时回滚数据库状态

### 5. 接口定义更新
- 文件：`pkg/service/aiingress/interface.go`
- 更新 `UpdateAIGateway` 接口定义

## 核心实现逻辑

### 扩容流程
1. **参数验证**：检查副本数是否在1-5范围内
2. **数据库更新**：先更新数据库中的副本数记录
3. **Kubernetes操作**：
   - 获取托管集群客户端
   - 查找 `higress-gateway` Deployment
   - 更新副本数
   - 添加重启注解确保Pod更新
   - 等待Deployment就绪
4. **错误处理**：扩容失败时回滚数据库状态

### 安全机制
- **参数验证**：严格限制副本数范围（1-5）
- **原子性操作**：扩容失败时自动回滚数据库状态
- **超时控制**：等待Deployment就绪最多5分钟
- **详细日志**：记录每个步骤的执行情况

## 测试建议

### 1. 单元测试
```bash
# 测试参数验证
curl -X PUT /api/aigw/v1/aigateway/{instanceId} \
  -H "Content-Type: application/json" \
  -d '{"replicas": 0}'  # 应该返回参数错误

curl -X PUT /api/aigw/v1/aigateway/{instanceId} \
  -H "Content-Type: application/json" \
  -d '{"replicas": 6}'  # 应该返回参数错误
```

### 2. 功能测试
```bash
# 正常扩容测试
curl -X PUT /api/aigw/v1/aigateway/{instanceId} \
  -H "Content-Type: application/json" \
  -d '{"replicas": 3}'  # 应该成功扩容到3个副本

# 验证扩容结果
kubectl get deployment higress-gateway -n istio-system-{instanceId}
```

### 3. 边界测试
- 测试最小副本数（1）
- 测试最大副本数（5）
- 测试相同副本数（无变化）
- 测试网络异常情况
- 测试Kubernetes集群不可用情况

## 注意事项

### 1. 兼容性
- 新增的 `replicas` 参数为可选参数，不影响现有功能
- 响应结构向后兼容

### 2. 性能影响
- 扩容操作可能需要几分钟时间
- 建议在业务低峰期进行扩容操作
- 扩容过程中可能有短暂的服务波动

### 3. 监控建议
- 监控扩容操作的成功率
- 监控Deployment就绪时间
- 监控扩容后的资源使用情况

## 错误处理

### 常见错误场景
1. **参数错误**：副本数超出范围
2. **权限错误**：无法访问Kubernetes集群
3. **资源不足**：集群资源不足以支持扩容
4. **网络错误**：与Kubernetes API通信失败
5. **超时错误**：Deployment就绪超时

### 错误恢复
- 数据库状态自动回滚
- 详细错误日志便于问题排查
- 用户可重试操作

## 后续优化建议

1. **异步处理**：考虑将扩容操作改为异步处理，避免长时间阻塞
2. **进度查询**：提供扩容进度查询接口
3. **批量操作**：支持批量实例扩容
4. **自动扩容**：基于负载指标的自动扩容功能
5. **扩容策略**：支持更多扩容策略（如滚动更新策略）
